create table integrated_ddml.flow (
  snapshot_date date(14), --Date when running the ETL job
  --Columns from value:
  flow_id varchar,
  flow_set varchar,
  flow_name varchar,
  enabled boolean,
  matching_strategy varchar,
  location_markets varchar, --value.location_markets as comma separated string
  matching_diner_types varchar, --value.matching_diner_types as comma separated string
  matching_order_type varchar, --value.matching_diner_types as comma separated string
  matching_query_tokens varchar, --value.matching_diner_types as comma separated string
  matching_query_type varchar, --value.matching_diner_types as comma separated string
  matching_mealtime varchar, --value.matching_diner_types as comma separated string
  matching_application varchar, --value.matching_diner_types as comma separated string
  updated_user varchar,
  updated_timestamp timestamp,
  --columns from --value.routing_groups[i]
  group_name varchar,
  group_order int,
  routing_percentage float,
  variation varchar,
  ensemble_name varchar,
  ensemble_weight varchar
) WITH (
  external_location = 'S3://grubhub-dl-data-assets-prod/integrated_ddml.db/flow',
  format = 'PARQUET',
  partitioned_by = ARRAY['snapshot_date']
  );


create table integrated_ddml.market(
   snapshot_date date(14), --Date when running the ETL job
   --Columns from value:
   market_name varchar,
   geo_polygon varchar,
   geohashes varchar, --Convert to comma separated string
   city varchar,
   state varchar,
   zipcode varchar,
   region_uuid varchar,
   region_name varchar,
   updated_user varchar,
   updated_timestamp timestamp
) WITH (
   external_location = 'S3://grubhub-dl-data-assets-prod/integrated_ddml.db/market',
   format = 'PARQUET',
   partitioned_by = ARRAY['snapshot_date']
);


create table integrated_ddml.ensemble(
    snapshot_date date(14), --Date when running the ETL job
    --Columns from value:
    ensemble_name varchar,
    ensemble_description varchar,
    signature_def varchar,
    models varchar, --Convert to comma separated string
    weights varchar --Save value.ensemble_weights as a json string
) WITH (
    external_location = 'S3://grubhub-dl-data-assets-prod/integrated_ddml.db/ensemble',
    format = 'PARQUET',
    partitioned_by = ARRAY['snapshot_date']
);


create table integrated_ddml.model(
    snapshot_date date, --Date when running the ETL job
    model_name varchar,
    model_description varchar,
    model_type varchar,
    signature_def varchar,
    location varchar,
    version varchar,
    versioning_strategy varchar,
    model_features varchar --Save value.model_features as a json string
) WITH (
   external_location = 'S3://grubhub-dl-data-assets-prod/integrated_ddml.db/model',
   format = 'PARQUET',
   partitioned_by = ARRAY['snapshot_date']
);


create table integrated_ddml.flow_request(
    request_date date, --date(request_time)
    request_time datetime,
    request_duration_ms bigint,
    --Columns from request:
    request_application_id varchar,
    request_diner_id varchar,
    request_flow_set varchar,
    request_lat varchar,
    request_lng varchar,
    request_total_orders varchar,
    request_variation_id varchar,
    request_diner_type varchar,
    request_resolved_geohash varchar,
    request_tracking_id varchar,
    request_application_type varchar,
    request_query_tokens varchar, --As comma separated string
    request_mealtime varchar,
    request_query_type varchar,
    request_order_type varchar,
    request_when_for varchar,
    caller_tracking_id varchar,
    --Columns from enriched_request:
    enr_request_diner_type varchar,
    enr_request_application_type varchar,
    enr_request_query_tokens varchar, --As comma separated string
    enr_request_mealtime varchar,
    enr_request_query_type varchar,
    --Columns from response:
    flow_request_id varchar,
    tracking_id varchar,
    flow_id varchar,
    flow_set varchar,
    flow_name varchar,
    --Columns from response.routing_group:
    routing_group varchar,
    routing_percentage float,
    routing_rand float,
    variation varchar,
    ensemble_name varchar,
    ensemble_weight varchar,
    --Columns from response.matching_strategy.match:
    match_market_id varchar,
    match_geohash varchar,
    match_diner_type varchar,
    match_order_type varchar,
    match_mealtime varchar,
    match_application_type varchar,
    match_query_type varchar,
    match_query_tokens varchar --As comma separated string
) WITH (
   external_location = 'S3://grubhub-dl-data-assets-prod/integrated_ddml.db/flow_request',
   format = 'PARQUET',
   partitioned_by = ARRAY['request_date']
);



create table integrated_ddml.ensemble_invocation(
    request_date date, --date(request_time)
    request_time datetime,
    request_duration_ms bigint,
    --Columns from request:
    caller_tracking_id varchar,
    ensemble_name varchar,
    ensemble_weight varchar,
    global_features varchar, --json string
    features varchar, --json string
    response varchar --json string

) WITH (
   external_location = 'S3://grubhub-dl-data-assets-prod/integrated_ddml.db/ensemble_invocation',
   format = 'PARQUET',
   partitioned_by = ARRAY['request_date']
);

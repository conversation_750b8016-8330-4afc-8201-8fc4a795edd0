package com.grubhub.garcon;

import com.datastax.oss.driver.api.querybuilder.QueryBuilder;
import com.datastax.oss.driver.api.core.CqlIdentifier;
import com.datastax.oss.driver.api.core.CqlSession;
import com.datastax.oss.driver.api.core.metadata.schema.RelationMetadata;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.test.cassandra.EmbeddedCassandraExt;
import io.vavr.collection.List;
import lombok.experimental.UtilityClass;

import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.KEYSPACE;

@UtilityClass
public class DaoUtils {

    public static void truncateAllTables(Casserole casserole, EmbeddedCassandraExt embeddedCassandraExt) {
        loadTableNames(embeddedCassandraExt).map(t -> QueryBuilder.truncate(KEYSPACE, t).build()).forEach(casserole::execute);
    }

    private static List<String> loadTableNames(EmbeddedCassandraExt embeddedCassandraExt) {
        CqlSession session = embeddedCassandraExt.getSession();
        List<String> tableNames = session
                .getMetadata()
                .getKeyspace(KEYSPACE)
                .get()
                .getTables()
                .values()
                .stream()
                .map(RelationMetadata::getName)
                .map(CqlIdentifier::toString)
                .collect(List.collector());
        session.close();
        return tableNames;

    }

    public static void truncateMarketTable(Casserole casserole) {
        casserole.execute(QueryBuilder.truncate("ddml_control_plane", "markets").build());
    }

}

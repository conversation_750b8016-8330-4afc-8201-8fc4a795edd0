package com.grubhub.garcon.shared;


import com.datastax.oss.driver.api.core.ConsistencyLevel;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.casserole.config.Config;
import com.grubhub.roux.json.RouxObjectMapper;
import com.grubhub.roux.test.cassandra.DataSetContainer;
import com.grubhub.roux.test.cassandra.MultiVersionCasseroleDataSet;
import com.grubhub.roux.test.casserole.builder.CasseroleBuilder;

public class SharedEmbeddedCassandra {
    public static final String KEYSPACE = "ddml_control_plane";
    public static final DataSetContainer DATA = DataSetContainer.of(new MultiVersionCasseroleDataSet("../data/cql", KEYSPACE));

    public static Casserole initCasserole(int port) {
        final Config config = Config.builder()
                .keyspace(KEYSPACE)
                .defaultReadConsistencyLevel(ConsistencyLevel.ONE)
                .defaultQueryTimeoutMs(3000)
                .port(port)
                .build();
        config.getQueryLogger().setEnabled(true);

        return CasseroleBuilder.buildCasserole(
                port,
                KEYSPACE,
                RouxObjectMapper.newStandard(),
                config
        );

    }

}

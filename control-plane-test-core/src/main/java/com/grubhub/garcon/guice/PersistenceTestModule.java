package com.grubhub.garcon.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.google.inject.Provides;
import com.google.inject.Scopes;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.MarketDao;
import com.grubhub.garcon.controlplane.cassandra.dao.impl.MarketDaoImpl;
import com.grubhub.garcon.shared.SharedEmbeddedCassandra;
import com.grubhub.roux.LazySingleton;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.test.cassandra.EmbeddedCassandraExt;

import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.DATA;

public class PersistenceTestModule extends AbstractModule {

    public static final EmbeddedCassandraExt EMBEDDED_CASSANDRA = new EmbeddedCassandraExt(DATA);

    @Override
    protected void configure() {
        bindScope(LazySingleton.class, Scopes.SINGLETON);
        bind(MarketDao.class).to(MarketDaoImpl.class).in(LazySingleton.class);
    }

    @Provides
    @LazySingleton
    @Named("casserole-v2-ddmlControlPlane")
    @Inject
    public Casserole controlPlaneData() {
        return SharedEmbeddedCassandra.initCasserole(EMBEDDED_CASSANDRA.getPort());
    }
}

package com.grubhub.garcon.controlplaneapi.resource.controlplane;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionResultDTO;
import com.grubhub.roux.api.SecurityDemand;
import com.grubhub.roux.api.responses.GetResponse;
import io.vavr.collection.List;
import lombok.NonNull;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;

@Path("ddmlcontrolplane/gdpactions")
public interface GdpActionsResource {

    @GET
    @Path("/process/pending/all")
    @SecurityDemand(claim = "gh-admin", claimKey = "DDML-CONTROL-PLANE")
    GetResponse<List<GdpActionResultDTO>> processPendingActions();

    @GET
    @Path("/{actionDate}")
    @SecurityDemand(claim = "gh-admin", claimKey = "DDML-CONTROL-PLANE")
    GetResponse<List<GdpActionResultDTO>> getAllProcessedForDate(@PathParam("actionDate") @NonNull String actionDate);

}

package com.grubhub.garcon.controlplaneapi.constraints;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.dto.ApplicationType;
import com.grubhub.garcon.controlplaneapi.annotations.ValidRoutingGroupCriteria;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class FlowValidator implements ConstraintValidator<ValidRoutingGroupCriteria, FlowApi> {

    @Override
    public boolean isValid(FlowApi flow, ConstraintValidatorContext context) {
        return notEmptyRoutingGroupCriteriaList(flow) &&
                eachCriteriaHasUniqueGroupNames(flow) &&
                routingGroupCriteriaContainsDefault(flow) &&
                validateAppSeparation(flow);
    }


    /**
     * Each criteria will only allow unique group_name fields.
     */
    private boolean eachCriteriaHasUniqueGroupNames(FlowApi flow) {
        for (Map.Entry<String, List<FlowRoutingGroupApi>> criteriaEntry : flow.getRoutingGroupsCriteria().entrySet()) {
            Map<String, List<FlowRoutingGroupApi>> groupsByName = criteriaEntry.getValue().stream()
                    .collect(Collectors.groupingBy(FlowRoutingGroupApi::getGroupName));

            boolean isValid = groupsByName.values()
                    .stream()
                    .noneMatch(group -> group.size() > 1);

            if (!isValid) {
                log.error("Invalid flow flow_id={}, flow_name={} because the criteria={} has a duplicated group name.",
                        flow.getFlowId(), flow.getFlowName(), criteriaEntry.getKey());
                return false;
            }
        }

        return true;
    }

    /**
     * Validates that the routing_groups_criteria should never be empty.
     */
    private boolean notEmptyRoutingGroupCriteriaList(FlowApi flow) {
        boolean notEmpty = flow.getRoutingGroupsCriteria() != null && !flow.getRoutingGroupsCriteria().isEmpty();

        if (!notEmpty) {
            log.error("Invalid flow flow_id={}, flow_name={}. Field routing_groups_criteria is empty.", flow.getFlowId(), flow.getFlowName());
        }

        return notEmpty;
    }

    /**
     * Validates that the routing_groups_criteria should always contains the "default" group.
     */
    private boolean routingGroupCriteriaContainsDefault(FlowApi flow) {
        boolean containsDefaultGroup = flow.getRoutingGroupsCriteria() != null
                && flow.getRoutingGroupsCriteria().get("default") != null && !flow.getRoutingGroupsCriteria().get("default").isEmpty();
        if (!containsDefaultGroup) {
            log.error("Invalid flow flow_id={}, flow_name={} because the default routing group(s) is missing", flow.getFlowId(), flow.getFlowName());
        }
        return containsDefaultGroup;
    }

    private boolean validateAppSeparation(FlowApi flow) {
        return appNamesShouldBeValid(flow) && validAppSelectionCriteria(flow);
    }

    /**
     * Validates that the routing_groups_criteria has valid names:
     * - The "default" group.
     * - The rest should be valid app names.
     */
    private boolean appNamesShouldBeValid(FlowApi flow) {
        boolean validNames = flow.getRoutingGroupsCriteria().keySet().stream()
                .allMatch(criteria -> criteria.equals("default") || ApplicationType.isValidId(criteria));

        if (!validNames) {
            log.error("Invalid flow flow_id={}, flow_name={}. There are names for selection criteria that are not valid " +
                    "for routing group criteria separation.", flow.getFlowId(), flow.getFlowName());
        }

        return validNames;
    }

    /**
     * Validates a consistent app separation.
     * - If there are app groups within routing_groups_criteria, routing_group_set_election_criteria = 'matching_applications' should be defined.
     * - If routing_group_set_election_criteria = 'matching_applications' is defined, matching_applications should be empty.
     */
    private boolean validAppSelectionCriteria(FlowApi flow) {
        boolean criteriaIsMatchingApplications = StringUtils.isNotBlank(flow.getRoutingGroupSetElectionCriteria()) &&
                flow.getRoutingGroupSetElectionCriteria().equals("matching_applications");
        boolean hasMatchingApplications = flow.getMatchingApplications() != null && !flow.getMatchingApplications().isEmpty();
        List<String> appCriteriaList = flow.getRoutingGroupsCriteria().keySet().stream()
                .filter(ApplicationType::isValidId)
                .collect(Collectors.toList());

        if (!criteriaIsMatchingApplications && !appCriteriaList.isEmpty()) {
            log.error("Invalid flow flow_id={}, flow_name={}. There is a application separation in the routing groups. " +
                            "You need to add routing_group_set_election_criteria = 'matching_applications' as as part of the flow definition. "
                    , flow.getFlowId(), flow.getFlowName()
            );
            return false;
        }

        if (criteriaIsMatchingApplications && hasMatchingApplications) {
            log.error("Invalid flow flow_id={}, flow_name={}. It's not possible to separate the routing groups by app if the flow is also " +
                    "filtering by matching_applications, since it could bring conflicts. ", flow.getFlowId(), flow.getFlowName()
            );
            return false;
        }

        return true;
    }

    @Override
    public void initialize(ValidRoutingGroupCriteria constraintAnnotation) {
    }
}

package com.grubhub.garcon.controlplaneapi.constraints;

import com.grubhub.garcon.controlplaneapi.annotations.RoutingPercentageSum;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupApi;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public class RoutingPercentageSumValidator implements ConstraintValidator<RoutingPercentageSum, Map<String, List<FlowRoutingGroupApi>>> {

    @Override
    public void initialize(RoutingPercentageSum constraintAnnotation) {
    }

    @Override
    public boolean isValid(Map<String, List<FlowRoutingGroupApi>> routingGroups, ConstraintValidatorContext context) {
        return Option.of(routingGroups)
                .getOrElse(Collections::emptyMap)
                .entrySet()
                .stream()
                .allMatch(entry -> currentCriteriaIsValid(entry.getKey(), entry.getValue()));
    }

    private boolean currentCriteriaIsValid(String routingGroupCriteria, List<FlowRoutingGroupApi> routingGroups) {
        BigDecimal sum = routingGroups
                .stream()
                .map(FlowRoutingGroupApi::getRoutingPercentage)
                .map(f -> BigDecimal.valueOf(f).setScale(6, RoundingMode.HALF_UP))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        boolean result = sum.compareTo(BigDecimal.ONE) == 0;

        if (!result) {
            log.warn("The group for criteria routing_group_criteria={} is not valid, the percentages do not sum to 1", routingGroupCriteria);
        }
        return result;

    }
}

package com.grubhub.garcon.controlplaneapi.resource.controlplane;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketByGeohashDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.EntityCollectionDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketDTO;
import com.grubhub.roux.api.SecurityDemand;
import com.grubhub.roux.api.responses.DeleteResponse;
import com.grubhub.roux.api.responses.GetResponse;
import com.grubhub.roux.api.responses.UpdateResponse;
import io.vavr.collection.List;
import lombok.NonNull;

import javax.validation.Valid;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.QueryParam;

@Path("ddmlcontrolplane/markets")
public interface MarketResource {

    /**
     * Retrieve the market by market name
     * @param marketName name associated with the market
     * @return market associated with the market name
     */
    @GET
    @Path("/{marketName}")
    GetResponse<MarketDTO> findMarket(@PathParam("marketName") @NonNull String marketName);

    @PUT
    @SecurityDemand(claim = "gh-admin", claimKey = "DDML-CONTROL-PLANE")
    UpdateResponse createOrUpdateMarket(@Valid MarketDTO market);

    @DELETE
    @Path("/{marketName}")
    @SecurityDemand(claim = "gh-admin", claimKey = "DDML-CONTROL-PLANE")
    DeleteResponse deleteMarket(@PathParam("marketName") @NonNull String marketName);

    @GET
    @Path("/all")
    GetResponse<List<EntityCollectionDTO>> getAll();

    @GET
    @Path("/marketsByGeohashes")
    GetResponse<List<MarketByGeohashDTO>> getMarketByGeohashes(
            @QueryParam("precision") int precision,
            @QueryParam("geohash") java.util.List<String> geohashes
    );

    @DELETE
    @Path("/marketsByGeohashes")
    @SecurityDemand(claim = "gh-admin", claimKey = "DDML-CONTROL-PLANE")
    DeleteResponse deleteMarketByGeohashes(@QueryParam("precision") int precision, @QueryParam("geohashes") java.util.List<String> geohashes);

    @PUT
    @Path("/marketsByGeohashes")
    @SecurityDemand(claim = "gh-admin", claimKey = "DDML-CONTROL-PLANE")
    UpdateResponse updateMarketsByGeohashes(java.util.List<MarketByGeohashDTO> marketByGeohashes);
}

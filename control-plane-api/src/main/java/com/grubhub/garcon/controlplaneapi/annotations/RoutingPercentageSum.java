package com.grubhub.garcon.controlplaneapi.annotations;


import com.grubhub.garcon.controlplaneapi.constraints.RoutingPercentageSumValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = RoutingPercentageSumValidator.class)
public @interface RoutingPercentageSum {
    String message() default "The routing percentages sum must be equal to 1";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

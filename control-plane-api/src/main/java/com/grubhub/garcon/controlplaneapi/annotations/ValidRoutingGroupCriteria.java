package com.grubhub.garcon.controlplaneapi.annotations;

import com.grubhub.garcon.controlplaneapi.constraints.FlowValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = FlowValidator.class)
public @interface ValidRoutingGroupCriteria {
    String message() default "Invalid flow. routing_groups_criteria and/or routing_group_set_election_criteria are not valid. Check service logs.";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

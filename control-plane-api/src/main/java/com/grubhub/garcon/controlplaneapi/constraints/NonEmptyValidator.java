package com.grubhub.garcon.controlplaneapi.constraints;

import com.grubhub.garcon.controlplaneapi.annotations.NonEmpty;
import io.vavr.collection.Traversable;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

public class NonEmptyValidator implements ConstraintValidator<NonEmpty, Traversable<?>> {

    @Override
    public void initialize(NonEmpty constraintAnnotation) {}

    @Override
    public boolean isValid(Traversable<?> elements, ConstraintValidatorContext context) {
        return !(Objects.isNull(elements) || elements.isEmpty());
    }
}

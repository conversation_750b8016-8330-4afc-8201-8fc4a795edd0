package com.grubhub.garcon.ensembler.custom;

import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.ensembler.cassandra.models.Ensemble;
import io.vavr.control.Option;
import lombok.Value;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

@Value
public class OptionalEnsemble {
    Ensemble content;

    public static OptionalEnsemble of(Ensemble ensemble) {
        return new OptionalEnsemble(ensemble);
    }

    public Option<Ensemble> asOptional() {
        return Option.of(this.content);
    }

    public static OptionalEnsemble empty() {
        return of(null);
    }

    public <T> Option<T> map(Function<Ensemble, T> mapping) {
        return Option.of(this.content).map(mapping);
    }

    public OptionalEnsemble withModelList(Function<Ensemble, Ensemble> mapping) {
        return Optional.ofNullable(this.content)
                .map(mapping)
                .map(OptionalEnsemble::new)
                .orElse(empty());
    }

    public static OptionalEnsemble toDTO(Function<EnsembleDTO, Ensemble> mapping, EnsembleDTO ensembleDTO) {
        return of(mapping.apply(ensembleDTO));
    }

    public Option<Ensemble> trackUserAction(Supplier<String> email) {
        return asOptional()
                .peek(ensemble -> ensemble.setUpdatedUser(getEmail(email)))
                .peek(ensemble -> ensemble.setUpdatedTimestamp(Instant.now()));
    }

    private String getEmail(Supplier<String> email) {
        return StringUtils.isEmpty(content.getUpdatedUser()) ? email.get() : content.getUpdatedUser();
    }

    public void saveToCassandra(Consumer<Ensemble> consumer) {
        Optional.ofNullable(this.content).ifPresent(consumer);
    }
}

package com.grubhub.garcon.controlplane.bedrock;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeClient;

@Singleton
public class BedrockRuntimeClientProvider implements Provider<BedrockRuntimeClient> {

    private final BedrockRuntimeClient bedrockRuntimeClient;

    @Inject
    public BedrockRuntimeClientProvider(String region) {
        this.bedrockRuntimeClient = BedrockRuntimeClient.builder()
                .credentialsProvider(DefaultCredentialsProvider.create())
                .region(Region.of(region))
                .build();
    }

    @Override
    public BedrockRuntimeClient get() {
        return bedrockRuntimeClient;
    }
}

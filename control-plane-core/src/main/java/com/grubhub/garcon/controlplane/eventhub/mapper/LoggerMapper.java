package com.grubhub.garcon.controlplane.eventhub.mapper;

import com.grubhub.garcon.controlplane.dto.EnrichedResolveFlowRequest;
import com.grubhub.garcon.controlplane.eventhub.model.LoggedResolveFlowRequest;
import com.grubhub.garcon.controlplane.mapper.MapperUtil;
import org.mapstruct.Mapper;

@Mapper(uses = MapperUtil.class)
public interface LoggerMapper {

    LoggedResolveFlowRequest mapResolveFlowRequest(EnrichedResolveFlowRequest resolveFlowRequest);

}

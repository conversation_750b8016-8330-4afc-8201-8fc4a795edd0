package com.grubhub.garcon.controlplane.mapper;

import com.grubhub.garcon.controlplane.cassandra.models.Market;
import com.grubhub.garcon.controlplane.cassandra.models.MarketByGeohash;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketByGeohashDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface MarketMapper {

    @Mapping(target = "regionUuid", expression = "java(MapperUtil.uuidAsString(market.getRegionUuid()))")
    @Mapping(target = "geohashes", expression = "java(VavrMapper.toVavrSet(market.getGeohashes()))")
    MarketDTO toDTO(Market market);

    @Mapping(target = "regionUuid", expression = "java(MapperUtil.uuidFromString(market.getRegionUuid()))")
    @Mapping(target = "geohashes", expression = "java(VavrMapper.toJavaSet(market.getGeohashes()))")
    Market toModel(MarketDTO market);

    MarketByGeohash toModel(MarketByGeohashDTO marketByGeohash);
    MarketByGeohashDTO toDTO(MarketByGeohash marketByGeohash);

}

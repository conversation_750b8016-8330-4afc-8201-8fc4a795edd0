package com.grubhub.garcon.controlplane.s3;

import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import com.google.inject.Inject;
import javax.inject.Provider;

public class S3ClientProvider implements Provider<S3Client> {

    @Inject
    private AwsCredentialsProvider awsCredentialsProvider;

    @Override
    public S3Client get() {
        return S3Client.builder()
                .credentialsProvider(awsCredentialsProvider)
                .region(Region.US_EAST_1)
                .build();
    }
}

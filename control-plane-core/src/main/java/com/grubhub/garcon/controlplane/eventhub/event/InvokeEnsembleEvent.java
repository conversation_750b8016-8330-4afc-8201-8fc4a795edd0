package com.grubhub.garcon.controlplane.eventhub.event;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EnsembleInvocationRequest;
import io.vavr.collection.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class InvokeEnsembleEvent {
    private Long requestTime;
    private Long requestDurationMs;
    private EnsembleInvocationRequest request;
    private List<Float> response;
    private List<LoggedInvokeModel> modelsInvocations;
    private int pageNumber;
}

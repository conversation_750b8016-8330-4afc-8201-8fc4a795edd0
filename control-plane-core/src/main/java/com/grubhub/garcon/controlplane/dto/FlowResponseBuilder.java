package com.grubhub.garcon.controlplane.dto;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowResponseDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.RoutingGroupResponseDTO;
import com.grubhub.roux.uuid.UuidUtil;
import io.vavr.collection.List;
import lombok.Builder;
import lombok.Getter;
import lombok.val;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;
import java.util.UUID;

@Builder
@Getter
public class FlowResponseBuilder {
    private RoutingGroupResponseDTO routingGroupResponse;
    private FlowWithGeoHashDTO flowWithGeoHashDTO;
    private List<String> markets;
    private List<FeatureDTO> features;

    public String getFlowName() {
        return flowWithGeoHashDTO.getFlowName();
    }

    public String getVariationId() {
        return flowWithGeoHashDTO.getResolveFlowRequest().getVariationId();
    }

    public boolean isVariationIdNullOrEmpty() {
        val variationId = flowWithGeoHashDTO.getResolveFlowRequest().getVariationId();
        return Objects.isNull(variationId) || StringUtils.isEmpty(variationId);
    }

    public List<FlowByGeoHashDTO> getFlowByGeoHashes() {
        return flowWithGeoHashDTO.getFlowByGeoHashes();
    }

    public FlowByGeoHashDTO selectFirstFromList() {
        return flowWithGeoHashDTO.getFlowByGeoHashes().get(0);
    }

    public FlowByGeoHashDTO selectOneThatMatchesGeoHash() {
        return flowWithGeoHashDTO.getFlowByGeoHashes().toStream()
                .find(flowByGeoHash -> flowByGeoHash.getGeohash().equals(flowWithGeoHashDTO.getResolvedGeoHash()))
                .getOrElseThrow(RuntimeException::new);
    }

    public String getFlowId() {
        return UuidUtil.decode(flowWithGeoHashDTO.getFlowId()).toString();
    }

    public String getFlowSet() {
        return flowWithGeoHashDTO.getFlowSet();
    }

    public String getTrackingId() {
        return flowWithGeoHashDTO.getTrackingId();
    }

    public String getResolvedOrderType() {
        return flowWithGeoHashDTO.getResolvedOrderType();
    }

    public String getMealtime() {
        return flowWithGeoHashDTO.getMealtime();
    }

    public String getResolvedMealtime() {
        return flowWithGeoHashDTO.getResolvedMealtime();
    }

    public String getResolvedApplicationType() {
        return flowWithGeoHashDTO.getResolvedApplicationType();
    }

    public String getResolvedApplicationVersion() {
        return flowWithGeoHashDTO.getApplicationVersion();
    }

    public String getResolvedUserDomain() {
        return flowWithGeoHashDTO.getUserDomain();
    }

    public String getResolvedUserEmail() {
        return flowWithGeoHashDTO.getUserEmail();
    }

    public String getResolvedQueryType() {
        return flowWithGeoHashDTO.getResolvedQueryType();
    }

    public List<String> getResolvedQueryTokens() {
        return flowWithGeoHashDTO.getResolvedQueryTokens();
    }

    public List<String> getResolvedRequestedTags() {
        return flowWithGeoHashDTO.getResolvedRequestedTags();
    }

    public String getResolvedQueryKeyword() {
        return flowWithGeoHashDTO.getResolvedQueryKeyword();
    }

    public String getResolvedClientEntityId() {
        return flowWithGeoHashDTO.getResolvedClientEntityId();
    }

    public String getResolvedBrand() {
        return flowWithGeoHashDTO.getResolvedBrand();
    }

    public FlowResponseDTO build() {
        return FlowResponseDTO.builder()
                .flowRequestId(String.valueOf(UUID.randomUUID()))
                .flowId(getFlowId())
                .flowSet(getFlowSet())
                .routingGroup(getRoutingGroupResponse())
                .trackingId(getTrackingId())
                .flowName(getFlowName())
                .features(getFeatures())
                .build();
    }
}

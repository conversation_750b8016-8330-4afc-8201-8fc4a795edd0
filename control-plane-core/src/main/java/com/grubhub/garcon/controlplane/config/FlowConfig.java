package com.grubhub.garcon.controlplane.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.ensembler.config.ModelGroupConfig;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.val;

import java.util.Map;
import java.util.regex.Pattern;

@Data
@NoArgsConstructor
public class FlowConfig {
    private Integer dayDivisor = 4;
    private Integer precision = 5;
    private Boolean flipCoordinates = true;
    private Integer totalOrdersMax = 5;
    private Integer invokeEnsembleEventSamplesPerPage = 200;
    private Boolean eventHubLogEnabled = true;
    private Boolean eventHubLogEnsembleInvoke = true;
    private Boolean invokeEnsembleEventLogModelsInvocations = false;
    private Boolean configurationCaching = true;
    private Integer routingGroupsSeed = 0;
    private Integer routingGroupsGeohashPrecision = 9;
    private Boolean modelFeaturesStoreDefaultInCache = false;
    private Boolean ddmlConfigurationWriteEnabled = true;
    private Boolean ddmlConfigurationCleanDuringConfigureModels = false;
    private String ddmlConfigurationSeed = "default";
    private String flowSetList = "Search,Topics,SearchIntent,Offer";
    private Integer ddmlConfigurationDeleteBatchSize = 500;
    private Map<String, ModelGroupConfig> modelGroupConfigs;
    private Boolean cacheroleMultiGetFeatureEnabled = false;
    private Integer deleteModelsOlderThanDays = 40;
    private Boolean newGeohashesResolutionEnabled = false;

    public Stream<String> getFlowSetAsStream() {
        return Option.of(flowSetList)
                .map(aFlowSetList -> Stream.ofAll(Pattern.compile("\\,").splitAsStream(aFlowSetList)))
                .getOrElse(Stream::empty);

    }

    @JsonIgnore
    public String getTfsClusterNameForModel(ModelDTO modelDTO) {
        if (!this.getModelGroupConfigs().containsKey(modelDTO.getModelGroup())) {
            throw new RuntimeException(String.format("There is no model group %s for model %s found " +
                    "on tfs clusters %s", modelDTO.getModelGroup(), modelDTO.getModelName(), this.getModelGroupConfigs().keySet()));
        }
        val modelGroupConfig = this.getModelGroupConfigs().get(modelDTO.getModelGroup());
        return Option.of(modelGroupConfig.getTfsClusterName())
                .getOrElseThrow(() -> new RuntimeException(
                        String.format("There is no cluster %s for model %s within group %s found " +
                                        "on the following tfs clusters %s", modelGroupConfig.getTfsClusterName(),
                                modelDTO.getModelName(),
                                modelDTO.getModelGroup(),
                                this.getModelGroupConfigs().keySet()))
                );
    }

    public boolean isNewGeohashesResolutionEnabled() {
        return Option.of(this)
                .map(FlowConfig::getNewGeohashesResolutionEnabled)
                .getOrElse(false);
    }
}

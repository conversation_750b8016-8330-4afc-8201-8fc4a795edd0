package com.grubhub.garcon.controlplane.eventhub.event;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.grubhub.garcon.controlplane.dto.EnrichedResolveFlowRequest;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowResponseDTO;
import com.grubhub.garcon.controlplane.eventhub.model.LoggedResolveFlowRequest;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;

@Data
@Builder
public class ResolveFlowEvent {

    private LoggedResolveFlowRequest request;
    private FlowResponseDTO response;
    private Instant requestTime;
    private Long requestDurationMs;
    @JsonIgnore
    private EnrichedResolveFlowRequest enrichedResolveFlowRequest;

}

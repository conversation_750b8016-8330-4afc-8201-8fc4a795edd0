package com.grubhub.garcon.controlplane.dto;

import com.grubhub.garcon.controlplane.cassandra.models.DinerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class FlowByGeoHashDTO {
    private String flowSet;
    private String geohash;
    private UUID flowId;
    private String marketName;
    private DinerType resolvedDinerType;
}

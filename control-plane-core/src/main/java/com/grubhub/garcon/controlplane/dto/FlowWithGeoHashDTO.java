package com.grubhub.garcon.controlplane.dto;

import com.grubhub.garcon.controlplane.util.VersionMatcher;
import com.grubhub.garcon.controlplaneapi.models.OrderType;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupDTO;
import com.grubhub.roux.uuid.UuidUtil;
import io.vavr.collection.List;
import io.vavr.collection.Set;
import io.vavr.control.Option;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;
import lombok.With;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Builder
@AllArgsConstructor
@Value
@With
@Slf4j
public class FlowWithGeoHashDTO {
    FlowDTO flow;
    List<FlowByGeoHashDTO> flowByGeoHashes;
    EnrichedResolveFlowRequest resolveFlowRequest;

    public String getClassificationModelOutputName() {
        return flow.getClassificationModelOutputName();
    }

    public String getClassificationModelName() {
        return flow.getClassificationModelName();
    }

    public boolean isEnabled() {
        return flow.isEnabled();
    }

    public String getFlowId() {
        return flow.getFlowId();
    }

    public String getFlowName() {
        return flow.getFlowName();
    }

    public String getFlowSet() {
        return flow.getFlowSet();
    }

    public boolean matchingModelClassificationsNonNullOrEmpty() {
        return Objects.nonNull(flow.getMatchingModelClassifications()) &&
                !flow.getMatchingModelClassifications().isEmpty();
    }

    public String getTrackingId() {
        return resolveFlowRequest.getTrackingId();
    }

    public String getUserDomain() {
        return resolveFlowRequest.getUserDomain();
    }


    public String getUserEmail() {
        return resolveFlowRequest.getUserEmail();
    }

    public String getApplicationVersion() {
        return resolveFlowRequest.getApplicationVersion();
    }

    public String getResolvedOrderType() {
        return Optional.ofNullable(flow)
                .map(FlowDTO::getResolvedOrderType)
                .map(OrderType::name)
                .orElse(null);
    }

    public String getResolvedQueryType() {
        return flow.getResolvedQueryType();
    }

    public String getMealtime() {
        return resolveFlowRequest.getMealtime();
    }

    public String getResolvedMealtime() {
        return flow.getResolvedMealtime();
    }

    public EnrichedResolveFlowRequest getResolveFlowRequest() {
        return resolveFlowRequest;
    }

    public String getResolvedApplicationType() {
        return flow.getResolvedApplication();
    }

    public List<String> getResolvedQueryTokens() {
        return flow.getResolvedQueryTokens();
    }

    public UUID getFlowIdAsUUID() {
        return UuidUtil.decode(flow.getFlowId());
    }

    public Set<String> getLocationMarkets() {
        return flow.getLocationMarkets();
    }

    public List<String> getResolvedRequestedTags() {
        return flow.getResolvedRequestedTags();
    }

    public String getResolvedQueryKeyword() {
        return flow.getResolvedQueryKeyword();
    }

    public String getResolvedClientEntityId() {
        return flow.getResolvedClientEntityId();
    }

    public String getResolvedBrand(){
        return flow.getResolvedBrand();
    }

    private boolean matchingDinerTypesIsEmpty() {
        return flow.getMatchingDinerTypes().isEmpty();
    }

    private boolean matchingOrderTypesIsEmpty() {
        return flow.getMatchingOrderTypes().isEmpty();
    }

    private boolean matchingMealTimeIsEmpty() {
        return flow.getMatchingMealtime().isEmpty();
    }

    private boolean matchingApplicationsIsEmpty() {
        return flow.getMatchingApplications().isEmpty();
    }

    private boolean matchingDomainsIsEmpty() {
        return flow.getMatchingUserDomains() == null || flow.getMatchingUserDomains().isEmpty();
    }

    private boolean matchingEmailsIsEmpty() {
        return flow.getMatchingUserEmails() == null || flow.getMatchingUserEmails().isEmpty();
    }

    private boolean matchingClientEntityIdsIsEmpty() {
        return flow.getMatchingClientEntityIds() == null || flow.getMatchingClientEntityIds().isEmpty();
    }

    private boolean matchingApplicationVersionIsEmpty() {
        return flow.getMatchingApplicationVersions() == null || flow.getMatchingApplicationVersions().isEmpty();
    }

    private boolean matchingBrandsIsEmpty() {
        return flow.getMatchingBrands() == null || flow.getMatchingBrands().isEmpty();
    }

    public boolean containsDinerType() {
        if (matchingDinerTypesIsEmpty()) {
            log.debug("Flow with id flow_id={} matched diner type due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }
        boolean containsDinerType = flow.getMatchingDinerTypes()
                .toStream()
                .map(String::toUpperCase)
                .find(type -> Objects.equals(type, flow.getResolvedDinerType()))
                .isDefined();
        log.debug("Flow with id flow_id={} {} diner type request = {}",
                flow.getFlowId(), containsDinerType ? "matched" : "not matched", resolveFlowRequest);
        return containsDinerType;
    }

    public boolean containsOrderTypes() {
        if (matchingOrderTypesIsEmpty()) {
            log.debug("Flow with id flow_id={} matched order types due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }
        boolean containsOrderTypes = flow.getMatchingOrderTypes()
                .toStream()
                .filter(StringUtils::isNotEmpty)
                .map(String::toUpperCase)
                .map(OrderType::fromString)
                .find(orderType -> resolveFlowRequest.getOrderType() != null && orderType == OrderType.fromString(resolveFlowRequest.getOrderType()))
                .peek(flow::setResolvedOrderType)
                .isDefined();
        log.debug("Flow with id flow_id={} {} order types request = {}", flow.getFlowId(), containsOrderTypes ? "matched" : "not matched", resolveFlowRequest);
        return containsOrderTypes;
    }

    public boolean containsMealTime() {
        if (matchingMealTimeIsEmpty()) {
            log.debug("Flow with id flow_id={} matched meal time due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }
        boolean containsMealTime = flow.getMatchingMealtime()
                .toStream()
                .find(mealtime -> mealtime.equals(resolveFlowRequest.getMealtime()))
                .peek(flow::setResolvedMealtime)
                .isDefined();

        log.debug("Flow with id flow_id={} {} meal time request = {}", flow.getFlowId(), containsMealTime ? "matched" : "not matched", resolveFlowRequest);
        return containsMealTime;
    }

    public boolean containsMatchingApplication() {
        if (matchingApplicationsIsEmpty()) {
            log.debug("Flow with id flow_id={} matched application type due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }

        boolean containsMatchingApplication = flow.getMatchingApplications()
                .toStream()
                .find(matchingApplication -> matchingApplication.equals(resolveFlowRequest.getApplicationType()))
                .peek(flow::setResolvedApplication)
                .isDefined();
        log.debug("Flow with id flow_id={} {} application type request = {}",
                flow.getFlowId(), containsMatchingApplication ? "matched" : "not matched", resolveFlowRequest);
        return containsMatchingApplication;
    }

    public boolean containsRequestedTags() {
        if (requestedTagsIsEmpty()) {
            log.debug("Flow with id flow_id={} matched requested tags due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }

        boolean containsRequestedTags = flow.getMatchingRequestTags()
                .toStream()
                .find(requestedTag -> Option.of(resolveFlowRequest.getRequestTags())
                        .filter(Objects::nonNull)
                        .getOrElse(HashSet::new).contains(requestedTag))
                .peek(this::addResolvedRequestedTags)
                .isDefined();

        log.debug("Flow with id flow_id={} {} requested tags request = {}",
                flow.getFlowId(), containsRequestedTags ? "matched" : "not matched", resolveFlowRequest);
        return containsRequestedTags;
    }

    public boolean containsModelClassification() {
       if (flow.getMatchingModelClassifications().isEmpty()) {
           log.debug("Flow with id flow_id={} matched model classification due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
           return true;
       }

       if (resolveFlowRequest.getResolvedModelClassifications() == null || resolveFlowRequest.getResolvedModelClassifications().isEmpty()) {
           log.debug("Flow with id flow_id={} matched model classification due to empty resolved classifications for request={}",
                   flow.getFlowId(), resolveFlowRequest);
           return false;
       }

        val resolvedModelClassifications = Option.of(resolveFlowRequest.getResolvedModelClassifications())
                .map(io.vavr.collection.HashSet::ofAll)
                .getOrElse(io.vavr.collection.HashSet::empty);

        boolean containsModelClassification = flow.getMatchingModelClassifications()
                .toStream()
                .find(resolvedModelClassifications::contains)
                .isDefined();

        log.debug("Flow with id flow_id={} {} model classification request = {}",
                flow.getFlowId(), containsModelClassification ? "matched" : "not matched", resolveFlowRequest);
        return containsModelClassification;
    }

    private boolean requestedTagsIsEmpty() {
        return flow.getMatchingRequestTags().isEmpty();
    }

    public boolean containsQueryType() {
        if (matchingQueryTypesIsEmpty()) {
            log.debug("Flow with id flow_id={} matched query type due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }
        boolean containsQueryType = flow.getMatchingQueryTypes()
                .toStream()
                .filter(StringUtils::isNotEmpty)
                .find(matchingQueryType -> matchingQueryType.equals(resolveFlowRequest.getQueryType().name()))
                .peek(flow::setResolvedQueryType)
                .isDefined();

        log.debug("Flow with id flow_id={} {} query type request = {}",
                flow.getFlowId(), containsQueryType ? "matched" : "not matched", resolveFlowRequest);
        return containsQueryType;
    }

    private boolean matchingQueryTypesIsEmpty() {
        return flow.getMatchingQueryTypes().isEmpty();
    }


    public boolean containsQueryTokens() {
        if (matchingQueryTokensIsEmpty()) {
            log.debug("Flow with id flow_id={} matched query tokens due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }

        return flow.getMatchingQueryTokens()
                .toStream()
                .find(queryToken -> resolveFlowRequest.getQueryTokens().contains(queryToken))
                .peek(this::addResolvedQueryTokens)
                .isDefined();
    }

    public boolean containsQueryKeywords() {
        if (matchingQueryKeywordsIsEmpty()) {
            log.debug("Flow with id flow_id={} matched query keywords due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }

        boolean containsQueryKeywords = flow.getMatchingQueryKeywords()
                .toStream()
                .filter(StringUtils::isNotEmpty)
                .find(queryKeyword -> resolveFlowRequest.getQueryKeywordsText().contains(queryKeyword))
                .peek(flow::setResolvedQueryKeyword)
                .isDefined();

        log.debug("Flow with id flow_id={} {} query keywords request = {}",
                flow.getFlowId(), containsQueryKeywords ? "matched" : "not matched", resolveFlowRequest);
        return containsQueryKeywords;
    }

    private boolean matchingQueryKeywordsIsEmpty() {
        return flow.getMatchingQueryKeywords().isEmpty();
    }

    public boolean containsUserDomains() {
        if (matchingDomainsIsEmpty()) {
            log.debug("Flow with id flow_id={} matched user domains due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }
        boolean containsUserDomains = flow.getMatchingUserDomains()
                .toStream()
                .find(domain -> domain.equalsIgnoreCase(resolveFlowRequest.getUserDomain()))
                .peek(flow::setResolvedUserDomain)
                .isDefined();

        log.debug("Flow with id flow_id={} {} user domains request = {}",
                flow.getFlowId(), containsUserDomains ? "matched" : "not matched", resolveFlowRequest);
        return containsUserDomains;
    }

    public boolean containsClientEntityIds() {

        if (matchingClientEntityIdsIsEmpty()) {
            logMatchResult(true, "due to empty condition");
            return true;
        }

        boolean containsClientEntityId = flow.getMatchingClientEntityIds()
                .toStream()
                .find(id -> id.equalsIgnoreCase(resolveFlowRequest.getClientEntityId()))
                .peek(flow::setResolvedClientEntityId)
                .isDefined();

        logMatchResult(containsClientEntityId, "");
        return containsClientEntityId;
    }

    private void logMatchResult(boolean matched, String reason) {
        String logMessage = matched ? "matched" : "not matched";
        log.debug("Flow with id flow_id={} {} client entity id for request={} {}",
                flow.getFlowId(), logMessage, resolveFlowRequest, reason);
    }

    public boolean containsUserEmail() {
        if (matchingEmailsIsEmpty()) {
            log.debug("Flow with id flow_id={} matched user email due to empty condition for request={}",
                    flow.getFlowId(), resolveFlowRequest);
            return true;
        }
        boolean containsUserEmail = flow.getMatchingUserEmails()
                .toStream()
                .find(email -> email.equalsIgnoreCase(resolveFlowRequest.getUserEmail()))
                .peek(flow::setResolvedUserEmail)
                .isDefined();

        log.debug("Flow with id flow_id={} {} user email for request = {}",
                flow.getFlowId(), containsUserEmail ? "matched" : "not matched", resolveFlowRequest);
        return containsUserEmail;
    }


    public boolean containsApplicationVersions() {
        if (matchingApplicationVersionIsEmpty()) {
            log.debug("Flow with id flow_id={} matched aplication versions due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }
        boolean containsApplicationVersions = flow.getMatchingApplicationVersions()
                .toStream()
                .find(this::matchingVersionId)
                .peek(flow::setResolvedApplicationVersion)
                .isDefined();

        log.debug("Flow with id flow_id={} {} aplication versions {}",
                flow.getFlowId(), containsApplicationVersions ? "matched" : "not matched", resolveFlowRequest);
        return containsApplicationVersions;
    }

    public boolean containsMatchingBrands() {
        if (matchingBrandsIsEmpty()) {
            log.debug("Flow with id flow_id={} matched brand due to empty condition for request={}", flow.getFlowId(), resolveFlowRequest);
            return true;
        }

        boolean containsMatchingBrand = flow.getMatchingBrands()
                .toStream()
                .find(matchingBrand -> matchingBrand.equalsIgnoreCase(resolveFlowRequest.getBrand()))
                .peek(flow::setResolvedBrand)
                .isDefined();
        log.debug("Flow with id flow_id={} {} brand = {}",
                flow.getFlowId(), containsMatchingBrand ? "matched" : "not matched", resolveFlowRequest);
        return containsMatchingBrand;
    }

    private boolean matchingQueryTokensIsEmpty() {
        return flow.getMatchingQueryTokens().isEmpty();
    }

    public String getResolvedGeoHash() {
        return resolveFlowRequest.getResolvedGeoHash();
    }

    public void addResolvedQueryTokens(String queryToken) {
        flow.appendQueryToken(queryToken);
    }

    public void addResolvedRequestedTags(String requestTag) {
        flow.appendRequestedTag(requestTag);
    }

    public boolean matchingVersionId(String version){
        return VersionMatcher.versionMatches(version, resolveFlowRequest.getApplicationVersion());
    }

    public String getResolvedRoutingGroupElectionCriteria() {
        return flow.getResolvedRoutingGroupElectionCriteria();
    }

    public String getModelInferenceCacheKey() {
        return getClassificationModelName() + "|" + getClassificationModelOutputName();
    }

    public boolean containsRoutingGroupSelectionCriteria() {
        flow.setResolvedRoutingGroupElectionCriteria(resolveFlowRequest.getRoutingGroupElectionCriteria());
        return true;
    }

    public List<FlowRoutingGroupDTO> getRoutingGroupsForFlowCriteria() {
        if (flow.getRoutingGroupsCriteria() == null || flow.getRoutingGroupsCriteria().isEmpty()) {
            return flow.getRoutingGroups();
        }
        if (resolveFlowRequest != null && flow.getRoutingGroupsCriteria().get(resolveFlowRequest.getApplicationType()).isDefined()) {
            return flow.getRoutingGroupsCriteria().get(resolveFlowRequest.getApplicationType()).get();
        }

        return flow.getRoutingGroupsCriteria().get("default").get();
    }
}

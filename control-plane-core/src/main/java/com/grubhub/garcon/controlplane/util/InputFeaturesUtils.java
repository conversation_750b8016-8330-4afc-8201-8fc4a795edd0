package com.grubhub.garcon.controlplane.util;

import io.vavr.collection.Map;
import io.vavr.control.Try;

public class InputFeaturesUtils {

    public static float readFloatValueOrException(Map<String, Object> inputFeatures,
                                                  String modelName,
                                                  Object value,
                                                  Float defaultValue,
                                                  String featureName) {
        return Try.of(() -> Double.valueOf(ControlPlaneUtils.parseDouble(value, defaultValue)).floatValue())
                .getOrElseThrow(() -> new RuntimeException(
                        String.format("%s is not in the right format in the input_features=%s for model_name=%s",
                                featureName, inputFeatures, modelName)));
    }

}

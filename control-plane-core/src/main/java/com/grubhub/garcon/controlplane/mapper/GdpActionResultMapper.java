package com.grubhub.garcon.controlplane.mapper;

import com.grubhub.garcon.controlplane.cassandra.models.GdpActionResult;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionResultDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface GdpActionResultMapper {

    @Mapping(target = "action.actionDate", source = "result.actionDate")
    @Mapping(target = "action.actionName", source = "result.actionName")
    @Mapping(target = "action.actionType", source = "result.actionType")
    @Mapping(target = "action.actionGroup", source = "result.actionGroup")
    @Mapping(target = "action.sequenceNumber", source = "result.sequenceNumber")
    @Mapping(target = "action.filePath", source = "result.filePath")
    @Mapping(target = "action.createdDateTime", source = "result.createdDateTime")
    @Mapping(target = "action.actionContent", source = "result.actionContent")
    GdpActionResultDTO toDTO(GdpActionResult result);

    @Mapping(target = "actionDate", source = "result.action.actionDate")
    @Mapping(target = "actionName", source = "result.action.actionName")
    @Mapping(target = "actionType", source = "result.action.actionType")
    @Mapping(target = "actionGroup", source = "result.action.actionGroup")
    @Mapping(target = "sequenceNumber", source = "result.action.sequenceNumber")
    @Mapping(target = "filePath", source = "result.action.filePath")
    @Mapping(target = "createdDateTime", source = "result.action.createdDateTime")
    @Mapping(target = "actionContent", source = "result.action.actionContent")
    GdpActionResult toModel(GdpActionResultDTO result);

}

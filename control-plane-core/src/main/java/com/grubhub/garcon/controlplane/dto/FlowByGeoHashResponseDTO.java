package com.grubhub.garcon.controlplane.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class FlowByGeoHashResponseDTO {
    private FlowByGeoHashDTO flowByGeoHash;
    private String resolvedDinerType;

    public UUID getFlowId() {
        return flowByGeoHash.getFlowId();
    }

    public String getMarketName() {
        return flowByGeoHash.getMarketName();
    }

    public String getGeoHash() {
        return flowByGeoHash.getGeohash();
    }

}

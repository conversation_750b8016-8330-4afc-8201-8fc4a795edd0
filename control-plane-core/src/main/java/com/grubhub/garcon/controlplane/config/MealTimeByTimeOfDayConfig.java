package com.grubhub.garcon.controlplane.config;

import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@NoArgsConstructor
@Data
public class MealTimeByTimeOfDayConfig {

    private Map<String, TimeOfDayConfig> timeOfDay = new HashMap<>();
    private String breakfastStartTime = "06:00";
    private String breakfastEndTime = "10:00";
    private String lunchStartTime = "10:00";
    private String lunchEndTime = "14:00";
    private String midDayStartTime = "14:00";
    private String midDayEndTime = "17:00";
    private String dinnerStartTime = "17:00";
    private String dinnerEndTime = "22:00";
    private String lateNightStartTime = "22:00";
    private String lateNightEndTime = "06:00";
    private JavaDateTimeHelper dateTimeHelper;

    public MealTimeByTimeOfDayConfig(MealTimeByTimeOfDayConfig mealTimeByTimeOfDayConfig, JavaDateTimeHelper dateTimeHelper) {
        this.breakfastStartTime = mealTimeByTimeOfDayConfig.breakfastStartTime;
        this.breakfastEndTime = mealTimeByTimeOfDayConfig.breakfastEndTime;
        this.lunchStartTime = mealTimeByTimeOfDayConfig.lunchStartTime;
        this.lunchEndTime = mealTimeByTimeOfDayConfig.lunchEndTime;
        this.midDayStartTime = mealTimeByTimeOfDayConfig.midDayStartTime;
        this.midDayEndTime = mealTimeByTimeOfDayConfig.midDayEndTime;
        this.dinnerStartTime = mealTimeByTimeOfDayConfig.dinnerStartTime;
        this.dinnerEndTime = mealTimeByTimeOfDayConfig.dinnerEndTime;
        this.lateNightStartTime = mealTimeByTimeOfDayConfig.lateNightStartTime;
        this.lateNightEndTime = mealTimeByTimeOfDayConfig.lateNightEndTime;
        this.dateTimeHelper = dateTimeHelper;
        initialiseMap();
    }

    public MealTimeByTimeOfDayConfig(String breakfastStartTime,
                                     String breakfastEndTime,
                                     String lunchStartTime,
                                     String lunchEndTime,
                                     String midDayStartTime,
                                     String midDayEndTime,
                                     String dinnerStartTime,
                                     String dinnerEndTime,
                                     String lateNightStartTime,
                                     String lateNightEndTime,
                                     JavaDateTimeHelper dateTimeHelper) {
        this.breakfastStartTime = breakfastStartTime;
        this.breakfastEndTime = breakfastEndTime;
        this.lunchStartTime = lunchStartTime;
        this.lunchEndTime = lunchEndTime;
        this.midDayStartTime = midDayStartTime;
        this.midDayEndTime = midDayEndTime;
        this.dinnerStartTime = dinnerStartTime;
        this.dinnerEndTime = dinnerEndTime;
        this.lateNightStartTime = lateNightStartTime;
        this.lateNightEndTime = lateNightEndTime;
        this.dateTimeHelper = dateTimeHelper;
        this.timeOfDay = new HashMap<>();
        initialiseMap();
    }

    public void setBreakfastStartTime(String breakfastStartTime) {
        this.breakfastStartTime = breakfastStartTime;
        timeOfDay.merge("breakfast", addBreakfast(), (newValue, existingValue) ->
                new TimeOfDayConfig(breakfastStartTime, existingValue.getEnd(), dateTimeHelper)
        );
    }

    public void setBreakfastEndTime(String breakfastEndTime) {
        this.breakfastEndTime = breakfastEndTime;
        timeOfDay.merge("breakfast", addBreakfast(), (newValue, existingValue) ->
                new TimeOfDayConfig(existingValue.getEnd(), breakfastEndTime , dateTimeHelper)
        );
    }

    public void setLunchStartTime(String lunchStartTime) {
        this.lunchStartTime = lunchStartTime;
        timeOfDay.merge("lunch", addLunch(), (newValue, existingValue) ->
                new TimeOfDayConfig(lunchStartTime, existingValue.getEnd(), dateTimeHelper)
        );
    }

    public void setLunchEndTime(String lunchEndTime) {
        this.lunchEndTime = lunchEndTime;
        timeOfDay.merge("lunch", addLunch(), (newValue, existingValue) ->
                new TimeOfDayConfig(existingValue.getStart(), lunchEndTime, dateTimeHelper)
        );
    }

    public void setMidDayStartTime(String midDayStartTime) {
        this.midDayStartTime = midDayStartTime;
        timeOfDay.merge("mid-day", addMidDay(), (newValue, existingValue) ->
                new TimeOfDayConfig(midDayStartTime, existingValue.getEnd(), dateTimeHelper)
        );
    }

    public void setMidDayEndTime(String midDayEndTime) {
        this.midDayEndTime = midDayEndTime;
        timeOfDay.merge("mid-day", addMidDay(), (newValue, existingValue) ->
                new TimeOfDayConfig(existingValue.getStart(), midDayEndTime, dateTimeHelper)
        );
    }

    public void setDinnerStartTime(String dinnerStartTime) {
        this.dinnerStartTime = dinnerStartTime;
        timeOfDay.merge("dinner", addDinner(), (newValue, existingValue) ->
                new TimeOfDayConfig(dinnerStartTime, existingValue.getEnd(), dateTimeHelper)
        );
    }

    public void setDinnerEndTime(String dinnerEndTime) {
        this.dinnerEndTime = dinnerEndTime;
        timeOfDay.merge("dinner", addDinner(), (newValue, existingValue) ->
                new TimeOfDayConfig(existingValue.getStart(), dinnerEndTime, dateTimeHelper)
        );
    }

    public void setLateNightStartTime(String lateNightStartTime) {
        this.lateNightStartTime = lateNightStartTime;
        timeOfDay.merge("late-night", addLateNight(), (newValue, existingValue) ->
                new TimeOfDayConfig(lateNightStartTime, existingValue.getEnd(), dateTimeHelper)
        );
    }

    public void setLateNightEndTime(String lateNightEndTime) {
        this.lateNightEndTime = lateNightEndTime;
        timeOfDay.merge("late-night", addLateNight(), (newValue, existingValue) ->
                new TimeOfDayConfig(existingValue.getStart(), lateNightEndTime, dateTimeHelper)
        );
    }

    private void initialiseMap() {
        timeOfDay.put("breakfast", addBreakfast());
        timeOfDay.put("lunch", addLunch());
        timeOfDay.put("mid-day", addMidDay());
        timeOfDay.put("dinner", addDinner());
        timeOfDay.put("late-night", addLateNight());
    }

    private TimeOfDayConfig addLateNight() {
        return new TimeOfDayConfig(lateNightStartTime, lateNightEndTime, dateTimeHelper);
    }

    private TimeOfDayConfig addDinner() {
        return new TimeOfDayConfig(dinnerStartTime, dinnerEndTime, dateTimeHelper);
    }

    private TimeOfDayConfig addMidDay() {
        return new TimeOfDayConfig(midDayStartTime, midDayEndTime, dateTimeHelper);
    }

    private TimeOfDayConfig addLunch() {
        return new TimeOfDayConfig(lunchStartTime, lunchEndTime, dateTimeHelper);
    }

    private TimeOfDayConfig addBreakfast() {
        return new TimeOfDayConfig(breakfastStartTime, breakfastEndTime, dateTimeHelper);
    }

    public Optional<String> getTimeOfDayIdentifier(ZonedDateTime whenFor, ZoneOffset timezoneId) {
        for (Map.Entry<String, TimeOfDayConfig> entry : getTimeOfDay().entrySet()) {
            val pair = createPair(entry, timezoneId);
            if (startIsBeforeEnd(pair)) {
                    if (isInRange(whenFor, pair)) {
                        return keyAsOptional(entry);
                    }
            } else {
                if (isGreaterThanOrLessThan(whenFor, pair)) {
                    return keyAsOptional(entry);
                }
            }
        }
        return Optional.empty();
    }

    private boolean startIsBeforeEnd(Pair<ZonedDateTime, ZonedDateTime> pair) {
        val start = pair.getLeft();
        val end = pair.getRight();
        return start.isBefore(end);
    }

    private static Optional<String> keyAsOptional(Map.Entry<String, TimeOfDayConfig> entry) {
        return Optional.of(entry.getKey());
    }

    private Pair<ZonedDateTime, ZonedDateTime> createPair(
            Map.Entry<String, TimeOfDayConfig> entry,
            ZoneOffset timezoneId) {
        val start = entry.getValue().getStartAsDateTime(timezoneId);
        val end = entry.getValue().getEndAsDateTime(timezoneId);
        return ImmutablePair.of(start, end);
    }

    private static boolean isGreaterThanOrLessThan(
            ZonedDateTime whenFor,
            Pair<ZonedDateTime, ZonedDateTime> pair) {
        val start = pair.getLeft();
        val end = pair.getRight();
        return whenFor.getHour() >= start.getHour() ||
                whenFor.getHour() < end.getHour();
    }

    private static boolean isInRange(ZonedDateTime whenFor, Pair<ZonedDateTime, ZonedDateTime> pair) {
        val start = pair.getLeft();
        val end = pair.getRight();
        return whenFor.getHour() >= start.getHour() &&
                whenFor.getHour() < end.getHour();
    }
}

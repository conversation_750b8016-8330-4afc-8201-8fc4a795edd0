package com.grubhub.garcon.controlplane.guice;

import com.google.inject.AbstractModule;
import com.grubhub.garcon.controlplane.eventhub.ControlPlaneLogger;
import com.grubhub.garcon.controlplane.eventhub.mapper.LoggerMapper;
import com.grubhub.garcon.controlplane.eventhub.mapper.LoggerMapperImpl;
import com.grubhub.garcon.controlplane.mapper.EntitiesAliasMapper;
import com.grubhub.garcon.controlplane.mapper.EntitiesAliasMapperImpl;
import com.grubhub.garcon.controlplane.mapper.EntityCollectionMapper;
import com.grubhub.garcon.controlplane.mapper.EntityCollectionMapperImpl;
import com.grubhub.garcon.controlplane.mapper.FlowByGeoHashMapper;
import com.grubhub.garcon.controlplane.mapper.FlowByGeoHashMapperImpl;
import com.grubhub.garcon.controlplane.mapper.FlowMapper;
import com.grubhub.garcon.controlplane.mapper.FlowMapperImpl;
import com.grubhub.garcon.controlplane.mapper.FlowRoutingGroupCriteriaMapper;
import com.grubhub.garcon.controlplane.mapper.FlowRoutingGroupCriteriaMapperImpl;
import com.grubhub.garcon.controlplane.mapper.FlowRoutingGroupMapper;
import com.grubhub.garcon.controlplane.mapper.FlowRoutingGroupMapperImpl;
import com.grubhub.garcon.controlplane.mapper.FlowServiceApiMapper;
import com.grubhub.garcon.controlplane.mapper.FlowServiceApiMapperImpl;
import com.grubhub.garcon.controlplane.mapper.GdpActionResultMapper;
import com.grubhub.garcon.controlplane.mapper.GdpActionResultMapperImpl;
import com.grubhub.garcon.controlplane.mapper.MarketMapper;
import com.grubhub.garcon.controlplane.mapper.MarketMapperImpl;
import com.grubhub.garcon.controlplane.mapper.RoutingGroupResponseMapper;
import com.grubhub.garcon.controlplane.mapper.RoutingGroupResponseMapperImpl;
import com.grubhub.garcon.controlplane.mapper.rpc.EnsembleInvocationRequestMapper;
import com.grubhub.garcon.controlplane.mapper.rpc.EnsembleInvocationRequestMapperImpl;
import com.grubhub.garcon.controlplane.mapper.rpc.ModelInferenceRequestMapper;
import com.grubhub.garcon.controlplane.mapper.rpc.ModelInferenceRequestMapperImpl;
import com.grubhub.garcon.controlplane.mapper.rpc.ModelInferenceSequenceRequestMapper;
import com.grubhub.garcon.controlplane.mapper.rpc.ModelInferenceSequenceRequestMapperImpl;
import com.grubhub.garcon.controlplane.mapper.rpc.ResolveFlowRequestMapper;
import com.grubhub.garcon.controlplane.mapper.rpc.ResolveFlowRequestMapperImpl;
import com.grubhub.garcon.ensembler.mapper.EnsembleMapper;
import com.grubhub.garcon.ensembler.mapper.EnsembleMapperImpl;
import com.grubhub.garcon.ensembler.mapper.FeatureValueMapper;
import com.grubhub.garcon.ensembler.mapper.FeatureValueMapperImpl;
import com.grubhub.garcon.ensembler.mapper.ModelFeatureMapper;
import com.grubhub.garcon.ensembler.mapper.ModelFeatureMapperImpl;
import com.grubhub.garcon.ensembler.mapper.ModelMapper;
import com.grubhub.garcon.ensembler.mapper.ModelMapperImpl;
import com.grubhub.garcon.ensembler.mapper.ModelOutputMapper;
import com.grubhub.garcon.ensembler.mapper.ModelOutputMapperImpl;
import com.grubhub.garcon.ensembler.mapper.ModelTestInvocationStatusMapper;
import com.grubhub.garcon.ensembler.mapper.ModelTestInvocationStatusMapperImpl;
import com.grubhub.roux.LazySingleton;

public class CoreModule extends AbstractModule {

    @Override
    protected void configure() {
        bind(MarketMapper.class).to(MarketMapperImpl.class).in(LazySingleton.class);
        bind(FlowMapper.class).to(FlowMapperImpl.class).in(LazySingleton.class);
        bind(FlowByGeoHashMapper.class).to(FlowByGeoHashMapperImpl.class).in(LazySingleton.class);
        bind(RoutingGroupResponseMapper.class).to(RoutingGroupResponseMapperImpl.class).in(LazySingleton.class);
        bind(FlowRoutingGroupMapper.class).to(FlowRoutingGroupMapperImpl.class).in(LazySingleton.class);
        bind(FlowRoutingGroupCriteriaMapper.class).to(FlowRoutingGroupCriteriaMapperImpl.class).in(LazySingleton.class);
        bind(FlowServiceApiMapper.class).to(FlowServiceApiMapperImpl.class).in(LazySingleton.class);
        bind(GdpActionResultMapper.class).to(GdpActionResultMapperImpl.class).in(LazySingleton.class);
        bind(EnsembleInvocationRequestMapper.class).to(EnsembleInvocationRequestMapperImpl.class).in(LazySingleton.class);
        bind(ModelInferenceRequestMapper.class).to(ModelInferenceRequestMapperImpl.class).in(LazySingleton.class);
        bind(ResolveFlowRequestMapper.class).to(ResolveFlowRequestMapperImpl.class).in(LazySingleton.class);
        bind(ModelTestInvocationStatusMapper.class).to(ModelTestInvocationStatusMapperImpl.class).in(LazySingleton.class);

        // Ensembler
        bind(EnsembleMapper.class).to(EnsembleMapperImpl.class).in(LazySingleton.class);
        bind(ModelMapper.class).to(ModelMapperImpl.class).in(LazySingleton.class);
        bind(ModelFeatureMapper.class).to(ModelFeatureMapperImpl.class).in(LazySingleton.class);
        bind(ModelOutputMapper.class).to(ModelOutputMapperImpl.class).in(LazySingleton.class);
        bind(FeatureValueMapper.class).to(FeatureValueMapperImpl.class).in(LazySingleton.class);
        bind(EntityCollectionMapper.class).to(EntityCollectionMapperImpl.class).in(LazySingleton.class);
        bind(EntitiesAliasMapper.class).to(EntitiesAliasMapperImpl.class).in(LazySingleton.class);
        bind(ModelInferenceSequenceRequestMapper.class).to(ModelInferenceSequenceRequestMapperImpl.class).in(LazySingleton.class);

        bind(ControlPlaneLogger.class).in(LazySingleton.class);
        bind(LoggerMapper.class).to(LoggerMapperImpl.class).in(LazySingleton.class);
    }
}

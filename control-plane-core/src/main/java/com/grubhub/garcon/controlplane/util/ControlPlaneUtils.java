package com.grubhub.garcon.controlplane.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import io.vavr.CheckedRunnable;
import io.vavr.Tuple2;
import io.vavr.collection.*;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.concurrent.CompletableFuture;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;

@UtilityClass
@Slf4j
public class ControlPlaneUtils {

    public static Try<Void> safeInvocationWithSideEffects(CheckedRunnable runnable, String failureMessage) {
         return Try.run(runnable)
                .onFailure(ex -> log.error(failureMessage, ex));
    }

    public static <T> Stream<T> waitForAll(Stream<CompletableFuture<T>> tasks) {
        CompletableFuture.allOf(tasks.toJavaStream().toArray(CompletableFuture[]::new)).join();
        return tasks.map(CompletableFuture::join);
    }

    public static <T> java.util.stream.Stream<T> waitForCompletion(java.util.List<CompletableFuture<T>> tasks) {
        return tasks.stream().map(CompletableFuture::join);
    }

    public static <T> java.util.List<T> waitListForCompletion(java.util.List<CompletableFuture<T>> tasks) {
        return tasks.stream().map(CompletableFuture::join).collect(toList());
    }

    public static <T> List<T> getVavrList(Object collection) {
        if (collection instanceof String) {
            try {
                collection = ObjectMapperHelper.INSTANCE.readValue(((String) collection), new TypeReference<List<String>>() {});
            } catch (Exception e) {
                log.warn("Failed to parse feature value for key key={}", collection, e);
            }
        }
        if (collection instanceof java.util.List<?>) {
            return List.ofAll((java.util.List<T>) collection);
        }
        return ((List<T>) collection);
    }

    public static <U, V, T extends Option<V>> Map<U, V> waitForCompletion(Map<CompletableFuture<U>, CompletableFuture<T>> tasks) {
        return tasks
                .map(entry -> entry._1().thenCombine(entry._2(), (key, value) -> new Tuple2<>(key, value)))
                .map(CompletableFuture::join)
                .reject(tuple -> tuple._2.isEmpty())
                .map(tuple -> new Tuple2<>(tuple._1, tuple._2.get()))
                .collect(HashMap.collector());
    }

    public static <T> List<T> waitForCompletion(Traversable<CompletableFuture<T>> tasks) {
        return tasks.map(CompletableFuture::join).toList();
    }

    public static <T> List<T> waitForCompletionWithOptions(Traversable<CompletableFuture<Option<T>>> tasks) {
        return tasks.map(CompletableFuture::join).filter(Option::isDefined).map(Option::get).toList();
    }

    public static <T> List<T> flatten(Traversable<Option<T>> items) {
        return items.filter(Option::isDefined).map(Option::get).toList();
    }


    public static boolean isTrue(Boolean b) {
        return b != null && b;
    }

    public static <K, V> Map<K, V> merge(Traversable<Map<K, V>> maps) {
        if (maps == null || maps.isEmpty()) {
            return HashMap.empty();
        }
        return maps.reduce(Map::merge);
    }

    public static <K, V> String toString(Map<K, V> map) {
        if (map == null || map.isEmpty()) {
            return "{}";
        }
        return "{" + map.map(e -> e._1 + " -> " + (e._2)).mkString(", ") + "}";
    }

    public static <K, V> String toString(java.util.Map<K, V> map) {
        return map == null ? "{}" : toString(HashMap.ofAll(map));
    }

    public Double parseDouble(String str, double defaultValue) {
        try {
            if (str == null || str.isEmpty()) {
                return defaultValue;
            }
            return Double.parseDouble(str);
        } catch (Exception e) {
            log.warn("Invalid double_string={}, defaultValue={}", str, defaultValue, e);
            return defaultValue;
        }
    }

    public Double parseDoubleWithZeroAsDefault(String str) {
        return parseDouble(str, 0.0);
    }

    public Double parseDouble(Object obj, double defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (obj instanceof Double) {
            return (Double) obj;
        }
        if (obj instanceof Float) {
            return ((Float) obj).doubleValue();
        }
        if (obj instanceof Integer) {
            return ((Integer) obj).doubleValue();
        }
        if (obj instanceof Long) {
            return ((Long) obj).doubleValue();
        }
        if (obj instanceof Short) {
            return ((Short) obj).doubleValue();
        }
        if (obj instanceof Byte) {
            return ((Byte) obj).doubleValue();
        }
        if (obj instanceof Boolean) {
            return ((Boolean) obj) ? 1d : 0d;
        }
        return parseDouble(String.valueOf(obj), defaultValue);
    }

    public int parseInteger(String str, int defaultValue) {
        try {
            if (str == null || str.isEmpty()) {
                return defaultValue;
            }
            return Integer.parseInt(str);
        } catch (Exception e) {
            log.warn("Invalid int_string={}, defaultValue={}", str, defaultValue, e);
            return defaultValue;
        }
    }

    public int parseInteger(Object obj, int defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (obj instanceof Double) {
            return ((Double) obj).intValue();
        }
        if (obj instanceof Float) {
            return ((Float) obj).intValue();
        }
        if (obj instanceof Integer) {
            return (Integer) obj;
        }
        if (obj instanceof Long) {
            return ((Long) obj).intValue();
        }
        if (obj instanceof Short) {
            return ((Short) obj).intValue();
        }
        if (obj instanceof Byte) {
            return ((Byte) obj).intValue();
        }
        if (obj instanceof Boolean) {
            return ((Boolean) obj) ? 1 : 0;
        }
        return parseInteger(String.valueOf(obj), defaultValue);
    }

    public boolean parseBoolean(Object obj, boolean defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }
        String str = String.valueOf(obj);
        try {
            if (str == null || str.isEmpty()) {
                return defaultValue;
            }
            return Boolean.parseBoolean(str);
        } catch (Exception e) {
            log.warn("Invalid boolean_string={}, defaultValue={}", str, defaultValue, e);
            return defaultValue;
        }
    }

    public static List<?> toVavrList(Object obj) {
        if (obj == null) {
            log.debug("Trying to convert a null object, returning an empty list");
            return List.empty();
        }

        if (obj instanceof String) {
            try {
                obj = ObjectMapperHelper.INSTANCE.readValue(((String) obj), new TypeReference<List<String>>() {});
            } catch (Exception e) {
                log.warn("Failed to convert string to list raw_value={}", obj, e);
            }
        }

        if (obj instanceof List) {
            return (List<?>) obj;
        }
        if (obj instanceof Traversable) {
            return List.ofAll((Traversable<?>) obj);
        }
        if (obj instanceof java.util.Collection) {
            return List.ofAll((java.util.Collection<?>) obj);
        }
        throw new RuntimeException("Could not convert object to vavr list, obj=" + obj);
    }

    public static boolean parseBooleanFlag(Object flag, String message) {
        if (flag instanceof Boolean) {
            return (boolean) flag;
        } else if (flag instanceof String) {
            return Boolean.parseBoolean((String) flag);
        }
        throw new RuntimeException(String.format(message, flag));
    }

    public static List<Float> toVavrListFloat(Object obj) {
        List<?> list = toVavrList(obj);
        if (list.isEmpty()) {
            return List.empty();
        }

        Object firstElement = list.get(0);

        if (firstElement instanceof Float) {
            return (List<Float>) list;
        }

        return list.map(value -> Double.valueOf(parseDouble(value, 0)).floatValue());
    }

    public static List<String> toVavrListString(Object obj) {
        List<?> list = toVavrList(obj);
        if (list.isEmpty()) {
            return List.empty();
        }

        Object firstElement = list.get(0);
        if (!(firstElement instanceof String)) {
            throw new RuntimeException("Could not convert object to vavr list, obj=" + obj);
        }
        return (List<String>) list;
    }

    public static java.util.List<?> toJavaList(Object obj) {
        if (obj == null) {
            return emptyList();
        }
        if (obj instanceof java.util.List) {
            return (java.util.List<?>) obj;
        }
        if (obj instanceof List) {
            return ((List<?>) obj).toJavaList();
        }
        if (obj instanceof Traversable) {
            return List.ofAll((Traversable<?>) obj).toJavaList();
        }
        if (obj instanceof java.util.Collection) {
            return new java.util.ArrayList<>((java.util.Collection<?>) obj);
        }
        throw new RuntimeException("Could not convert object to java list, obj=" + obj);
    }

    public static java.util.List<Float> toJavaListFloat(Object obj) {
        val list = toJavaList(obj);
        if (list.isEmpty()) {
            return emptyList();
        }

        Object firstElement = list.get(0);

        if (firstElement instanceof Float) {
            return (java.util.List<Float>) list;
        }

        return list.stream()
                .map(value -> Double.valueOf(parseDouble(value, 0)).floatValue())
                .collect(Collectors.toList());
    }

    public static java.util.List<String> toJavaListString(Object obj) {
        val list = toJavaList(obj);
        if (list.isEmpty()) {
            return emptyList();
        }

        Object firstElement = list.get(0);
        if (!(firstElement instanceof String)) {
            throw new RuntimeException("Could not convert object to java list, obj=" + obj);
        }
        return (java.util.List<String>) list;
    }

    public static <T> Collector<T, ?, T> toSingleton(IllegalStateException exception) {
        return Collectors.collectingAndThen(
                Collectors.toList(),
                list -> {
                    if (list.size() != 1) {
                        throw exception;
                    }
                    return list.get(0);
                }
        );
    }

    public static <T> List<T> waitForCompletion(List<CompletableFuture<List<T>>> completableFutures) {
        return completableFutures
                .toStream()
                .collect(collectingAndThen(toList(),
                        ControlPlaneUtils::waitForListCompletion))
                .collect(List.collector());
    }

    public static <T> java.util.stream.Stream<T> waitForListCompletion(java.util.List<CompletableFuture<List<T>>> completableFutures) {
        return completableFutures
                .stream()
                .map(CompletableFuture::join)
                .flatMap(List::toJavaStream);
    }


    public static List<Float> parseFloatList(String rawValue) {
        return parseDoubleList(rawValue).map(Double::floatValue);
    }

    public static List<Long> parseLongList(String rawValue) {
        return parseDoubleList(rawValue).map(Double::longValue);
    }

    public static List<Double> parseDoubleList(String rawValue) {
        return Try.of(() -> Option.of(
                                ObjectMapperHelper.INSTANCE.readValue(rawValue, new TypeReference<List<String>>() {}))
                        .getOrElse(List::empty)
                        .map(ControlPlaneUtils::parseDoubleWithZeroAsDefault)
                )
                .onFailure(ex -> log.error("Failed to parse to double array raw_value={}", rawValue, ex))
                .getOrElse(List.empty());
    }

    public static List<Integer> parseIntegerList(String rawValue) {
        return parseDoubleList(rawValue).map(Double::intValue);
    }

    public static List<String> parseStringList(String rawValue) {
        return List.of(parseStringArray(rawValue));
    }

    public static String[] parseStringArray(String rawValue) {
        return Try.of(() -> Option.of(
                                ObjectMapperHelper.INSTANCE.readValue(rawValue, new TypeReference<List<String>>() {}))
                        .getOrElse(List::empty)
                        .toStream()
                        .toJavaArray(String[]::new)
                )
                .onFailure(ex -> log.error("Failed to parse to string array raw_value={}", rawValue, ex))
                .getOrElse(() -> new String[] {});
    }

    public static Boolean[] parseBooleanArray(String rawValue) {
        return Try.of(() -> Option.of(
                                ObjectMapperHelper.INSTANCE.readValue(rawValue, new TypeReference<List<String>>() {}))
                        .getOrElse(List::empty)
                        .toStream()
                        .map(Boolean::valueOf)
                        .toJavaArray(Boolean[]::new)
                )
                .onFailure(ex -> log.error("Failed to parse to boolean array raw_value={}", rawValue, ex))
                .getOrElse(() -> new Boolean[] {});
    }
}

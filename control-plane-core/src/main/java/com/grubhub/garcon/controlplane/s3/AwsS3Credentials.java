package com.grubhub.garcon.controlplane.s3;

import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.auth.credentials.AwsCredentials;

@NoArgsConstructor
@Data
public class AwsS3Credentials implements AwsCredentials {

    private String accessKeyId = "";
    private String secretKey = "";

    @Override
    public String accessKeyId() {
        return this.accessKeyId;
    }

    @Override
    public String secretAccessKey() {
        return this.secretKey;
    }
}

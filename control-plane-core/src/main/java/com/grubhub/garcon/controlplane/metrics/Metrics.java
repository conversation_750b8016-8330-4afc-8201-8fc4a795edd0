package com.grubhub.garcon.controlplane.metrics;

import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class Metrics {
    public static final String ENSEMBLER_NAMESPACE = "ddml.ensembler";
    public static final String ENSEMBLE_NAME_TAG = "ensemble_name";

    public static String name(Class type, String... segments) {
        List<String> collectedSegments = new ArrayList<>();
        collectedSegments.add(ENSEMBLER_NAMESPACE);
        collectedSegments.add(type.getSimpleName());
        Collections.addAll(collectedSegments, segments);

        return String.join(".", collectedSegments);
    }

    public static Tags ensembleTags(@Nullable String ensemble) {
        return ensemble == null ? Tags.empty() : Tags.of(ENSEMBLE_NAME_TAG, ensemble);
    }

    public static Tag createTag(String tagName, String value) {
        return Tag.of(tagName, value);
    }
}

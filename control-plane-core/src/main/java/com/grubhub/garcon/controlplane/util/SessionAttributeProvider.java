package com.grubhub.garcon.controlplane.util;

import com.google.inject.Inject;
import com.grubhub.roux.OptionalRequestInfoProvider;
import com.grubhub.roux.RequestInfo;
import com.grubhub.roux.api.GHSession;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Slf4j
public class SessionAttributeProvider {
    private static final String BRAND_CLAIM_ID = "brand";

    private final OptionalRequestInfoProvider optionalRequestInfoProvider;

    public String resolveUserEmail() {
        return getGhSessions()
                .map(GHSession::getEmail)
                .getOrNull();
    }

    public String resolveTrackingId() {
        return getGhSessions()
                .map(GHSession::getTrackingId)
                .map(String::valueOf)
                .getOrNull();

    }

    public String resolveBrand() {
        return getGhSessions()
                .map(ghSession -> ghSession.getNamedClaim(BRAND_CLAIM_ID))
                .map(String::valueOf)
                .getOrNull();
    }

    private Option<GHSession> getGhSessions() {
        return Option.ofOptional(optionalRequestInfoProvider
                        .get())
                .map(RequestInfo::getGhSession);
    }

}

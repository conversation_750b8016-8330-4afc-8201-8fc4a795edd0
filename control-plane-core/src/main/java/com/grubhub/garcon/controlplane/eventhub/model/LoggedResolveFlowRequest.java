package com.grubhub.garcon.controlplane.eventhub.model;

import io.vavr.collection.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoggedResolveFlowRequest {
    private String applicationId;
    private String applicationVersion;
    private String dinerId;
    private String flowSet;
    private Double lat;
    private Double lng;
    private Integer totalOrders;
    private String variationId;
    private String whenFor;
    private String callerTrackingId;
    private String dinerType;
    private String resolvedGeoHash;
    private String trackingId;
    private String applicationType;
    private List<String> queryTokens;
    private String mealtime;
    private String queryType;
    private String orderType;
    private Set<String> requestTags;
    private Set<String> resolvedModelClassifications;
    private String queryKeywords;

}

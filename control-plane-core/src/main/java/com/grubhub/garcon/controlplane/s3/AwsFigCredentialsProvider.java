package com.grubhub.garcon.controlplane.s3;

import com.google.inject.Inject;
import lombok.RequiredArgsConstructor;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;

/**
 * Pickup AWS key and secret from FIG
 */
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class AwsFigCredentialsProvider implements AwsCredentialsProvider {

    private final AwsS3Credentials credentials;

    @Override
    public AwsCredentials resolveCredentials() {
        return this.credentials;
    }
}

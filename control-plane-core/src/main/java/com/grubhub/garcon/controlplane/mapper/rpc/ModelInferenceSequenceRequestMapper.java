package com.grubhub.garcon.controlplane.mapper.rpc;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceRequest;
import com.grubhub.garcon.controlplanerpc.model.ModelInferenceSequenceRequestRpc;
import org.mapstruct.Mapper;

@Mapper
public interface ModelInferenceSequenceRequestMapper {

    ModelInferenceSequenceRequest toDTO(ModelInferenceSequenceRequestRpc modelInferenceSequenceRequestRpc);
}

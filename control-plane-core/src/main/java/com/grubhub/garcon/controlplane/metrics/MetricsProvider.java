package com.grubhub.garcon.controlplane.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;

import javax.inject.Inject;
import java.util.Collections;

public class MetricsProvider {
    private final MeterRegistry meterRegistry;

    @Inject
    public MetricsProvider(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    public Counter count(String metricName, Iterable<Tag> tags) {
        return meterRegistry.counter(metricName, tags);
    }

    public Counter count(String metricName, Tag tag) {
        return this.count(metricName, Collections.singletonList(tag));
    }

    public Counter count(String metricName) {
        return this.count(metricName, Collections.emptyList());
    }
}

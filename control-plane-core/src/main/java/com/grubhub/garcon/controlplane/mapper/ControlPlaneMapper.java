package com.grubhub.garcon.controlplane.mapper;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.cassandra.models.*;
import com.grubhub.garcon.controlplane.config.FeatureValueConfig;
import com.grubhub.garcon.controlplane.dto.FlowByGeoHashDTO;
import com.grubhub.garcon.controlplane.dto.FlowResponseBuilder;
import com.grubhub.garcon.controlplane.mapper.rpc.EnsembleInvocationRequestMapper;
import com.grubhub.garcon.controlplane.mapper.rpc.ModelInferenceRequestMapper;
import com.grubhub.garcon.controlplane.mapper.rpc.ModelInferenceSequenceRequestMapper;
import com.grubhub.garcon.controlplane.mapper.rpc.ResolveFlowRequestMapper;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowResponseApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.*;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.*;
import com.grubhub.garcon.controlplanerpc.model.*;
import com.grubhub.garcon.ensembler.cassandra.models.*;
import com.grubhub.garcon.ensembler.mapper.*;
import io.vavr.Tuple;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class ControlPlaneMapper {
    private final FlowMapper flowMapper;
    private final FlowByGeoHashMapper flowByGeoHashMapper;
    private final MarketMapper marketMapper;
    private final RoutingGroupResponseMapper routingGroupResponseMapper;
    private final FlowRoutingGroupMapper flowRoutingGroupMapper;
    private final ModelMapper modelMapper;
    private final ModelFeatureMapper modelFeatureMapper;
    private final EnsembleMapper ensembleMapper;
    private final ModelOutputMapper modelOutputMapper;
    private final FeatureValueMapper featureValueMapper;
    private final EntityCollectionMapper entityCollectionMapper;
    private final GdpActionResultMapper gdpActionResultMapper;
    private final EnsembleInvocationRequestMapper ensembleInvocationRequestMapper;
    private final ModelInferenceRequestMapper modelInferenceRequestMapper;
    private final ResolveFlowRequestMapper resolveFlowRequestMapper;
    private final FlowServiceApiMapper flowServiceApiMapper;
    private final EntitiesAliasMapper entitiesAliasMapper;
    private final ModelInferenceSequenceRequestMapper modelInferenceSequenceRequestMapper;
    private final ModelTestInvocationStatusMapper modelTestInvocationStatusMapper;
    private final FlowRoutingGroupCriteriaMapper flowRoutingGroupCriteriaMapper;
    private final FeatureValueConfig featureValueConfig;

    public FlowRoutingGroupsCriteriaDTO toDTO(FlowRoutingGroupCriteria flowRoutingGroupCriteria, List<FlowRoutingGroupV2> flowRoutingGroups) {
        return flowRoutingGroupCriteriaMapper.toDTO(flowRoutingGroupCriteria)
                .withRoutingGroups(toDTOs(flowRoutingGroups));
    }

    public List<FlowRoutingGroupCriteria> toModel(List<FlowRoutingGroupsCriteriaDTO> flowRoutingGroupCriteria, String routingGroupCriteria) {
        return flowRoutingGroupCriteria.toStream()
                .map(rgc -> flowRoutingGroupCriteriaMapper.toModel(rgc, StringUtils.isBlank(routingGroupCriteria) ? "default" : routingGroupCriteria))
                .toList();
    }

    public ModelTestInvocationStatus toModel(ModelTestInvocationStatusDTO status) {
        return modelTestInvocationStatusMapper.toModel(status);
    }

    public ModelTestInvocationStatusDTO toDTO(ModelTestInvocationStatus status) {
        return modelTestInvocationStatusMapper.toDTO(status);
    }
    public EntityCollection toModel(EntityCollectionDTO entityCollection) {
        return entityCollectionMapper.toModel(entityCollection);
    }

    public MarketByGeohash toModel(MarketByGeohashDTO marketByGeohash) {
        return marketMapper.toModel(marketByGeohash);
    }

    public FlowResponseRpc toRpc(FlowResponseApi flowResponseApi) {
        return flowServiceApiMapper.toRpc(flowResponseApi);
    }

    public ModelInferenceOutputRpc toRpc(ModelInferenceOutput modelInferenceOutput) {
        val response = modelInferenceOutput.getResponse()
                .map(entry -> Tuple.of(
                        entry._1,
                        entry._2.map(i -> new ModelInferenceOutputTypeRpc(i.getJavaValue())).toJavaList()
                ))
                .toMap(Function.identity()).toJavaMap();
        return new ModelInferenceOutputRpc(response);
    }

    public FlowByMarketDTO toDTO(FlowByMarket flowByMarket) {
        return flowMapper.toDTO(flowByMarket);
    }

    public FlowByMarket toModel(FlowByMarketDTO flowByMarket) {
        return flowMapper.toModel(flowByMarket);
    }

    public EnsembleInvocationRequest toDTO(EnsembleInvocationRequestRpc ensembleInvocationRequestRpc) {
        return ensembleInvocationRequestMapper.toDTO(ensembleInvocationRequestRpc);
    }

    public MarketByGeohashDTO toDTO(MarketByGeohash marketByGeohash) {
        return marketMapper.toDTO(marketByGeohash);
    }

    public ResolveFlowRequest toDTO(ResolveFlowRequestRpc resolveFlowRequest) {
        return resolveFlowRequestMapper.toDTO(resolveFlowRequest);
    }
    public ModelInferenceRequest toDTO(ModelInferenceRequestRpc modelInferenceRequestRpc) {
        return modelInferenceRequestMapper.toDTO(modelInferenceRequestRpc);
    }

    public EntityCollectionDTO toDTO(EntityCollection entityCollection) {
        return entityCollectionMapper.toDTO(entityCollection);
    }

    public EntitiesAliasDTO toDTO(EntitiesAlias entitiesAlias) {
        return entitiesAliasMapper.toDTO(entitiesAlias);
    }

    public FeatureValue toModel(FeatureValueDTO featureValue) {
        return featureValueMapper.toModel(featureValue, featureValueConfig);
    }

    public FeatureKey toModel(FeatureKeyDTO featureKey) {
        return featureValueMapper.toModel(featureKey);
    }

    public FeatureValueDTO toDTO(FeatureValue featureValue) {
        return featureValueMapper.toDTO(featureValue);
    }

    public FeatureKeyDTO toDTO(FeatureKey featureKey) {
        return featureValueMapper.toDTO(featureKey);
    }

    public EnsembleDTO toDTO(Ensemble ensemble,
                             List<ModelDTO> modelList,
                             Map<String, String> aliasesForModels,
                             Map<String, Map<String, Float>> ensembleWeightsMap,
                             Map<String, Map<String, String>> ensembleFunctionMap) {
        return ensembleMapper.toDTO(ensemble, modelList, aliasesForModels, ensembleWeightsMap, ensembleFunctionMap);
    }

    public Ensemble toModel(EnsembleDTO ensembleDTO) {
        return ensembleMapper.toModel(ensembleDTO);
    }

    public ModelDTO toDTO(Model model, List<FeatureDTO> modelFeatures, List<ModelOutputDTO> modelOutputs) {
        return modelMapper.toDTO(model, modelFeatures, modelOutputs);
    }
    public FeatureDTO toDTO(ModelFeature modelFeature) {
        return modelFeatureMapper.toDTO(modelFeature);
    }

    public ModelOutputDTO toDTO(ModelOutput output) {
        return modelOutputMapper.toDTO(output);
    }

    public ModelFeature toModel(FeatureDTO featureDTO) {
        return modelFeatureMapper.toModel(featureDTO);
    }
    public ModelFeature toModel(FeatureDTO featureDTO, ModelDTO modelDTO) {
        return modelFeatureMapper.toModel(featureDTO, modelDTO);
    }

    public ModelOutput toModel(String modelName, ModelOutputDTO output) {
        return modelOutputMapper.toModel(modelName, output);
    }

    public ModelDTO toDTO(Model model) {
        return modelMapper.toDTO(model);
    }

    public Model toModel(ModelDTO modelDTO) {
        return modelMapper.toModel(modelDTO);
    }

    public FlowDTO toDTO(Flow flow, List<FlowRoutingGroupDTO> routingGroups, Map<String, List<FlowRoutingGroupDTO>> routingGroupsCriteria) {
        return flowMapper.toDTO(flow, routingGroups, routingGroupsCriteria);
    }

    public FlowDTO toDTO(Flow flow) {
        return flowMapper.toDTO(flow);
    }

    public FlowSummaryDTO toSummaryDTO(Flow flow) {
        return flowMapper.toSummaryDTO(flow);
    }

    public FlowDTO toDTO(FlowApi flow) {
        return flowMapper.toApi(flow);
    }

    public MarketDTO toDTO(Market market) {
        return marketMapper.toDTO(market);
    }

    public FlowRoutingGroupDTO toDTO(FlowRoutingGroupV2 flowRoutingGroup) {
        return routingGroupResponseMapper.toDTO(flowRoutingGroup);
    }

    public List<FlowRoutingGroupDTO> toDTOs(List<FlowRoutingGroupV2> flowRoutingGroups) {
        return flowRoutingGroups.toStream().map(this::toDTO).collect(List.collector());
    }

    public Market toModel(MarketDTO market) {
        return marketMapper.toModel(market);
    }

    public FlowByGeoHashDTO toDTO(FlowByGeoHash flowByGeoHash, DinerType dinerType) {
        return flowByGeoHashMapper.toDTO(flowByGeoHash, dinerType);
    }

    public Flow toModel(FlowDTO flowDTO) {
        return flowMapper.toModel(flowDTO);
    }


    public FlowRoutingGroupV2 toModelV2(FlowRoutingGroupDTO routingGroupRequest, FlowDTO flowDTO) {
        return flowRoutingGroupMapper.toModelV2(routingGroupRequest, flowDTO);
    }

    public FlowByGeoHash toModel(@NonNull Flow flow, @NonNull String geohash, @NonNull String marketName) {
        return flowByGeoHashMapper.toModel(flow, geohash, marketName);
    }

    public RoutingGroupResponseDTO toRoutingGroupResponse(FlowRoutingGroupV2 routingGroup, double routingRand) {
        return routingGroupResponseMapper
                .toRoutingGroupResponse(flowRoutingGroupMapper.toDTO(routingGroup), routingRand);
    }

    private MatchingStrategyDTO createMatchingStrategy(FlowResponseBuilder flowWithGeoHash, List<String> markets) {
        val matchBuilder = MatchDTO.builder();
        getFlowByGeoHash(flowWithGeoHash)
                .peek(flowByGeoHash -> matchBuilder.marketId(flowByGeoHash.getMarketName())
                .dinerType(flowByGeoHash.getResolvedDinerType().getType())
                .geohash(flowByGeoHash.getGeohash()));

        return MatchingStrategyDTO.builder()
                .type(flowWithGeoHash.isVariationIdNullOrEmpty() ? "" : "DEBUG")
                .markets(markets)
                .match(matchBuilder
                        .applicationType(flowWithGeoHash.getResolvedApplicationType())
                        .applicationVersion(flowWithGeoHash.getResolvedApplicationVersion())
                        .mealtime(flowWithGeoHash.getResolvedMealtime())
                        .orderType(flowWithGeoHash.getResolvedOrderType())
                        .queryTokens(flowWithGeoHash.getResolvedQueryTokens())
                        .queryType(flowWithGeoHash.getResolvedQueryType())
                        .variationId(flowWithGeoHash.getVariationId())
                        .userDomain(flowWithGeoHash.getResolvedUserDomain())
                        .userEmail(flowWithGeoHash.getResolvedUserEmail())
                        .requestTags(flowWithGeoHash.getResolvedRequestedTags())
                        .queryKeyword(flowWithGeoHash.getResolvedQueryKeyword())
                        .clientEntityId(flowWithGeoHash.getResolvedClientEntityId())
                        .brand(flowWithGeoHash.getResolvedBrand())
                        .build())
                .build();
    }


    public FlowResponseDTO toFlowResponse(FlowResponseBuilder builder) {
        val matchingStrategy = createMatchingStrategy(builder, builder.getMarkets());
        val flowResponse = builder.build();
        return flowResponse.withMatchingStrategy(matchingStrategy);
    }

    private Option<FlowByGeoHashDTO> getFlowByGeoHash(FlowResponseBuilder builder) {
        if (!builder.isVariationIdNullOrEmpty()) {
            return Option.none();
        }
        return Option.of(builder)
                .filter(aBuilder -> builder.getFlowByGeoHashes().size() == 1)
                .map(FlowResponseBuilder::selectFirstFromList)
                .orElse(() -> Option.of(builder).map(FlowResponseBuilder::selectOneThatMatchesGeoHash));
    }

    public GdpActionResult toModel(GdpActionResultDTO result) {
        return gdpActionResultMapper.toModel(result);
    }

    public GdpActionResultDTO toDTO(GdpActionResult result) {
        return gdpActionResultMapper.toDTO(result);
    }

    public ModelInferenceSequenceRequest toDTO(ModelInferenceSequenceRequestRpc modelInferenceSequenceRequestRpc) {
        return modelInferenceSequenceRequestMapper.toDTO(modelInferenceSequenceRequestRpc);
    }

    public FlowByFlowSetDTO toDTO(FlowByFlowSet flowByFlowSet) {
        return flowMapper.toDTO(flowByFlowSet);
    }

    public FlowByFlowSet toModel(FlowByFlowSetDTO flowByFlowSet) {
        return flowMapper.toModel(flowByFlowSet);
    }

    public FlowByFlowSetRpc toRpc(FlowByFlowSetDTO flowByFlowSet) {
        return flowMapper.toRpc(flowByFlowSet);
    }

    public FlowRpc toRpc(FlowApi flowApi) {
        return flowMapper.toRpc(flowApi);
    }

    public EntityCollectionRpc toRpc(EntityCollectionDTO entityCollection) {
        return entityCollectionMapper.toRpc(entityCollection);
    }
}

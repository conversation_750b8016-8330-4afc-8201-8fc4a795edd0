package com.grubhub.garcon.controlplane.mapper;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupDTO;
import lombok.NonNull;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface FlowRoutingGroupMapper {
    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidAsString(flowRoutingGroup.getFlowId()))")
    FlowRoutingGroupDTO toDTO(@NonNull FlowRoutingGroupV2 flowRoutingGroup);

    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidFromString(flow.getFlowId()))")
    @Mapping(target = "routingGroupCriteria", source = "routingGroupDTO.routingGroupCriteria", defaultValue = "default")
    @Mapping(target = "groupOrder", source = "routingGroupDTO.groupOrder")
    FlowRoutingGroupV2 toModelV2(@NonNull FlowRoutingGroupDTO routingGroupDTO, FlowDTO flow);


}

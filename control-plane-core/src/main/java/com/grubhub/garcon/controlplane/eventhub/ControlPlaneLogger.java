package com.grubhub.garcon.controlplane.eventhub;

import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.eventhub.event.InvokeEnsembleEvent;
import com.grubhub.garcon.controlplane.eventhub.event.InvokeModelEvent;
import com.grubhub.garcon.controlplane.eventhub.event.LoggedInvokeModel;
import com.grubhub.garcon.controlplane.eventhub.event.ResolveFlowEvent;
import com.grubhub.garcon.controlplane.eventhub.event.UpdateEnsembleEvent;
import com.grubhub.garcon.controlplane.eventhub.event.UpdateFeatureEvent;
import com.grubhub.garcon.controlplane.eventhub.event.UpdateFlowEvent;
import com.grubhub.garcon.controlplane.eventhub.event.UpdateMarketEvent;
import com.grubhub.garcon.controlplane.eventhub.event.UpdateModelEvent;
import com.grubhub.garcon.controlplane.eventhub.mapper.LoggerMapper;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EnsembleInvocationRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import com.grubhub.roux.eventhub.EventHubPublisher;
import io.vavr.Tuple2;
import io.vavr.collection.List;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;

import com.google.inject.Inject;
import java.time.Duration;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class ControlPlaneLogger {
    public static final String UPDATE_MODEL_EVENT_NAME = "update_model_event";
    public static final String UPDATE_ENSEMBLE_EVENT_NAME = "update_ensemble_event";
    public static final String UPDATE_MARKET_EVENT_NAME = "update_market_event";
    public static final String UPDATE_FLOW_EVENT_NAME = "update_flow_event";
    public static final String RESOLVE_FLOW_EVENT_NAME = "resolve_flow_event";
    public static final String UPDATE_FEATURE_EVENT_NAME = "update_model_feature_event";
    public static final String INVOKE_ENSEMBLE_EVENT_NAME =  "invoke_ensemble_event";
    public static final String INVOKE_MODEL_EVENT_NAME =  "invoke_model_event";

    public static final String UPDATE_REQUEST_TYPE = "UPDATE";
    public static final String DELETE_REQUEST_TYPE = "DELETE";

    private final EventHubPublisher eventHubPublisher;
    private final JavaDateTimeHelper dateTimeHelper;
    private final LoggerMapper loggerMapper;
    private final FlowConfig flowConfig;
    @Inject
    public ControlPlaneLogger(EventHubPublisher eventHubPublisher,
                              JavaDateTimeHelper dateTimeHelper,
                              LoggerMapper loggerMapper,
                              FlowConfig flowConfig) {
        this.eventHubPublisher = eventHubPublisher;
        this.dateTimeHelper = dateTimeHelper;
        this.loggerMapper = loggerMapper;
        this.flowConfig = flowConfig;
    }

    public void logResolveFlowRequest(ResolveFlowEvent partialBuildEvent) {
        ResolveFlowEvent resolveFlowEvent = ResolveFlowEvent.builder()
                .request(loggerMapper.mapResolveFlowRequest(partialBuildEvent.getEnrichedResolveFlowRequest()))
                .response(partialBuildEvent.getResponse())
                .requestDurationMs(Duration.between(partialBuildEvent.getRequestTime(), dateTimeHelper.instant()).toMillis())
                .requestTime(partialBuildEvent.getRequestTime())
                .build();

        publishToEventHub(RESOLVE_FLOW_EVENT_NAME, resolveFlowEvent);
    }

    public void logCreateModelFeature(FeatureDTO featureDTO) {
        UpdateFeatureEvent event = UpdateFeatureEvent.builder()
                .actionType(UPDATE_REQUEST_TYPE)
                .updateTime(dateTimeHelper.instant().toEpochMilli())
                .featureDTO(featureDTO)
                .build();

        publishToEventHub(UPDATE_FEATURE_EVENT_NAME, event);
    }

    public void logCreateModel(ModelDTO modelDTO) {
        logUpdateModelEvent(modelDTO, UPDATE_REQUEST_TYPE);
    }
    public void logUpdateModel(ModelDTO modelDTO) {
        logUpdateModelEvent(modelDTO, UPDATE_REQUEST_TYPE);
    }
    public void logDeleteModel(ModelDTO modelDTO) {
        logUpdateModelEvent(modelDTO, DELETE_REQUEST_TYPE);
    }

    public void logCreateEnsemble(EnsembleDTO ensembleDTO) {
        logUpdateEnsembleEvent(ensembleDTO, UPDATE_REQUEST_TYPE);
    }

    public void logUpdateEnsemble(EnsembleDTO ensembleDTO) {
        logUpdateEnsembleEvent(ensembleDTO, UPDATE_REQUEST_TYPE);
    }

    public void logDeleteEnsemble(EnsembleDTO ensembleDTO) {
        logUpdateEnsembleEvent(ensembleDTO, DELETE_REQUEST_TYPE);
    }

    public void logCreateMarket(MarketDTO marketDTO) {
        logUpdateMarketEvent(marketDTO, UPDATE_REQUEST_TYPE);
    }
    public void logUpdateMarket(MarketDTO marketDTO) {
        logUpdateMarketEvent(marketDTO, UPDATE_REQUEST_TYPE);
    }
    public void logDeleteMarket(MarketDTO marketDTO) {
        logUpdateMarketEvent(marketDTO, DELETE_REQUEST_TYPE);
    }

    public void logCreateFlow(FlowDTO flowDTO) {
        logUpdateFlowEvent(flowDTO, UPDATE_REQUEST_TYPE);
    }
    public void logUpdateFlow(FlowDTO flowDTO) {
        logUpdateFlowEvent(flowDTO, UPDATE_REQUEST_TYPE);
    }
    public void logDeleteFlow(FlowDTO flowDTO) {
        logUpdateFlowEvent(flowDTO, DELETE_REQUEST_TYPE);
    }

    private void logUpdateModelEvent(ModelDTO modelDTO, String actionType) {

        UpdateModelEvent updateModelEvent = UpdateModelEvent.builder()
                .actionType(actionType)
                .updateTime(dateTimeHelper.instant().toEpochMilli())
                .model(modelDTO)
                .build();

        publishToEventHub(UPDATE_MODEL_EVENT_NAME, updateModelEvent);
    }

    private void logUpdateEnsembleEvent(EnsembleDTO ensembleDTO, String actionType) {
        UpdateEnsembleEvent updateEnsemble = UpdateEnsembleEvent.builder()
                .actionType(actionType)
                .updateTime(dateTimeHelper.instant().toEpochMilli())
                .ensemble(ensembleDTO)
                .build();
        publishToEventHub(UPDATE_ENSEMBLE_EVENT_NAME, updateEnsemble);
    }


    private void publishToEventHub(String eventName, Object event) {
        if (flowConfig.getEventHubLogEnabled()) {
            eventHubPublisher.publish(eventName, getEventId(), event);
        }
    }

    public void logInvokeEnsembleEvent(EnsembleInvocationRequest ensembleInvocationRequest,
                                       List<Float> response,
                                       List<LoggedInvokeModel> modelsInvocations,
                                       Duration requestDuration) {

        if (flowConfig.getEventHubLogEnsembleInvoke()) {
            InvokeEnsembleEvent invokeEnsembleEvent = InvokeEnsembleEvent.builder()
                    .request(ensembleInvocationRequest)
                    .response(response)
                    .requestTime(dateTimeHelper.instant().toEpochMilli())
                    .requestDurationMs(requestDuration.toMillis())
                    .modelsInvocations(modelsInvocations)
                    .build();

            try {
                List<InvokeEnsembleEvent> splitEvents = splitEventIntoSamples(invokeEnsembleEvent);
                publishSlicedEventsToEventhub(splitEvents);
            } catch (Exception e) {
                log.error("Error sending split ensemble={}, weight={}, callerTrackingId={}, " +
                                "features_size={}, globalFeatures={}, features_first={} to eventhub",
                        ensembleInvocationRequest.getEnsembleName(),
                        ensembleInvocationRequest.getEnsembleWeight(),
                        ensembleInvocationRequest.getCallerTrackingId(),
                        ensembleInvocationRequest.getFeatures().size(),
                        ControlPlaneUtils.toString(ensembleInvocationRequest.getGlobalFeatures()),
                        ensembleInvocationRequest.getFeatures().isEmpty() ? "{}" :
                                ControlPlaneUtils.toString(ensembleInvocationRequest.getFeatures().get(0)), e
                );

                trySendingAgain(invokeEnsembleEvent, ensembleInvocationRequest);
            }
        }
    }

    private void trySendingAgain(InvokeEnsembleEvent invokeEnsembleEvent, EnsembleInvocationRequest ensembleInvocationRequest) {
        try {
            publishToEventHub(INVOKE_ENSEMBLE_EVENT_NAME, invokeEnsembleEvent);
        } catch (Exception e) {
            log.error("Error sending entire ensemble={}, weight={}, callerTrackingId={}, " +
                            "features_size={}, globalFeatures={}, features_first={} to eventhub",
                    ensembleInvocationRequest.getEnsembleName(),
                    ensembleInvocationRequest.getEnsembleWeight(),
                    ensembleInvocationRequest.getCallerTrackingId(),
                    ensembleInvocationRequest.getFeatures().size(),
                    ControlPlaneUtils.toString(ensembleInvocationRequest.getGlobalFeatures()),
                    ensembleInvocationRequest.getFeatures().isEmpty() ? "{}" :
                            ControlPlaneUtils.toString(ensembleInvocationRequest.getFeatures().get(0)), e
            );
        }
    }

    private void publishSlicedEventsToEventhub(List<InvokeEnsembleEvent> splitEvents) {
        splitEvents.forEach(event -> publishToEventHub(INVOKE_ENSEMBLE_EVENT_NAME, event));
    }

    List<InvokeEnsembleEvent> splitEventIntoSamples(InvokeEnsembleEvent invokeEnsembleEvent) {
        return List.ofAll(invokeEnsembleEvent.getRequest().getFeatures())
                .grouped(flowConfig.getInvokeEnsembleEventSamplesPerPage())
                .toStream()
                .zipWithIndex()
                .map(SlicedRequestFeatures::new)
                .map(slicedFeatures -> InvokeEnsembleEvent.builder()
                        .pageNumber(slicedFeatures.getPageNumber())
                        .request(buildSlicedEnsembleInvocationRequest(invokeEnsembleEvent, slicedFeatures))
                        .requestTime(invokeEnsembleEvent.getRequestTime())
                        .requestDurationMs(invokeEnsembleEvent.getRequestDurationMs())
                        .response(buildSlicedResponse(invokeEnsembleEvent, slicedFeatures))
                        .modelsInvocations(buildSlicedModelsInvocations(invokeEnsembleEvent, slicedFeatures))
                        .build())
                .collect(List.collector());
    }

    private List<LoggedInvokeModel> buildSlicedModelsInvocations(InvokeEnsembleEvent invokeEnsembleEvent,
                                                                 SlicedRequestFeatures slicedFeatures) {
        if (!flowConfig.getInvokeEnsembleEventLogModelsInvocations()) {
            return List.empty();
        }
        return invokeEnsembleEvent.getModelsInvocations().map(modelInvocations -> LoggedInvokeModel
                .builder()
                .modelName(modelInvocations.getModelName())
                .processedFeatures(
                        modelInvocations.getProcessedFeatures()
                                .slice(
                                        slicedFeatures.getStartingPosition(),
                                        slicedFeatures.getEndingPosition()
                                )
                )
                .response(modelInvocations.getResponse()
                        .slice(
                                slicedFeatures.getStartingPosition(),
                                slicedFeatures.getEndingPosition()
                        )
                )
                .build()
        );
    }

    private List<Float> buildSlicedResponse(InvokeEnsembleEvent invokeEnsembleEvent, SlicedRequestFeatures slicedRequestFeatures) {
        return invokeEnsembleEvent.getResponse().slice(slicedRequestFeatures.getStartingPosition(), slicedRequestFeatures.getEndingPosition());
    }

    private EnsembleInvocationRequest buildSlicedEnsembleInvocationRequest(InvokeEnsembleEvent invokeEnsembleEvent,
                                                                           SlicedRequestFeatures slicedRequestFeatures) {
        return EnsembleInvocationRequest.builder()
                .globalFeatures(invokeEnsembleEvent.getRequest().getGlobalFeatures())
                .features(slicedRequestFeatures.getSlicedFeatures())
                .callerTrackingId(invokeEnsembleEvent.getRequest().getCallerTrackingId())
                .ensembleWeight(invokeEnsembleEvent.getRequest().getEnsembleWeight())
                .ensembleName(invokeEnsembleEvent.getRequest().getEnsembleName())
                .build();
    }

    public void logInvokeModelRequest(ModelInferenceInput input, ModelInferenceRequest request, ModelInferenceOutput output,
                                      io.vavr.collection.Map<String, List<ModelInferenceOutputType>> rawOutput, Duration requestDuration) {
        InvokeModelEvent event = InvokeModelEvent.builder()
                .request(request)
                .response(ModelInferenceOutputType.toValueMap(rawOutput))
                .normalizedResponse(output.getResponseValueMap())
                .requestTime(dateTimeHelper.instant().toEpochMilli())
                .requestDurationMs(requestDuration.toMillis())
                .modelCategory(input.getModel().getModelCategory())
                .isDefaultResponse(output.isDefault())
                .build();
        publishToEventHub(INVOKE_MODEL_EVENT_NAME, event);
    }

    private void logUpdateMarketEvent(MarketDTO marketDTO, String actionType) {
        UpdateMarketEvent updateMarketEvent = UpdateMarketEvent.builder()
                .actionType(actionType)
                .updateTime(dateTimeHelper.instant().toEpochMilli())
                .market(marketDTO)
                .build();

        publishToEventHub(UPDATE_MARKET_EVENT_NAME, updateMarketEvent);
    }

    private void logUpdateFlowEvent(FlowDTO flowDTO, String actionType) {
        UpdateFlowEvent updateFlowEvent = UpdateFlowEvent.builder()
                .actionType(actionType)
                .updateTime(dateTimeHelper.instant().toEpochMilli())
                .flow(flowDTO)
                .build();

        publishToEventHub(UPDATE_FLOW_EVENT_NAME, updateFlowEvent);
    }

    private String getEventId() {
        return String.valueOf(UUID.randomUUID());
    }

    @Value
    private class SlicedRequestFeatures {
        List<Map<String, Object>> slicedFeatures;
        int pageNumber;

        public SlicedRequestFeatures(Tuple2<List<Map<String, Object>>, Integer> tuple) {
            this.slicedFeatures = tuple._1;
            this.pageNumber = tuple._2;
        }

        public java.util.List<Map<String, Object>> getSlicedFeatures() {
            return slicedFeatures.asJava();
        }

        private int getEndingPosition() {
            return ((pageNumber + 1) * flowConfig.getInvokeEnsembleEventSamplesPerPage());
        }

        private int getStartingPosition() {
            return pageNumber * flowConfig.getInvokeEnsembleEventSamplesPerPage();
        }
    }
}

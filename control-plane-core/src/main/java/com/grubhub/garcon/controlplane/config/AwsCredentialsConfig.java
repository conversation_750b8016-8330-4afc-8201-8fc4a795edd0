package com.grubhub.garcon.controlplane.config;

import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.auth.credentials.AwsCredentials;

@NoArgsConstructor
@Data
public class AwsCredentialsConfig implements AwsCredentials  {

    private String accessKeyId = "";
    private String secretKey = "";

    @Override
    public String accessKeyId() {
        return this.accessKeyId;
    }

    @Override
    public String secretAccessKey() {
        return this.secretKey;
    }
}

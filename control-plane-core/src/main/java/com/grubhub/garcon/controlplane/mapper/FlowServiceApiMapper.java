package com.grubhub.garcon.controlplane.mapper;

import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FeatureApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowResponseApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupsCriteriaApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.MatchApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.MatchingStrategyApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowResponseDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupsCriteriaDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MatchDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MatchingStrategyDTO;
import com.grubhub.garcon.controlplanerpc.model.FeatureRpc;
import com.grubhub.garcon.controlplanerpc.model.FlowResponseRpc;
import io.vavr.collection.List;
import io.vavr.control.Option;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Mapper
public interface FlowServiceApiMapper {

    @Mapping(target = "matchingDinerTypes", expression = "java(VavrMapper.toJavaSet(flow.getMatchingDinerTypes()))")
    @Mapping(target = "locationMarkets", expression = "java(VavrMapper.toJavaSet(flow.getLocationMarkets()))")
    @Mapping(target = "matchingOrderTypes", expression = "java(VavrMapper.toJavaSet(flow.getMatchingOrderTypes()))")
    @Mapping(target = "matchingQueryTokens", expression = "java(VavrMapper.toJavaList(flow.getMatchingQueryTokens()))")
    @Mapping(target = "matchingQueryTypes", expression = "java(VavrMapper.toJavaSet(flow.getMatchingQueryTypes()))")
    @Mapping(target = "matchingMealtime", expression = "java(VavrMapper.toJavaSet(flow.getMatchingMealtime()))")
    @Mapping(target = "matchingApplications", expression = "java(VavrMapper.toJavaSet(flow.getMatchingApplications()))")
    @Mapping(target = "matchingUserDomains", expression = "java(VavrMapper.toJavaSet(flow.getMatchingUserDomains()))")
    @Mapping(target = "matchingUserEmails", expression = "java(VavrMapper.toJavaSet(flow.getMatchingUserEmails()))")
    @Mapping(target = "matchingApplicationVersions", expression = "java(VavrMapper.toJavaSet(flow.getMatchingApplicationVersions()))")
    @Mapping(target = "matchingRequestTags", expression = "java(VavrMapper.toJavaSet(flow.getMatchingRequestTags()))")
    @Mapping(target = "matchingModelClassifications", expression = "java(VavrMapper.toJavaSet(flow.getMatchingModelClassifications()))")
    @Mapping(target = "matchingQueryKeywords", expression = "java(VavrMapper.toJavaSet(flow.getMatchingQueryKeywords()))")
    @Mapping(target = "routingGroupsSeedRotation", expression = "java(VavrMapper.toJavaList(flow.getRoutingGroupsSeedRotation()))")
    @Mapping(target = "matchingClientEntityIds", expression = "java(VavrMapper.toJavaSet(flow.getMatchingClientEntityIds()))")
    @Mapping(target = "matchingBrands", expression = "java(VavrMapper.toJavaSet(flow.getMatchingBrands()))")
    @Mapping(target = "routingGroupsCriteria",
            expression = "java(convertRoutingGroupsCriteria(flow))")
    FlowApi toApi(FlowDTO flow);

    default Map<String, java.util.List<FlowRoutingGroupApi>> convertRoutingGroupsCriteria(FlowDTO flow) {
        return Option.of(flow)
                .map(FlowDTO::getRoutingGroupsCriteria)
                .filter(Objects::nonNull)
                .map(routingGroupsCriteria ->
                        routingGroupsCriteria.mapValues(values -> values.toStream()
                                .map(dto -> {
                                    if (dto.getGroupOrder() == null) {
                                        // To be deleted. This is useful to avoid NPE when deleting corrupted records in the DB.
                                        // group order should never be null.
                                        dto.setGroupOrder(0);
                                    }

                                    return dto;
                                })
                                .map(this::toApi)
                                .sortBy(FlowRoutingGroupApi::getGroupOrder).toJavaList()))
                .map(VavrMapper::toJavaMap)
                .getOrElse(() -> Collections.singletonMap("default", flow.getRoutingGroups().map(this::toApi).collect(Collectors.toList())));
    }

    @Mapping(target = "featureStoreFields", expression = "java(VavrMapper.listToJavaSet(featureDTO.getFeatureStoreFields()))")
    FeatureApi toApi(FeatureDTO featureDTO);

    @Mapping(target = "features", expression = "java(VavrMapper.toJavaList(flowResponse.getFeatures().map(this::toApi)))")
    @Mapping(target = "matchingStrategy", source = "matchingStrategy")
    FlowResponseApi toApi(FlowResponseDTO flowResponse);

    FlowResponseApi toDTO(FlowResponseRpc flowResponseRpc);

    FlowRoutingGroupApi toApi(FlowRoutingGroupDTO flowRoutingGroupDTO);

    @Mapping(target = "routingGroups", source = "routingGroups", qualifiedByName = "toApis")
    FlowRoutingGroupsCriteriaApi toApi(FlowRoutingGroupsCriteriaDTO flowRoutingGroupsCriteriaDTO);

    @Named("toApis")
    default java.util.List<FlowRoutingGroupApi> toApis(List<FlowRoutingGroupDTO> dtos) {
        return Option.of(dtos).getOrElse(List.empty()).toStream().map(this::toApi).toJavaList();
    }

    @Mapping(target = "queryTokens", expression = "java(VavrMapper.toJavaList(matchDTO.getQueryTokens()))")
    @Mapping(target = "requestTags", expression = "java(VavrMapper.toJavaList(matchDTO.getRequestTags()))")
    MatchApi toApi(MatchDTO matchDTO);

    @Mapping(target = "markets", expression = "java(VavrMapper.toJavaList(matchingStrategyDTO.getMarkets()))")
    @Mapping(target = "matchingDinerTypes", expression = "java(VavrMapper.toJavaList(matchingStrategyDTO.getMatchingDinerTypes()))")
    MatchingStrategyApi toApi(MatchingStrategyDTO matchingStrategyDTO);

    @Mapping(target = "matchingDinerTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingDinerTypes()))")
    @Mapping(target = "matchingQueryTokens", expression = "java(VavrMapper.toVavrList(flow.getMatchingQueryTokens()))")
    @Mapping(target = "locationMarkets", expression = "java(VavrMapper.toVavrSet(flow.getLocationMarkets()))")
    @Mapping(target = "matchingOrderTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingOrderTypes()))")
    @Mapping(target = "matchingQueryTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingQueryTypes()))")
    @Mapping(target = "matchingMealtime", expression = "java(VavrMapper.toVavrSet(flow.getMatchingMealtime()))")
    @Mapping(target = "matchingApplications", expression = "java(VavrMapper.toVavrSet(flow.getMatchingApplications()))")
    @Mapping(target = "matchingUserDomains", expression = "java(VavrMapper.toVavrSet(flow.getMatchingUserDomains()))")
    @Mapping(target = "matchingUserEmails", expression = "java(VavrMapper.toVavrSet(flow.getMatchingUserEmails()))")
    @Mapping(target = "matchingApplicationVersions", expression = "java(VavrMapper.toVavrSet(flow.getMatchingApplicationVersions()))")
    @Mapping(target = "matchingRequestTags", expression = "java(VavrMapper.toVavrSet(flow.getMatchingRequestTags()))")
    @Mapping(target = "matchingModelClassifications", expression = "java(VavrMapper.toVavrSet(flow.getMatchingModelClassifications()))")
    @Mapping(target = "matchingQueryKeywords", expression = "java(VavrMapper.toVavrSet(flow.getMatchingQueryKeywords()))")
    @Mapping(target = "routingGroupsSeedRotation",
            expression = "java(VavrMapper.toVavrList(flow.getRoutingGroupsSeedRotation()))")
    @Mapping(target = "matchingClientEntityIds",
            expression = "java(VavrMapper.toVavrSet(flow.getMatchingClientEntityIds()))")
    @Mapping(target = "matchingBrands",
            expression = "java(VavrMapper.toVavrSet(flow.getMatchingBrands()))")
    @Mapping(target = "routingGroupsCriteria",
            expression = "java(VavrMapper.toVavrMap(" +
                    "flow.getRoutingGroupsCriteria())" +
                    ".mapValues(values -> io.vavr.collection.List.ofAll(values.stream().map(this::toDTO)" +
                    ".collect(java.util.stream.Collectors.toList()))))")
    FlowDTO toDTO(FlowApi flow);

    @Mapping(target = "featureStoreFields", expression = "java(VavrMapper.setToVavrList(featureRpc.getFeatureStoreFields()))")
    FeatureDTO toDTO(FeatureRpc featureRpc);

    @Mapping(target = "featureStoreFields", expression = "java(VavrMapper.setToVavrList(featureApi.getFeatureStoreFields()))")
    FeatureDTO toDTO(FeatureApi featureApi);

    @Mapping(target = "features", expression = "java(VavrMapper.toVavrList(flowResponseApi.getFeatures()).map(this::toDTO))")
    FlowResponseDTO toDTO(FlowResponseApi flowResponseApi);

    FlowResponseRpc toRpc(FlowResponseApi flowResponseApi);

    FlowRoutingGroupDTO toDTO(FlowRoutingGroupApi flowRoutingGroupApi);

    @Mapping(target = "routingGroups", source = "routingGroups", qualifiedByName = "apiToDTOsVavrJava")
    FlowRoutingGroupsCriteriaDTO toDTO(FlowRoutingGroupsCriteriaApi flowRoutingGroupsCriteriaApi);

    @Named("apiToDTOs")
    default List<FlowRoutingGroupDTO> apiToDTOsVavr(List<FlowRoutingGroupApi> apis) {
        return apis.toStream().map(this::toDTO).toList();
    }

    @Named("apiToDTOsVavrJava")
    default List<FlowRoutingGroupDTO> apiToDTOsVavrJava(java.util.List<FlowRoutingGroupApi> apis) {
        return List.ofAll(apis.stream().map(this::toDTO));
    }

    @Mapping(target = "queryTokens", expression = "java(VavrMapper.toVavrList(matchApi.getQueryTokens()))")
    @Mapping(target = "requestTags", expression = "java(VavrMapper.toVavrList(matchApi.getRequestTags()))")
    MatchDTO toDTO(MatchApi matchApi);

    @Mapping(target = "markets", expression = "java(VavrMapper.toVavrList(matchingStrategyApi.getMarkets()))")
    @Mapping(target = "matchingDinerTypes", expression = "java(VavrMapper.toVavrList(matchingStrategyApi.getMatchingDinerTypes()))")
    MatchingStrategyDTO toDTO(MatchingStrategyApi matchingStrategyApi);

}

package com.grubhub.garcon.controlplane.mapper;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.EntityCollectionDTO;
import com.grubhub.garcon.controlplanerpc.model.EntityCollectionRpc;
import com.grubhub.garcon.ensembler.cassandra.models.EntityCollection;
import org.mapstruct.Mapper;

@Mapper
public interface EntityCollectionMapper {

    EntityCollection toModel(EntityCollectionDTO entityCollection);
    EntityCollectionDTO toDTO(EntityCollection entityCollection);
    EntityCollectionRpc toRpc(EntityCollectionDTO entityCollection);

}

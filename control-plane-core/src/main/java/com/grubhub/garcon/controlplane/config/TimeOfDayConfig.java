package com.grubhub.garcon.controlplane.config;

import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;


@Slf4j
@Data
@RequiredArgsConstructor
public class TimeOfDayConfig {
    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    public static final String DEFAULT_START_HOURS = "00:00";
    public static final String DEFAULT_END_HOURS = "23:00";

    private final String start;
    private final String end;
    private final JavaDateTimeHelper dateTimeHelper;

    public ZonedDateTime getStartAsDateTime(ZoneOffset zoneOffset){
        return parseDateTime(start, DEFAULT_START_HOURS, zoneOffset);
    }

    public ZonedDateTime getEndAsDateTime(ZoneOffset timeZoneId){
        return parseDateTime(end, DEFAULT_END_HOURS, timeZoneId);
    }

    private ZonedDateTime parseDateTime(String time, String defaultTime, ZoneOffset zoneOffset){
        try {
            return dateTimeHelper.atZone(zoneOffset).with(parseLocalTimeFor(time));
        } catch (Exception ex){
            log.error("TimeOfDayConfig.parseDateTime error parsing time={}, it will use={} as default", time, defaultTime, ex);
        }

        return  dateTimeHelper.atZone(zoneOffset).with(parseLocalTimeFor(defaultTime));
    }

    private LocalTime parseLocalTimeFor(String time) {
        return LocalTime.parse(time, FORMATTER);
    }
}


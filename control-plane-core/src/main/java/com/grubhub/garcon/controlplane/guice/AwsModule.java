package com.grubhub.garcon.controlplane.guice;

import com.google.inject.PrivateModule;
import com.grubhub.garcon.controlplane.bedrock.BedrockRuntimeClientProvider;
import com.grubhub.garcon.controlplane.s3.AwsFigCredentialsProvider;
import com.grubhub.garcon.controlplane.s3.S3ClientProvider;
import com.grubhub.garcon.controlplane.sagemaker.SageMakerRuntimeClientProvider;
import com.grubhub.roux.LazySingleton;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;

/**
 * Module that houses everything AWS-related.
 */
public class AwsModule extends PrivateModule {

    @Override
    protected void configure() {
        // AWS SDK V2
        bind(AwsCredentialsProvider.class).to(AwsFigCredentialsProvider.class).in(LazySingleton.class);
        bind(S3Client.class).toProvider(S3ClientProvider.class).in(LazySingleton.class);
        bind(BedrockRuntimeClient.class).toProvider(BedrockRuntimeClientProvider.class).in(LazySingleton.class);
        bind(SageMakerRuntimeClient.class).toProvider(SageMakerRuntimeClientProvider.class).in(LazySingleton.class);

        expose(AwsCredentialsProvider.class);
        expose(S3Client.class);
        expose(BedrockRuntimeClient.class);
        expose(SageMakerRuntimeClient.class);
    }
}

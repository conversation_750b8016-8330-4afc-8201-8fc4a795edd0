package com.grubhub.garcon.controlplane.mapper;

import com.grubhub.garcon.controlplane.cassandra.models.Flow;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByFlowSet;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByMarket;
import com.grubhub.garcon.controlplaneapi.models.BucketingMode;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.*;
import com.grubhub.garcon.controlplanerpc.model.FlowByFlowSetRpc;
import com.grubhub.garcon.controlplanerpc.model.FlowRoutingGroupRpc;
import com.grubhub.garcon.controlplanerpc.model.FlowRpc;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.stream.Collectors;

@Mapper(uses = {FlowServiceApiMapper.class, FlowRoutingGroupCriteriaMapper.class})
public interface FlowMapper {

    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidAsString(flow.getFlowId()))")
    @Mapping(target = "routingGroups", source = "routingGroups")
    @Mapping(target = "matchingQueryTokens", expression = "java(VavrMapper.toVavrList(flow.getMatchingQueryTokens()))")
    @Mapping(target = "locationMarkets", expression = "java(VavrMapper.toVavrSet(flow.getLocationMarkets()))")
    @Mapping(target = "matchingOrderTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingOrderTypes()))")
    @Mapping(target = "matchingQueryTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingQueryTypes()))")
    @Mapping(target = "matchingMealtime", expression = "java(VavrMapper.toVavrSet(flow.getMatchingMealtime()))")
    @Mapping(target = "matchingApplications", expression = "java(VavrMapper.toVavrSet(flow.getMatchingApplications()))")
    @Mapping(target = "matchingApplicationVersions", expression = "java(VavrMapper.toVavrSet(flow.getMatchingApplicationVersions()))")
    @Mapping(target = "matchingUserDomains", expression = "java(VavrMapper.toVavrSet(flow.getMatchingUserDomains()))")
    @Mapping(target = "matchingUserEmails", expression = "java(VavrMapper.toVavrSet(flow.getMatchingUserEmails()))")
    @Mapping(target = "matchingDinerTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingDinerTypes()))")
    @Mapping(target = "matchingRequestTags", expression = "java(VavrMapper.toVavrSet(flow.getMatchingRequestTags()))")
    @Mapping(target = "matchingModelClassifications", expression = "java(VavrMapper.toVavrSet(flow.getMatchingModelClassifications()))")
    @Mapping(target = "matchingQueryKeywords", expression = "java(VavrMapper.toVavrSet(flow.getMatchingQueryKeywords()))")
    @Mapping(target = "routingGroupsSeedRotation", expression = "java(VavrMapper.toVavrList(flow.getRoutingGroupsSeedRotation()))")
    @Mapping(target = "routingGroupsCriteria", source = "routingGroupsCriteria")
    @Mapping(target = "matchingClientEntityIds", expression = "java(VavrMapper.toVavrSet(flow.getMatchingClientEntityIds()))")
    @Mapping(target = "matchingBrands", expression = "java(VavrMapper.toVavrSet(flow.getMatchingBrands()))")
    @Mapping(target = "routingGroupsBucketingMode", expression = "java(toBucketingMode(flow.getRoutingGroupsBucketingMode()))")
    FlowDTO toDTO(Flow flow, List<FlowRoutingGroupDTO> routingGroups, Map<String, List<FlowRoutingGroupDTO>> routingGroupsCriteria);

    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidAsString(flow.getFlowId()))")
    @Mapping(target = "matchingQueryTokens", expression = "java(VavrMapper.toVavrList(flow.getMatchingQueryTokens()))")
    @Mapping(target = "locationMarkets", expression = "java(VavrMapper.toVavrSet(flow.getLocationMarkets()))")
    @Mapping(target = "matchingOrderTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingOrderTypes()))")
    @Mapping(target = "matchingQueryTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingQueryTypes()))")
    @Mapping(target = "matchingMealtime", expression = "java(VavrMapper.toVavrSet(flow.getMatchingMealtime()))")
    @Mapping(target = "matchingApplications", expression = "java(VavrMapper.toVavrSet(flow.getMatchingApplications()))")
    @Mapping(target = "matchingApplicationVersions", expression = "java(VavrMapper.toVavrSet(flow.getMatchingApplicationVersions()))")
    @Mapping(target = "matchingUserDomains", expression = "java(VavrMapper.toVavrSet(flow.getMatchingUserDomains()))")
    @Mapping(target = "matchingUserEmails", expression = "java(VavrMapper.toVavrSet(flow.getMatchingUserEmails()))")
    @Mapping(target = "matchingDinerTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingDinerTypes()))")
    @Mapping(target = "matchingRequestTags", expression = "java(VavrMapper.toVavrSet(flow.getMatchingRequestTags()))")
    @Mapping(target = "matchingModelClassifications", expression = "java(VavrMapper.toVavrSet(flow.getMatchingModelClassifications()))")
    @Mapping(target = "matchingQueryKeywords", expression = "java(VavrMapper.toVavrSet(flow.getMatchingQueryKeywords()))")
    @Mapping(target = "routingGroupsSeedRotation", expression = "java(VavrMapper.toVavrList(flow.getRoutingGroupsSeedRotation()))")
    @Mapping(target = "matchingClientEntityIds", expression = "java(VavrMapper.toVavrSet(flow.getMatchingClientEntityIds()))")
    @Mapping(target = "matchingBrands", expression = "java(VavrMapper.toVavrSet(flow.getMatchingBrands()))")
    @Mapping(target = "routingGroupsCriteria", ignore = true)
    @Mapping(target = "routingGroupsBucketingMode", expression = "java(toBucketingMode(flow.getRoutingGroupsBucketingMode()))")
    FlowDTO toDTO(Flow flow);

    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidAsString(flow.getFlowId()))")
    @Mapping(target = "matchingOrderTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingOrderTypes()))")
    FlowSummaryDTO toSummaryDTO(Flow flow);

    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidFromString(flow.getFlowId()))")
    @Mapping(target = "routingGroupsCriteria", expression = "java(extractRoutingGroupsCriteriaNames(flow.getRoutingGroupsCriteria()))")
    @Mapping(target = "matchingDinerTypes", expression = "java(VavrMapper.toJavaSet(flow.getMatchingDinerTypes()))")
    @Mapping(target = "locationMarkets", expression = "java(VavrMapper.toJavaSet(flow.getLocationMarkets()))")
    @Mapping(target = "matchingOrderTypes", expression = "java(VavrMapper.toJavaSet(flow.getMatchingOrderTypes()))")
    @Mapping(target = "matchingQueryTokens", expression = "java(VavrMapper.toJavaList(flow.getMatchingQueryTokens()))")
    @Mapping(target = "matchingQueryTypes", expression = "java(VavrMapper.toJavaSet(flow.getMatchingQueryTypes()))")
    @Mapping(target = "matchingMealtime", expression = "java(VavrMapper.toJavaSet(flow.getMatchingMealtime()))")
    @Mapping(target = "matchingApplications", expression = "java(VavrMapper.toJavaSet(flow.getMatchingApplications()))")
    @Mapping(target = "matchingApplicationVersions", expression = "java(VavrMapper.toJavaSet(flow.getMatchingApplicationVersions()))")
    @Mapping(target = "matchingUserDomains", expression = "java(VavrMapper.toJavaSet(flow.getMatchingUserDomains()))")
    @Mapping(target = "matchingUserEmails", expression = "java(VavrMapper.toJavaSet(flow.getMatchingUserEmails()))")
    @Mapping(target = "matchingRequestTags", expression = "java(VavrMapper.toJavaSet(flow.getMatchingRequestTags()))")
    @Mapping(target = "matchingModelClassifications", expression = "java(VavrMapper.toJavaSet(flow.getMatchingModelClassifications()))")
    @Mapping(target = "matchingQueryKeywords", expression = "java(VavrMapper.toJavaSet(flow.getMatchingQueryKeywords()))")
    @Mapping(target = "routingGroupsSeedRotation", expression = "java(VavrMapper.toJavaList(flow.getRoutingGroupsSeedRotation()))")
    @Mapping(target = "matchingClientEntityIds", expression = "java(VavrMapper.toJavaSet(flow.getMatchingClientEntityIds()))")
    @Mapping(target = "matchingBrands", expression = "java(VavrMapper.toJavaSet(flow.getMatchingBrands()))")
    @Mapping(target = "routingGroupsBucketingMode", expression = "java(toStringBucketingMode(flow.getRoutingGroupsBucketingMode()))")
    Flow toModel(FlowDTO flow);

    @Mapping(target = "matchingDinerTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingDinerTypes()))")
    @Mapping(target = "locationMarkets", expression = "java(VavrMapper.toVavrSet(flow.getLocationMarkets()))")
    @Mapping(target = "matchingOrderTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingOrderTypes()))")
    @Mapping(target = "matchingQueryTokens", expression = "java(VavrMapper.toVavrList(flow.getMatchingQueryTokens()))")
    @Mapping(target = "matchingQueryTypes", expression = "java(VavrMapper.toVavrSet(flow.getMatchingQueryTypes()))")
    @Mapping(target = "matchingMealtime", expression = "java(VavrMapper.toVavrSet(flow.getMatchingMealtime()))")
    @Mapping(target = "matchingApplications", expression = "java(VavrMapper.toVavrSet(flow.getMatchingApplications()))")
    @Mapping(target = "matchingApplicationVersions", expression = "java(VavrMapper.toVavrSet(flow.getMatchingApplicationVersions()))")
    @Mapping(target = "matchingUserDomains", expression = "java(VavrMapper.toVavrSet(flow.getMatchingUserDomains()))")
    @Mapping(target = "matchingUserEmails", expression = "java(VavrMapper.toVavrSet(flow.getMatchingUserEmails()))")
    @Mapping(target = "matchingRequestTags", expression = "java(VavrMapper.toVavrSet(flow.getMatchingRequestTags()))")
    @Mapping(target = "matchingModelClassifications", expression = "java(VavrMapper.toVavrSet(flow.getMatchingModelClassifications()))")
    @Mapping(target = "matchingQueryKeywords", expression = "java(VavrMapper.toVavrSet(flow.getMatchingQueryKeywords()))")
    @Mapping(target = "routingGroupsSeedRotation", expression = "java(VavrMapper.toVavrList(flow.getRoutingGroupsSeedRotation()))")
    @Mapping(target = "routingGroupsCriteria", expression = "java(VavrMapper.toVavrMap(flow.getRoutingGroupsCriteria())" +
            ".mapValues(this::apiToDTOsVavrJava))")
    @Mapping(target = "matchingClientEntityIds", expression = "java(VavrMapper.toVavrSet(flow.getMatchingClientEntityIds()))")
    @Mapping(target = "matchingBrands", expression = "java(VavrMapper.toVavrSet(flow.getMatchingBrands()))")
    FlowDTO toApi(FlowApi flow);

    @Mapping(target = "routingGroupsCriteria", expression = "java(toRoutingGroupsCriteriaMap(flow.getRoutingGroupsCriteria()))")
    @Mapping(target = "routingGroupsBucketingMode", expression = "java(toStringBucketingMode(flow.getRoutingGroupsBucketingMode()))")
    FlowRpc toRpc(FlowApi flow);

    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidFromString(flowByMarket.getFlowId()))")
    FlowByMarket toModel(FlowByMarketDTO flowByMarket);

    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidAsString(flowByMarket.getFlowId()))")
    FlowByMarketDTO toDTO(FlowByMarket flowByMarket);

    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidFromString(flowByFlowSet.getFlowId()))")
    FlowByFlowSet toModel(FlowByFlowSetDTO flowByFlowSet);

    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidAsString(flowByFlowSet.getFlowId()))")
    FlowByFlowSetDTO toDTO(FlowByFlowSet flowByFlowSet);

    FlowByFlowSetRpc toRpc(FlowByFlowSetDTO flowByFlowSet);

    default java.util.Set<String> extractRoutingGroupsCriteriaNames(Map<String, List<FlowRoutingGroupDTO>> routingGroupsByCriteria) {
        return Option.of(routingGroupsByCriteria)
                .map(Map::keySet)
                .getOrElse(HashSet::empty)
                .toJavaSet();
    }

    default java.util.Set<String> extractRoutingGroupCriteriaNames(List<FlowRoutingGroupsCriteriaDTO> routingGroupsCriteriaDTOS) {
        return Option.of(routingGroupsCriteriaDTOS)
                .map(requests -> requests.toStream().map(FlowRoutingGroupsCriteriaDTO::getRoutingGroupCriteria).collect(HashSet.collector()))
                .map(HashSet::toJavaSet)
                .getOrElse(java.util.HashSet::new);
    }

    default List<FlowRoutingGroupsCriteriaDTO> toRoutingGroupsCriteria(List<FlowRoutingGroupDTO> routingGroups) {
        return routingGroups
                .toStream()
                .map(routingGroup -> FlowRoutingGroupsCriteriaDTO.builder()
                        .flowId(routingGroup.getFlowId())
                        .routingGroupCriteria(routingGroup.getRoutingGroupCriteria())
                        .build()
                ).toList();
    }


    @Named("routingGroupDTOsToApis")
    default List<FlowRoutingGroupDTO> apiToDTOsVavrJava(java.util.List<FlowRoutingGroupApi> apis) {
        return List.ofAll(apis.stream().map(this::flowRoutingGroupToDTO));
    }

    FlowRoutingGroupDTO flowRoutingGroupToDTO(FlowRoutingGroupApi flowRoutingGroupApi);

    default java.util.List<FlowRoutingGroupRpc> toRoutingGroupRpcs(java.util.List<FlowRoutingGroupApi> apis) {
        return apis.stream()
                .map(this::flowRoutingGroupApiToRpc)
                .collect(Collectors.toList());
    }

    default java.util.Map<String, java.util.List<FlowRoutingGroupRpc>> toRoutingGroupsCriteriaMap
            (java.util.Map<String, java.util.List<FlowRoutingGroupApi>> routingGroupsCriteria) {
        return routingGroupsCriteria.entrySet().stream()
                .collect(Collectors.toMap(java.util.Map.Entry::getKey, entry -> toRoutingGroupRpcs(entry.getValue())));
    }

    default String toStringBucketingMode(BucketingMode bucketingMode) {
        return bucketingMode == null ? null : bucketingMode.getValue();
    }

    default BucketingMode toBucketingMode(String bucketingMode) {
        return bucketingMode == null ? null : BucketingMode.fromValue(bucketingMode);
    }

    FlowRoutingGroupRpc flowRoutingGroupApiToRpc(FlowRoutingGroupApi flowRoutingGroupApi);

}

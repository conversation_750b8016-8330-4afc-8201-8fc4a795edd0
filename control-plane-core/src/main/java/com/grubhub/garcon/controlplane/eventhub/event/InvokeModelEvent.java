package com.grubhub.garcon.controlplane.eventhub.event;

import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class InvokeModelEvent {
    private Long requestTime;
    private Long requestDurationMs;
    ModelInferenceRequest request;
    Map<String, List<?>> response;
    Map<String, List<?>> normalizedResponse;
    private String modelCategory;
    private boolean isDefaultResponse;
}

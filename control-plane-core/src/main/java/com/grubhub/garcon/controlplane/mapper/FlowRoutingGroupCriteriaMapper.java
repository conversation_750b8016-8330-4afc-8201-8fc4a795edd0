package com.grubhub.garcon.controlplane.mapper;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupCriteria;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupsCriteriaApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupsCriteriaDTO;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.NonNull;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
@SuppressWarnings(value = "")
public interface FlowRoutingGroupCriteriaMapper {

    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidAsString(flowRoutingGroupCriteria.getFlowId()))")
    @Mapping(target = "routingGroups", ignore = true)
    FlowRoutingGroupsCriteriaDTO toDTO(@NonNull FlowRoutingGroupCriteria flowRoutingGroupCriteria);

    @Mapping(target = "routingGroups", source = "routingGroups", qualifiedByName = "apiToDTOsVavrJava")
    FlowRoutingGroupsCriteriaDTO toDTO(@NonNull FlowRoutingGroupsCriteriaApi flowRoutingGroupsCriteriaApi);

    @Named("apiToDTOsVavrJava")
    default List<FlowRoutingGroupDTO> apiToDTOsVavrJava(java.util.List<FlowRoutingGroupApi> apis) {
        return List.ofAll(apis.stream().map(this::flowRoutingGroupToDTO));
    }

    FlowRoutingGroupDTO flowRoutingGroupToDTO(FlowRoutingGroupApi flowRoutingGroupApi);


    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidFromString(flowRoutingGroupCriteria.getFlowId()))")
    @Mapping(target = "routingGroups", expression = "java(extractRoutingGroupNames(flowRoutingGroupCriteria.getRoutingGroups()))")
    @Mapping(target = "routingGroupCriteria", source = "routingGroupCriteria")
    @SuppressFBWarnings({"RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE"})
    FlowRoutingGroupCriteria toModel(FlowRoutingGroupsCriteriaDTO flowRoutingGroupCriteria, String routingGroupCriteria);

    default java.util.Set<String> extractRoutingGroupNames(List<FlowRoutingGroupDTO> routingGroupRequests) {
        return Option.of(routingGroupRequests)
                .map(requests ->  requests.toStream().map(FlowRoutingGroupDTO::getGroupName).collect(HashSet.collector()))
                .map(HashSet::toJavaSet)
                .getOrElse(java.util.HashSet::new);
    }

}

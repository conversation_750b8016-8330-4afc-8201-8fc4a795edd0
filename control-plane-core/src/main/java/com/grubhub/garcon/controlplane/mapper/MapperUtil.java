package com.grubhub.garcon.controlplane.mapper;

import lombok.experimental.UtilityClass;

import java.util.Optional;
import java.util.UUID;

@UtilityClass
public class MapperUtil {

    public static String uuidAsString(UUID uuid) {
        return Optional.ofNullable(uuid)
                .map(UUID::toString)
                .orElse(null);
    }

    public static UUID uuidFromString(String uuid) {
        return Optional.ofNullable(uuid)
                .map(UUID::fromString)
                .orElse(null);
    }
}

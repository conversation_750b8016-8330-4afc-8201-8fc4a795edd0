package com.grubhub.garcon.controlplane.sagemaker;

import com.google.inject.Inject;
import com.google.inject.Provider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.auth.credentials.*;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;

@Slf4j
public class SageMakerRuntimeClientProvider implements Provider<SageMakerRuntimeClient> {

    private final SageMakerRuntimeClient sageMakerRuntimeClient;

    @Inject
    public SageMakerRuntimeClientProvider(SageMakerConfig config) {

        AwsCredentialsProvider credentialsProvider;
        AwsCredentials credentials = config.getAwsCredentials();
        String accessKeyId = credentials.accessKeyId();

        if (credentials == null || StringUtils.isAnyBlank(accessKeyId, credentials.secretAccessKey())) {
            credentialsProvider = DefaultCredentialsProvider.create();
            accessKeyId = credentialsProvider.resolveCredentials().accessKeyId();

        } else {
            credentialsProvider =
                    StaticCredentialsProvider.create(
                            AwsBasicCredentials.create(
                                    credentials.accessKeyId(),
                                    credentials.secretAccessKey()));
        }

        log.info("Connecting to SageMaker with accessKeyId={}", accessKeyId);

        sageMakerRuntimeClient = SageMakerRuntimeClient.builder()
                .credentialsProvider(credentialsProvider)
                .region(Region.of(config.getRegion()))
                .build();
    }

    @Override
    public SageMakerRuntimeClient get() {
        return sageMakerRuntimeClient;
    }
}

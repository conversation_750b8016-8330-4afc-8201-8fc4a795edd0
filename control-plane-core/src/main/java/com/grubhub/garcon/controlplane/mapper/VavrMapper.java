package com.grubhub.garcon.controlplane.mapper;

import io.vavr.collection.HashMap;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Set;
import io.vavr.control.Option;
import lombok.experimental.UtilityClass;

import java.util.Collections;

@UtilityClass
public class VavrMapper {

    public static <T> java.util.List<T> toJavaList(List<T> list) {
        return Option.of(list)
                .map(List::toJavaList)
                .getOrElse(Collections::emptyList);
    }

    public static <T> java.util.Set<T> listToJavaSet(List<T> list) {
        return Option.of(list)
                .map(List::toJavaSet)
                .getOrElse(Collections::emptySet);
    }

    public static <T> List<T> toVavrList(java.util.List<T> list) {
        return Option.of(list)
                .map(List::ofAll)
                .getOrElse(List::empty);
    }

    public static <T> List<T> setToVavrList(java.util.Set<T> list) {
        return Option.of(list)
                .map(List::ofAll)
                .getOrElse(List::empty);
    }

    public static <T> java.util.Set<T> toJavaSet(Set<T> set) {
        return Option.of(set)
                .map(Set::toJavaSet)
                .getOrElse(Collections::emptySet);
    }

    public static <T> Set<T> toVavrSet(java.util.Set<T> set) {
        return Option.of(set)
                .map(HashSet::ofAll)
                .getOrElse(HashSet::empty);
    }

    public static <K, V> Map<K, V> toVavrMap(java.util.Map<K, V> map) {
        return Option.of(map)
                .map(HashMap::ofAll)
                .getOrElse(HashMap::empty);
    }

    public static <K, V> java.util.Map<K, V> toJavaMap(Map<K, V> map) {
        return Option.of(map)
                .map(Map::toJavaMap)
                .getOrElse(Collections::emptyMap);
    }

}

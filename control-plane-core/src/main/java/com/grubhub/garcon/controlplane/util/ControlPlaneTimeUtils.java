package com.grubhub.garcon.controlplane.util;

import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import lombok.experimental.UtilityClass;

import java.time.ZoneOffset;
import java.time.temporal.ChronoField;

@UtilityClass
public class ControlPlaneTimeUtils {

    public static int getDayOfYear(JavaDateTimeHelper dateTimeHelper) {
        return dateTimeHelper.atZone(ZoneOffset.UTC).get(ChronoField.DAY_OF_YEAR);
    }

    public static long getEpochForDay(JavaDateTimeHelper dateTimeHelper) {
        return dateTimeHelper.atZone(ZoneOffset.UTC).toLocalDate().atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli();
    }
}

package com.grubhub.garcon.controlplane.mapper;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.RoutingGroupResponseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface RoutingGroupResponseMapper {

    RoutingGroupResponseDTO toRoutingGroupResponse(FlowRoutingGroupDTO flowRoutingGroup, double routingRand);
    @Mapping(target = "flowId", expression = "java(MapperUtil.uuidAsString(flowRoutingGroup.getFlowId()))")
    FlowRoutingGroupDTO toDTO(FlowRoutingGroupV2 flowRoutingGroup);
}

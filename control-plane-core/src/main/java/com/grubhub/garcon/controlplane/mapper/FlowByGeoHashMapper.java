package com.grubhub.garcon.controlplane.mapper;

import com.grubhub.garcon.controlplane.cassandra.models.DinerType;
import com.grubhub.garcon.controlplane.cassandra.models.Flow;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import com.grubhub.garcon.controlplane.dto.FlowByGeoHashDTO;
import lombok.NonNull;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface FlowByGeoHashMapper {

    @Mapping(target = "flowSet", source = "flow.flowSet")
    @Mapping(target = "flowId", source = "flow.flowId")
    FlowByGeoHash toModel(@NonNull Flow flow, @NonNull String geohash, @NonNull String marketName);

    FlowByGeoHashDTO toDTO(@NonNull FlowByGeoHash flowByGeoHash, @NonNull DinerType resolvedDinerType);
}

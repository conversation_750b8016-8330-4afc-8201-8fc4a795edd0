package com.grubhub.garcon.controlplane.config;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class GdpDataTransferConfig {
    private Boolean actionsPollEnabled = false;
    private Integer allowedNumberOfPendingVersionByModel = 1; // Zero means no limit.
    private String bucket = "grubhub-gdp-data-transfer-dev";
    private String actionsPrefix = "/roux-model-serving/ddml_control_plane/actions/";
}

package com.grubhub.garcon.controlplane.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.config.MealTimeByTimeOfDayConfig;
import com.grubhub.garcon.controlplaneapi.models.QueryType;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.ResolveFlowRequest;
import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.With;

import java.util.Set;
import java.util.regex.Pattern;

import static java.util.Collections.emptySet;

@Getter
@Builder(toBuilder = true)
@RequiredArgsConstructor
@AllArgsConstructor
@With
@ToString
public class EnrichedResolveFlowRequest {
    private static final String REGEX_SPLIT_IN_WORDS = "\\s+";
    private final String applicationId;
    private final String dinerId;
    private final String flowSet;
    private final double lat;
    private final double lng;
    private final int totalOrders;
    private final String variationId;
    private final String whenFor;
    private final String callerTrackingId;
    @JsonIgnore
    private String resolvedGeoHash;
    @JsonIgnore
    private FlowConfig flowConfig;
    @JsonIgnore
    private MealTimeByTimeOfDayConfig mealTimeByTimeOfDayConfig;
    private String trackingId;
    private String brand;
    private String applicationType;
    private String applicationVersion;
    @Builder.Default
    private List<String> queryTokens = List.empty();
    private String userDomain;
    private String userEmail;
    private String mealtime;
    private QueryType queryType;
    private String orderType;
    private Set<String> requestTags;
    private String clientEntityId;
    @Builder.Default
    @JsonIgnore
    private Set<String> resolvedModelClassifications = emptySet();
    private String queryKeywordsText;

    private String routingGroupElectionCriteria;

    public EnrichedResolveFlowRequest(ResolveFlowRequest resolveFlowRequest) {
        this.applicationId = resolveFlowRequest.getApplicationId();
        this.applicationVersion = resolveFlowRequest.getApplicationVersion();
        this.dinerId = resolveFlowRequest.getDinerId();
        this.flowSet = resolveFlowRequest.getFlowSet();
        this.lat = resolveFlowRequest.getLat();
        this.lng = resolveFlowRequest.getLng();
        this.totalOrders = resolveFlowRequest.getTotalOrders();
        this.variationId = resolveFlowRequest.getVariationId();
        this.applicationType = ApplicationType.of(this.applicationId).getType();
        this.orderType = resolveFlowRequest.getOrderType();
        this.callerTrackingId = resolveFlowRequest.getCallerTrackingId();
        this.whenFor = resolveFlowRequest.getWhenFor();
        this.requestTags = resolveFlowRequest.getRequestTags();
        this.queryTokens = splitQueryIntoWords(resolveFlowRequest);
        this.clientEntityId = resolveFlowRequest.getClientEntityId();
    }

    private List<String> splitQueryIntoWords(ResolveFlowRequest resolveFlowRequest) {
        return Option.of(resolveFlowRequest)
                .map(request -> request.getQueryText() != null ? request.getQueryText() : "")
                .map(this::splitter)
                .getOrElse(List::empty);
    }

    private List<String> splitter(String queryText) {
        return Pattern.compile(REGEX_SPLIT_IN_WORDS).splitAsStream(queryText).collect(List.collector());
    }

    public ResolveFlowRequest asResolveFlowRequest() {
        return ResolveFlowRequest
                .builder()
                .variationId(variationId)
                .applicationId(applicationId)
                .applicationVersion(applicationVersion)
                .dinerId(dinerId)
                .flowSet(flowSet)
                .lat(lat)
                .lng(lng)
                .totalOrders(totalOrders)
                .whenFor(whenFor)
                .callerTrackingId(callerTrackingId)
                .orderType(orderType)
                .requestTags(requestTags)
                .queryText(fromTokensToQuery())
                .build();
    }

    private String fromTokensToQuery() {
        return Option
                .of(queryTokens)
                .getOrElse(List.empty())
                .toStream()
                .foldLeft(new StringBuilder(), (acc, token) -> acc.append(token).append(" "))
                .toString()
                .trim();
    }

    public int getGeoHashPrecision() {
        return flowConfig.getPrecision();
    }
}

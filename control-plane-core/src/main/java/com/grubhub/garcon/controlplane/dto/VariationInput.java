package com.grubhub.garcon.controlplane.dto;

import io.vavr.collection.Stream;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang.StringUtils;

import java.util.UUID;

@Value
@Slf4j
public class VariationInput {

    private static final String SEP = "\\|";
    private static final int MIN_TOKENS = 3;

    String flowSet;
    UUID flowId;
    String routingGroupName;

    public VariationInput(String variation) {
        try {
            val variationTokens = Stream.of(variation.split(SEP)).toJavaArray(String[]::new);
            if (variationTokens.length < MIN_TOKENS) {
                throw new IllegalArgumentException("Variation id is not in the proper format! Usage <flow_set>|<flow_id>|<routing_group_name>");
            }
            flowSet = variationTokens[0];
            flowId = UUID.fromString(variationTokens[1]);
            routingGroupName = variationTokens[2];
        } catch (Exception e) {
            log.error("Error when trying to parse variation input={} Usage <flow_set>|<flow_id>|<routing_group_name> ", variation, e);
            throw new IllegalArgumentException("Error when trying to parse variation input=" + variation + "!" +
                    "Usage <flow_set>|<flow_id>|<routing_group_name>");
        }
    }

    public static boolean hasValidFormat(String variation) {
        return StringUtils.isNotBlank(variation) && variation.split(SEP).length >= MIN_TOKENS;
    }

}

package com.grubhub.garcon.controlplane.metrics;

import com.grubhub.roux.tracing.shared.StartSpanResult;
import com.grubhub.roux.tracing.shared.TracingHelper;
import com.grubhub.roux.tracing.shared.TracingHelperFactory;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

import java.util.Collections;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class Spans {
    public static final TracingHelper TRACING_HELPER = TracingHelperFactory.getInstance();

    public static <T> T span(String operationName, Map<String, String> tags, Supplier<T> supplier) {
        StartSpanResult startSpanResult = TRACING_HELPER.startSpan(operationName, tags);
        try {
            return supplier.get();
        } finally {
            TRACING_HELPER.finish(startSpanResult.getScope(), startSpanResult.getSpan());
        }
    }

    public static <T> T span(String operationName, Supplier<T> supplier) {
        return span(operationName, Collections.emptyMap(), supplier);
    }

    public static Tags convertTagsMapToList(Map<String, String> tags) {
        java.util.List<Tag> tagList = tags.entrySet()
                .stream()
                .map(entry -> Tag.of(entry.getKey(), String.valueOf(entry.getValue())))
                .collect(Collectors.toList());

        return Tags.of(tagList);
    }
}

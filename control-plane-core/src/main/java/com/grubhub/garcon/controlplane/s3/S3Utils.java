package com.grubhub.garcon.controlplane.s3;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import io.vavr.collection.List;
import io.vavr.collection.Set;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CopyObjectRequest;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Object;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.Charset;

/**
 * Common utils for AWS S3
 */
@Slf4j
public abstract class S3Utils {

    private static final ObjectMapper OBJECT_MAPPER = ObjectMapperHelper.INSTANCE;

    public static List<S3Object> listObjectsInPrefix(S3Client s3, String path) {
        try {
            val s3Path = S3Path.from(path);
            val request = ListObjectsV2Request.builder().bucket(s3Path.getBucket()).prefix(s3Path.getKey()).build();
            val response = s3.listObjectsV2Paginator(request);
            return Stream.ofAll(response.stream()).map(page -> List.ofAll(page.contents())).flatMap(i -> i).toList();
        } catch (Exception e) {
            throw new RuntimeException(String.format("Error listing objects in S3 path=%s", path), e);
        }
    }


    public static List<String> listObjectsPathInPrefix(S3Client s3, String path) {
        val s3Path = S3Path.from(path);
        return listObjectsInPrefix(s3, path).map(obj -> S3Path.from(s3Path.getBucket(), obj.key()).getUri());
    }

    public static List<String> listFilesInPrefix(S3Client s3, String path, Set<String> extensions) {
        val s3Path = S3Path.from(path);
        return listObjectsInPrefix(s3, path)
                .filter(obj -> extensions.exists(ext -> obj.key().endsWith("." + ext)))
                .map(obj -> S3Path.from(s3Path.getBucket(), obj.key()).getUri());
    }

    public static String getLastDirNameInPrefix(S3Client s3, String path) {
        val s3Path = S3Path.from(path);
        val dirs = listObjectsPathInPrefix(s3, path);
        Set<String> dirNames = dirs.map(dirFullPath -> {
            val entries = dirFullPath.replace(s3Path.getUri(), "").split("/");
            return Option.when(entries.length > 0 && !entries[0].isEmpty(), entries[0]);
        }).filter(Option::isDefined).map(Option::get).toSet();
        return dirNames.max().getOrElse("");
    }

    public static void deleteObject(S3Client s3, String path) {
        try {
            val s3Path = S3Path.from(path);
            val request = DeleteObjectRequest.builder().bucket(s3Path.getBucket()).key(s3Path.getKey()).build();
            s3.deleteObject(request);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Error deleting object in S3 path=%s", path), e);
        }
    }

    public static void copyObject(S3Client s3, String srcPath, String dstPath) {
        try {
            val s3SrcPath = S3Path.from(srcPath);
            val s3DstPath = S3Path.from(dstPath);
            val request = CopyObjectRequest.builder()
                    .copySource(s3SrcPath.getBucketKey())
                    .destinationBucket(s3DstPath.getBucket()).destinationKey(s3DstPath.getKey())
                    .build();
            s3.copyObject(request);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Error copying S3 object from source=%s to dest=%s", srcPath, dstPath), e);
        }
    }

    public static List<String> loadTextObjectLines(S3Client s3, String path) {
        BufferedReader reader = null;
        try {
            val s3Path = S3Path.from(path);
            val request = GetObjectRequest.builder().bucket(s3Path.getBucket()).key(s3Path.getKey()).build();
            val response = s3.getObject(request);
            reader = new BufferedReader(new InputStreamReader(response, Charset.defaultCharset()));
            String line;
            val buffer = new java.util.ArrayList<String>();
            while ((line = reader.readLine()) != null) {
                buffer.add(line);
            }
            return List.ofAll(buffer);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Error loading text lines from S3 object path=%s", path), e);
        } finally {
            close(reader);
        }
    }

    public static String loadTextObject(S3Client s3, String path) {
        return loadTextObjectLines(s3, path).mkString("\n");
    }

    public static <T> T loadJsonObject(S3Client s3, String path, Class<T> valueType) {
        try {
            val text = loadTextObject(s3, path);
            return OBJECT_MAPPER.readValue(text, valueType);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Error loading json object from S3 path=%s, valueType=%s", path, valueType), e);
        }
    }

    public static void writeTextObject(S3Client s3, String path, String content) {
        try {
            val s3Path = S3Path.from(path);
            val request = PutObjectRequest.builder().bucket(s3Path.getBucket()).key(s3Path.getKey()).build();
            val body = RequestBody.fromString(content);
            s3.putObject(request, body);
        } catch (Exception e) {
            val contentLimit = String.valueOf(content).substring(1000);
            throw new RuntimeException(String.format("Error writing text object to S3 path=%s, content=%s", path, contentLimit), e);
        }
    }

    public static <T> void writeJsonObject(S3Client s3, String path, T object) {
        try {
            val content = OBJECT_MAPPER.writeValueAsString(object);
            writeTextObject(s3, path, content);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Error writing json object to S3 path=%s, object=%s", path, object), e);
        }
    }

    private static void close(Closeable item) {
        try {
            if (item != null) {
                item.close();
            }
        } catch (Exception e) {
            throw new RuntimeException("Error closing item", e);
        }
    }

    public static S3Path path(String path) {
        return S3Path.from(path);
    }

    public static S3Path path(String bucket, String key) {
        return S3Path.from(bucket, key);
    }

    @Data
    public static class S3Path {
        private static final String S3_START = "s3://";
        private final String bucket;
        private final String key;

        private S3Path(String bucket, String key) {
            this.bucket = bucket;
            this.key = key;
        }

        public static S3Path from(String path) {
            try {
                val uri = new URI(path);
                val prefix = uri.getPath().substring(1);
                return new S3Path(uri.getHost(), prefix);
            } catch (URISyntaxException e) {
                throw new RuntimeException(String.format("Error parsing S3 path=%s", path), e);
            }
        }

        public static S3Path from(String bucket, String key) {
            String validBucket = (bucket.endsWith("/") ? bucket.substring(0, bucket.length() - 1) : bucket).replace(S3_START, "");
            val validKey = key.startsWith("/") ? key : "/" + key;
            return S3Path.from(S3_START + validBucket + validKey);
        }

        public String getUri() {
            return S3_START + getBucketKey();
        }

        public String getBucketKey() {
            return bucket + "/" + key;
        }

        public String getFileName() {
            val tokens = key.split("/");
            if (tokens.length > 0) {
                return tokens[tokens.length - 1];
            }
            return "";
        }
    }


}

INSERT INTO ddml_control_plane.ensemble_weights(ensemble_name, ensemble_weight, model_weights) VALUES('search_m1_m2', 'half_and_half', {'search_m1':0.5, 'search_m2': 0.5 });
INSERT INTO ddml_control_plane.ensemble_weights(ensemble_name, ensemble_weight, model_weights) VALUES('search_m1_m2', 'only_m1', {'search_m1' : 1.0, 'search_m2' : 0.0 });
INSERT INTO ddml_control_plane.ensemble_weights(ensemble_name, ensemble_weight, model_weights) VALUES('search_m1_m2', '80_and_20', {'search_m1' : 0.8, 'search_m2': 0.2 });
INSERT INTO ddml_control_plane.ensemble_weights(ensemble_name, ensemble_weight, model_weights) VALUES('search_generic_m_ensemble', '80_and_20_generic', {'search_generic_m_model_1' : 0.8, 'search_generic_m_model_2': 0.2 });
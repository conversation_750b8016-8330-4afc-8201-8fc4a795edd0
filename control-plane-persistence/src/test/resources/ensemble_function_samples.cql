INSERT INTO ddml_control_plane.ensemble_functions(ensemble_name, ensemble_function, formula, function_type) VALUES('search_m1_m2', 'ensemble_function','generic_formula', 'generic_type');
INSERT INTO ddml_control_plane.ensemble_functions(ensemble_name, ensemble_function, formula, function_type) VALUES('search_m1_m2', 'default','0.48 * search_m1_LATEST + 0.48 * search_m2_LATEST + 0.04 * search_m3_LATEST', 'inline');
INSERT INTO ddml_control_plane.ensemble_functions(ensemble_name, ensemble_function, formula) VALUES('search_m1_m2', '90_to_10','0.9 * search_m1_LATEST + 0.1 * search_m2_LATEST');

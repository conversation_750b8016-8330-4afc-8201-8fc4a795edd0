INSERT INTO ddml_control_plane.models(model_name, model_description, model_type, location, version, versioning_strategy) VALUES('search_m1', 'LTR model', 'TENSOR_FLOW', 's3:\/\/bucket\/...\/', '1.0', 'DYNAMIC');
INSERT INTO ddml_control_plane.models(model_name, model_description, model_type) VALUES('search_m2', 'M2 model', 'FUNCTION');
INSERT INTO ddml_control_plane.models(model_name, model_description, model_type, signature_def) VALUES('search_generic_m_model_1', 'generic M model 1', 'TENSOR_FLOW','{\"x\": \"FLOAT\" }');
INSERT INTO ddml_control_plane.models(model_name, model_description, model_type, signature_def) VALUES('search_generic_m_model_2', 'generic M model 2', 'FUNCTION','{\"x\": \"FLOAT\" }');
INSERT INTO ddml_control_plane.models(model_name, model_description, model_type, signature_def) VALUES('search_generic_m_model_3', 'generic M model 3', 'FUNCTION','{\"x\": \"FLOAT\" }');
INSERT INTO ddml_control_plane.models(model_name, model_description, model_type, signature_def) VALUES('search_strange_m_model', 'strange M model', 'TENSOR_FLOW','{\"strange_type\": \"STRANGE\" }');
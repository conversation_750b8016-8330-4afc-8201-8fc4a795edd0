package com.grubhub.garcon.ensembler.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.ensembler.cassandra.dao.ModelDao;
import com.grubhub.garcon.ensembler.cassandra.models.Model;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static com.grubhub.garcon.ensembler.cassandra.dao.factory.ModelFactory.m1Model;
import static org.junit.jupiter.api.Assertions.*;


public class ModelDaoTest extends DaoTestBase {

    private ModelDao modelDao;

    @BeforeEach
    void beforeEach() {
        modelDao = new ModelDaoImpl(casserole);
    }

    @Test
    public void insert_should_save_to_cassandra() {
        Model model = m1Model();

        modelDao.insert(model);

        Optional<Model> actualModelAsOptional = modelDao.select(model.getModelName());
        assertTrue(actualModelAsOptional.isPresent());
        assertEquals(model, actualModelAsOptional.get());

    }

    @Test
    public void update_should_execute_successfully() {
        String sampleModelName = "search_m1";
        Optional<Model> actualModelInitialState = modelDao.select(sampleModelName);
        Model modelSample = actualModelInitialState.get();
        String initialDescription = "LTR model";
        String updatedDescription = "new_model_description";

        modelSample.setModelDescription(updatedDescription);

        modelDao.update(modelSample);

        Optional<Model> actualModelAsOptional = modelDao.select(sampleModelName);
        assertTrue(actualModelAsOptional.isPresent());
        assertEquals(updatedDescription, actualModelAsOptional.get().getModelDescription());
        assertNotEquals(initialDescription, actualModelAsOptional.get().getModelDescription());
    }

    @Test
    public void delete_should_execute_successfully() {
        Model model = m1Model();

        modelDao.insert(model);
        modelDao.delete(model.getModelName());

        Optional<Model> modelAsOptional = modelDao.select(model.getModelName());
        assertFalse(modelAsOptional.isPresent());
    }

}

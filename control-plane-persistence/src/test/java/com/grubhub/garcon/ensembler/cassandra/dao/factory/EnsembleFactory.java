package com.grubhub.garcon.ensembler.cassandra.dao.factory;

import com.grubhub.garcon.ensembler.cassandra.models.Ensemble;

import io.vavr.collection.HashSet;

import java.util.Set;

public class EnsembleFactory {
    public static Ensemble ensemble1() {

        Set<String> models = HashSet.of("search_m1", "search_m2").toJavaSet();

        return Ensemble.builder()
                .ensembleName("search_m1_m2")
                .ensembleDescription("LTR model")
                .status("ENABLED")
                .models(models)
                .version("1.0")
                .build();
    }

    public static Ensemble genericEnsembleM() {
        Set<String> models = HashSet.of("search_generic_m_model_1", "search_generic_m_model_2").toJavaSet();

        return Ensemble.builder()
                .ensembleName("search_generic_m_ensemble")
                .ensembleDescription("generic m-type models")
                .status("ENABLED")
                .version("1.0")
                .build();
    }
}

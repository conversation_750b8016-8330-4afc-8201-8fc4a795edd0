package com.grubhub.garcon.ensembler.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.ensembler.cassandra.dao.ModelFeatureDao;
import com.grubhub.garcon.ensembler.cassandra.models.ModelFeature;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.grubhub.garcon.controlplane.cassandra.utils.DaoUtils.truncateModelFeatureTable;
import static com.grubhub.garcon.ensembler.cassandra.dao.factory.ModelFeatureFactory.createM1ModelFeature;
import static com.grubhub.garcon.ensembler.cassandra.dao.factory.ModelFeatureFactory.createModelFeatureWithModelName;
import static org.assertj.core.api.Assertions.assertThat;


class ModelFeatureDaoTest extends DaoTestBase {

    private ModelFeatureDao modelFeatureDao;

    @BeforeEach
    void beforeEach() {
        modelFeatureDao = new ModelFeatureDaoImpl(casserole);
    }

    @Test
    void insert_should_save_to_cassandra() {
        ModelFeature modelFeature = createM1ModelFeature();

        modelFeatureDao.insert(modelFeature);

        List<ModelFeature> modelFeatures = modelFeatureDao.selectAll(modelFeature.getModelName());
        assertThat(modelFeatures).isNotEmpty();
    }

    @Test
    void selectAll_should_select_all_features_for_a_given_model() {
        ModelFeature m1ModelFeature = createM1ModelFeature();
        ModelFeature m2ModelFeature = createModelFeatureWithModelName("search_m1");
        modelFeatureDao.insert(m1ModelFeature);
        modelFeatureDao.insert(m2ModelFeature);

        List<ModelFeature> modelFeatures = modelFeatureDao.selectAll(m1ModelFeature.getModelName());
        assertThat(modelFeatures).isNotEmpty();
        assertThat(modelFeatures).hasSize(2);
    }


    @Test
    void update_should_be_reflected_into_database() {
        ModelFeature m1ModelFeature = createM1ModelFeature();
        modelFeatureDao.insert(m1ModelFeature);

        List<ModelFeature> modelFeatures = modelFeatureDao.selectAll(m1ModelFeature.getModelName());
        assertThat(modelFeatures).isNotEmpty();
        ModelFeature modelFeature = modelFeatures.get(0);

        modelFeature.setFeatureLocation("updated feature location");

        modelFeatureDao.update(modelFeature);

        List<ModelFeature> updatedModelFeatures = modelFeatureDao.selectAll(m1ModelFeature.getModelName());
        assertThat(updatedModelFeatures).isNotEmpty();
        ModelFeature updatedModelFeature = updatedModelFeatures.get(0);
        assertThat(updatedModelFeature.getFeatureLocation()).isEqualTo("updated feature location");
    }

    @Test
    void deleteAll_should_delete_successfully() {
        ModelFeature m1ModelFeature = createM1ModelFeature();
        modelFeatureDao.insert(m1ModelFeature);
        List<ModelFeature> modelFeatures = modelFeatureDao.selectAll(m1ModelFeature.getModelName());
        assertThat(modelFeatures).isNotEmpty();

        modelFeatureDao.delete(m1ModelFeature.getModelName());

        assertThat(modelFeatureDao.selectAll(m1ModelFeature.getModelName())).isEmpty();
    }

    @AfterEach
    void afterEach() {
        truncateModelFeatureTable(casserole);
    }
}

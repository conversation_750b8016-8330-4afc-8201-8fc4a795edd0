package com.grubhub.garcon.ensembler.cassandra.dao.factory;

import com.grubhub.garcon.ensembler.cassandra.models.EnsembleFunction;

public class EnsembleFunctionFactory {

    public static EnsembleFunction ensembleFunction1() {

        return EnsembleFunction.builder()
                .ensembleName("search_m1_m2")
                .ensembleFunction("ensemble_function")
                .formula("generic_formula")
                .functionType("generic_type")
                .build();
    }

    public static EnsembleFunction ensembleFunction2() {

        return EnsembleFunction.builder()
                .ensembleName("search_m1_m2")
                .ensembleFunction("default")
                .formula("0.48 * search_m1_LATEST + 0.48 * search_m2_LATEST + 0.04 * search_m3_LATEST")
                .functionType("inline")
                .build();
    }
    public static EnsembleFunction ensembleFunction3() {

        return EnsembleFunction.builder()
                .ensembleName("search_m1_m2")
                .ensembleFunction("90_to_10")
                .formula("0.9 * search_m1_LATEST + 0.1 * search_m2_LATEST")
                .build();
    }
}

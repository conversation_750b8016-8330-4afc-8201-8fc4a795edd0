package com.grubhub.garcon.ensembler.cassandra.dao.factory;

import com.grubhub.garcon.ensembler.cassandra.models.EnsembleWeight;
import io.vavr.collection.HashMap;

public class EnsembleWeightFactory {
    public static EnsembleWeight ensembleWeight1() {
        return EnsembleWeight.builder()
                .ensembleName("search_m1_m2")
                .ensembleWeight("half_and_half")
                .modelWeights(HashMap.of("search_m1", 0.5f, "search_m2", 0.5f).toJavaMap())
                .build();
    }

    public static EnsembleWeight ensembleWeight2() {
        return EnsembleWeight.builder()
                .ensembleName("search_m1_m2")
                .ensembleWeight("only_m1")
                .modelWeights(HashMap.of("search_m1", 1.0f, "search_m2", 0.0f).toJavaMap())
                .build();
    }

    public static EnsembleWeight ensembleWeight3() {
        return EnsembleWeight.builder()
                .ensembleName("search_m1_m2")
                .ensembleWeight("80_and_20")
                .modelWeights(HashMap.of("search_m1", 0.8f, "search_m2", 0.2f).toJavaMap())
                .build();
    }

    public static EnsembleWeight genericEnsembleWeightM() {
        return EnsembleWeight.builder()
                .ensembleName("search_generic_m_ensemble")
                .ensembleWeight("80_and_20_generic")
                .modelWeights(HashMap.of("search_generic_m_model_1", 0.8f, "search_generic_m_model_2", 0.2f).toJavaMap())
                .build();
    }
}

package com.grubhub.garcon.ensembler.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.ensembler.cassandra.dao.EnsembleDao;
import com.grubhub.garcon.ensembler.cassandra.dao.ModelDao;
import com.grubhub.garcon.ensembler.cassandra.models.Ensemble;
import com.grubhub.garcon.ensembler.cassandra.models.EnsembleFunction;
import com.grubhub.garcon.ensembler.cassandra.models.EnsembleWeight;
import com.grubhub.garcon.ensembler.cassandra.models.EntitiesAlias;
import com.grubhub.garcon.ensembler.cassandra.models.Model;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static com.grubhub.garcon.ensembler.cassandra.dao.factory.EnsembleFactory.ensemble1;
import static com.grubhub.garcon.ensembler.cassandra.dao.utils.DaoUtils.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

public class EnsembleDaoTest extends DaoTestBase {
    private EnsembleDao ensembleDao;

    @BeforeEach
    void beforeEach() {
        ModelDao modelDao = new ModelDaoImpl(casserole);
        ensembleDao = new EnsembleDaoImpl(casserole, modelDao);
    }

    @Test
    public void insert_should_save_to_cassandra() {
        Ensemble ensemble = ensemble1();

        ensembleDao.insert(ensemble);

        Optional<Ensemble> actualEnsembleAsOptional = ensembleDao.select(ensemble.getEnsembleName());
        assertTrue(actualEnsembleAsOptional.isPresent());
        assertEquals(ensemble, actualEnsembleAsOptional.get());
    }

    @Test
    public void selectModelsForEnsemble_should_return_a_list_of_models() {
        Ensemble ensemble = ensemble1();
        // Populate data
        executeEnsembleSamples(casserole);
        executeModelSamples(casserole);

        List<Model> modelList = ensembleDao.selectModelsForEnsemble(ensemble);

        // Ensure both models are present
        assertEquals(2, modelList.size());

        // Check expected values match
        List<String> actualModelNamesList = modelList.stream().map(Model::getModelName).toList();
        List<String> expectedModelNamesList = List.of("search_m1", "search_m2");

        assertTrue(actualModelNamesList.containsAll(expectedModelNamesList));
    }

    @Test
    public void selectAllWeightsForEnsemble_should_return_a_list_of_ensemble_weights() {
        Ensemble ensemble = ensemble1();

        // Populate data
        executeEnsembleSamples(casserole);
        executeEnsembleWeightSamples(casserole);
        List<EnsembleWeight> ensembleWeightsList = ensembleDao.selectAllEnsembleWeightsForEnsemble(ensemble);

        // Ensure all weights are present
        assertEquals(3, ensembleWeightsList.size());

        List<String> weightsNamesList = ensembleWeightsList.stream().map(EnsembleWeight::getEnsembleWeight).toList();
        List<String> expectedWeightNamesList = List.of("half_and_half", "only_m1", "80_and_20");

        assertTrue(weightsNamesList.containsAll(expectedWeightNamesList));
    }

    @Test
    public void selectAllFunctionsForEnsemble_should_return_a_list_of_ensemble_functions() {
        Ensemble ensemble = ensemble1();

        //Populate Data
        executeEnsembleSamples(casserole);
        executeModelSamples(casserole);
        executeEnsembleFunctionSamples(casserole);

        List<EnsembleFunction> ensembleFunctionList = ensembleDao.selectAllEnsembleFunctionsForEnsemble(ensemble);

        //ensure all functions are present
        assertEquals(3, ensembleFunctionList.size());

        List<String> functionsNamesList = ensembleFunctionList.stream().map(EnsembleFunction::getEnsembleFunction).toList();
        List<String> expectedFunctionNamesList = List.of("default", "90_to_10", "ensemble_function");

        assertTrue(functionsNamesList.containsAll(expectedFunctionNamesList));
    }

    @Test
    public void selectEnsembleWeightForEnsembleByName_should_return_one_ensemble_weight() {
        Ensemble ensemble = ensemble1();

        // Populate data
        executeEnsembleSamples(casserole);
        executeEnsembleWeightSamples(casserole);

        Optional<EnsembleWeight> ensembleWeightOptional = ensembleDao.selectEnsembleWeightForEnsembleByName(
                ensemble.getEnsembleName(),
                "half_and_half"
        );

        assertTrue(ensembleWeightOptional.isPresent());
        // Ensure single ensemble weight is present
        assertEquals(ensembleWeightOptional.get().getEnsembleWeight(), "half_and_half");

    }

    @Test
    public void selectEnsembleFunction_should_return_function() {
        Ensemble ensemble = ensemble1();
        executeEnsembleSamples(casserole);
        executeEnsembleFunctionSamples(casserole);

        Optional<EnsembleFunction> ensembleFunctionOptional = ensembleDao.selectEnsembleFunctionForEnsembleByName(
                ensemble.getEnsembleName(),
                "default");
        assertTrue(ensembleFunctionOptional.isPresent());
        assertEquals(ensembleFunctionOptional.get().getEnsembleFunction(), "default");
    }

    @Test
    public void delete_should_execute_successfully() {
        Ensemble ensemble = ensemble1();

        ensembleDao.insert(ensemble);
        Optional<Ensemble> existingEnsemble = ensembleDao.select(ensemble.getEnsembleName());
        assertTrue(existingEnsemble.isPresent());

        ensembleDao.delete(ensemble.getEnsembleName());
        Optional<Ensemble> ensembleAsOptional = ensembleDao.select(ensemble.getEnsembleName());
        assertFalse(ensembleAsOptional.isPresent());
    }

    @Test
    public void deleteAlias_should_execute_successfully() {
        ensembleDao.updateAlias("alias1", "ensemble1");
        Optional<EntitiesAlias> existingAlias = ensembleDao.getAlias("alias1");
        assertThat(existingAlias.isPresent()).isTrue();

        ensembleDao.deleteAlias("alias1");

        Optional<EntitiesAlias> deletedAlias = ensembleDao.getAlias("alias1");
        assertThat(deletedAlias.isPresent()).isFalse();
    }

}

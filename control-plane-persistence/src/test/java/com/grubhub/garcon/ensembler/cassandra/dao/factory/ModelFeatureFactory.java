package com.grubhub.garcon.ensembler.cassandra.dao.factory;

import com.grubhub.garcon.ensembler.cassandra.models.ModelFeature;
import io.vavr.collection.List;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ModelFeatureFactory {

    public static ModelFeature createM1ModelFeature() {
        return ModelFeature.builder()
                .featureSourceType("FEATURE_STORE_INTERNAL")
                .featureLocation("new york")
                .featureName("clickLTRRestaurantFeature")
                .featureStoreFields(List.of("featurestore1", "featurestore2").toJavaList())
                .modelName("search_m1")
                .build();
    }

    public static ModelFeature createModelFeatureWithModelName(String modelName) {
        return ModelFeature.builder()
                .featureSourceType("RUNTIME")
                .featureLocation("chicago")
                .featureName("dinerRestaurantFeature")
                .featureStoreFields(List.of("featurestore3", "featurestore4").toJavaList())
                .modelName(modelName)
                .build();
    }
}

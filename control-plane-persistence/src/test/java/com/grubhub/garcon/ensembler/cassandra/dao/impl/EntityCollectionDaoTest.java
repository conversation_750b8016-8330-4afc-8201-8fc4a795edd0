package com.grubhub.garcon.ensembler.cassandra.dao.impl;

import com.google.common.collect.ImmutableSet;
import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.ensembler.cassandra.dao.EntityCollectionDao;
import com.grubhub.garcon.ensembler.cassandra.models.EntityCollection;
import io.vavr.collection.List;
import lombok.val;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static org.assertj.core.api.Assertions.assertThat;

class EntityCollectionDaoTest extends DaoTestBase {

    private EntityCollectionDao entityCollectionDao;

    @BeforeEach
    void beforeEach() {
        entityCollectionDao = new EntityCollectionDaoImpl(casserole);
    }

    @Test
    void update() {
        val entityCollection = EntityCollection.builder()
                .collectionName("test collection")
                .entityId("ABC")
                .entityStatus("INITIALISED")
                .entityCategory("testCategory")
                .build();
        entityCollectionDao.update(entityCollection);

        val entityCollectionAsOption = entityCollectionDao.select(
                entityCollection.getCollectionName(), entityCollection.getEntityId()
        );

        assertThat(entityCollectionAsOption.isDefined()).isTrue();
        entityCollectionAsOption.peek(existingEntityCollection ->
            assertThat(existingEntityCollection.getEntityStatus()).isEqualTo(entityCollection.getEntityStatus())
        );

        entityCollectionAsOption.peek(existingEntityCollection ->
                assertThat(existingEntityCollection.getEntityCategory()).isEqualTo(entityCollection.getEntityCategory())
        );

        entityCollection.setEntityStatus("COMPLETED");
        entityCollectionDao.update(entityCollection);

        val entityCollectionAfterUpdateAsOption = entityCollectionDao.select(
                entityCollection.getCollectionName(), entityCollection.getEntityId()
        );

        assertThat(entityCollectionAfterUpdateAsOption.isDefined()).isTrue();
        entityCollectionAfterUpdateAsOption.peek(existingEntityCollection ->
                assertThat(existingEntityCollection.getEntityStatus()).isEqualTo(entityCollection.getEntityStatus()));
    }

    @Test
    void select() {
        val entityCollection = EntityCollection.builder()
                .collectionName("test collection")
                .entityId("ABC")
                .entityStatus("INITIALISED")
                .build();
        entityCollectionDao.update(entityCollection);

        val entityCollectionAsOption = entityCollectionDao.select(
                entityCollection.getCollectionName(), entityCollection.getEntityId()
        );

        assertThat(entityCollectionAsOption.isDefined()).isTrue();
        entityCollectionAsOption.peek(existingEntityCollection ->
                assertThat(existingEntityCollection.getEntityStatus()).isEqualTo(entityCollection.getEntityStatus())
        );
    }

    @Test
    void select_all_entities_regardless_of_status() {
        val firstEntityCollection = EntityCollection.builder()
                .collectionName("test 1 collection")
                .entityId("ABC")
                .entityStatus("INITIALISED")
                .build();

        val secondEntityCollection = EntityCollection.builder()
                .collectionName("test 2 collection")
                .entityId("DEF")
                .entityStatus("PROCESSING")
                .build();

        val thirdEntityCollection = EntityCollection.builder()
                .collectionName("test 1 collection")
                .entityId("GHJ")
                .build();


        List.of(firstEntityCollection, secondEntityCollection, thirdEntityCollection)
                .forEach(entityCollectionDao::update);

        val entityCollection = entityCollectionDao.selectAll(
                firstEntityCollection.getCollectionName()
        );

        assertThat(entityCollection).hasSize(2);
        assertThat(entityCollection.map(EntityCollection::getEntityId)).containsExactlyInAnyOrder("ABC", "GHJ");
    }

    @Test
    void select_all_entities_with_status_null_and_ids_in_list() {
        val firstEntityCollection = EntityCollection.builder()
                .collectionName("test 1 collection")
                .entityId("ABC")
                .entityStatus("INITIALISED")
                .build();

        val secondEntityCollection = EntityCollection.builder()
                .collectionName("test 3 collection")
                .entityId("DEF")
                .entityStatus("PROCESSING")
                .build();

        val thirdEntityCollection = EntityCollection.builder()
                .collectionName("test 3 collection")
                .entityId("GHJ")
                .build();


        List.of(firstEntityCollection, secondEntityCollection, thirdEntityCollection)
                .forEach(entityCollectionDao::update);

        val entityCollection = entityCollectionDao.selectAllByIds(
                thirdEntityCollection.getCollectionName(), null, ImmutableSet.of("GHJ", "DEF")
        );

        assertThat(entityCollection).hasSize(2);
        assertThat(entityCollection.map(EntityCollection::getEntityId)).containsExactlyInAnyOrder("GHJ", "DEF");
        assertThat(entityCollection.map(EntityCollection::getCollectionName)).containsOnly("test 3 collection");
    }

    @Test
    void selectAllByCollectionNameAndNullStatus() {
        val firstEntityCollection = EntityCollection.builder()
                .collectionName("test 1 collection")
                .entityId("ABC")
                .entityStatus("INITIALISED")
                .build();

        val secondEntityCollection = EntityCollection.builder()
                .collectionName("test 2 collection")
                .entityId("DEF")
                .entityStatus("PROCESSING")
                .build();

        val thirdEntityCollection = EntityCollection.builder()
                .collectionName("test 1 collection")
                .entityId("ABC")
                .build();


        List.of(firstEntityCollection, secondEntityCollection, thirdEntityCollection)
                .forEach(entityCollectionDao::update);

        val entityCollection = entityCollectionDao.selectAll(
                firstEntityCollection.getCollectionName()
        );

        assertThat(entityCollection).isNotEmpty();
        assertThat(entityCollection.size()).isEqualTo(1);
        assertThat(entityCollection.map(EntityCollection::getCollectionName)).containsExactly(thirdEntityCollection.getCollectionName());

    }

    @Test
    void delete() {
        val entityCollection = EntityCollection.builder()
                .collectionName("test collection")
                .entityId("ABC")
                .entityStatus("INITIALISED")
                .build();
        entityCollectionDao.update(entityCollection);

        entityCollectionDao.delete(entityCollection.getCollectionName(), entityCollection.getEntityId());

        assertThat(entityCollectionDao.select(entityCollection.getCollectionName(), entityCollection.getEntityId()).isEmpty()).isTrue();
    }

    @AfterEach
    void afterEach() {
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }

}

package com.grubhub.garcon.ensembler.cassandra.dao.utils;

import com.grubhub.garcon.ensembler.cassandra.dao.factory.EnsembleFactory;
import com.grubhub.garcon.ensembler.cassandra.dao.factory.EnsembleFunctionFactory;
import com.grubhub.garcon.ensembler.cassandra.dao.factory.EnsembleWeightFactory;
import com.grubhub.garcon.ensembler.cassandra.dao.factory.ModelFactory;
import com.grubhub.garcon.ensembler.cassandra.models.Ensemble;
import com.grubhub.garcon.ensembler.cassandra.models.EnsembleFunction;
import com.grubhub.garcon.ensembler.cassandra.models.EnsembleWeight;
import com.grubhub.garcon.ensembler.cassandra.models.Model;
import com.grubhub.roux.casserole.api.Casserole;
import io.vavr.collection.List;
import lombok.experimental.UtilityClass;

@UtilityClass
public class DaoUtils {

    public static void executeModelSamples(Casserole casserole) {
        List<Model> models = List.of(
                ModelFactory.m1Model(),
                ModelFactory.m2Model(),
                ModelFactory.genericModel1(),
                ModelFactory.genericModel2(),
                ModelFactory.genericModel3(),
                ModelFactory.strangeModelTypeM()
        );
        casserole.insertMany(models);
    }

    public static void executeEnsembleSamples(Casserole casserole) {
        List<Ensemble> ensembles = List.of(
                EnsembleFactory.ensemble1(),
                EnsembleFactory.genericEnsembleM()
        );
        casserole.insertMany(ensembles);
    }

    public static void executeEnsembleWeightSamples(Casserole casserole) {
        List<EnsembleWeight> ensembleWeights = List.of(
                EnsembleWeightFactory.ensembleWeight1(),
                EnsembleWeightFactory.ensembleWeight2(),
                EnsembleWeightFactory.ensembleWeight3(),
                EnsembleWeightFactory.genericEnsembleWeightM()
        );
        casserole.insertMany(ensembleWeights);
    }

    public static void executeEnsembleFunctionSamples(Casserole casserole) {
        List<EnsembleFunction> ensembleFunctions = List.of(
                EnsembleFunctionFactory.ensembleFunction1(),
                EnsembleFunctionFactory.ensembleFunction2(),
                EnsembleFunctionFactory.ensembleFunction3()
        );
        casserole.insertMany(ensembleFunctions);
    }

}

package com.grubhub.garcon.ensembler.cassandra.dao.factory;

import com.grubhub.garcon.ensembler.cassandra.models.Model;
import lombok.experimental.UtilityClass;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;

@UtilityClass
public class ModelFactory {
    public static Model m1Model() {
        return Model.builder()
                .modelName("search_m1")
                .modelDescription("LTR model")
                .modelType("TENSOR_FLOW")
                .location("'s3:\\/\\/bucket\\/...\\/'")
                .version("1.0")
                .versioningStrategy("DYNAMIC")
                .status("ENABLED")
                .processedFeaturesFilter(new HashSet<>())
                .processedFeaturesMapping(new HashMap<>())
                .searchEmbeddingsIndexNames(Collections.emptyList())
                .build();
    }

    public static Model m2Model() {
        return Model.builder()
                .modelName("search_m2")
                .modelDescription("M2 model")
                .modelType("FUNCTION")
                .build();
    }

    public static Model genericModel1() {
        return Model.builder()
                .modelName("search_generic_m_model_1")
                .modelDescription("generic M model 1")
                .modelType("TENSOR_FLOW")
                .build();
    }

    public static Model genericModel2() {
        return Model.builder()
                .modelName("search_generic_m_model_2")
                .modelDescription("generic M model 2")
                .modelType("FUNCTION")
                .build();
    }

    public static Model genericModel3() {
        return Model.builder()
                .modelName("search_generic_m_model_3")
                .modelDescription("generic M model 3")
                .modelType("FUNCTION")
                .build();
    }

    public static Model strangeModelTypeM() {
        return Model.builder()
                .modelName("search_strange_m_model")
                .modelDescription("strange M model")
                .build();
    }
}

package com.grubhub.garcon.ensembler.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.ensembler.cassandra.dao.FeatureValueDao;
import com.grubhub.garcon.ensembler.cassandra.models.FeatureValue;
import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.val;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class FeatureValueDaoTest extends DaoTestBase {
    private FeatureValueDao featureValueDao;

    @BeforeEach
    void beforeEach() {
        featureValueDao = new FeatureValueDao(casserole);
    }

    @Test
    void selectAsync_should_execute_successfully() {
        val featureValue = FeatureValue.builder()
                .featureValue("feature test value")
                .featureName("feature test name")
                .featureKey("restaurant id")
                .featureValue("7871")
                .majorVersion(1)
                .minorVersion(2)
                .build();
        featureValueDao.createOrUpdate(featureValue);

        val featureValueAsOption = featureValueDao.selectAsync(featureValue.toFeatureKey()).get();
        assertTrue(featureValueAsOption.isPresent());
        val existingFeatureValue = featureValueAsOption.get();
        assertThat(existingFeatureValue.getFeatureName()).isEqualTo(featureValue.getFeatureName());
        assertThat(existingFeatureValue.getFeatureValue()).isEqualTo(featureValue.getFeatureValue());
        assertThat(existingFeatureValue.getMajorVersion()).isEqualTo(featureValue.getMajorVersion());
        assertThat(existingFeatureValue.getMinorVersion()).isEqualTo(featureValue.getMinorVersion());
        assertThat(existingFeatureValue.toFeatureKey()).isEqualTo(featureValue.toFeatureKey());
    }

    @Test
    void createOrUpdateMany_should_create_if_not_exist() {
        val featureValue = createFeature();

        assertThat(featureValueDao.selectAsync(featureValue.toFeatureKey()).get().isEmpty()).isTrue();
        featureValueDao.createOrUpdate(featureValue);
        assertThat(featureValueDao.selectAsync(featureValue.toFeatureKey()).get().isEmpty()).isFalse();

        val featureValueAsOption = featureValueDao.selectAsync(featureValue.toFeatureKey()).get();
        assertThat(featureValueAsOption.isEmpty()).isFalse();
        assertThat(featureValueAsOption.get().getFeatureValue()).isEqualTo("7871");
    }

    @Test
    void createOrUpdateMany_should_update_existing_featureValue() {
        val featureValue = createFeature();

        assertThat(featureValueDao.selectAsync(featureValue.toFeatureKey()).get().isEmpty()).isTrue();
        featureValueDao.createOrUpdate(featureValue);
        assertThat(featureValueDao.selectAsync(featureValue.toFeatureKey()).get().isEmpty()).isFalse();

        featureValue.setFeatureValue("updated feature value");

        featureValueDao.createOrUpdate(featureValue);

        val featureValueAsOption = featureValueDao.selectAsync(featureValue.toFeatureKey()).get();
        assertThat(featureValueAsOption.isEmpty()).isFalse();
        val existingFeatureValue = featureValueAsOption.get();
        assertThat(existingFeatureValue.getFeatureValue()).isEqualTo("updated feature value");
    }

    private FeatureValue createFeature() {
        return FeatureValue.builder()
                .featureName("feature test name")
                .featureKey("restaurant id")
                .featureValue("7871")
                .majorVersion(1)
                .minorVersion(2)
                .build();
    }

    @Test
    void createOrUpdateMany_should_execute_successfully() {
        val featureValues = createFeatures();

        featureValueDao.createOrUpdateMany(featureValues.asJava());

        val selectedFeatures = selectFeaturesInParallel(featureValues);

        assertThat(selectedFeatures).isNotEmpty();
        assertThat(selectedFeatures).containsExactlyInAnyOrder(featureValues.toJavaArray(FeatureValue[]::new));
    }

    @Test
    void delete_should_execute_successfully() {
        val featureValue = createFeature();
        featureValueDao.createOrUpdate(featureValue);
        assertThat(featureValueDao.selectAsync(featureValue.toFeatureKey()).get().isPresent()).isTrue();

        featureValueDao.delete(featureValue.toFeatureKey());

        assertThat(featureValueDao.selectAsync(featureValue.toFeatureKey()).get().isPresent()).isFalse();
    }

    private List<FeatureValue> selectFeaturesInParallel(List<FeatureValue> featureValues) {
        return List.ofAll(featureValueDao.selectAsync(featureValues.get(0).toFeatureKey())
                .plus(featureValueDao.selectAsync(featureValues.get(1).toFeatureKey())).getList());
    }

    private Stream<FeatureValue> waitForCompletion(java.util.List<CompletableFuture<Option<FeatureValue>>> completableFutures) {
        return completableFutures
                .stream()
                .map(CompletableFuture::join)
                .filter(Option::isDefined)
                .map(Option::get);
    }

    private List<FeatureValue> createFeatures() {
        val firstFeatureValue = FeatureValue.builder()
                .featureValue("feature test value 1")
                .featureName("feature test name 1")
                .featureKey("restaurant id 1")
                .featureValue("7871")
                .majorVersion(1)
                .minorVersion(2)
                .build();
        val secondFeatureValue = FeatureValue.builder()
                .featureValue("feature test value 2")
                .featureName("feature test name 2")
                .featureKey("restaurant id 2")
                .featureValue("7872")
                .majorVersion(3)
                .minorVersion(4)
                .build();
        return List.of(firstFeatureValue, secondFeatureValue);
    }

    @AfterEach
    void afterEach() {
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }


}

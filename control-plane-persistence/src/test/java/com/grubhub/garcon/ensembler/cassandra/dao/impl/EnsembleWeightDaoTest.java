package com.grubhub.garcon.ensembler.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.ensembler.cassandra.dao.EnsembleWeightDao;
import com.grubhub.garcon.ensembler.cassandra.models.EnsembleWeight;
import io.vavr.control.Option;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static com.grubhub.garcon.ensembler.cassandra.dao.factory.EnsembleWeightFactory.ensembleWeight1;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


public class EnsembleWeightDaoTest extends DaoTestBase {
    private EnsembleWeightDao ensembleWeightDao;

    @BeforeEach
    void beforeEach() {
        ensembleWeightDao = new EnsembleWeightDaoImpl(casserole);
    }

    @Test
    public void insert_should_save_to_cassandra() {
        EnsembleWeight ensembleWeight = ensembleWeight1();

        ensembleWeightDao.insert(ensembleWeight);

        Option<EnsembleWeight> actualEnsembleWeightAsOptional = ensembleWeightDao.select(
                ensembleWeight.getEnsembleName(),
                ensembleWeight.getEnsembleWeight()
        );
        assertTrue(actualEnsembleWeightAsOptional.isDefined());
        actualEnsembleWeightAsOptional.peek(actualEnsembleWeight -> assertEquals(ensembleWeight, actualEnsembleWeight));
    }
}

package com.grubhub.garcon.ensembler.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.ensembler.cassandra.dao.ModelTestInvocationStatusDao;
import com.grubhub.garcon.ensembler.cassandra.models.ModelTestInvocationStatus;
import io.vavr.collection.List;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


class ModelTestInvocationStatusDaoTest extends DaoTestBase {

    private ModelTestInvocationStatusDao modelTestInvocationStatusDao;

    @BeforeEach
    void beforeEach() {
        modelTestInvocationStatusDao = new ModelTestInvocationStatusDaoImpl(casserole);
    }

    @Test
    void select() {
    }

    @Test
    void insert_shouldSaveToDatabase() {
        String region = "eu_west_1";
        ModelTestInvocationStatus invocationStatus = ModelTestInvocationStatus.builder()
                .modelName("search_m1")
                .status("SUCCESS")
                .region(region)
                .build();
        modelTestInvocationStatusDao.insert(invocationStatus);
        Option<ModelTestInvocationStatus> invocationStatusFromDb = modelTestInvocationStatusDao.select(invocationStatus.getModelName(), region);
        assertTrue(invocationStatusFromDb.isDefined());
        invocationStatusFromDb.peek(status -> assertEquals(invocationStatus, status));
    }

    @Test
    void selectMany_shouldReturnExpected(){
        String region = "eu_west_";
        Stream.range(0, 10).map(i -> ModelTestInvocationStatus.builder()
                .modelName("search_m1")
                .status("SUCCESS")
                .region(region + i)
                .build())
                .forEach(modelTestInvocationStatusDao::insert);

        List<ModelTestInvocationStatus> statuses = modelTestInvocationStatusDao.fetchTestInvocationStatus("search_m1");

        assertThat(statuses).hasSize(10);
        assertThat(statuses.map(ModelTestInvocationStatus::getRegion)).containsAll(Stream.range(0, 10).map(i -> region + i));
    }

    @Test
    void deleteMany() {
        String region = "eu_west_1";
        Stream.range(0, 10).map(i -> ModelTestInvocationStatus.builder()
                        .modelName("search_m1")
                        .status("SUCCESS")
                        .region(region)
                        .build())
                .forEach(modelTestInvocationStatusDao::insert);

        modelTestInvocationStatusDao.deleteMany("search_m1");

        Option<ModelTestInvocationStatus> model = modelTestInvocationStatusDao.select("search_m1", region);
        assertThat(model.isDefined()).isFalse();

    }

    @AfterEach
    void tearDown() {
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }
}

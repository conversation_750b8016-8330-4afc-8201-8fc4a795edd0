package com.grubhub.garcon.ensembler.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.ensembler.cassandra.dao.EnsembleFunctionDao;
import com.grubhub.garcon.ensembler.cassandra.models.EnsembleFunction;
import io.vavr.control.Option;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static com.grubhub.garcon.ensembler.cassandra.dao.factory.EnsembleFunctionFactory.ensembleFunction1;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class EnsembleFunctionDaoTest extends DaoTestBase {
    private EnsembleFunctionDao ensembleFunctionDao;

    @BeforeEach
    void beforeEach() {
        ensembleFunctionDao = new EnsembleFunctionDaoImpl(casserole);
    }

    @Test
    public void insert_should_save_tp_cassandra() {
        EnsembleFunction ensembleFunction = ensembleFunction1();

        ensembleFunctionDao.insert(ensembleFunction);

        Option<EnsembleFunction> actualEnsembleFunctionAsOptional = ensembleFunctionDao.select(
                ensembleFunction.getEnsembleName(),
                ensembleFunction.getEnsembleFunction()
        );
        assertTrue(actualEnsembleFunctionAsOptional.isDefined());
        actualEnsembleFunctionAsOptional
                .peek(actualEnsembleFunction -> assertEquals(ensembleFunction, actualEnsembleFunction));
    }
}

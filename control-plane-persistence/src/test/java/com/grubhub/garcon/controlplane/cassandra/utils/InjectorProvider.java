package com.grubhub.garcon.controlplane.cassandra.utils;

import com.google.inject.Guice;
import com.google.inject.Injector;
import com.grubhub.garcon.guice.PersistenceTestModule;
import lombok.experimental.UtilityClass;

@UtilityClass
public class InjectorProvider {

    public static final Injector INJECTOR;

    static {
        INJECTOR = Guice.createInjector(new PersistenceTestModule());
    }

}

package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByMarketDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByMarket;
import lombok.val;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static org.assertj.core.api.Assertions.assertThat;


class FlowByMarketDaoImplTest extends DaoTestBase {

    private FlowByMarketDao flowByMarketDao;

    @BeforeEach
    void beforeEach() {
        flowByMarketDao = new FlowByMarketDaoImpl(casserole);
    }

    @AfterEach
    void afterEach() {
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }

    @Test
    void insertMany_should_execute_successfully() {

        UUID flowId = UUID.randomUUID();
        FlowByMarket firstFlowByMarket = FlowByMarket.builder()
                .marketName("market1")
                .flowSet("flowSet1")
                .flowId(flowId)
                .build();
        FlowByMarket secondFlowByMarket = FlowByMarket.builder()
                .marketName("market1")
                .flowSet("flowSet1")
                .flowId(flowId)
                .build();
        flowByMarketDao.insertMany(List.of(firstFlowByMarket, secondFlowByMarket));

        val savedFirstFlowByMarket = flowByMarketDao.select(firstFlowByMarket.getFlowSet(), firstFlowByMarket.getMarketName(), flowId);
        assertThat(savedFirstFlowByMarket.isPresent()).isTrue();
        val savedSecondFlowByMarket = flowByMarketDao.select(secondFlowByMarket.getFlowSet(), secondFlowByMarket.getMarketName(), flowId);
        assertThat(savedSecondFlowByMarket.isPresent()).isTrue();
    }

    @Test
    void getFlowsByMarkets_should_execute_successfully() {
        UUID flowId = UUID.randomUUID();
        FlowByMarket firstFlowByMarket = FlowByMarket.builder()
                .marketName("market1")
                .flowSet("flowSet")
                .flowId(flowId)
                .build();
        FlowByMarket secondFlowByMarket = FlowByMarket.builder()
                .marketName("market2")
                .flowSet("flowSet")
                .flowId(flowId)
                .build();
        flowByMarketDao.insertMany(List.of(firstFlowByMarket, secondFlowByMarket));

        List<FlowByMarket> flowsByMarkets = flowByMarketDao.getFlowsByMarkets("flowSet", List.of("market1", "market2"));
        assertThat(flowsByMarkets.size()).isEqualTo(2);
        assertThat(flowsByMarkets.stream().map(FlowByMarket::getMarketName)).containsExactlyInAnyOrder("market1", "market2");
    }


    @Test
    void select_should_return_empty() {
        assertThat(flowByMarketDao.select(RandomStringUtils.randomAlphabetic(3), RandomStringUtils.randomAlphanumeric(4), UUID.randomUUID())
                .isPresent()).isFalse();
    }

    @Test
    void select_should_return_existing_flow_by_market() {
        UUID flowId = UUID.randomUUID();
        FlowByMarket firstFlowByMarket = FlowByMarket.builder()
                .marketName("market1")
                .flowSet("flowSet1")
                .flowId(flowId)
                .build();

        flowByMarketDao.insert(firstFlowByMarket);

        val existingFlowByMarket = flowByMarketDao.select(firstFlowByMarket.getFlowSet(), firstFlowByMarket.getMarketName(), flowId);
        assertThat(existingFlowByMarket.isPresent()).isTrue();

        val aFlowByMarket = existingFlowByMarket.get();
        assertThat(aFlowByMarket.getFlowId()).isEqualTo(flowId);
        assertThat(aFlowByMarket.getMarketName()).isEqualTo(firstFlowByMarket.getMarketName());
        assertThat(aFlowByMarket.getFlowSet()).isEqualTo(firstFlowByMarket.getFlowSet());
    }

    @Test
    void delete_should_remove_flow_by_market() {
        UUID flowId = UUID.randomUUID();
        FlowByMarket firstFlowByMarket = FlowByMarket.builder()
                .marketName("market1")
                .flowSet("flowSet1")
                .flowId(flowId)
                .build();

        flowByMarketDao.insert(firstFlowByMarket);
        assertThat(flowByMarketDao.select(firstFlowByMarket.getFlowSet(), firstFlowByMarket.getMarketName(), flowId).isPresent())
                .isTrue();
        flowByMarketDao.delete(firstFlowByMarket.getFlowSet(), firstFlowByMarket.getMarketName(), flowId);

        assertThat(flowByMarketDao.select(firstFlowByMarket.getFlowSet(), firstFlowByMarket.getMarketName(), flowId).isPresent())
                .isFalse();
    }
}

package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByGeoHashDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import lombok.val;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static com.grubhub.garcon.controlplane.cassandra.dao.factory.FlowByGeoHashFactory.createDefaultFlowByGeoHash;
import static com.grubhub.garcon.controlplane.cassandra.dao.factory.FlowByGeoHashFactory.flowByGeoHash;
import static com.grubhub.garcon.controlplane.cassandra.utils.DaoUtils.truncateFlowByGeoHashTable;
import static org.assertj.core.api.Assertions.assertThat;

public class FlowByGeoHashDaoTest extends DaoTestBase {

    private FlowByGeoHashDao flowByGeoHashDao;

    @BeforeEach
    void beforeEach() {
        flowByGeoHashDao = new FlowByGeoHashDaoImpl(casserole);
    }

    @AfterEach
    void afterEach() {
        truncateFlowByGeoHashTable(casserole);
    }

    @Test
    public void insert_should_save_to_cassandra() {
        val defaultFlowId = UUID.randomUUID();
        val flowByGeoHash = createDefaultFlowByGeoHash(defaultFlowId);
        flowByGeoHashDao.insert(flowByGeoHash);
        val flowByGeoHashAsOptional = flowByGeoHashDao.select(flowByGeoHash.getFlowSet(), flowByGeoHash.getGeohash());
        assertThat(flowByGeoHashAsOptional.isPresent()).isEqualTo(true);
        flowByGeoHashAsOptional.ifPresent(actualFlowByGeoHash -> assertThat(actualFlowByGeoHash).isEqualTo(flowByGeoHash));
    }

    @Test
    public void update_should_execute_successfully() {
        val flowByGeoHash = flowByGeoHash("DINNER", "dr72j");
        flowByGeoHashDao.insert(flowByGeoHash);
        flowByGeoHash.setMarketName("chicago");
        flowByGeoHashDao.update(flowByGeoHash);
        val flowByGeoHashAsOptional = flowByGeoHashDao.select(flowByGeoHash.getFlowSet(), flowByGeoHash.getGeohash());
        assertThat(flowByGeoHashAsOptional.isPresent()).isEqualTo(true);
        flowByGeoHashAsOptional.ifPresent(actualFlowByGeoHash -> assertThat(actualFlowByGeoHash.getMarketName()).isEqualTo("chicago"));
    }

    @Test
    public void delete_should_execute_successfully() {
        val flowByGeoHash = flowByGeoHash("SEARCH", "dr72jk");
        flowByGeoHashDao.insert(flowByGeoHash);
        flowByGeoHashDao.delete(flowByGeoHash.getFlowSet(), flowByGeoHash.getGeohash());
        val flowByGeoHashAsOptional = flowByGeoHashDao.select(flowByGeoHash.getFlowSet(), flowByGeoHash.getGeohash());
        assertThat(flowByGeoHashAsOptional.isPresent()).isEqualTo(false);
    }

    @Test
    public void delete_in_bulk_should_execute_successfully() {
        List<FlowByGeoHash> flowByGeoHashes = List.of(flowByGeoHash("SEARCH", "dr72jk"),
                flowByGeoHash("SEARCH", "dr72ja"),
                flowByGeoHash("SEARCH", "dr72jb"),
                flowByGeoHash("SEARCH", "dr72jc"),
                flowByGeoHash("SEARCH", "dr72jd")
        );
        flowByGeoHashDao.insertMany(flowByGeoHashes);

        assertThat(flowByGeoHashDao.select("SEARCH", "dr72jk").isPresent()).isTrue();
        assertThat(flowByGeoHashDao.select("SEARCH", "dr72ja").isPresent()).isTrue();
        assertThat(flowByGeoHashDao.select("SEARCH", "dr72jb").isPresent()).isTrue();
        assertThat(flowByGeoHashDao.select("SEARCH", "dr72jc").isPresent()).isTrue();
        assertThat(flowByGeoHashDao.select("SEARCH", "dr72jd").isPresent()).isTrue();

        flowByGeoHashDao.delete("SEARCH", flowByGeoHashes.stream().map(FlowByGeoHash::getGeohash).toList());

        assertThat(flowByGeoHashDao.select("SEARCH", "dr72jk").isPresent()).isFalse();
        assertThat(flowByGeoHashDao.select("SEARCH", "dr72ja").isPresent()).isFalse();
        assertThat(flowByGeoHashDao.select("SEARCH", "dr72jb").isPresent()).isFalse();
        assertThat(flowByGeoHashDao.select("SEARCH", "dr72jc").isPresent()).isFalse();
        assertThat(flowByGeoHashDao.select("SEARCH", "dr72jd").isPresent()).isFalse();
    }
}

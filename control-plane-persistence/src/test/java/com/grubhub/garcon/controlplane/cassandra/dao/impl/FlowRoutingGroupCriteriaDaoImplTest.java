package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowRoutingGroupCriteriaDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupCriteria;
import io.vavr.collection.HashSet;
import lombok.val;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static org.assertj.core.api.Assertions.assertThat;

class FlowRoutingGroupCriteriaDaoImplTest extends DaoTestBase {

    private FlowRoutingGroupCriteriaDao flowRoutingGroupCriteriaDao;

    @BeforeEach
    void beforeEach() {
        flowRoutingGroupCriteriaDao = new FlowRoutingGroupCriteriaDaoImpl(casserole);
    }

    @Test
    public void insert_should_save_to_cassandra() {
        val defaultFlowId = UUID.randomUUID();
        val flowRoutingGroupCriteria = FlowRoutingGroupCriteria.builder()
                .routingGroupCriteria("default")
                .flowId(defaultFlowId)
                .routingGroups(HashSet.of("matching_application").toJavaSet())
                .build();
        flowRoutingGroupCriteriaDao.insert(flowRoutingGroupCriteria);
        val flowRoutingGroupCriteriaAsOptional = flowRoutingGroupCriteriaDao
                .select(defaultFlowId, flowRoutingGroupCriteria.getRoutingGroupCriteria());
        assertThat(flowRoutingGroupCriteriaAsOptional.isPresent()).isEqualTo(true);
        assertThat(flowRoutingGroupCriteriaAsOptional.get()).isEqualTo(flowRoutingGroupCriteria);
    }

    @AfterEach
    void afterEach() {
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }
}

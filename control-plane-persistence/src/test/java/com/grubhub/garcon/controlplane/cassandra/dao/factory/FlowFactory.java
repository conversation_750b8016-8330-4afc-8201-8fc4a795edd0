package com.grubhub.garcon.controlplane.cassandra.dao.factory;

import com.grubhub.garcon.controlplane.cassandra.models.Flow;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import com.grubhub.roux.api.datetime.JavaDateTimeHelperBuilder;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import lombok.experimental.UtilityClass;
import lombok.val;

import java.time.Instant;
import java.util.Collections;
import java.util.UUID;

@UtilityClass
public class FlowFactory {
    private final Instant testTime = Instant.parse("2022-05-10T10:00:00Z");
    private final JavaDateTimeHelper dateTimeHelper = JavaDateTimeHelperBuilder.buildMockWithSupplier(() -> testTime);
    private static final String BASIC_FLOW_ID = "********-0000-0000-0000-************";

    public static Flow basicFlow() {
        return Flow.builder()
                .flowId(UUID.fromString(BASIC_FLOW_ID))
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(HashSet.of("manhattan", "san_francisco").toJavaSet())
                .matchingDinerTypes(HashSet.<String>empty().toJavaSet())
                .updatedUser("<EMAIL>")
                .updatedTimestamp(dateTimeHelper.instant())
                .matchingApplications(Collections.emptySet())
                .matchingApplicationVersions(Collections.emptySet())
                .matchingOrderTypes(Collections.emptySet())
                .matchingMealtime(Collections.emptySet())
                .matchingQueryTypes(Collections.emptySet())
                .matchingQueryTokens(Collections.emptyList())
                .routingGroupsCriteria(Collections.emptySet())
                .matchingQueryKeywords(Collections.emptySet())
                .matchingModelClassifications(Collections.emptySet())
                .matchingRequestTags(Collections.emptySet())
                .matchingUserDomains(Collections.emptySet())
                .matchingUserEmails(Collections.emptySet())
                .routingGroupsSeedRotation(Collections.emptyList())
                .matchingClientEntityIds(Collections.emptySet())
                .matchingBrands(Collections.emptySet())
                .build();
    }

    public static Flow createFlowWithId(UUID flowId) {
        return Flow.builder()
                .flowId(flowId)
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(HashSet.of("manhattan", "san_francisco").toJavaSet())
                .updatedUser("<EMAIL>")
                .updatedTimestamp(dateTimeHelper.instant())
                .build();
    }

    public static List<FlowRoutingGroupV2> basicFlowRoutingGroup() {
        val firstFlowRoutingGroup = FlowRoutingGroupV2
                .builder()
                .flowId(UUID.fromString(BASIC_FLOW_ID))
                .groupName("group_80")
                .routingPercentage(0.8f)
                .variation("distance")
                .ensembleName("search_m1_m2")
                .ensembleWeight("default")
                .groupProperties(Collections.emptyMap())
                .build();
        val secondFlowRoutingGroup = FlowRoutingGroupV2
                .builder()
                .flowId(UUID.fromString(BASIC_FLOW_ID))
                .groupName("group_20")
                .routingPercentage(0.8f)
                .variation("distance")
                .ensembleName("search_m1_m2")
                .ensembleWeight("conversion_only")
                .groupProperties(Collections.emptyMap())
                .build();

        return List.of(firstFlowRoutingGroup, secondFlowRoutingGroup);
    }
}

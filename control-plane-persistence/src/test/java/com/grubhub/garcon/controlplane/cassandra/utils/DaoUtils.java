package com.grubhub.garcon.controlplane.cassandra.utils;

import com.datastax.oss.driver.api.querybuilder.QueryBuilder;
import com.grubhub.roux.casserole.api.Casserole;
import lombok.experimental.UtilityClass;

@UtilityClass
public class DaoUtils {

    public static void truncateFlowByGeoHashTable(Casserole casserole) {
        casserole.execute(
                QueryBuilder.truncate("ddml_control_plane", "flow_geohashes").build());
    }


    public static void truncateModelFeatureTable(Casserole casserole) {
        casserole.execute(
                QueryBuilder.truncate("ddml_control_plane", "model_features").build());
    }

}

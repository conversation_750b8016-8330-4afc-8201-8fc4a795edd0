package com.grubhub.garcon.controlplane.cassandra.dao.factory;

import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import lombok.NonNull;
import lombok.experimental.UtilityClass;

import java.util.UUID;

@UtilityClass
public class FlowByGeoHashFactory {

    public static FlowByGeoHash createDefaultFlowByGeoHash(UUID flowId) {
        return FlowByGeoHash.builder()
                .flowId(flowId)
                .flowSet("SEARCH")
                .geohash("dr5ru")
                .marketName("los angeles")
                .build();
    }

    public static FlowByGeoHash flowByGeoHash(@NonNull String flowSet, @NonNull String geoHash) {
        return FlowByGeoHash.builder()
                .flowId(UUID.randomUUID())
                .flowSet(flowSet)
                .geohash(geoHash)
                .marketName("manhattan")
                .build();
    }
}

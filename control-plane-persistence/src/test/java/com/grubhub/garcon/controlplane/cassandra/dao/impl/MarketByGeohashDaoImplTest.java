
package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.controlplane.cassandra.dao.MarketByGeohashDao;
import com.grubhub.garcon.controlplane.cassandra.models.MarketByGeohash;
import lombok.val;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.IntStream;

import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static org.assertj.core.api.Assertions.assertThat;

class MarketByGeohashDaoImplTest extends DaoTestBase {

    private MarketByGeohashDao marketByGeohashDao;

    @BeforeEach
    void beforeEach() {
        marketByGeohashDao = new MarketByGeohashDaoImpl(casserole);
    }

    @Test
    void insert_should_insert_new_markets_by_geohash_if_they_do_not_exists() {
        int geohashPrecision = 6;
        val marketsByGeohashes = IntStream.rangeClosed(0, 3)
                .mapToObj(elem -> MarketByGeohash.builder()
                        .geohash("geohash_" + elem)
                        .geohashPrecision(geohashPrecision)
                        .marketName("market_" + elem)
                        .build())
                .toList();
        marketByGeohashDao.upsert(marketsByGeohashes);

        val fetchedMarketsByGeohashes = marketByGeohashDao.getMarketsByGeohashes(6, marketsByGeohashes.stream()
                .map(MarketByGeohash::getGeohash).toList());
        assertThat(fetchedMarketsByGeohashes).hasSize(4);
        assertThat(fetchedMarketsByGeohashes.stream().map(MarketByGeohash::getGeohash).toList()).containsAnyOf(
                IntStream.rangeClosed(0, 3)
                        .mapToObj(index -> "geohash_" + index)
                        .map(String::toString)
                        .toList()
                        .toArray(String[]::new)
        );
    }

    @Test
    void insert_should_update_markets_by_geohash_if_they_exists() {
        int geohashPrecision = 6;
        val marketsByGeohashes = IntStream.rangeClosed(0, 3)
                .mapToObj(elem -> MarketByGeohash.builder()
                        .geohash("geohash_" + elem)
                        .geohashPrecision(geohashPrecision)
                        .marketName("market_" + elem)
                        .build())
                .toList();
        marketByGeohashDao.upsert(marketsByGeohashes);

        int randomIndex = getRandomNumberInRange(0, 3);
        val marketToBeUpdated = marketsByGeohashes.get(randomIndex);
        marketToBeUpdated.setMarketName(marketToBeUpdated.getMarketName() + "_updated");

        marketByGeohashDao.upsert(marketsByGeohashes);
        List<MarketByGeohash> retrievedMarketsByGeohashes = marketByGeohashDao.getMarketsByGeohashes(
                geohashPrecision,
                marketsByGeohashes.stream().map(MarketByGeohash::getGeohash).toList()
        );
        assertThat(retrievedMarketsByGeohashes).isNotEmpty();
        assertThat(retrievedMarketsByGeohashes.stream()
                .anyMatch(m -> m.getMarketName().equals(marketToBeUpdated.getMarketName()))).isTrue();
    }

    private int getRandomNumberInRange(int min, int max) {
        return ThreadLocalRandom.current().nextInt((max - min) + 1) + min;
    }

    @Test
    void deleteMarketsByGeohashes() {
        int geohashPrecision = 6;
        val marketsByGeohashes = IntStream.rangeClosed(0, 3)
                .mapToObj(elem -> MarketByGeohash.builder()
                        .geohash("geohash_" + elem)
                        .geohashPrecision(geohashPrecision)
                        .marketName("market_" + elem)
                        .build())
                .toList();
        marketByGeohashDao.upsert(marketsByGeohashes);

        assertThat(marketByGeohashDao.getMarketsByGeohashes(geohashPrecision, marketsByGeohashes.stream().map(MarketByGeohash::getGeohash).toList()))
                .isNotEmpty();

        marketByGeohashDao.deleteMarketsByGeohashes(geohashPrecision, marketsByGeohashes.stream().map(MarketByGeohash::getGeohash).toList());

        assertThat(marketByGeohashDao.getMarketsByGeohashes(geohashPrecision, marketsByGeohashes.stream().map(MarketByGeohash::getGeohash).toList()))
                .isEmpty();
    }

    @AfterEach
    void afterEach() {
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }
}

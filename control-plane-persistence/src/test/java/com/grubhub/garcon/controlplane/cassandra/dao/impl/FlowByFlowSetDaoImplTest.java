package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.google.common.collect.ImmutableList;
import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByFlowSetDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByFlowSet;
import lombok.val;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static org.junit.jupiter.api.Assertions.*;

public class FlowByFlowSetDaoImplTest extends DaoTestBase {

    private FlowByFlowSetDao flowByFlowSetDao;

    @BeforeEach
    void beforeEach() {
        flowByFlowSetDao = new FlowByFlowSetDaoImpl(casserole);
    }

    @AfterEach
    void afterEach() {
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }

    @Test
    public void insert_should_save_to_cassandra() {
        val flowByFlowSet = createFlowByFlowSet("testFlowSet", true);
        flowByFlowSetDao.insert(flowByFlowSet);

        val savedFlowByFlowSet = flowByFlowSetDao.get(flowByFlowSet.getFlowSet(), flowByFlowSet.getFlowId());
        assertTrue(savedFlowByFlowSet.isPresent());
        assertEquals(flowByFlowSet, savedFlowByFlowSet.get());
    }

    @Test
    public void update_should_change_flow_by_flow_set() {
        val flowByFlowSet = createFlowByFlowSet("testFlowSet", true);
        flowByFlowSetDao.insert(flowByFlowSet);

        val savedFlowByFlowSet = flowByFlowSetDao.get(flowByFlowSet.getFlowSet(), flowByFlowSet.getFlowId());
        assertTrue(savedFlowByFlowSet.isPresent());
        assertTrue(savedFlowByFlowSet.get().isEnabled());

        flowByFlowSet.setEnabled(false);
        flowByFlowSetDao.update(flowByFlowSet);

        val updatedFlowByFlowSet = flowByFlowSetDao.get(flowByFlowSet.getFlowSet(), flowByFlowSet.getFlowId());
        assertTrue(updatedFlowByFlowSet.isPresent());
        assertFalse(updatedFlowByFlowSet.get().isEnabled());
    }

    @Test
    public void delete_should_remove_flow_by_flow_set() {
        val flowByFlowSet = createFlowByFlowSet("testFlowSet", true);
        flowByFlowSetDao.insert(flowByFlowSet);
        flowByFlowSetDao.delete(flowByFlowSet.getFlowSet(), flowByFlowSet.getFlowId());

        val savedFlowByFlowSet = flowByFlowSetDao.get(flowByFlowSet.getFlowSet(), flowByFlowSet.getFlowId());
        assertTrue(savedFlowByFlowSet.isEmpty());
    }

    @Test
    public void select_should_return_all_flows_in_flow_set() {
        val firstFlowByFlowSet = createFlowByFlowSet("flowSetOne", true);
        val secondFlowByFlowSet = createFlowByFlowSet("flowSetOne", true);
        val thirdFlowByFlowSet = createFlowByFlowSet("flowSetTwo", true);
        flowByFlowSetDao.insert(firstFlowByFlowSet);
        flowByFlowSetDao.insert(secondFlowByFlowSet);
        flowByFlowSetDao.insert(thirdFlowByFlowSet);
        val flowSetOneList = flowByFlowSetDao.select("flowSetOne");
        val flowSetTwoList = flowByFlowSetDao.select("flowSetTwo");

        val expectedFlowSetOneList = ImmutableList.of(firstFlowByFlowSet, secondFlowByFlowSet);
        assertEquals(2, flowSetOneList.size());
        assertTrue(flowSetOneList.containsAll(expectedFlowSetOneList));
        assertEquals(1, flowSetTwoList.size());
        assertTrue(flowSetTwoList.contains(thirdFlowByFlowSet));
    }

    private FlowByFlowSet createFlowByFlowSet(String flowSet, boolean enabled) {
        return FlowByFlowSet.builder()
                .flowSet(flowSet)
                .flowId(UUID.randomUUID())
                .enabled(enabled)
                .build();
    }
}

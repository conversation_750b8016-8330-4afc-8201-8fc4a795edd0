package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowDao;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowRoutingGroupDao;
import io.vavr.collection.HashSet;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static com.grubhub.garcon.controlplane.cassandra.dao.factory.FlowFactory.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


class FlowDaoTest extends DaoTestBase {

    private FlowDao flowDao;
    private FlowRoutingGroupDao flowRoutingGroupDao;
    private UUID flowId;
  
    @BeforeEach
    void beforeAll() {
        flowDao = new FlowDaoImpl(casserole);
        flowRoutingGroupDao = new FlowRoutingGroupDaoImpl(casserole);
        flowId = UUID.randomUUID();
    }

    @Test
    public void insert_should_save_to_cassandra() {
        val flow = basicFlow();
        flowDao.insert(flow);
        basicFlowRoutingGroup().forEach(flowRoutingGroupDao::insert);

        val flowAsOptional = flowDao.select(flow.getFlowId());
        assertTrue(flowAsOptional.isPresent());
        assertEquals(flow, flowAsOptional.get());

        val flowRoutingGroups = flowRoutingGroupDao.selectAllAsync(flow.getFlowId()).getList();
        assertEquals(2, flowRoutingGroups.size());
        assertThat(basicFlowRoutingGroup()).hasSameElementsAs(flowRoutingGroups);
    }

    @Test
    public void update_should_execute_successfully() {
        val flow = createFlowWithId(flowId);
        flowDao.insert(flow);
        val updatedLocationMarkets = HashSet.of("chicago").toJavaSet();
        flow.setLocationMarkets(updatedLocationMarkets);
        flowDao.update(flow);
        val flowAsOptional = flowDao.select(flowId);
        assertThat(flowAsOptional.isPresent()).isEqualTo(true);
        assertThat(flowAsOptional.get().getLocationMarkets()).hasSameElementsAs(updatedLocationMarkets);
    }

    @Test
    public void delete_should_execute_successfully() {
        val flow = createFlowWithId(flowId);
        flowDao.insert(flow);
        flowDao.delete(flowId);
        val flowAsOptional = flowDao.select(flowId);
        assertThat(flowAsOptional.isPresent()).isEqualTo(false);
    }
}

package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowRoutingGroupDao;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static com.grubhub.garcon.controlplane.cassandra.dao.factory.FlowRoutingGroupFactory.flowRoutingGroup;
import static com.grubhub.garcon.controlplane.cassandra.dao.factory.FlowRoutingGroupFactory.flowRoutingGroupWithId;
import static org.assertj.core.api.Assertions.assertThat;

class FlowRoutingGroupDaoTest extends DaoTestBase {

    private FlowRoutingGroupDao flowRoutingGroupDao;
    private UUID flowId;

    @BeforeEach
    void beforeEach() {
        flowRoutingGroupDao = new FlowRoutingGroupDaoImpl(casserole);
        flowId = UUID.randomUUID();
    }

    @Test
    public void insert_should_save_to_cassandra() {
        val flowRoutingGroup = flowRoutingGroup();
        flowRoutingGroupDao.insert(flowRoutingGroup);

        val flowRoutingGroupAsOptional = flowRoutingGroupDao.select(
                flowRoutingGroup.getFlowId(),
                flowRoutingGroup.getRoutingGroupCriteria(),
                flowRoutingGroup.getGroupName()
        );

        assertThat(flowRoutingGroupAsOptional.isPresent()).isEqualTo(true);
        assertThat(flowRoutingGroupAsOptional.get().getFlowId()).isEqualTo(flowRoutingGroup.getFlowId());
        assertThat(flowRoutingGroupAsOptional.get()).isEqualTo(flowRoutingGroup);
    }

    @Test
    public void delete_should_execute_successfully() {
        val flowRoutingGroup = flowRoutingGroupWithId(flowId);
        flowRoutingGroupDao.insert(flowRoutingGroup);
        flowRoutingGroupDao.delete(flowId, flowRoutingGroup.getRoutingGroupCriteria(), flowRoutingGroup.getGroupName());
        val flowRoutingGroupAsOptional = flowRoutingGroupDao.select(flowId, flowRoutingGroup.getRoutingGroupCriteria(), flowRoutingGroup.getGroupName());
        assertThat(flowRoutingGroupAsOptional.isPresent()).isEqualTo(false);
    }

    @Test
    public void update_should_execute_successfully() {
        val flowRoutingGroup = flowRoutingGroupWithId(flowId);
        flowRoutingGroupDao.insert(flowRoutingGroup);
        flowRoutingGroup.setEnsembleName("search_m4_m5");
        flowRoutingGroupDao.updateV2(flowRoutingGroup);
        val flowRoutingGroupAsOptional = flowRoutingGroupDao.select(flowId, flowRoutingGroup.getRoutingGroupCriteria(), flowRoutingGroup.getGroupName());
        assertThat(flowRoutingGroupAsOptional.isPresent()).isEqualTo(true);
        assertThat(flowRoutingGroupAsOptional.get().getEnsembleName()).isEqualTo("search_m4_m5");
    }
}

package com.grubhub.garcon.controlplane.cassandra.dao.factory;

import com.grubhub.garcon.controlplane.cassandra.models.Market;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import com.grubhub.roux.api.datetime.JavaDateTimeHelperBuilder;
import lombok.experimental.UtilityClass;

import java.time.temporal.ChronoUnit;

@UtilityClass
public class MarketFactory {

    private static final JavaDateTimeHelper JAVA_DATE_TIME_HELPER = JavaDateTimeHelperBuilder.buildReal();

    public static Market newYorkMarket() {
        return Market.builder()
                .city("New York")
                .marketName("manhattan")
                .state("NY")
                .updatedUser("<EMAIL>")
                .updatedTimestamp(JAVA_DATE_TIME_HELPER.instant().truncatedTo(ChronoUnit.MILLIS))
                .geoPolygon("{" +
                        "\"type\":\"Polygon\"," +
                        "\"coordinates\":" +
                        "   [[[-73.93799,40.87848]," +
                        "   [-74.040985,40.700684]," +
                        "   [-73.97026,40.69756]," +
                        "   [-73.9246,40.800816]," +
                        "   [-73.93799,40.87848]]]}")
                .build();
    }

    public static Market chicagoMarket() {
        return Market.builder()
                .city("Chicago")
                .marketName("chicago")
                .state("IL")
                .updatedUser("<EMAIL>")
                .updatedTimestamp(JAVA_DATE_TIME_HELPER.instant().truncatedTo(ChronoUnit.MILLIS))
                .geoPolygon("{" +
                        "\"type\":\"Polygon\"," +
                        "\"coordinates\":" +
                        "   [[" +
                        "       [-122.57446,37.796764]," +
                        "       [-122.54562,37.60553]," +
                        "       [-122.283325,37.60988]," +
                        "       [-122.39456,37.839073]," +
                        "       [-122.57446,37.796764]" +
                        "   ]]" +
                        "}")
                .build();
    }

    public static Market sanFranciscoMarket() {
        return Market.builder()
                .city("Chicago")
                .marketName("San Francisco")
                .state("CA")
                .updatedUser("<EMAIL>")
                .updatedTimestamp(JAVA_DATE_TIME_HELPER.instant().truncatedTo(ChronoUnit.MILLIS))
                .geoPolygon("{" +
                        "\"type\":\"Polygon\"," +
                        "\"coordinates\":" +
                        "   [[" +
                        "       [-87.798615,41.95847]," +
                        "       [-87.757416,41.76312]," +
                        "       [-87.561035,41.77746]," +
                        "       [-87.64618,41.964596]," +
                        "       [-87.798615,41.95847]" +
                        "   ]]" +
                        "}")
                .build();
    }
}

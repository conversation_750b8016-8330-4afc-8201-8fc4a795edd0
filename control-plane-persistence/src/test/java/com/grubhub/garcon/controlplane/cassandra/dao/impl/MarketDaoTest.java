package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.grubhub.garcon.DaoTestBase;
import com.grubhub.garcon.controlplane.cassandra.dao.MarketDao;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static com.grubhub.garcon.controlplane.cassandra.dao.factory.MarketFactory.chicagoMarket;
import static com.grubhub.garcon.controlplane.cassandra.dao.factory.MarketFactory.newYorkMarket;
import static com.grubhub.garcon.controlplane.cassandra.dao.factory.MarketFactory.sanFranciscoMarket;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;


class MarketDaoTest extends DaoTestBase {

    private MarketDao marketDao;

    @BeforeEach
    void beforeEach() {
        marketDao = new MarketDaoImpl(casserole);
    }

    @Test
    public void insert_should_save_to_cassandra() {
        val market = newYorkMarket();

        marketDao.insert(market);

        val actualMarketAsOptional = marketDao.select(market.getMarketName());
        assertTrue(actualMarketAsOptional.isPresent());
        val actualMarket = actualMarketAsOptional.get();

        assertEquals(market, actualMarket);
    }

    @Test
    public void update_should_execute_successfully() {
        val market = chicagoMarket();

        marketDao.insert(market);
        market.setUpdatedUser("<EMAIL>");
        marketDao.update(market);
        val actualMarketAsOptional = marketDao.select(market.getMarketName());

        assertTrue(actualMarketAsOptional.isPresent());
        val actualMarket = actualMarketAsOptional.get();
        assertEquals("<EMAIL>", actualMarket.getUpdatedUser());
    }

    @Test
    public void delete_should_execute_successfully() {
        val market = sanFranciscoMarket();

        marketDao.insert(market);
        marketDao.delete(market.getMarketName());

        val marketAsOptional = marketDao.select(market.getMarketName());
        assertFalse(marketAsOptional.isPresent());
    }

}

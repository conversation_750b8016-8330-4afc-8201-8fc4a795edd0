package com.grubhub.garcon.controlplane.cassandra.dao.factory;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import lombok.experimental.UtilityClass;

import java.util.Collections;
import java.util.UUID;

@UtilityClass
public class FlowRoutingGroupFactory {

    public static FlowRoutingGroupV2 flowRoutingGroup() {
        return FlowRoutingGroupV2.builder()
                .flowId(UUID.randomUUID())
                .groupName("group_50")
                .routingPercentage(0.23f)
                .variation("distance")
                .ensembleName("search_m2_m3")
                .ensembleWeight("conversion_only")
                .groupProperties(Collections.emptyMap())
                .build();
    }

    public static FlowRoutingGroupV2 flowRoutingGroupWithId(UUID flowId) {
        return FlowRoutingGroupV2.builder()
                .flowId(flowId)
                .groupName("group_54")
                .routingPercentage(0.232f)
                .variation("distance")
                .ensembleName("search_m1_m2")
                .ensembleWeight("default")
                .build();
    }

}

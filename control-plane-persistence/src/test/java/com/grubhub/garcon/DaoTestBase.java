package com.grubhub.garcon;

import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.test.cassandra.EmbeddedCassandraExt;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.extension.RegisterExtension;

import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.DATA;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.initCasserole;

public class DaoTestBase {

    @RegisterExtension
    public static final EmbeddedCassandraExt EMBEDDED_CASSANDRA = new EmbeddedCassandraExt(DATA);
    protected static Casserole casserole;

    @BeforeAll
    static void beforeClass() {
        casserole = initCasserole(EMBEDDED_CASSANDRA.getPort());
    }

    @AfterAll
    static void afterClass() {
        casserole.shutdown();
    }

}

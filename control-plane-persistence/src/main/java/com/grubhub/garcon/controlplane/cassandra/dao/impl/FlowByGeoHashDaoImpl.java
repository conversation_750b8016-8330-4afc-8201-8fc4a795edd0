package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByGeoHashDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


public class FlowByGeoHashDaoImpl implements FlowByGeoHashDao {

    private final Casserole casserole;

    @Inject
    public FlowByGeoHashDaoImpl(@Named("casserole-v2-ddmlControlPlane") Casserole casserole) {
        this.casserole = casserole;
    }

    @Override
    public void insert(@NonNull FlowByGeoHash flowByGeoHash) {
        casserole.insertSingle(flowByGeoHash);
    }

    @Override
    public void insertMany(@NonNull List<FlowByGeoHash> flowByGeoHashes) {
        casserole.insertMany(flowByGeoHashes);
    }

    @Override
    public Optional<FlowByGeoHash> select(@NonNull String flowSet, @NonNull String geohash) {
        return selectAll(flowSet, geohash).stream().findFirst();
    }

    @Override
    public List<FlowByGeoHash> selectAll(@NonNull String flowSet, @NonNull String geohash) {
        return casserole.selectPartition(FlowByGeoHash.partitionKey(flowSet, geohash));
    }

    @Override
    public StreamOperation<FlowByGeoHash> selectAllAsync(@NonNull String flowSet, @NonNull String geohash) {
        return casserole.select().partition(FlowByGeoHash.partitionKey(flowSet, geohash), FlowByGeoHash.class);
    }

    @Override
    public void update(@NonNull FlowByGeoHash flowByGeoHash) {
        casserole.updateSingle(flowByGeoHash);
    }

    @Override
    public void delete(@NonNull String flowSet, @NonNull String geohash) {
        casserole.deletePartition(FlowByGeoHash.partitionKey(flowSet, geohash));
    }

    @Override
    public void delete(@NonNull String flowSet, @NonNull List<String> geohashes) {
        List<FlowByGeoHash> keys = geohashes.stream().map(geohash -> FlowByGeoHash.partitionKey(flowSet, geohash)).toList();
        casserole.deleteManyPartitions(keys, FlowByGeoHash.class);
    }

    @Override
    public void delete(@NonNull String flowSet, @NonNull UUID flowId, @NonNull List<String> geohashes) {
        List<FlowByGeoHash> keys = geohashes.stream().map(geohash -> FlowByGeoHash.primaryKey(flowSet, geohash, flowId)).toList();
        casserole.deleteMany(keys, FlowByGeoHash.class);
    }
}

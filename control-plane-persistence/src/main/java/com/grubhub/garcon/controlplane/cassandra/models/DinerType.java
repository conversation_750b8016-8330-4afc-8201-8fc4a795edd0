package com.grubhub.garcon.controlplane.cassandra.models;

import io.vavr.collection.List;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.EnumSet;

@RequiredArgsConstructor
public enum DinerType {
    EMPTY_ALL(""),
    UNAUTHENTICATED("UNAUTHENTICATED"),
    NEW_DINER("NEW_DINER"),
    EXISTING_DINER("EXISTING_DINER");

    @Getter
    private final String type;

    private static final List<String> dinerTypeListExceptEmptyAll = EnumSet.allOf(DinerType.class)
            .stream().filter(dinerType -> dinerType != DinerType.EMPTY_ALL)
            .map(DinerType::getType)
            .collect(List.collector());

    public static List<String> getDinerTypesExceptEmptyAll() {
        return dinerTypeListExceptEmptyAll;
    }
}

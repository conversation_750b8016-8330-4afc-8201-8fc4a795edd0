package com.grubhub.garcon.controlplane.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import com.grubhub.roux.uuid.TimeUuid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.Set;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(FlowRoutingGroupCriteria.Constants.TABLE_NAME)
public class FlowRoutingGroupCriteria {

    @NonNull
    @PartitionKey
    @Builder.Default
    private UUID flowId = TimeUuid.generate();

    @NonNull
    @PartitionKey
    @Builder.Default
    private String routingGroupCriteria = "default";

    private Set<String> routingGroups;

    public static class Constants {
        public static final String TABLE_NAME = "flow_routing_group_criteria";
    }

    public static FlowRoutingGroupCriteria partitionKey(UUID flowId, String routingGroupCriteria) {
        return FlowRoutingGroupCriteria.builder()
                .flowId(flowId)
                .routingGroupCriteria(routingGroupCriteria)
                .build();
    }
}

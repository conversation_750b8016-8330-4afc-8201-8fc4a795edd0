package com.grubhub.garcon.controlplane.cassandra.dao;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import lombok.NonNull;

import java.util.List;
import java.util.UUID;
import java.util.Optional;


public interface FlowRoutingGroupDao {
    // Flow Routing Group CRUD Operations
    void insert(@NonNull FlowRoutingGroupV2 flowRoutingGroup);
    void insertManyV2(List<FlowRoutingGroupV2> flowRoutingGroups);

    Optional<FlowRoutingGroupV2> select(@NonNull UUID flowId, @NonNull String routingGroupCriteria, @NonNull String groupName);
    StreamOperation<FlowRoutingGroupV2> selectAllAsync(@NonNull UUID flowId);
    List<FlowRoutingGroupV2> selectAll(@NonNull UUID flowId);

    void updateV2(@NonNull FlowRoutingGroupV2 flowRoutingGroup);

    void delete(@NonNull UUID flowId, @NonNull String routingGroupCriteria, @NonNull String groupName);
}

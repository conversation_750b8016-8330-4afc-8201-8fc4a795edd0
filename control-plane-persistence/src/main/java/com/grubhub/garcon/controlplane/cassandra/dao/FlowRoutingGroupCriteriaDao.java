package com.grubhub.garcon.controlplane.cassandra.dao;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupCriteria;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface FlowRoutingGroupCriteriaDao {

    void insert(@NonNull FlowRoutingGroupCriteria flowRoutingGroupCriteria);

    void insert(List<FlowRoutingGroupCriteria> flowRoutingGroupCriteria);

    Optional<FlowRoutingGroupCriteria> select(@NonNull UUID flowId, @NonNull String routingGroupCriteria);

    void delete(@NonNull UUID flowId, @NonNull String routingGroupCriteria);

}

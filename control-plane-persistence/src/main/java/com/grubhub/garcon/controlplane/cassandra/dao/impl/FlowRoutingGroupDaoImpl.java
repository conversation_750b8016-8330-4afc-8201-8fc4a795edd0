package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowRoutingGroupDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


public class FlowRoutingGroupDaoImpl implements FlowRoutingGroupDao {
    private final Casserole casserole;

    @Inject
    public FlowRoutingGroupDaoImpl(@Named("casserole-v2-ddmlControlPlane") Casserole casserole) {
        this.casserole = casserole;
    }

    @Override
    public void insert(@NonNull FlowRoutingGroupV2 flowRoutingGroup) {
        casserole.insertSingle(flowRoutingGroup);
    }

    @Override
    public void insertManyV2(List<FlowRoutingGroupV2> flowRoutingGroups) {
        casserole.insertMany(flowRoutingGroups);
    }


    @Override
    public Optional<FlowRoutingGroupV2> select(@NonNull UUID flowId, @NonNull String routingGroupCriteria, @NonNull String groupName) {
        FlowRoutingGroupV2 flowRoutingGroupV2 = FlowRoutingGroupV2.builder()
                .flowId(flowId)
                .routingGroupCriteria(routingGroupCriteria)
                .groupName(groupName)
                .build();
        return casserole.selectSingle(flowRoutingGroupV2);
    }

    @Override
    public StreamOperation<FlowRoutingGroupV2> selectAllAsync(@NonNull UUID flowId) {
        return casserole.select().partition(flowId, FlowRoutingGroupV2.class);
    }

    @Override
    public List<FlowRoutingGroupV2> selectAll(@NonNull UUID flowId) {
        return casserole.selectPartition(flowId, FlowRoutingGroupV2.class);
    }

    @Override
    public void updateV2(@NonNull FlowRoutingGroupV2 flowRoutingGroup) {
        casserole.updateSingle(flowRoutingGroup);
    }

    @Override
    public void delete(@NonNull UUID flowId, @NonNull String routingGroupCriteria, @NonNull String groupName) {
        FlowRoutingGroupV2 flowRoutingGroupV2 = FlowRoutingGroupV2.builder()
                .flowId(flowId)
                .routingGroupCriteria(routingGroupCriteria)
                .groupName(groupName)
                .build();
        casserole.deleteSingle(flowRoutingGroupV2);
    }

}

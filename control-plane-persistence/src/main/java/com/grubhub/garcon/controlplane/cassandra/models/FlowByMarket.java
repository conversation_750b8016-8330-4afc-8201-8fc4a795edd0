package com.grubhub.garcon.controlplane.cassandra.models;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(FlowByMarket.Constants.TABLE_NAME)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FlowByMarket {

    @NonNull @PartitionKey String flowSet;
    @NonNull @PartitionKey String marketName;

    @Clustering<PERSON>ey UUID flowId;

    public static FlowByMarket partitionKey(String flowSet, String marketName) {
        return FlowByMarket.builder()
                .flowSet(flowSet)
                .marketName(marketName)
                .build();
    }

    public static FlowByMarket primaryKey(String flowSet, String marketName, UUID flowId) {
        return FlowByMarket.builder()
                .flowSet(flowSet)
                .marketName(marketName)
                .flowId(flowId)
                .build();
    }

    public static class Constants {
        public static final String TABLE_NAME = "flow_by_market";
    }

    @JsonIgnore
    public FlowByGeoHash toFlowByGeoHash(String geohash) {
        return new FlowByGeoHash(flowSet, geohash, flowId, marketName);
    }
}

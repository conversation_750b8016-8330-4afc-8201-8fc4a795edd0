package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowDao;
import com.grubhub.garcon.controlplane.cassandra.models.Flow;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.casserole.api.options.ReadOptions;
import com.grubhub.roux.casserole.api.operation.ValueOperation;
import io.vavr.collection.Set;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


public class FlowDaoImpl implements FlowDao {
    private final Casserole casserole;

    @Inject
    public FlowDaoImpl(@Named("casserole-v2-ddmlControlPlane")Casserole casserole) {
        this.casserole = casserole;
    }

    @Override
    public void insert(@NonNull Flow flow) {
        casserole.insertSingle(flow);
    }

    @Override
    public ValueOperation<Flow> selectAsync(@NonNull UUID flowId) {
        return casserole.select().single(flowId, Flow.class);
    }

    @Override
    public Optional<Flow> select(@NonNull UUID flowId) {
        return casserole.selectSingle(flowId, Flow.class);
    }

    @Override
    public void update(@NonNull Flow flow) {
        casserole.updateSingle(flow);
    }

    @Override
    public void delete(@NonNull UUID flowId) {
        casserole.deleteSingle(flowId, Flow.class);
    }

    @Override
    public List<Flow> selectFlowsWithIds(@NonNull Set<UUID> ids) {
        return casserole.select().partitions(ids, Flow.class, ReadOptions.DEFAULTS).getList();
    }
}

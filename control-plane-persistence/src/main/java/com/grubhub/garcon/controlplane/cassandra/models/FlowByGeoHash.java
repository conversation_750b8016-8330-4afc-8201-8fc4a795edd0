package com.grubhub.garcon.controlplane.cassandra.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(FlowByGeoHash.Constants.TABLE_NAME)
public class FlowByGeoHash {
    @NonNull
    @PartitionKey
    private String flowSet;
    @PartitionKey
    private String geohash;
    @ClusteringKey
    private UUID flowId;
    private String marketName;

    public static class Constants {
        public static final String TABLE_NAME = "flow_geohashes";
    }

    public static FlowByGeoHash primaryKey(String flowSet, String geohash, UUID flowId) {
        return FlowByGeoHash.builder()
                .flowSet(flowSet)
                .geohash(geohash)
                .flowId(flowId)
                .build();
    }

    public static FlowByGeoHash partitionKey(String flowSet, String geohash) {
        return FlowByGeoHash.builder()
                .flowSet(flowSet)
                .geohash(geohash)
                .build();
    }
}

package com.grubhub.garcon.controlplane.cassandra.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.With;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

import static java.util.Collections.emptySet;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(Market.Constants.TABLE_NAME)
@With
public class Market {

    @NonNull
    @PartitionKey
    private String marketName;

    private String geoPolygon;

    @Builder.Default
    private Set<String> geohashes = emptySet();

    private String city;

    private String state;

    private String zipcode;

    private UUID regionUuid;

    private String regionName;

    private String updatedUser;

    private Instant updatedTimestamp;

    public static class Constants {
        public static final String TABLE_NAME = "markets";
        public static final String MARKET_NAME_COLUMN = "market_name";
    }
}

package com.grubhub.garcon.controlplane.cassandra.dao;

import com.grubhub.garcon.controlplane.cassandra.models.Flow;
import com.grubhub.roux.casserole.api.operation.ValueOperation;
import io.vavr.collection.Set;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface FlowDao {
    // Flow CRUD Operations
    void insert(@NonNull Flow market);

    ValueOperation<Flow> selectAsync(@NonNull UUID flowId);
    Optional<Flow> select(@NonNull UUID flowId);

    void update(@NonNull Flow flow);

    void delete(@NonNull UUID flowId);

    // Flow Selection Operations
    List<Flow> selectFlowsWithIds(@NonNull Set<UUID> ids);
}

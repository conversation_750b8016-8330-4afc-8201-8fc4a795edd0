package com.grubhub.garcon.controlplane.cassandra.dao;

import com.grubhub.garcon.controlplane.cassandra.models.MarketByGeohash;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import com.grubhub.roux.casserole.api.operation.VoidOperation;

import java.util.List;


public interface MarketByGeohashDao {
    void upsert(List<MarketByGeohash> marketByGeohashes);
    VoidOperation upsertAsync(MarketByGeohash marketByGeohash);
    List<MarketByGeohash> getMarketsByGeohashes(int precision, List<String> geohashes);
    void deleteMarketsByGeohashes(int precision, List<String> geohashes);
    StreamOperation<MarketByGeohash> getMarketsByGeohashesAsync(int precision, List<String> geohashes);
    StreamOperation<MarketByGeohash> getMarketsByGeohashAsync(int precision, String geohash);
}

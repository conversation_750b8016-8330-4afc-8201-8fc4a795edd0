package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.GdpActionResultDao;
import com.grubhub.garcon.controlplane.cassandra.models.GdpActionResult;
import com.grubhub.roux.casserole.api.Casserole;
import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.val;


public class GdpActionResultDaoImpl implements GdpActionResultDao {

    private final Casserole casserole;

    @Inject
    public GdpActionResultDaoImpl(@Named("casserole-v2-ddmlControlPlane") Casserole casserole) {
        this.casserole = casserole;
    }

    @Override
    public void insert(GdpActionResult action) {
        casserole.insertSingle(action);
    }

    @Override
    public Option<GdpActionResult> select(String actionDate, String actionName) {
        val gdpActionResult = GdpActionResult.builder()
                .actionDate(actionDate)
                .actionName(actionName)
                .build();
        return Option.ofOptional(casserole.selectSingle(gdpActionResult));
    }

    @Override
    public List<GdpActionResult> selectAll(String actionDate) {
        return List.ofAll(casserole.selectPartition(actionDate, GdpActionResult.class));
    }
}

package com.grubhub.garcon.controlplane.cassandra.dao;

import com.grubhub.garcon.controlplane.cassandra.models.FlowByMarket;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface FlowByMarketDao {

    void insert(FlowByMarket flowByMarket);
    void insertMany(List<FlowByMarket> flowByMarkets);

    Optional<FlowByMarket> select(@NonNull String flowSet, @NonNull String marketName, @NonNull UUID flowId);
    List<FlowByMarket> getFlowsByMarkets(@NonNull String flowSet, List<String> marketNames);
    StreamOperation<FlowByMarket> getFlowsByMarketsAsync(@NonNull String flowSet, List<String> marketNames);
    StreamOperation<FlowByMarket> getFlowsByMarketAsync(@NonNull String flowSet, @NonNull String marketName);

    void delete(@NonNull String flowSet, @NonNull String marketName, @NonNull UUID flowId);
}

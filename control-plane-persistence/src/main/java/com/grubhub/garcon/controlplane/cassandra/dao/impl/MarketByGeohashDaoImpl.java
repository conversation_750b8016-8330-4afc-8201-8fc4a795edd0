package com.grubhub.garcon.controlplane.cassandra.dao.impl;


import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.MarketByGeohashDao;
import com.grubhub.garcon.controlplane.cassandra.models.MarketByGeohash;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.casserole.api.options.ReadOptions;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import com.grubhub.roux.casserole.api.operation.VoidOperation;

import java.util.List;


public class MarketByGeohashDaoImpl implements MarketByGeohashDao {
    private final Casserole casserole;

    @Inject
    public MarketByGeohashDaoImpl(@Named("casserole-v2-ddmlControlPlane") Casserole casserole) {
        this.casserole = casserole;
    }

    @Override
    public void upsert(List<MarketByGeohash> marketByGeohashes) {
        if (marketByGeohashes.isEmpty()) {
            return;
        }
        VoidOperation operation = upsertAsync(marketByGeohashes.get(0));
        for (int i = 1; i < marketByGeohashes.size(); i++) {
            operation = operation.and(upsertAsync(marketByGeohashes.get(i)));
        }
        operation.join();
    }

    @Override
    public VoidOperation upsertAsync(MarketByGeohash marketByGeohash) {
        return casserole.insert().single(marketByGeohash, MarketByGeohash.class);
    }

    @Override
    public List<MarketByGeohash> getMarketsByGeohashes(int precision, List<String> geohashes) {
        return getMarketsByGeohashesAsync(precision, geohashes).getList();
    }

    @Override
    public StreamOperation<MarketByGeohash> getMarketsByGeohashesAsync(int precision, List<String> geohashes) {
        List<MarketByGeohash> keys = getKeys(precision, geohashes);
        return casserole.select().partitions(keys, MarketByGeohash.class, ReadOptions.DEFAULTS);
    }

    @Override
    public StreamOperation<MarketByGeohash> getMarketsByGeohashAsync(int precision, String geohash) {
        return casserole.select().partition(MarketByGeohash.partitionKey(precision, geohash), MarketByGeohash.class);
    }

    @Override
    public void deleteMarketsByGeohashes(int precision, List<String> geohashes) {
        List<MarketByGeohash> keys = getKeys(precision, geohashes);
        casserole.deleteManyPartitions(keys, MarketByGeohash.class);
    }

    private List<MarketByGeohash> getKeys(int precision, List<String> geohashes) {
        return geohashes.stream()
                .map(geohash -> MarketByGeohash.partitionKey(precision, geohash))
                .toList();
    }
}

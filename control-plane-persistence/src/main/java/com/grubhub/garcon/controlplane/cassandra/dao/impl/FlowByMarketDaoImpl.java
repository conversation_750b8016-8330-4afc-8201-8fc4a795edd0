package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByMarketDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByMarket;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.casserole.api.options.ReadOptions;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


public class FlowByMarketDaoImpl implements FlowByMarketDao {

    private final Casserole casserole;

    @Inject
    public FlowByMarketDaoImpl(@Named("casserole-v2-ddmlControlPlane") Casserole casserole) {
        this.casserole = casserole;
    }

    @Override
    public void insert(FlowByMarket flowByMarket) {
        casserole.insertSingle(flowByMarket);
    }

    @Override
    public void insertMany(List<FlowByMarket> flowByMarket) {
        casserole.insertMany(flowByMarket);
    }

    @Override
    public Optional<FlowByMarket> select(@NonNull String flowSet, @NonNull String marketName, @NonNull UUID flowId) {
        FlowByMarket flowByMarket = FlowByMarket.primaryKey(flowSet, marketName, flowId);
        return casserole.selectSingle(flowByMarket);
    }

    @Override
    public List<FlowByMarket> getFlowsByMarkets(@NonNull String flowSet, List<String> marketNames) {
        return getFlowsByMarketsAsync(flowSet, marketNames).getList();
    }

    @Override
    public StreamOperation<FlowByMarket> getFlowsByMarketsAsync(@NonNull String flowSet, List<String> marketNames) {
        List<FlowByMarket> keys = marketNames.stream()
                .map(marketName -> FlowByMarket.partitionKey(flowSet, marketName))
                .toList();
        return casserole.select().partitions(keys, FlowByMarket.class, ReadOptions.DEFAULTS);
    }


    @Override
    public StreamOperation<FlowByMarket> getFlowsByMarketAsync(@NonNull String flowSet, @NonNull String marketName) {
        FlowByMarket key = FlowByMarket.partitionKey(flowSet, marketName);
        return casserole.select().partition(key, FlowByMarket.class);
    }

    @Override
    public void delete(@NonNull String flowSet, @NonNull String marketName, @NonNull UUID flowId) {
        FlowByMarket key = FlowByMarket.primaryKey(flowSet, marketName, flowId);
        casserole.deleteSingle(key);
    }

}

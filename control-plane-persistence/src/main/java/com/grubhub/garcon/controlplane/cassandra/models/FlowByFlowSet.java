package com.grubhub.garcon.controlplane.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.*;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(FlowByFlowSet.Constants.TABLE_NAME)
public class FlowByFlowSet {

    @NonNull
    @PartitionKey
    private String flowSet;
    @NonNull
    @ClusteringKey
    private UUID flowId;

    private boolean enabled;

    public static class Constants {
        public static final String TABLE_NAME = "flow_by_flow_set";
    }

    public static FlowByFlowSet primaryKey(String flowSet, UUID flowId) {
        return FlowByFlowSet
                .builder()
                .flowSet(flowSet)
                .flowId(flowId)
                .build();
    }
}

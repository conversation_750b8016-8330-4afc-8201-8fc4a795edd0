package com.grubhub.garcon.controlplane.cassandra.dao;

import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface FlowByGeoHashDao {

    void insert(@NonNull FlowByGeoHash flowByGeoHash);
    void insertMany(@NonNull List<FlowByGeoHash> flowByGeoHash);

    Optional<FlowByGeoHash> select(@NonNull String flowSet, @NonNull String geohash);
    List<FlowByGeoHash> selectAll(@NonNull String flowSet, @NonNull String geohash);
    StreamOperation<FlowByGeoHash> selectAllAsync(@NonNull String flowSet, @NonNull String geohash);

    void update(@NonNull FlowByGeoHash flowByGeoHash);

    void delete(@NonNull String flowSet, @NonNull String geohash);
    void delete(@NonNull String flowSet, @NonNull List<String> geohash);
    void delete(@NonNull String flowSet, @NonNull UUID flowId, @NonNull List<String> geohashes);
}

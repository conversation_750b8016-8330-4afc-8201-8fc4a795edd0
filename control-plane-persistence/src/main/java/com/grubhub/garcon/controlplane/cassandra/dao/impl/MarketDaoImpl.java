package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.MarketDao;
import com.grubhub.garcon.controlplane.cassandra.models.Market;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.casserole.api.options.ReadOptions;
import com.grubhub.roux.casserole.api.operation.ValueOperation;
import lombok.NonNull;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;


public class MarketDaoImpl implements MarketDao {

    private final Casserole casserole;

    @Inject
    public MarketDaoImpl(@Named("casserole-v2-ddmlControlPlane") Casserole casserole) {
        this.casserole = casserole;
    }

    @Override
    public void insert(@NonNull Market market) {
        casserole.insertSingle(market);
    }

    @Override
    public Optional<Market> select(@NonNull String marketName) {
        return casserole.selectSingle(marketName, Market.class);
    }

    @Override
    public ValueOperation<Market> selectAsync(@NonNull String marketName) {
        return casserole.select().single(marketName, Market.class);
    }

    @Override
    public Stream<Market> selectMarketsByNames(@NonNull Set<String> marketNames) {
        return casserole.select().partitions(marketNames, Market.class, ReadOptions.DEFAULTS).getList().stream();
    }

    @Override
    public Optional<Market> selectGlobalMarket() {
        return casserole.selectSingle("global", Market.class);
    }

    @Override
    public void update(@NonNull Market updatedMarket) {
        casserole.updateSingle(updatedMarket);
    }

    @Override
    public void delete(@NonNull String marketName) {
        casserole.deleteSingle(marketName, Market.class);
    }
}

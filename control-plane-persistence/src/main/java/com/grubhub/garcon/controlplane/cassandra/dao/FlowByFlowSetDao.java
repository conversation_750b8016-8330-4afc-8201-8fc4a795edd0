package com.grubhub.garcon.controlplane.cassandra.dao;

import com.grubhub.garcon.controlplane.cassandra.models.FlowByFlowSet;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface FlowByFlowSetDao {

    void insert(FlowByFlowSet flowByFlowSet);

    Optional<FlowByFlowSet> get(@NonNull String flowSet, @NonNull UUID flowId);
    List<FlowByFlowSet> select(@NonNull String flowSet);
    StreamOperation<FlowByFlowSet> selectAsync(@NonNull String flowSet);

    void update(@NonNull FlowByFlowSet flowByFlowSet);

    void delete(@NonNull String flowSet, @NonNull UUID flowId);
}

package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowRoutingGroupCriteriaDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupCriteria;
import com.grubhub.roux.casserole.api.Casserole;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


public class FlowRoutingGroupCriteriaDaoImpl implements FlowRoutingGroupCriteriaDao {

    private final Casserole casserole;

    @Inject
    public FlowRoutingGroupCriteriaDaoImpl(@Named("casserole-v2-ddmlControlPlane") Casserole casserole) {
        this.casserole = casserole;
    }

    @Override
    public void insert(@NonNull FlowRoutingGroupCriteria flowRoutingGroupCriteria) {
        casserole.insertSingle(flowRoutingGroupCriteria);
    }

    @Override
    public void insert(List<FlowRoutingGroupCriteria> flowRoutingGroupCriteria) {
        for (FlowRoutingGroupCriteria criteria: flowRoutingGroupCriteria){
            insert(criteria);
        }
    }

    @Override
    public Optional<FlowRoutingGroupCriteria> select(@NonNull UUID flowId, @NonNull String routingGroupCriteria) {
        return casserole.selectSingle(FlowRoutingGroupCriteria.partitionKey(flowId, routingGroupCriteria));
    }

    @Override
    public void delete(@NonNull UUID flowId, @NonNull String routingGroupCriteria) {
        casserole.deleteSingle(FlowRoutingGroupCriteria.partitionKey(flowId, routingGroupCriteria));
    }
}

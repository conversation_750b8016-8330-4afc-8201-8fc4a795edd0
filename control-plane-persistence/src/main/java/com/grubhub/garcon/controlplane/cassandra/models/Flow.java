package com.grubhub.garcon.controlplane.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(Flow.Constants.TABLE_NAME)
public class Flow {

    @NonNull
    @PartitionKey
    private UUID flowId;
    private String flowSet;
    private String flowName;
    private boolean enabled;
    private String matchingStrategy;
    private Set<String> matchingDinerTypes;
    private Set<String> locationMarkets;
    private String updatedUser;
    private Instant updatedTimestamp;
    private Set<String> matchingOrderTypes;
    private List<String> matchingQueryTokens;
    private Set<String> matchingQueryTypes;
    private Set<String> matchingQueryKeywords;
    private Set<String> matchingMealtime;
    private Set<String> matchingApplications;
    private Set<String> matchingUserDomains;
    private Set<String> matchingUserEmails;
    private Set<String> matchingApplicationVersions;
    private int priority; //Ascending (1 better than 2)
    private String routingGroupsBucketingMode;
    private Set<String> matchingRequestTags;
    private String classificationModelName;
    private String classificationModelOutputName;
    private Set<String> matchingModelClassifications;
    private Integer dinerTypeOrdersThreshold;
    private List<String> routingGroupsSeedRotation;
    private Boolean routingGroupsSeedRotationEnabled;
    private String routingGroupSetElectionCriteria;
    private Set<String> routingGroupsCriteria;
    private Set<String> matchingClientEntityIds;
    private Set<String> matchingBrands;

    @Builder.Default
    private int routingGroupsSeed = 0;

    public static class Constants {
        public static final String TABLE_NAME = "flows";
        public static final String FLOW_ID_COLUMN = "flow_id";
    }
}

package com.grubhub.garcon.controlplane.cassandra.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(FlowRoutingGroupV2.Constants.TABLE_NAME_V2)
public class FlowRoutingGroupV2 {

    @NonNull
    @PartitionKey
    private UUID flowId;
    @NonNull
    @ClusteringKey
    @Builder.Default
    private String routingGroupCriteria = "default";
    @NonNull
    @ClusteringKey
    private String groupName;
    private String variation;
    private float routingPercentage;
    private String ensembleName;
    private String ensembleWeight;
    private String ensembleFunction;
    private String ensembleStrategy;
    private Integer groupOrder;
    private Map<String, String> groupProperties = new HashMap<>();
    private boolean ensembleIsModel;

    public static class Constants {
        public static final String TABLE_NAME_V2 = "flow_routing_groups_v2";
        public static final String FLOW_ID_COLUMN = "flow_id";
        public static final String GROUP_NAME_COLUMN = "group_name";
        public static final String ROUTING_GROUP_CRITERIA_COLUMN = "routing_group_criteria";
    }

}

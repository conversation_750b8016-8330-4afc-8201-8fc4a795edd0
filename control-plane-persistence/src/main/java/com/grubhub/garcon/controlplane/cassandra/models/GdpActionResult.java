package com.grubhub.garcon.controlplane.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(GdpActionResult.Constants.TABLE_NAME)
public class GdpActionResult {

    @NonNull
    @PartitionKey
    private String actionDate;

    @NonNull
    @ClusteringKey
    private String actionName;

    private String actionType;
    private String actionGroup;
    private int sequenceNumber;
    private String filePath;
    private String createdDateTime;
    private String actionContent;
    private boolean ok;
    private String resultMsg;
    private String loadedDateTime;
    private String processedDateTime;

    public static class Constants {
        public static final String TABLE_NAME = "gdp_action_results";
    }
}

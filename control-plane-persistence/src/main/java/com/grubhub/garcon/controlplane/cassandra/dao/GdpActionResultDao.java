package com.grubhub.garcon.controlplane.cassandra.dao;

import com.grubhub.garcon.controlplane.cassandra.models.GdpActionResult;
import io.vavr.collection.List;
import io.vavr.control.Option;

public interface GdpActionResultDao {
    void insert(GdpActionResult action);
    Option<GdpActionResult> select(String actionDate, String actionName);
    List<GdpActionResult> selectAll(String actionDate);
}

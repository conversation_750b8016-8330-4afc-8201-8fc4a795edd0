package com.grubhub.garcon.controlplane.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;

@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
@Table(MarketByGeohash.Constants.TABLE_NAME)
@FieldNameConstants
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MarketByGeohash {

    @NonNull @PartitionKey Integer geohashPrecision;
    @NonNull @PartitionKey String geohash;

    @ClusteringKey String marketName;

    public static class Constants {
       public static final String TABLE_NAME = "market_by_geohash";
       public static final String GEOHASH_COLUMN = "geohash";
       public static final String GEOHASH_PRECISION = "geohash_precision";
    }

    public static MarketByGeohash partitionKey(Integer geohashPrecision, String geohash) {
        return MarketByGeohash.builder()
                .geohashPrecision(geohashPrecision)
                .geohash(geohash)
                .build();
    }
}

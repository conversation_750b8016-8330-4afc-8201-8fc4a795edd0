package com.grubhub.garcon.controlplane.cassandra.dao;

import com.grubhub.garcon.controlplane.cassandra.models.Market;
import com.grubhub.roux.casserole.api.operation.ValueOperation;
import lombok.NonNull;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

public interface MarketDao {

    void insert(@NonNull Market market);

    Optional<Market> select(@NonNull String marketName);
    ValueOperation<Market> selectAsync(@NonNull String marketName);
    Stream<Market> selectMarketsByNames(@NonNull Set<String> marketNames);
    Optional<Market> selectGlobalMarket();

    void update(@NonNull Market market);

    void delete(@NonNull String marketName);
}

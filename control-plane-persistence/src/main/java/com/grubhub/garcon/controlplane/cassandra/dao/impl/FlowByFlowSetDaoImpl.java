package com.grubhub.garcon.controlplane.cassandra.dao.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByFlowSetDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByFlowSet;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


public class FlowByFlowSetDaoImpl implements FlowByFlowSetDao {

    private final Casserole casserole;

    @Inject
    public FlowByFlowSetDaoImpl(@Named("casserole-v2-ddmlControlPlane") Casserole casserole) {
        this.casserole = casserole;
    }

    @Override
    public void insert(FlowByFlowSet flowByFlowSet) {
        casserole.insertSingle(flowByFlowSet);
    }

    @Override
    public Optional<FlowByFlowSet> get(@NonNull String flowSet, @NonNull UUID flowId) {
        return casserole.selectSingle(FlowByFlowSet.primaryKey(flowSet, flowId));
    }

    @Override
    public List<FlowByFlowSet> select(@NonNull String flowSet) {
        return casserole.selectPartition(flowSet, FlowByFlowSet.class);
    }

    @Override
    public StreamOperation<FlowByFlowSet> selectAsync(@NonNull String flowSet) {
        return casserole.select().partition(flowSet, FlowByFlowSet.class);
    }

    @Override
    public void update(@NonNull FlowByFlowSet flowByFlowSet) {
        casserole.updateSingle(flowByFlowSet);
    }

    @Override
    public void delete(@NonNull String flowSet, @NonNull UUID flowId) {
        casserole.deleteSingle(FlowByFlowSet.primaryKey(flowSet, flowId));
    }


}

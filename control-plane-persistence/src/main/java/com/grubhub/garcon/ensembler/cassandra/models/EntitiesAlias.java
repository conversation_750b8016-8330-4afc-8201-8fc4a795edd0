package com.grubhub.garcon.ensembler.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import io.vavr.Tuple2;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

import static com.grubhub.garcon.ensembler.cassandra.models.EntitiesAlias.Constants.TABLE_NAME;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(TABLE_NAME)
public class EntitiesAlias {

    public static Tuple2<String, String> EMPTY_ALIAS_AS_TUPLE = new Tuple2<>("", "");

    @PartitionKey
    private String collectionName;
    @PartitionKey
    private String aliasName;
    private String entityId;
    private Instant updatedTimestamp;

    public static EntitiesAlias partitionKey(String collectionName, String aliasName) {
        return EntitiesAlias.builder()
                .collectionName(collectionName)
                .aliasName(aliasName)
                .build();
    }

    public static EntitiesAlias ensemblePartitionKey(String aliasName) {
        return EntitiesAlias.builder()
                .collectionName(EntityCollection.Constants.COLLECTION_ENSEMBLE)
                .aliasName(aliasName)
                .build();
    }

    public static EntitiesAlias empty() {
        return EntitiesAlias.builder()
                .collectionName("")
                .aliasName("")
                .entityId("")
                .updatedTimestamp(Instant.now())
                .build();
    }

    public static class Constants {
        public static final String TABLE_NAME = "entities_alias";
    }
}

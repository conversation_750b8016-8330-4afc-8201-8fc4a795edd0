package com.grubhub.garcon.ensembler.cassandra.dao;

import com.grubhub.garcon.ensembler.cassandra.models.ModelFeature;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import lombok.NonNull;

import java.util.List;

public interface ModelFeatureDao {

    void insert(@NonNull ModelFeature modelFeature);

    StreamOperation<ModelFeature> selectAllAsync(@NonNull String modelName);
    List<ModelFeature> selectAll(@NonNull String modelName);

    void update(@NonNull ModelFeature modelFeature);
    void delete(@NonNull String modelName);
}


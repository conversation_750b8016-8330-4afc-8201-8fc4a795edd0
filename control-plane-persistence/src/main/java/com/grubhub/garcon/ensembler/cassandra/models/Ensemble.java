package com.grubhub.garcon.ensembler.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.*;

import java.time.Instant;
import java.util.Set;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(Ensemble.Constants.TABLE_NAME)
@With
public class Ensemble {
    @NonNull
    @PartitionKey
    private String ensembleName;

    private String ensembleDescription;

    private Set<String> models;
    private String updatedUser;
    private Instant updatedTimestamp;

    private String versioningStrategy;

    private String status;

    private String versioningEnsembleName;

    private String version;

    private String ensembleStrategy;

    public static class Constants {
        public static final String TABLE_NAME = "ensembles";

        public static final String STATUS_ENABLED = "ENABLED";
        public static final String STATUS_DISABLED = "DISABLED";
        public static final String STATUS_PENDING = "PENDING";
    }
}

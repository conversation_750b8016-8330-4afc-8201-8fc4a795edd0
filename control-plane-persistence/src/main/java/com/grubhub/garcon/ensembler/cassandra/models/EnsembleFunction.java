package com.grubhub.garcon.ensembler.cassandra.models;


import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(EnsembleFunction.Constants.TABLE_NAME)
public class EnsembleFunction {
    @NonNull
    @PartitionKey
    private String ensembleName;

    @ClusteringKey
    private String ensembleFunction;

    private String formula;

    private String functionType;

    public static class Constants {
        public static final String TABLE_NAME = "ensemble_functions";
    }
}

package com.grubhub.garcon.ensembler.cassandra.dao;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.ensembler.cassandra.models.FeatureKey;
import com.grubhub.garcon.ensembler.cassandra.models.FeatureValue;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import com.grubhub.roux.casserole.api.operation.ValueOperation;
import com.grubhub.roux.casserole.api.operation.VoidOperation;
import lombok.NonNull;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class FeatureValueDao {

    private final Casserole casserole;

    @Inject
    public FeatureValueDao(@Named("casserole-v2-ddmlControlPlane") Casserole casserole) {
        this.casserole = casserole;
    }


    public ValueOperation<FeatureValue> selectAsync(@NonNull FeatureKey featureKey) {
        return casserole.select().single(FeatureValue.primaryKey(featureKey), FeatureValue.class);
    }

    public Pair<FeatureKey, ValueOperation<FeatureValue>> selectAsyncPreservingTheOrder(@NonNull FeatureKey featureKey) {
        return Pair.of(featureKey, selectAsync(featureKey));
    }

    public Map<FeatureKey, Optional<FeatureValue>> selectMany(@NonNull List<FeatureKey> featureKeys) {
        if (featureKeys.isEmpty()) {
            return Map.of();
        }
        ValueOperation<FeatureValue> firstOperation = selectAsync(featureKeys.get(0));
        StreamOperation<FeatureValue> streamOperation = null;
        for (int i = 1; i < featureKeys.size(); i++) {
            if (streamOperation == null) {
                streamOperation = firstOperation.plus(selectAsync(featureKeys.get(i)));
            } else {
                streamOperation = streamOperation.plus(selectAsync(featureKeys.get(i)));
            }
        }
        if (streamOperation == null) {
            return Map.of(featureKeys.get(0), firstOperation.get());
        }
        List<FeatureValue> featureValues = streamOperation.getList();
        return featureValues.stream()
                .collect(Collectors.toMap(FeatureValue::toFeatureKey, Optional::of));
    }

    public void createOrUpdate(@NonNull FeatureValue featureValue) {
        casserole.updateSingle(featureValue);
    }

    public VoidOperation createOrUpdateAsync(@NonNull FeatureValue featureValue) {
        return casserole.update().single(featureValue, FeatureValue.class);
    }

    public void createOrUpdateMany(@NonNull List<FeatureValue> featureValues) {
        if (featureValues.isEmpty()) {
            return;
        }
        VoidOperation operation = casserole.update().single(featureValues.get(0), FeatureValue.class);
        for (int i = 1; i < featureValues.size(); i++) {
            operation = operation.and(casserole.update().single(featureValues.get(i), FeatureValue.class));
        }
        operation.join();
    }

    public void delete(@NonNull FeatureKey featureKey) {
        casserole.deleteSingle(FeatureValue.primaryKey(featureKey));
    }

    public void deleteMany(@NonNull List<FeatureKey> featureKeys) {
        casserole.deleteMany(featureKeys.stream().map(FeatureValue::primaryKey).toList());
    }
}

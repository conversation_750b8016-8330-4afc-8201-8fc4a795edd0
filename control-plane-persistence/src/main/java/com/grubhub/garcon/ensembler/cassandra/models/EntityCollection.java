package com.grubhub.garcon.ensembler.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(EntityCollection.Constants.TABLE_NAME)
public class EntityCollection {

    @NonNull
    @PartitionKey
    private String collectionName;

    @NonNull
    @ClusteringKey
    private String entityId;

    private String entityStatus;
    private String entityCategory;

    public static class Constants {
        public static final String TABLE_NAME = "entity_collection";

        public static final String COLLECTION_MODEL = "MODEL";
        public static final String COLLECTION_FLOW = "FLOW";
        public static final String COLLECTION_MARKET = "MARKET";
        public static final String COLLECTION_ENSEMBLE = "ENSEMBLE";
        public static final String COLLECTION_ALIAS = "ALIAS";
        public static final String COLLECTION_FLOW_SET = "FLOW_SET";

        public static final String NO_CATEGORY = "NO_CATEGORY";
    }

}

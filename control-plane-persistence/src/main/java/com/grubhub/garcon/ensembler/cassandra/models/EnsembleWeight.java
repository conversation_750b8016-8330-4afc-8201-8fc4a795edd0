package com.grubhub.garcon.ensembler.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.*;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(EnsembleWeight.Constants.TABLE_NAME)
public class EnsembleWeight {
    @NonNull
    @PartitionKey
    private String ensembleName;

    @ClusteringKey
    private String ensembleWeight;

    private Map<String, Float> modelWeights;

    public static class Constants {
        public static final String TABLE_NAME = "ensemble_weights";
    }
}

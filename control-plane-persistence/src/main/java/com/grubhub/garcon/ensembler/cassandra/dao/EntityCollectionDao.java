package com.grubhub.garcon.ensembler.cassandra.dao;

import com.grubhub.garcon.ensembler.cassandra.models.EntityCollection;
import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.NonNull;

public interface EntityCollectionDao {

    void update(@NonNull EntityCollection entityCollection);
    Option<EntityCollection> select(@NonNull String collectionName, @NonNull String entityId);
    List<EntityCollection> selectAll(@NonNull String collectionName);
    List<EntityCollection> selectAll(@NonNull String collectionName, String entityStatus);

    List<EntityCollection> selectAll(@NonNull String collectionName, String entityStatus, String entityCategory);

    List<EntityCollection> selectAllByIds(@NonNull String collectionName, String entityStatus, java.util.Set<String> entityIds);
    void delete(@NonNull String collectionName, @NonNull String entityId);

}

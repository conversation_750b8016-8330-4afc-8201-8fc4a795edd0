package com.grubhub.garcon.ensembler.cassandra.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.grubhub.garcon.ensembler.cassandra.models.Model.Constants.TABLE_NAME;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(TABLE_NAME)
public class Model {

    @NonNull
    @PartitionKey
    private String modelName;

    private String modelDescription;

    private String modelType;

    private String location;

    private String version;

    private String versioningStrategy;

    private String status;

    private String versioningModelName;

    private Set<String> processedFeaturesFilter;

    private String updatedUser;

    private Instant updatedTimestamp;

    private Map<String, String> processedFeaturesMapping;

    private String servingLocation;

    private Boolean tfUseExamplesSerialization;

    private String tfExamplesSerializationName;

    private String modelGroup;

    private List<String> searchEmbeddingsIndexNames;
    private Integer maxDynamicModels;
    private String modelCategory;
    private Integer searchEmbeddingsVectorDimension;
    private Boolean skipAgeReporting;
    private Integer inputBatchSize;
    private Boolean batchingEnabled;

    public static class Constants {
        public static final String TABLE_NAME = "models";
        public static final String MODEL_NAME_COLUMN = "model_name";
    }
}

package com.grubhub.garcon.ensembler.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import com.grubhub.roux.casserole.api.metadata.UseTtl;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(FeatureValue.Constants.TABLE_NAME)
public class FeatureValue {

    @PartitionKey
    private String featureName;
    @PartitionKey
    private Integer minorVersion;
    @PartitionKey
    private Integer majorVersion;
    @PartitionKey
    private String featureKey;
    private String featureValue;
    @UseTtl
    private int ttl;

    public static class Constants {
        public static final String TABLE_NAME = "feature_value3";
    }

    public FeatureKey toFeatureKey() {
        return new FeatureKey(featureName, minorVersion, majorVersion, featureKey);
    }

    public static FeatureValue primaryKey(FeatureKey featureKey) {
        return FeatureValue
                .builder()
                .featureName(featureKey.getFeatureName())
                .featureKey(featureKey.getFeatureKey())
                .majorVersion(featureKey.getMajorVersion())
                .minorVersion(featureKey.getMinorVersion())
                .build();
    }

    public static FeatureValue partitionKey(FeatureKey featureKey) {
        return FeatureValue
                .builder()
                .featureName(featureKey.getFeatureName())
                .featureKey(featureKey.getFeatureKey())
                .majorVersion(featureKey.getMajorVersion())
                .minorVersion(featureKey.getMinorVersion())
                .build();
    }
}

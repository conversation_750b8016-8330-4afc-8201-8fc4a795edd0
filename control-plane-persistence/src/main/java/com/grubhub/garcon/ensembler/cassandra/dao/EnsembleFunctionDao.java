package com.grubhub.garcon.ensembler.cassandra.dao;

import com.grubhub.garcon.ensembler.cassandra.models.EnsembleFunction;
import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.NonNull;

public interface EnsembleFunctionDao {

    void insert(@NonNull EnsembleFunction ensembleFunction);
    List<EnsembleFunction> selectAll(@NonNull String ensembleName);
    Option<EnsembleFunction> select(@NonNull String ensembleName, @NonNull String ensembleFunction);
    void update(@NonNull EnsembleFunction ensembleFunction);
    void delete(@NonNull String ensembleName, @NonNull String ensembleFunction);
    void deleteAll(@NonNull String ensembleName);
}

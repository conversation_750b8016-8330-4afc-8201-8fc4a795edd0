package com.grubhub.garcon.ensembler.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.Column;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.grubhub.garcon.ensembler.cassandra.models.ModelFeature.Constants.TABLE_NAME;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(TABLE_NAME)
public class ModelFeature {

    @NonNull
    @PartitionKey
    private String modelName;
    @ClusteringKey
    private String featureName;
    private String majorVersion;
    private String minorVersion;
    private String featureSourceType;
    @Column("feature_store_fields_list")
    private List<String> featureStoreFields;
    private String status;
    private String featureLocation;
    private Boolean featureOptional;
    private String featureDefaultValue;
    private String featureDefaultType;
    private Integer featureOrder;
    private boolean skipFetching;
    private boolean exposeFeatureStoreFieldsAsFeature;

    @Builder.Default
    private String functionName = "";
    @Builder.Default
    private Map<String, String> functionInputs = Collections.emptyMap();
    @Builder.Default
    private Map<String, String> functionOutputs = Collections.emptyMap();

    @Builder.Default
    private String functionScope = "RUNTIME";

    public static class Constants {
        public static final String TABLE_NAME = "model_features";
    }

    public static ModelFeature primaryKey(String modelName, String featureName) {
        return ModelFeature.builder()
                .modelName(modelName)
                .featureName(featureName)
                .build();
    }

    public static ModelFeature partitionKey(String modelName) {
        return ModelFeature.builder()
                .modelName(modelName)
                .build();
    }
}

package com.grubhub.garcon.ensembler.cassandra.dao;

import com.grubhub.garcon.ensembler.cassandra.models.ModelTestInvocationStatus;
import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.NonNull;

public interface ModelTestInvocationStatusDao {
    Option<ModelTestInvocationStatus> select(@NonNull String modelName, @NonNull String region);
    void insert(@NonNull ModelTestInvocationStatus modelTestInvocationStatus);
    List<ModelTestInvocationStatus> fetchTestInvocationStatus(@NonNull String modelName);

    void deleteMany(String modelName);
}

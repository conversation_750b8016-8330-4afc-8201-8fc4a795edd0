package com.grubhub.garcon.ensembler.cassandra.models;

import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;
import lombok.*;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(ModelOutput.Constants.TABLE_NAME)
public class ModelOutput {

    @NonNull
    @PartitionKey
    private String modelName;

    @NonNull
    @ClusteringKey
    private String outputName;
    private String outputType;
    private List<Integer> outputShape;
    private boolean normalized;
    private Double normalizedAvg;
    private Double normalizedStd;
    private String normalizedType;
    private Double normalizedMin = 0d;
    private Double normalizedMax = 1d;
    private Double normalizedRangeMin = 0d;
    private Double normalizedRangeMax = 1d;

    public static class Constants {
        public static final String TABLE_NAME = "model_outputs";
        public static final String MODEL_NAME_COLUMN = "model_name";
    }
}

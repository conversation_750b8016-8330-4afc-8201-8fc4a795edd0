package com.grubhub.garcon.ensembler.cassandra.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import com.grubhub.roux.casserole.api.metadata.ClusteringKey;
import com.grubhub.roux.casserole.api.metadata.PartitionKey;
import com.grubhub.roux.casserole.api.metadata.Table;

import static com.grubhub.garcon.ensembler.cassandra.models.ModelTestInvocationStatus.Constants.TABLE_NAME;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Table(TABLE_NAME)
public class ModelTestInvocationStatus {

    @NonNull
    @PartitionKey
    private String modelName;

    @ClusteringKey
    private String region;

    private String status;

    public static class Constants {
        public static final String TABLE_NAME = "model_test_invocation_status";
        public static final String MODEL_NAME_COLUMN = "model_name";
        public static final String REGION_NAME_COLUMN = "region";
    }
}

package com.grubhub.garcon.utils;

import io.vavr.Tuple2;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

@UtilityClass
@Slf4j
public class ControlPlanePersistenceUtils {


    public static <U, V, T extends Option<V>> Map<U, V> waitForCompletion(Map<CompletableFuture<U>, CompletableFuture<T>> tasks) {
        return tasks
                .map(entry -> entry._1().thenCombine(entry._2(), (key, value) -> new Tuple2<>(key, value)))
                .map(CompletableFuture::join)
                .reject(tuple -> tuple._2.isEmpty())
                .map(tuple -> new Tuple2<>(tuple._1, tuple._2.get()))
                .collect(HashMap.collector());
    }


}

package com.grubhub.garcon.controlplanerpc.model;

import lombok.*;
import lombok.extern.jackson.Jacksonized;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Jacksonized
public class FlowRoutingGroupRpc {
    private String flowId;
    @NotEmpty(message = "Flow routing group name is required")
    private String groupName;
    @NotEmpty(message = "Variation is required")
    private String variation;
    private float routingPercentage;
    @NotEmpty(message = "Ensemble name is required")
    private String ensembleName;
    @NotEmpty(message = "Ensemble weight is required")
    private String ensembleWeight;
    @Singular
    private Map<String, String> groupProperties;
    private boolean ensembleIsModel;
}

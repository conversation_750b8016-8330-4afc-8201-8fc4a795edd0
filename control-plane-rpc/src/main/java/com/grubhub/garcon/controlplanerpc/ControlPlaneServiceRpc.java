package com.grubhub.garcon.controlplanerpc;

import com.grubhub.garcon.controlplanerpc.model.*;
import com.grubhub.roux.rpc.api.RpcMethod;
import com.grubhub.roux.rpc.api.RpcParam;

import java.util.List;

/**
 * Discovery Control Plane main RPC interface
 */
public interface ControlPlaneServiceRpc {

    /**
     * Select the right Flow to use for the given request. Executes all targeting rules configured for the given flowSet.
     * @param request resolve-flow request
     * @return selected Flow and Group to be used for this request
     */
    @RpcMethod
    FlowResponseRpc resolveFlow(@RpcParam("resolveFlowRequest") ResolveFlowRequestRpc request);


    /**
     * Selects the right Flows to use for every request given in the list. Calls resolveFlow for each request given in the list.
     * @param request list of resolve-flow requests
     * @return list of selected Flow and Group to be used for each given request
     */
    @RpcMethod
    List<FlowResponseRpc> resolveFlowBulk(@RpcParam("resolveFlowRequest") List<ResolveFlowRequestRpc> request);

    /**
     * Invoke a model with the given inputs.
     * Only valid for models with a single output of type FLOAT. Use invokeModelInferenceMultiOutput for multiple outputs.
     * @param modelInferenceRequest invocation request
     * @return output for each sample of the model (in the same order as ModelInferenceRequestRpc.features)
     */
    @RpcMethod
    List<Float> invokeModelInference(@RpcParam("modelInferenceRequest") ModelInferenceRequestRpc modelInferenceRequest);

    /**
     * Invoke a model with the given inputs.
     * The model can return multiple outputs.
     * @param modelInferenceRequest inputs to invoke the model
     * @return map of all model outputs. Each key of the map contains a list with outputs in the same order as ModelInferenceRequestRpc.features
     */
    @RpcMethod
    ModelInferenceOutputRpc invokeModelInferenceMultiOutput(@RpcParam("modelInferenceRequest") ModelInferenceRequestRpc modelInferenceRequest);

    /**
     * Invoke an ensemble (group of models) with the given request and returned the combined output of all models.
     * @param ensembleInvocationRequest invocation request
     * @return linear combination of all models output (in the same order as EnsembleInvocationRequestRpc.features)
     */
    @RpcMethod
    List<Float> invokeEnsemble(@RpcParam("ensembleInvocationRequest") EnsembleInvocationRequestRpc ensembleInvocationRequest);

    /**
     * Invoke a sequence of models in a single request, one after the other one. Outputs of a model can be used as inputs of the following one.
     * @param modelInferenceSequenceRequest request with sequence of models
     * @return outputs of the final model in the sequence
     */
    @RpcMethod
    ModelInferenceOutputRpc invokeSequenceOfModels(@RpcParam("modelInferenceSequenceRequest") ModelInferenceSequenceRequestRpc modelInferenceSequenceRequest);

    /**
     * Gets flows that are under the specified flowSet. Can be limited to only enabled or disabled ones (all flows if enabled value is null).
     * @param flowsByFlowSetRequest request with flowSet and enabled values
     * @return flowIds and enabled status of flows in the flowSet.
     */
    @RpcMethod
    List<FlowByFlowSetRpc> getFlowsByFlowSet(@RpcParam("flowsByFlowSetRequest") FlowsByFlowSetRequestRpc flowsByFlowSetRequest);

    /**
     * Gets the entire flow based on the given request.
     * @param flowRequest request with flowId
     * @return full flow configuration
     */
    @RpcMethod
    FlowRpc getFlow(@RpcParam("flowRequest") FlowRequestRpc flowRequest);

    /**
     * Gets all markets
     * @return list of entityCollections; the entityId of each one corresponds to the market name.
     */
    @RpcMethod
    List<EntityCollectionRpc> getMarkets();
}

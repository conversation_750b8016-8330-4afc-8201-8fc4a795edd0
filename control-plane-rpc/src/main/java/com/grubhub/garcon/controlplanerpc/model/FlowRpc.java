package com.grubhub.garcon.controlplanerpc.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class FlowRpc {
    private String flowId;
    private String flowSet;
    private String flowName;
    private boolean enabled;
    private String matchingStrategy;
    private Set<String> matchingDinerTypes = Collections.emptySet();
    private Set<String> locationMarkets;
    private String updatedUser;
    private Date updatedTimestamp;
    private String routingGroupSetElectionCriteria;
    private Map<String, List<FlowRoutingGroupRpc>> routingGroupsCriteria;
    private Set<String> matchingOrderTypes;
    private List<String> matchingQueryTokens;
    private Set<String> matchingQueryTypes;
    private Set<String> matchingQueryKeywords;
    private Set<String> matchingMealtime;
    private Set<String> matchingApplications;
    private Set<String> matchingUserDomains;
    private Set<String> matchingUserEmails;
    private Set<String> matchingApplicationVersions;
    private Set<String> matchingClientEntityIds;
    private Set<String> matchingBrands;
    private int priority; //Ascending (1 better than 2)
    private String routingGroupsBucketingMode;
    private Set<String> matchingRequestTags;
    private String classificationModelName;
    private String classificationModelOutputName;
    private Set<String> matchingModelClassifications;
    private Integer dinerTypeOrdersThreshold;
    private List<String> routingGroupsSeedRotation;
    private Boolean routingGroupsSeedRotationEnabled;
    @Builder.Default
    private int routingGroupsSeed = 0;
}

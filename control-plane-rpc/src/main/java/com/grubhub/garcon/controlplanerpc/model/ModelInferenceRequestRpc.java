package com.grubhub.garcon.controlplanerpc.model;

import lombok.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Request to invoke a model
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class ModelInferenceRequestRpc {

    /**
     * ID provided by caller that will be logged for tracking purposes
     */
    private String callerTrackingId;

    /**
     * Name of the model to be invoked
     */
    private String modelName;

    /**
     * Common features to all rows in features list.
     * Only primitive types are supported as values (float, long, String) and lists of (float, long, String)
     */
    @Builder.Default
    private Map<String, Object> globalFeatures = Collections.emptyMap();

    /**
     * Each individual invocation (samples) to send to this model.
     * Each row contains a Map of key-value features.
     * Only primitive types are supported as values (float, long, String) and lists of (float, long, String)
     */
    @Builder.Default
    private List<Map<String, Object>> features = Collections.emptyList();

}

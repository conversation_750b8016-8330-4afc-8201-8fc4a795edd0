package com.grubhub.garcon.controlplanerpc.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

import java.util.Collections;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class FlowResponseRpc {

    private String flowName;
    private String flowRequestId;
    private String trackingId;
    private String flowId;
    private String flowSet;
    private MatchingStrategyRpc matchingStrategy;
    private RoutingGroupResponseRpc routingGroup;
    private String callerTrackingId;

    @Builder.Default
    private FlowResolutionStatus flowResolutionStatus = FlowResolutionStatus.RESOLVED;

    @Builder.Default
    private List<FeatureRpc> features = Collections.emptyList();

}

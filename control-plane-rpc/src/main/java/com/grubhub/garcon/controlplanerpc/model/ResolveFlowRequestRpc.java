package com.grubhub.garcon.controlplanerpc.model;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.Range;

import java.util.Set;

@Getter
@Builder(toBuilder = true)
@RequiredArgsConstructor
@ToString
public class ResolveFlowRequestRpc {
    private final String applicationId;
    private final String applicationVersion;
    private final String dinerId;
    @NotEmpty(message = "Flow set is required")
    private final String flowSet;
    @Range(min = -90, max = 90)
    private final double lat;
    @Range(min = -180, max = 180)
    private final double lng;
    private final int totalOrders;
    private final String variationId;
    private final String orderType;
    @Builder.Default
    private final String queryText = "";
    private final String whenFor;
    private final String callerTrackingId;
    private final Set<String> requestTags;
    private final String clientEntityId;
}

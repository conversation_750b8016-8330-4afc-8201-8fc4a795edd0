package com.grubhub.garcon.controlplanerpc.model;

import io.vavr.collection.Map;
import io.vavr.collection.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ModelRpc implements Serializable {

    private static final long serialVersionUID = -9197463857997289214L;
    private static final String DYNAMIC = "DYNAMIC";
    private static final String ENABLED = "ENABLED";

    private String modelName;
    private String modelDescription;

    private String modelType;
    private String location;
    private String version;
    private String versioningStrategy;
    private String status;

    private List<FeatureRpc> modelFeatures;

    private List<ModelOutputRpc> modelOutputs;

    private String versioningModelName;

    private Set<String> processedFeaturesFilter;

    private String updatedUser;

    private Instant updatedTimestamp;

    private Map<String, String> processedFeaturesMapping;

    private String servingLocation;

    private Boolean tfUseExamplesSerialization;

    private String tfExamplesSerializationName;

}

package com.grubhub.garcon.controlplanerpc.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.List;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MatchRpc {
    private String marketId;
    private String geohash;
    private String dinerType;
    private String orderType;
    private String mealtime;
    private String applicationType;
    private String queryType;
    private List<String> queryTokens;
    private String variationId;
    private Set<String> queryKeywords;
}

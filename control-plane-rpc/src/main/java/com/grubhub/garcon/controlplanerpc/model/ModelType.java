package com.grubhub.garcon.controlplanerpc.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * When updating model types here, also update ModelInferenceType.java
 */
@RequiredArgsConstructor
@Getter
public enum ModelType {

    TENSOR_FLOW,
    FUNCTION,
    QUERY_EXPANSION_LLM,
    SORT_ASC,
    SORT_DESC,
    DISTANCE_FALLOFF,
    LINEAR_COMBINATION,
    FEATURE<PERSON>_FETCH,
    RA<PERSON><PERSON>_SORT,
    RANDOM_SAMPLE,
    RA<PERSON>OM_DISTRIBUTION,
    SEARCH_EMBEDDINGS,
    TEXT_SIMILARITY,
    VECTOR_COSINE_SIMILARITY,
    SCORE_AGGREGATION,
    RANKING_POST_PROCESSING,
    RULE_MATCHING,
    PRECOMPUTED_RANKER,
    SAGE_MAKER;

    public boolean isEqualTo(String modelType) {
        return ModelType.valueOf(modelType) == this;
    }

}

package com.grubhub.garcon.controlplanerpc.model;

import lombok.*;

import java.util.Collections;
import java.util.List;

/**
 * Request to invoke a sequence of models
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class ModelInferenceSequenceRequestRpc {

    /**
     * ID provided by caller that will be logged for tracking purposes
     */
    private String callerTrackingId;

    /**
     * List containing the models and their attributes in the sequence
     */
    @Builder.Default
    private List<ModelInferenceSequenceItemRpc> invocations = Collections.emptyList();

}

package com.grubhub.garcon.controlplanerpc.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Output of a model invocation.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@With
public class ModelInferenceOutputRpc {

    /**
     * One entry for each output.
     * Use the helper-get methods of this class to extract the right type of each output.
     * See supported output types in enum: ModelOutputType
     * The order of each element on the outputs lists is the same as in ModelInferenceRequestRpc.features
     */
    private Map<String, List<ModelInferenceOutputTypeRpc>> response;

    /**
     * Get response with the given name and of type FLOAT
     */
    @JsonIgnore
    public List<Float> getFloatResponse(String outputName) {
        return response.get(outputName).stream().map(ModelInferenceOutputTypeRpc::getFloat).collect(Collectors.toList());
    }

    /**
     * Get response with the given name and of type STRING
     */
    @JsonIgnore
    public List<String> getStringResponse(String outputName) {
        return response.get(outputName).stream().map(ModelInferenceOutputTypeRpc::getString).collect(Collectors.toList());
    }

    /**
     * Get response with the given name and of type LONG
     */
    @JsonIgnore
    public List<Long> getLongResponse(String outputName) {
        return response.get(outputName).stream().map(ModelInferenceOutputTypeRpc::getLong).collect(Collectors.toList());
    }

    /**
     * Get response with the given name and of type FLOAT_ARRAY
     */
    @JsonIgnore
    public List<List<Float>> getFloatArrayResponse(String outputName) {
        return response.get(outputName).stream().map(ModelInferenceOutputTypeRpc::getFloatArray).collect(Collectors.toList());
    }

    /**
     * Get response with the given name and of type STRING_ARRAY
     */
    @JsonIgnore
    public List<List<String>> getStringArrayResponse(String outputName) {
        return response.get(outputName).stream().map(ModelInferenceOutputTypeRpc::getStringArray).collect(Collectors.toList());
    }

    /**
     * Get response with the given name and of type LONG_ARRAY
     */
    @JsonIgnore
    public List<List<Long>> getLongArrayResponse(String outputName) {
        return response.get(outputName).stream().map(ModelInferenceOutputTypeRpc::getLongArray).collect(Collectors.toList());
    }
}

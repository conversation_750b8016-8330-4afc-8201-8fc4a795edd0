package com.grubhub.garcon.controlplanerpc.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class ModelNestedOutputInfoRpc {

    private String featureName;
    private Integer sourceItemIndex;
    private String sourceOutputName;
    private ModelNestedOutputPlacementRpc placement;
    private ModelNestedOutputTransformationRpc transformation;
}

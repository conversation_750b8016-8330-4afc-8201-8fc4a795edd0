package com.grubhub.garcon.controlplanerpc.model;

import com.google.common.collect.Streams;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.grubhub.garcon.controlplanerpc.model.ModelNestedOutputPlacementRpc.FEATURE;
import static com.grubhub.garcon.controlplanerpc.model.ModelNestedOutputTransformationRpc.NONE;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class ModelInferenceSequenceItemRpc {

    /**
     * Name of the model in the sequence to be invoked
     */
    private String modelName;

    /**
     * Common features to all rows in features list.
     * Only primitive types are supported as values (float, long, String) and lists of (float, long, String)
     */
    @Builder.Default
    private Map<String, Object> globalFeatures = Collections.emptyMap();

    /**
     * Each individual invocation (samples) to send to this model.
     * Each row contains a Map of key-value features.
     * Only primitive types are supported as values (float, long, String) and lists of (float, long, String)
     */
    @Builder.Default
    private List<Map<String, Object>> features = Collections.emptyList();

    /**
     * Specific outputs that will be taken from the previous item in the sequence
     * Key is the output from the previous model and becomes the value to be put into each row of features
     * Value becomes the new key of the feature attribute to be put into each row of features
     */
    @Builder.Default
    private Map<String, String> nestedOutputs = Collections.emptyMap();

    private List<ModelNestedOutputInfoRpc> nestedOutputInfo;

    private ExecutionConditionRpc preCondition;


    public void populateNestedOutputInfo() {
        if (nestedOutputInfo != null && !nestedOutputInfo.isEmpty()) {
            return;
        }
        List<ModelNestedOutputInfoRpc> newNestedOutputInfo = Streams.zip(getEntriesStream(nestedOutputs), getCounterStream(nestedOutputs),
                        this::buildNestedOutputInfo)
                .collect(Collectors.toList());
        this.nestedOutputInfo = newNestedOutputInfo;
    }

    private Stream<Map.Entry<String, String>> getEntriesStream(Map<String, String> outputs) {
        return outputs.entrySet().stream();
    }

    private Stream<Integer> getCounterStream(Map<String, String> outputs) {
        return IntStream.range(0, outputs.size()).boxed();
    }

    private ModelNestedOutputInfoRpc buildNestedOutputInfo(Map.Entry<String, String> entry, Integer counter) {
        return ModelNestedOutputInfoRpc.builder()
                .featureName(entry.getKey())
                .sourceItemIndex(counter == 0 ? null : counter - 1)
                .placement(FEATURE)
                .transformation(NONE)
                .build();
    }
}

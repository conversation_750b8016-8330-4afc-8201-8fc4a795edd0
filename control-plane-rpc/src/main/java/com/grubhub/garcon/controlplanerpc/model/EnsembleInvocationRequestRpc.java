package com.grubhub.garcon.controlplanerpc.model;

import lombok.*;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class EnsembleInvocationRequestRpc {

    /**
     * ID provided by caller that will be logged for tracking purposes
     */
    private String callerTrackingId;

    /**
     * Name of the ensemble to be invoked
     */
    private String ensembleName;

    /**
     * Name of the set of weights that we want to use to combine all the underlying models of this ensemble
     */
    private String ensembleWeight;

    /**
     * Name of the function that we want to use to combine all the underlying models of this ensemble
     */
    private String ensembleFunction;

    /**
     * Name of the strategy that we want to use for this ensemble (functions or weights, default is weights)
     */

    private String ensembleStrategy;

    /**
     * Common features to all rows in features list
     */
    private Map<String, Object> globalFeatures;

    /**
     * Each individual invocation (samples) to send to this model.
     * Each row contains a Map of key-value features.
     * Only primitive types are supported as values (float, long, String) and lists of (float, long, String)
     */
    private List<Map<String, Object>> features;
}

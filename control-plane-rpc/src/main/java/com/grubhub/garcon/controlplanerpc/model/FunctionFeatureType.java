package com.grubhub.garcon.controlplanerpc.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.EnumSet;

@RequiredArgsConstructor
@Getter
public enum FunctionFeatureType {

    COORDINATES_TO_GEOHASH,
    GEOHASH_TO_COORDINATES,
    STR_LOWER,
    TEXT_NORM_PORTER_STEMMER,
    STR_COALESCE,
    STR_CONCAT,
    STR_CONCAT3,
    STR_CONCAT4,
    STR_MAP;

    public static FunctionFeatureType valueOfIgnoreCase(String functionName) {
        return EnumSet.allOf(FunctionFeatureType.class).stream()
                .filter(type -> type.name().equalsIgnoreCase(functionName))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(String.format("Function name=%s is not a valid FeatureFunctionType", functionName)));
    }

    public static boolean isValidFunctionType(String functionName) {
        return EnumSet.allOf(FunctionFeatureType.class).stream()
                .anyMatch(type -> type.name().equalsIgnoreCase(functionName));
    }
}

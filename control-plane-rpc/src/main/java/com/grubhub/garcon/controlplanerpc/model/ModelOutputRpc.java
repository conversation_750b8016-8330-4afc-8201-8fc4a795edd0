package com.grubhub.garcon.controlplanerpc.model;

import io.vavr.collection.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class ModelOutputRpc implements Serializable {
    private String outputName;
    private String outputType;
    private List<Integer> outputShape;
    private Boolean normalized;
    private Double normalizedAvg;
    private Double normalizedStd;
    private String normalizedType;
    @Builder.Default
    private Double normalizedMin = 0d;
    @Builder.Default
    private Double normalizedMax = 1d;
    @Builder.Default
    private Double normalizedRangeMin = 0d;
    @Builder.Default
    private Double normalizedRangeMax = 1d;
}

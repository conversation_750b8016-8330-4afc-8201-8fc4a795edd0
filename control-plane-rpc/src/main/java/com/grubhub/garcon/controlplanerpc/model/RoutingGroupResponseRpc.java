package com.grubhub.garcon.controlplanerpc.model;

import lombok.*;

import java.util.HashMap;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class RoutingGroupResponseRpc {
    private String groupName;
    private double routingPercentage;
    private double routingRand;
    private String variation;
    private String ensembleName;
    private String ensembleWeight;
    private String ensembleFunction;
    private String ensembleStrategy;
    private Map<String, String> groupProperties = new HashMap<>();
    private boolean ensembleIsModel;
}

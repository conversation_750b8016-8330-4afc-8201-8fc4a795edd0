package com.grubhub.garcon.controlplanerpc.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class MatchingStrategyRpc {
    private String type;
    private List<String> markets;
    private List<String> matchingDinerTypes;
    private MatchRpc match;
}

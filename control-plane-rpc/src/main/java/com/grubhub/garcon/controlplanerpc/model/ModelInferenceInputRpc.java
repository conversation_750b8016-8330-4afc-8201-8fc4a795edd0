package com.grubhub.garcon.controlplanerpc.model;

import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ModelInferenceInputRpc {
    @NotNull(message = "Model should be provided")
    private ModelRpc model;
    private List<Map<String, Object>> processedFeatures;
}

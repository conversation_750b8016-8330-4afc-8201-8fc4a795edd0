package com.grubhub.garcon.controlplanerpc.model;

import lombok.*;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@With
public class FeatureRpc implements Serializable {
    private String featureName;
    private String majorVersion;
    private String minorVersion;
    private String featureSourceType;
    private String featureLocation;
    private String status;
    private Set<String> featureStoreFields;
    private Boolean featureOptional;
    private String featureDefaultValue;
    private String featureDefaultType;
    private String functionName;
    private Map<String, String> functionInputs;
    private Map<String, String> functionOutputs;
}


package com.grubhub.garcon.controlplanerpc.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ModelInferenceOutputTypeRpc {

    private Object value;

    /**
     * Constructor for json deserialization, do not use
     */
    public ModelInferenceOutputTypeRpc() {
        this("");
    }

    public ModelInferenceOutputTypeRpc(Object value) {
        this.validate(value);
        this.value = value;
    }

    private void validate(Object value) {
        if (!(value instanceof Float) && !(value instanceof String) && !(value instanceof Long) && !(value instanceof List)) {
            throw new RuntimeException(String.format("Invalid value_type=%s, class=%s", value, value != null ? value.getClass() : null));
        }
    }

    public Object getValue() {
        return value;
    }

    /**
     * Setter for json deserialization, do not use
     */
    public void setValue(Object value) {
        this.value = value;
    }

    @JsonIgnore
    public float getFloat() {
        try {
            if (this.value instanceof Float) {
                return (float) this.value;
            } else if (this.value instanceof Long) {
                return ((Long) this.value).floatValue();
            } else if (this.value instanceof Integer) {
                return ((Integer) this.value).floatValue();
            } else if (this.value instanceof Double) {
                return ((Double) this.value).floatValue();
            }
            log.warn("The value={} is not of type Float so the next cast it will fail!!!", value);
            return (float) this.value;
        } catch (Exception e) {
            throw new RuntimeException(String.format("Could not cast model_output_value=%s to=FLOAT", this.value));
        }
    }

    @JsonIgnore
    public String getString() {
        try {
            return String.valueOf(this.value);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Could not cast model_output_value=%s to=STRING", this.value));
        }
    }

    @JsonIgnore
    public long getLong() {
        try {
            if (this.value instanceof Long) {
                return (long) this.value;
            } else if (this.value instanceof Integer) {
                return ((Integer) this.value).longValue();
            } else if (this.value instanceof Float) {
                return ((Float) this.value).longValue();
            } else if (this.value instanceof Double) {
                return ((Double) this.value).longValue();
            }
            log.warn("The value={} is not of type Long so the next cast it may fail!!!", value);
            return (long) this.value;
        } catch (Exception e) {
            throw new RuntimeException(String.format("Could not cast model_output_value=%s to=LONG", this.value));
        }
    }

    @JsonIgnore
    public List<Float> getFloatArray() {
        if (this.value == null || ((List<Float>) this.value).isEmpty()) {
            return Collections.emptyList();
        }

        try {
            if (((List<?>) this.value).get(0) instanceof Float) {
                return (List<Float>) this.value;
            } else if (((List<?>) this.value).get(0) instanceof Double) {
                return ((List<Double>) this.value).stream().map((Double num) -> Float.valueOf(num.floatValue()))
                        .collect(Collectors.toList());
            }
            log.warn("The list of values value={} is not of type Float so the next cast it may fail!!!", value);
            return (List<Float>) this.value;
        } catch (Exception e) {
            throw new RuntimeException(String.format("Could not cast model_output_value=%s to=FLOAT_ARRAY", this.value));
        }
    }

    @JsonIgnore
    public List<Long> getLongArray() {
        if (this.value == null || ((List<Long>) this.value).isEmpty()) {
            return Collections.emptyList();
        }
        try {
            if (((List<?>) this.value).get(0) instanceof Long) {
                return (List<Long>) this.value;
            } else if (((List<?>) this.value).get(0) instanceof Double) {
                return ((List<Double>) this.value).stream().map(Double::longValue)
                        .collect(Collectors.toList());
            } else if (((List<?>) this.value).get(0) instanceof Integer) {
                return ((List<Integer>) this.value).stream().map(Integer::longValue)
                        .collect(Collectors.toList());
            } else if (((List<?>) this.value).get(0) instanceof Float) {
                return ((List<Float>) this.value).stream().map(Float::longValue)
                        .collect(Collectors.toList());
            }

            log.warn("The list of values value={} is not of type Long so the next cast it may fail!!!", value);
            return (List<Long>) this.value;
        } catch (Exception e) {
            throw new RuntimeException(String.format("Could not cast model_output_value=%s to=LONG_ARRAY", this.value));
        }
    }

    @JsonIgnore
    public List<String> getStringArray() {
        if (this.value == null || ((List<String>) this.value).isEmpty()) {
            return Collections.emptyList();
        }

        try {
            return (((List<?>) this.value).stream().map(String::valueOf).collect(Collectors.toList()));
        } catch (Exception e) {
            throw new RuntimeException(String.format("Could not cast model_output_value=%s to=STRING_ARRAY", this.value));
        }
    }
}

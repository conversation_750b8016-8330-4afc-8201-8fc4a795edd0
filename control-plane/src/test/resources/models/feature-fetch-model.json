{"model_name": "{{modelName}}", "model_description": "test_features_fetch_robert", "model_type": "FEATURES_FETCH", "signature_def": "<PENDING>", "location": "", "version": "1.0", "versioning_strategy": "STATIC", "status": "ENABLED", "versioning_model_name": "test_features_fetch_robert", "updated_user": "mleone", "serving_location": "", "model_features": [{"feature_name": "key", "feature_key_type": "DINER_ID", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": ""}, {"feature_name": "test_features_fetch_robert_feature", "major_version": "1", "minor_version": "0", "feature_key_type": "DINER_ID", "feature_source_type": "FEATURE_STORE_INTERNAL", "feature_location": "", "status": "PENDING", "feature_store_fields": ["key"], "feature_optional": true, "feature_default_value": "{\"number\": 0}"}], "model_outputs": [{"output_name": "number", "output_type": "FLOAT", "output_shape": [1], "normalized": false}]}
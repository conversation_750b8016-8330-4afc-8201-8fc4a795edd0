{"model_name": "search_intentcandidates_diner2vec_v3.6", "model_description": "Search intentful candidate selection - Model to get diner/query embedding vector - Model None", "model_type": "TENSOR_FLOW", "location": "s3://grubhub-gdp-data-transfer-dev/roux-model-serving/ddml_control_plane/models/search_intentcandidates_diner2vec/model/", "version": "3.6", "versioning_strategy": "DYNAMIC", "status": "PENDING", "model_features": [{"feature_name": "app", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "feature_default_type": "STRING", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "cuisine_filter", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "feature_default_type": "NULL", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "diner_geohash_4", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "feature_default_type": "STRING", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "mealtime", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "feature_default_type": "STRING", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "query_text", "feature_source_type": "RUNTIME", "status": "DISABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "feature_default_type": "STRING", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "search_intentcandidates_diner2vec_restaurant_feature", "major_version": "3", "minor_version": "5", "feature_source_type": "FEATURE_STORE_INTERNAL", "feature_location": "s3://grubhub-gdp-data-transfer-dev/roux-model-serving/ddml_control_plane/models/search_intentcandidates_diner2vec/search_intentcandidates_diner2vec_restaurant_feature/", "status": "ENABLED", "feature_store_fields": ["RESTAURANT_ID"], "feature_optional": true, "feature_default_value": "{}", "feature_order": 0, "skip_fetching": true, "function_inputs": {}, "function_outputs": {}}], "model_outputs": [{"output_name": "query_net", "output_type": "FLOAT_ARRAY", "output_shape": [32], "normalized": false}], "versioning_model_name": "search_intentcandidates_diner2vec", "processed_features_filter": [], "updated_user": "GDP_ACTION", "updated_timestamp": "2022-04-09T06:47:05.933Z", "processed_features_mapping": {}, "serving_location": "s3://vnwre0qnbk1qy9zw-ddml-models/modelserving/roux-model-serving/ddml_control_plane/models/search_intentcandidates_diner2vec/3", "tf_use_examples_serialization": true, "tf_examples_serialization_name": "examples", "model_group": "search", "search_embeddings_index_names": [], "max_dynamic_models": 2, "model_category": "search_candidate_selection"}
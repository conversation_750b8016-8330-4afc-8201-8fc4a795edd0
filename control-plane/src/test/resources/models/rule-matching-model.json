{"model_name": "multi_page_menu_experience_v1.0", "model_description": "Multi Page Menu Experience for Verticals", "model_type": "RULE_MATCHING", "version": "1.0", "versioning_strategy": "STATIC", "status": "ENABLED", "model_features": [{"feature_name": "rules", "major_version": null, "minor_version": null, "feature_source_type": "RUNTIME", "feature_location": null, "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "{\"HAS_MULTI_PAGE_EXPERIENCE\":\"!isEmpty(merchant_types) && menu_items_count >= expected_menu_items_count && menu_items_image_coverage >= expected_menu_items_image_coverage\",\"ILLUSTRATIVE_MENU_EXPERIENCE\":\"!isEmpty(merchant_types) && menu_items_count >= expected_menu_items_count && menu_items_image_coverage >= expected_menu_items_image_coverage && categories_image_coverage >= expected_categories_image_coverage\"}", "feature_default_type": "STRING", "feature_order": null, "skip_fetching": false, "expose_feature_store_fields_as_feature": false, "function_name": null, "function_inputs": {}, "function_outputs": {}, "function_scope": null}, {"feature_name": "merchant_id", "major_version": null, "minor_version": null, "feature_source_type": "RUNTIME", "feature_location": null, "status": "ENABLED", "feature_store_fields": [], "feature_optional": false, "feature_default_value": "none_supplied", "feature_default_type": "STRING", "feature_order": null, "skip_fetching": false, "expose_feature_store_fields_as_feature": false, "function_name": null, "function_inputs": {}, "function_outputs": {}, "function_scope": null}, {"feature_name": "merchant_types", "major_version": null, "minor_version": null, "feature_source_type": "RUNTIME", "feature_location": null, "status": "ENABLED", "feature_store_fields": [], "feature_optional": false, "feature_default_value": "", "feature_default_type": "STRING", "feature_order": null, "skip_fetching": false, "expose_feature_store_fields_as_feature": false, "function_name": null, "function_inputs": {}, "function_outputs": {}, "function_scope": null}, {"feature_name": "menu_items_count", "major_version": null, "minor_version": null, "feature_source_type": "RUNTIME", "feature_location": null, "status": "ENABLED", "feature_store_fields": [], "feature_optional": false, "feature_default_value": "-1", "feature_default_type": "INTEGER", "feature_order": null, "skip_fetching": false, "expose_feature_store_fields_as_feature": false, "function_name": null, "function_inputs": {}, "function_outputs": {}, "function_scope": null}, {"feature_name": "menu_items_image_coverage", "major_version": null, "minor_version": null, "feature_source_type": "RUNTIME", "feature_location": null, "status": "ENABLED", "feature_store_fields": [], "feature_optional": false, "feature_default_value": "-1", "feature_default_type": "INTEGER", "feature_order": null, "skip_fetching": false, "expose_feature_store_fields_as_feature": false, "function_name": null, "function_inputs": {}, "function_outputs": {}, "function_scope": null}, {"feature_name": "categories_image_coverage", "major_version": null, "minor_version": null, "feature_source_type": "RUNTIME", "feature_location": null, "status": "ENABLED", "feature_store_fields": [], "feature_optional": false, "feature_default_value": "-1", "feature_default_type": "INTEGER", "feature_order": null, "skip_fetching": false, "expose_feature_store_fields_as_feature": false, "function_name": null, "function_inputs": {}, "function_outputs": {}, "function_scope": null}, {"feature_name": "expected_menu_items_count", "major_version": null, "minor_version": null, "feature_source_type": "RUNTIME", "feature_location": null, "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "250", "feature_default_type": "INTEGER", "feature_order": null, "skip_fetching": false, "expose_feature_store_fields_as_feature": false, "function_name": null, "function_inputs": {}, "function_outputs": {}, "function_scope": null}, {"feature_name": "expected_menu_items_image_coverage", "major_version": null, "minor_version": null, "feature_source_type": "RUNTIME", "feature_location": null, "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "80", "feature_default_type": "INTEGER", "feature_order": null, "skip_fetching": false, "expose_feature_store_fields_as_feature": false, "function_name": null, "function_inputs": {}, "function_outputs": {}, "function_scope": null}, {"feature_name": "expected_categories_image_coverage", "major_version": null, "minor_version": null, "feature_source_type": "RUNTIME", "feature_location": null, "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "80", "feature_default_type": "INTEGER", "feature_order": null, "skip_fetching": false, "expose_feature_store_fields_as_feature": false, "function_name": null, "function_inputs": {}, "function_outputs": {}, "function_scope": null}], "model_outputs": [{"output_name": "HAS_MULTI_PAGE_EXPERIENCE", "output_type": "STRING", "output_shape": [1], "normalized": false, "normalized_avg": null, "normalized_std": null, "normalized_type": null, "normalized_min": null, "normalized_max": null, "normalized_range_min": null, "normalized_range_max": null}, {"output_name": "ILLUSTRATIVE_MENU_EXPERIENCE", "output_type": "STRING", "output_shape": [1], "normalized": false, "normalized_avg": null, "normalized_std": null, "normalized_type": null, "normalized_min": null, "normalized_max": null, "normalized_range_min": null, "normalized_range_max": null}], "versioning_model_name": "rule_matching", "processed_features_filter": [], "processed_features_mapping": {}, "tf_use_examples_serialization": null, "tf_examples_serialization_name": null, "model_group": "search", "search_embeddings_index_names": [], "max_dynamic_models": 5, "model_category": "verticals_models", "search_embeddings_vector_dimension": null, "skip_age_reporting": false, "external_inference_feature_batch_size": null, "external_inference_feature_batch_enabled": false}
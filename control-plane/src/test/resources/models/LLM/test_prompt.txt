I want you to CLASSIFY and EXPAND Grubhub customers search queries according to the users intent.


The intent can only be one of: ADDRESS, ALCOHOL, CONVENIENCE, CUISINE, DIET, DISH, GROCERY, MEAL_TIME, PHARMACY, RES<PERSON><PERSON>AN<PERSON>, TOBACCO, OTHER
Provide 3 expansions for the following query. Return the 3 expansions.
Make them all in one line separated by commas.
Do not provide an explanation or preamble.
Query: {{query}}
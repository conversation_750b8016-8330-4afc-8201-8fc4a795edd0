{"model_name": "test_tensor_flow_model_2", "model_description": "Test TF model 2", "model_type": "TENSOR_FLOW", "versioning_model_name": "search_", "version": "3.124", "location": "s3://grubhub-gdp-data-transfer-dev/roux-model-serving/ddml_control_plane/models/search_intentcandidates_diner2vec/model/", "versioning_strategy": "DYNAMIC", "status": "ENABLED", "model_features": [{"feature_name": "runtime_feature_3", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "feature_default_type": "STRING", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "runtime_feature_4", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "feature_default_type": "NULL", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}], "model_outputs": [{"output_name": "output", "output_type": "FLOAT", "output_shape": [1], "normalized": false}], "processed_features_filter": [], "processed_features_mapping": {}, "tf_use_examples_serialization": true, "tf_examples_serialization_name": "examples", "model_group": "search", "search_embeddings_index_names": []}
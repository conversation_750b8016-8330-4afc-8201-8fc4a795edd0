{"model_name": "search_m1_v3.45", "model_description": "M1 - Order Max - Click LTR model", "model_type": "TENSOR_FLOW", "location": "s3://grubhub-gdp-data-transfer-dev/roux-model-serving/ddml_control_plane/models/search_m1/model/", "version": "3.45", "versioning_strategy": "DYNAMIC", "status": "ENABLED", "model_features": [{"feature_name": "DINER_ID", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "RESTAURANT_ID", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "app", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "iOS Native", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "cuisine_filter", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_type": "NULL", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "geo<PERSON>h", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "mealtime", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "dinner", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "query_text", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "", "skip_fetching": false, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "search_m1_diner_feature", "major_version": "3", "minor_version": "45", "feature_source_type": "FEATURE_STORE_INTERNAL", "feature_location": "s3://grubhub-gdp-data-transfer-dev/roux-model-serving/ddml_control_plane/models/search_m1/search_m1_diner_feature/", "status": "ENABLED", "feature_store_fields": ["DINER_ID"], "feature_optional": true, "feature_order": 1, "feature_default_value": "{\"diner_id\": \"\", \"user_search_tokens\": [], \"user_click_history\": [], \"user_order_history\": [], \"user_gotos\": []}", "skip_fetching": false, "expose_feature_store_fields_as_feature": true, "function_inputs": {}, "function_outputs": {}}, {"feature_name": "search_m1_restaurant_feature", "major_version": "3", "minor_version": "45", "feature_source_type": "FEATURE_STORE_INTERNAL", "feature_location": "s3://grubhub-gdp-data-transfer-dev/roux-model-serving/ddml_control_plane/models/search_m1/search_m1_restaurant_feature/", "status": "ENABLED", "feature_store_fields": ["RESTAURANT_ID"], "feature_optional": true, "feature_order": 2, "feature_default_value": "{\"rid\": \"\", \"name\": \"\", \"cuisines\": \"\", \"rest_geohash\": \"\", \"modified_cbsa_name\": \"\"}", "skip_fetching": false, "expose_feature_store_fields_as_feature": false, "function_inputs": {}, "function_outputs": {}}], "model_outputs": [{"output_name": "outputs", "output_type": "FLOAT", "output_shape": [1], "normalized": true, "normalized_avg": 268.2796677, "normalized_std": 1.55, "normalized_type": "STANDARD", "normalized_min": 195.1624451, "normalized_max": 262.2120056, "normalized_range_min": 0.0, "normalized_range_max": 1.0}], "versioning_model_name": "search_m1", "updated_user": "GDP_ACTION", "updated_timestamp": "2022-06-15T13:36:57.428Z", "processed_features_mapping": {}, "serving_location": "s3://vnwre0qnbk1qy9zw-ddml-models/modelserving/roux-model-serving/ddml_control_plane/models/search_m1/model/3", "tf_use_examples_serialization": true, "tf_examples_serialization_name": "inputs", "model_group": "search", "search_embeddings_index_names": []}
{"model_name": "{{modelName}}", "model_description": "test_knn", "versioning_model_name": "test_knn", "model_type": "SEARCH_EMBEDDINGS", "status": "ENABLED", "versioning_strategy": "DYNAMIC", "location": "s3://grubhub-gdp-data-transfer-dev/roux-model-serving/ddml_control_plane/models/test_knn/model/", "serving_location": "s3://vnwre0qnbk1qy9zw-ddml-models/modelserving/roux-model-serving/ddml_control_plane/models/test_knn/model/1", "version": "1.0", "signature_def": "<PENDING>", "search_embeddings_index_names": ["search-embeddings"], "model_features": [{"feature_name": "k", "feature_key_type": "DINER_ID", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "1000", "feature_default_type": "INTEGER"}, {"feature_name": "search_k", "feature_key_type": "DINER_ID", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": true, "feature_default_value": "3000", "feature_default_type": "INTEGER"}, {"feature_name": "query_vector", "feature_key_type": "DINER_ID", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": false}, {"feature_name": "index_name_list", "feature_key_type": "DINER_ID", "feature_source_type": "RUNTIME", "status": "ENABLED", "feature_store_fields": [], "feature_optional": false}, {"feature_name": "test_knn_mapping_feature", "major_version": "1", "minor_version": "0", "feature_key_type": "DINER_ID", "feature_source_type": "FEATURE_STORE_INTERNAL", "feature_location": "s3://grubhub-gdp-data-transfer-dev/roux-model-serving/ddml_control_plane/models/test_knn/test_knn_mapping_feature/", "status": "PENDING", "feature_store_fields": ["index_name", "entity_id"], "feature_optional": true, "feature_default_value": "{\"index_name\": \"\", \"entity_id\": \"\", \"domain_entity_id\": \"\"}", "skip_fetching": true}], "model_outputs": [{"output_name": "entities", "output_type": "STRING_ARRAY", "output_shape": [1], "normalized": false}, {"output_name": "scores", "output_type": "FLOAT_ARRAY", "output_shape": [1], "normalized": false}], "search_embeddings_index_name_list": ["master_index"], "search_embeddings_vector_dimension": 5, "max_dynamic_models": 2}
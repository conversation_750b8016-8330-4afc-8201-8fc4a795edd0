FROM gradle:6.9.4-jdk11 AS builder
RUN mkdir -p /app/control-plane-dependencies
ENV APP_HOME=/usr/app/
WORKDIR $APP_HOME

COPY --chown=gradle:gradle . /home/<USER>/ddml-control-plane
USER root
WORKDIR /home/<USER>/ddml-control-plane
RUN chown -R gradle /home/<USER>/ddml-control-plane
RUN /home/<USER>/ddml-control-plane/gradlew control-plane:download && \
  unzip /home/<USER>/ddml-control-plane/control-plane/build/libs/tensorflow-serving-client/tensorflow-serving-client-*.jar -d /app/proto

EXPOSE 50000 8888
FROM adven27/grpc-wiremock
COPY --from=builder /app/proto/tensorflow_serving /proto/tensorflow_serving
COPY --from=builder /app/proto/tensorflow/core /proto/tensorflow/core

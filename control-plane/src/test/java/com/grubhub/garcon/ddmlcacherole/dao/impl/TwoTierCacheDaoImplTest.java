package com.grubhub.garcon.ddmlcacherole.dao.impl;

import com.google.common.base.Optional;
import com.grubhub.garcon.controlplane.config.TwoTierCacheConfig;
import com.grubhub.garcon.ddmlcacherole.dao.CacheDao;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.grubhub.garcon.controlplane.services.common.service.CacheKeyProvider.FLOW;
import static com.grubhub.garcon.controlplane.services.common.service.CacheKeyProvider.MODEL;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

public class TwoTierCacheDaoImplTest {

    private CacheDao subject;

    @Mock
    private CacheDao secondTierDao;
    @Mock
    private CacheDao firstTierDao;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        TwoTierCacheConfig config = new TwoTierCacheConfig();
        config.setEnabled(false); // Disabled by default.
        config.setEntityLevel(new HashMap<>());

        when(firstTierDao.isEnabled()).thenReturn(true);
        when(secondTierDao.isEnabled()).thenReturn(true);

        subject = new TwoTierCacheDaoImpl(secondTierDao, firstTierDao, config);
    }

    @Test
    public void getAsync_getFuture_twoTierDisabled() throws ExecutionException, InterruptedException {
        final String key = "cacheKey";
        final Optional<String> expectedResult = Optional.of("flow_data");

        when(secondTierDao.getAsync(key, String.class, FLOW)).thenReturn(CompletableFuture.completedFuture(expectedResult));

        CompletableFuture<Optional<String>> result = subject.getAsync(key, String.class, FLOW);

        assertNotNull(result);
        assertEquals(expectedResult, result.get());

        verify(secondTierDao, times(1)).getAsync(key, String.class, FLOW);
        verify(firstTierDao, never()).getAsync(anyString(), any(), anyString());
        verify(firstTierDao, never()).putAsync(anyString(), any(), anyString());
    }

    @Test
    public void getAsync_getFuture_twoTierEnabledButNotForEntity() throws ExecutionException, InterruptedException {
        // We are enabling two-tier caching for flows, but this test sets a model invocation.
        enableTwoTierCacheForFlow();

        final String key = "cacheKey";
        final Optional<String> expectedResult = Optional.of("model_data");

        when(secondTierDao.getAsync(key, String.class, MODEL)).thenReturn(CompletableFuture.completedFuture(expectedResult));

        CompletableFuture<Optional<String>> result = subject.getAsync(key, String.class, MODEL);

        assertNotNull(result);
        assertEquals(expectedResult, result.get());

        verify(secondTierDao, times(1)).getAsync(key, String.class, MODEL);
        verify(firstTierDao, never()).getAsync(anyString(), any(), anyString());
        verify(firstTierDao, never()).putAsync(anyString(), any(), anyString());
    }

    @Test
    public void getAsync_getFuture_twoTierEnabledButFistTierDisabled() throws ExecutionException, InterruptedException {
        // We are enabling two-tier caching for flows, but this test sets a model invocation.
        enableTwoTierCacheForFlow();
        when(firstTierDao.isEnabled()).thenReturn(false);

        final String key = "cacheKey";
        final Optional<String> expectedResult = Optional.of("flow_data");

        when(secondTierDao.getAsync(key, String.class, FLOW)).thenReturn(CompletableFuture.completedFuture(expectedResult));

        CompletableFuture<Optional<String>> result = subject.getAsync(key, String.class, FLOW);

        assertNotNull(result);
        assertEquals(expectedResult, result.get());

        verify(secondTierDao, times(1)).getAsync(key, String.class, FLOW);
        verify(firstTierDao, never()).getAsync(anyString(), any(), anyString());
        verify(firstTierDao, never()).putAsync(anyString(), any(), anyString());
    }

    @Test
    public void getAsync_twoTierEnabled_firstTierReturns() throws ExecutionException, InterruptedException {
        enableTwoTierCacheForFlow();

        final String key = "cacheKey";
        final Optional<String> expectedResult = Optional.of("flow_data");

        when(firstTierDao.getAsync(key, String.class, FLOW)).thenReturn(CompletableFuture.completedFuture(expectedResult));

        CompletableFuture<Optional<String>> result = subject.getAsync(key, String.class, FLOW);

        assertNotNull(result);
        assertEquals(expectedResult, result.get());

        verify(firstTierDao, times(1)).getAsync(key, String.class, FLOW);
        verify(secondTierDao, never()).getAsync(anyString(), any(), anyString());
        verify(firstTierDao, never()).putAsync(anyString(), any(), anyString());
    }

    @Test
    public void getAsync_twoTierEnabled_secondTierReturns() throws ExecutionException, InterruptedException {
        enableTwoTierCacheForFlow();

        final String key = "cacheKey";
        final Optional<String> expectedResult = Optional.of("flow_data");

        // First tier returns empty.
        when(firstTierDao.getAsync(key, String.class, FLOW)).thenReturn(CompletableFuture.completedFuture(Optional.absent()));
        when(secondTierDao.getAsync(key, String.class, FLOW)).thenReturn(CompletableFuture.completedFuture(expectedResult));

        CompletableFuture<Optional<String>> result = subject.getAsync(key, String.class, FLOW);

        assertNotNull(result);
        assertEquals(expectedResult, result.get());

        verify(firstTierDao, times(1)).getAsync(key, String.class, FLOW);
        verify(secondTierDao, times(1)).getAsync(key, String.class, FLOW);
        verify(firstTierDao, times(1)).putAsync(key, expectedResult, FLOW);
    }

    @Test
    public void getAsync_twoTierEnabled_bothTierEmpty() throws ExecutionException, InterruptedException {
        enableTwoTierCacheForFlow();

        final String key = "cacheKey";
        final Optional<String> expectedResult = Optional.absent();

        // First tier returns empty.
        when(firstTierDao.getAsync(key, String.class, FLOW)).thenReturn(CompletableFuture.completedFuture(Optional.absent()));
        when(secondTierDao.getAsync(key, String.class, FLOW)).thenReturn(CompletableFuture.completedFuture(Optional.absent()));

        CompletableFuture<Optional<String>> result = subject.getAsync(key, String.class, FLOW);

        assertNotNull(result);
        assertEquals(expectedResult, result.get());

        verify(firstTierDao, times(1)).getAsync(key, String.class, FLOW);
        verify(secondTierDao, times(1)).getAsync(key, String.class, FLOW);
        verify(firstTierDao, never()).putAsync(key, expectedResult, FLOW);
    }

    @Test
    public void putAsync_checkInvocation_twoTierDisabled() {
        final String key = "cacheKey";
        final String value = "value";
        subject.putAsync(key, value, FLOW);

        verify(secondTierDao, times(1)).putAsync(key, value, FLOW);
        verify(firstTierDao, never()).putAsync(anyString(), any(), anyString());
    }

    @Test
    public void putAsync_checkInvocation_twoTierEnabled() {
        enableTwoTierCacheForFlow();

        final String key = "cacheKey";
        final String value = "value";
        subject.putAsync(key, value, FLOW);

        verify(secondTierDao, times(1)).putAsync(key, value, FLOW);
        verify(firstTierDao, times(1)).putAsync(key, value, FLOW);
    }

    @Test
    public void deleteBatch_checkInvocation_twoTierDisabled() {
        final List<String> keys = List.of("cacheKey1", "cacheKey2");
        subject.deleteBatch(keys, FLOW);

        verify(secondTierDao, times(1)).deleteBatch(keys, FLOW);
        verify(firstTierDao, never()).deleteBatch(anyList(), anyString());
    }

    @Test
    public void deleteBatch_checkInvocation_twoTierEnabled() {
        enableTwoTierCacheForFlow();

        final List<String> keys = List.of("cacheKey1", "cacheKey2");
        subject.deleteBatch(keys, FLOW);

        verify(secondTierDao, times(1)).deleteBatch(keys, FLOW);
        verify(firstTierDao, times(1)).deleteBatch(keys, FLOW);
    }

    @Test
    public void deleteBatch_checkInvocation_twoTierEnabled_firstTierFails_bothCalled() {
        enableTwoTierCacheForFlow();
        final List<String> keys = List.of("cacheKey1", "cacheKey2");

        doThrow(new RuntimeException()).when(firstTierDao).deleteBatch(keys, FLOW);

        subject.deleteBatch(keys, FLOW);

        verify(secondTierDao, times(1)).deleteBatch(keys, FLOW);
        verify(firstTierDao, times(1)).deleteBatch(keys, FLOW);
    }

    private void enableTwoTierCacheForFlow() {
        TwoTierCacheConfig config = new TwoTierCacheConfig();
        config.setEnabled(true);

        Map<String, Boolean> configMap = new HashMap<>();
        configMap.put(FLOW, true); // Enabling flow two-tier cache.
        config.setEntityLevel(configMap);

        subject = new TwoTierCacheDaoImpl(secondTierDao, firstTierDao, config);
    }
}

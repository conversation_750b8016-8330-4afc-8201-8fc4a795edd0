package com.grubhub.garcon.ddmlcacherole.dao.impl;

import com.google.common.base.Optional;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.SettableFuture;
import com.grubhub.garcon.cacherole.Cacherole;
import com.grubhub.garcon.ddmlcacherole.dao.CacheDao;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.grubhub.garcon.controlplane.services.common.service.CacheKeyProvider.FLOW;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@Slf4j
public class SimpleCacheDaoTest {

    private CacheDao subject;

    @Mock
    Cacherole cacherole;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        subject = new DdmlConfigCacheDaoExternal(cacherole);
    }

    @Test
    public void getAsync_getFuture_success() throws ExecutionException, InterruptedException {
        final String key = "cacheKey";
        final Optional<String> expectedResult = Optional.of("flow_data");

        // Create listenable future expected result.
        SettableFuture<Optional<String>> settableFuture = SettableFuture.create();
        settableFuture.set(expectedResult);
        ListenableFuture<Optional<String>> listenableFuture = settableFuture;

        when(cacherole.getAsync(key, String.class)).thenReturn(listenableFuture);

        CompletableFuture<Optional<String>> result = subject.getAsync(key, String.class, FLOW);

        assertNotNull(result);
        assertEquals(expectedResult, result.get());

        verify(cacherole, times(1)).getAsync(key, String.class);
    }

    @Test
    public void getAsync_getFuture_exception() {
        final String key = "cacheKey";

        when(cacherole.getAsync(key, String.class)).thenThrow(new RuntimeException());

        assertThrows(RuntimeException.class, () -> subject.getAsync(key, String.class, FLOW));
        verify(cacherole, times(1)).getAsync(key, String.class);
    }

    @Test
    public void putAsync_checkInvocation_success() {
        final String key = "cacheKey";
        final String value = "value";
        subject.putAsync(key, value, FLOW);

        verify(cacherole, times(1)).putAsync(key, value);
    }

    @Test
    public void putAsync_checkInvocation_exception() {
        final String key = "cacheKey";
        final String value = "value";
        when(cacherole.putAsync(key, value)).thenThrow(new RuntimeException());

        assertThrows(RuntimeException.class, () -> subject.putAsync(key, value, FLOW));

        verify(cacherole, times(1)).putAsync(key, value);
    }

    @Test
    public void deleteBatch_checkInvocation_success() {
        final List<String> keys = List.of("cacheKey1", "cacheKey2");
        subject.deleteBatch(keys, FLOW);

        verify(cacherole, times(1)).deleteBatch(keys.toArray(String[]::new));
    }

}

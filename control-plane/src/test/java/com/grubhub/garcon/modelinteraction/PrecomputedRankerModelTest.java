package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplane.services.ensembler.impl.BaseEnsembleTest;
import com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.*;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import com.grubhub.garcon.modelinteraction.precomputedranker.PrecomputedMode;
import com.grubhub.garcon.modelinteraction.precomputedranker.PrecomputedRankerModel;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import lombok.val;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class PrecomputedRankerModelTest extends BaseEnsembleTest {

    String featureStoreKey = "RESTAURANT_ID";

    @Test
    void test_defaultRanker() {

        val model = createPrecomputedRankerModel();
        createTopMerchantsStoredFeatureValues(model);

        val globalFeatures = ModelFactory.invocationFeatureBuilder().build();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures(featureStoreKey, List.of("r1", "r2", "r3")))
                .build());

        String outputKey = "output";
        assertThat(modelOutput.getResponse().containsKey(outputKey)).isTrue();
        assertThat(modelOutput.getResponse().get(outputKey).isDefined()).isTrue();
        List<ModelInferenceOutputType> outputList = modelOutput.getResponse().get(outputKey).get();
        assertThat(outputList.size()).isEqualTo(3);
        assertThat(outputList.get(0).getFloat()).isEqualTo(0.888f);
        assertThat(outputList.get(1).getFloat()).isEqualTo(0.777f);
        assertThat(outputList.get(2).getFloat()).isEqualTo(0.999f); // all values returned
    }


    @Test
    void test_topNRanker() {

        val model = createPrecomputedRankerModel();
        createTopMerchantsStoredFeatureValues(model);

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(PrecomputedRankerModel.MODE, PrecomputedMode.SCORE_TOP_N.name()) //top_n mode set
                .add(PrecomputedRankerModel.TOP_N, "2")
                .build();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures(featureStoreKey, List.of("r1", "r2", "r3")))
                .build());

        String outputKey = "output";
        assertThat(modelOutput.getResponse().containsKey(outputKey)).isTrue();
        assertThat(modelOutput.getResponse().get(outputKey).isDefined()).isTrue();
        List<ModelInferenceOutputType> outputList = modelOutput.getResponse().get(outputKey).get();
        assertThat(outputList.size()).isEqualTo(3);
        assertThat(outputList.get(0).getFloat()).isEqualTo(0.888f);
        assertThat(outputList.get(1).getFloat()).isEqualTo(0.0f); // only top 2 values returned
        assertThat(outputList.get(2).getFloat()).isEqualTo(0.999f);
    }

    @Test
    void test_topNRanker_N_too_big() {

        val model = createPrecomputedRankerModel();
        createTopMerchantsStoredFeatureValues(model);

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(PrecomputedRankerModel.MODE, PrecomputedMode.SCORE_TOP_N.name()) //top_n mode set
                .add(PrecomputedRankerModel.TOP_N, "5")
                .build();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures(featureStoreKey, List.of("r1", "r2", "r3")))
                .build());

        String outputKey = "output";
        assertThat(modelOutput.getResponse().containsKey(outputKey)).isTrue();
        assertThat(modelOutput.getResponse().get(outputKey).isDefined()).isTrue();
        List<ModelInferenceOutputType> outputList = modelOutput.getResponse().get(outputKey).get();
        assertThat(outputList.size()).isEqualTo(3);
        assertThat(outputList.get(0).getFloat()).isEqualTo(0.888f);
        assertThat(outputList.get(1).getFloat()).isEqualTo(0.777f); // all returned
        assertThat(outputList.get(2).getFloat()).isEqualTo(0.999f);
    }


    private ModelDTO createPrecomputedRankerModel() {
        val model = ModelDTO
                .builder()
                .modelName("precomputed_ranker_v1.0")
                .modelDescription("precomputing scores")
                .versioningModelName("precomputed_ranker")
                .modelType(ModelType.PRECOMPUTED_RANKER.name())
                .status(ModelStatus.ENABLED.getName())
                .version("1.0")
                .versioningStrategy(ModelDTO.STATIC)
                .modelFeatures(List.of(
                        ModelFactory.buildRuntimeFeatureRequired(featureStoreKey),
                        ModelFactory.buildRuntimeFeatureDefaultStr(PrecomputedRankerModel.MODE, PrecomputedMode.SCORE_TOP_N.name()),
                        ModelFactory.buildRuntimeFeatureDefaultStr(PrecomputedRankerModel.TOP_N, "3"),
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr(featureStoreKey),
                        ModelFactory.buildInternalFeature("precomputed_ranker", "top_merchants_score_feature", 1, 1, 0,
                                false,
                                List.of(featureStoreKey),
                                HashMap.of(
                                        featureStoreKey, "",
                                        PrecomputedRankerModel.SCORE, 0.0f
                                )
                        )))
                .modelOutputs(List.of(ModelOutputDTO
                        .builder()
                        .outputName("output")
                        .outputType(ModelOutputType.FLOAT.name())
                        .outputShape(List.of(50))
                        .normalized(false)
                        .build()))
                .build();

        this.modelService.createOrUpdateModel(model);
        return model;
    }

    private void createTopMerchantsStoredFeatureValues(ModelDTO model) {
        List<FeatureValueDTO> featureValues = List.of(
                ModelFactory.buildFeatureValue(
                        model, "top_merchants_score_feature",
                        List.of("r1"),
                        HashMap.of(
                                featureStoreKey, "r1",
                                PrecomputedRankerModel.SCORE, 0.888f
                        )),
                ModelFactory.buildFeatureValue(
                        model, "top_merchants_score_feature",
                        List.of("r2"),
                        HashMap.of(
                                featureStoreKey, "r2",
                                PrecomputedRankerModel.SCORE, 0.777f
                        )),
                ModelFactory.buildFeatureValue(
                        model, "top_merchants_score_feature",
                        List.of("r3"),
                        HashMap.of(
                                featureStoreKey, "r3",
                                PrecomputedRankerModel.SCORE, 0.999f
                        ))
        );
        featureValueService.createOrUpdateMany(featureValues);
    }
}

package com.grubhub.garcon.modelinteraction.rankingpostprocessing;

import com.google.common.collect.ImmutableList;
import org.apache.commons.math3.random.RandomGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith({MockitoExtension.class})
class RandomInsertRankingPostProcessingTest {

    private RandomInsertRankingPostProcessing subject;
    private RandomGenerator randomGenerator;

    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException {
        subject = new RandomInsertRankingPostProcessing(
                "seed", ImmutableList.of("5", "8"), 5);

        randomGenerator = mock(RandomGenerator.class);
        injectMock(subject, "randomGenerator", randomGenerator);
    }

    public static void injectMock(Object targetObject, String fieldName, Object mockObject) throws NoSuchFieldException, IllegalAccessException {
        Field field = targetObject.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(targetObject, mockObject);
    }

    @Test
    public void testPinEntities() {
        when(randomGenerator.nextInt(Mockito.anyInt()))
                .thenReturn(1) // First call returns 1
                .thenReturn(0); // Second call returns 0

        List<String> entityIds = Arrays.asList("1", "2", "3", "4", "5", "6", "7", "8");

        List<String> result = subject.process(entityIds);

        System.out.println(result);
        System.out.println(result.size());
        assertEquals(8, result.size(), "Result list should contain 8 elements.");
        assertEquals("5", result.get(2), "5 should be at position 2 even though it was inserted initially on position 1.");
        assertEquals("8", result.get(0), "8 should be at position 0 as per mocked random.");
    }

    @Test
    public void process_should_return_targetEntities() {
        List<String> result = subject.process(null);

        assertEquals(2, result.size(), "Result list should return 2 elements.");

        result = subject.process(emptyList());

        assertEquals(2, result.size(), "Result list should contain 2 elements.");

    }
}

package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplane.services.ensembler.impl.BaseEnsembleTest;
import com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.Test;

import static com.grubhub.garcon.modelinteraction.RankingPostProcessingModel.OUTPUT_ENTITIES;
import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
public class RandPairPostProcessingModelTest extends BaseEnsembleTest {

    @Test
    public void randPair_should_swapCorrectlyTwoEntries_1() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed")
                .add("strategy", "RAND_PAIR")
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_1"),
                                ModelFactory.invocationFeature("entity_id", "id_2"),
                                ModelFactory.invocationFeature("entity_id", "id_3")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey("entities")).isTrue();
        assertThat(modelOutput1.getResponse().get("entities").isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(3);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_1", "id_3", "id_2"));
    }

    @Test
    public void randPair_should_swapCorrectlyTwoEntries_2() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_different_very")
                .add("strategy", "RAND_PAIR")
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_1"),
                                ModelFactory.invocationFeature("entity_id", "id_2"),
                                ModelFactory.invocationFeature("entity_id", "id_3")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey("entities")).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(3);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_2", "id_1", "id_3"));
    }

    @Test
    public void randPair_should_swapCorrectly_sameSeed() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed")
                .add("strategy", "RAND_PAIR")
                .build();

        for (int i = 0; i < 3; i++) {
            val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                    .builder()
                    .callerTrackingId("test")
                    .modelName(model.getModelName())
                    .globalFeatures(globalFeatures)
                    .features(
                            java.util.List.of(
                                    ModelFactory.invocationFeature("entity_id", "id_1"),
                                    ModelFactory.invocationFeature("entity_id", "id_2"),
                                    ModelFactory.invocationFeature("entity_id", "id_3")
                            )
                    )
                    .build());

            assertThat(modelOutput1.getResponse().containsKey("entities")).isTrue();
            assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
            assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(3);
            assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                    .isEqualTo(io.vavr.collection.List.of("id_1", "id_3", "id_2"));
        }
    }

    @Test
    public void randPair_should_handleOneEntry() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_3")
                .add("strategy", "RAND_PAIR")
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_1")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_1"));
    }

    @Test
    public void randPair_should_handleTwoEntries() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed")
                .add("strategy", "RAND_PAIR")
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_1"),
                                ModelFactory.invocationFeature("entity_id", "id_2")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(2);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_2", "id_1"));
    }

    private ModelDTO createRankPostProcessingModel() {
        val model = ModelFactory.createRankPostProcessing();
        this.modelService.createOrUpdateModel(model);
        return model;
    }
}

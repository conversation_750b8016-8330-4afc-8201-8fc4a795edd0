package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplane.services.ensembler.impl.BaseEnsembleTest;
import com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureValueDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.modelinteraction.distribution.BetaDistribution;
import com.grubhub.garcon.modelinteraction.distribution.DistributionResultType;
import com.grubhub.garcon.modelinteraction.distribution.DistributionType;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@Slf4j
public class RandomDistributionModelTest extends BaseEnsembleTest {
    private final ResultCaptor<BetaDistribution> betaInputsCaptor = new ResultCaptor<>();

    @BeforeEach
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        super.setUp();

        clearInvocations(super.randomDistributionFactory);
        reset(super.randomDistributionFactory);
        betaInputsCaptor.reset();
        doAnswer(betaInputsCaptor).when(randomDistributionFactory).createRandomDistribution(any(), any(), any());
    }

    @Test
    void randomRestaurantImageModel_should_return_top1_for_r1() {
        val model = createRandomRestaurantImagesModel();

        createRestaurantImagesStoredFeatureValues(model, getDefaultRandomRestaurantImagesFeatureKeys());

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.TOP_N, 1)
                .add(RandomDistributionModel.TRACKING_ID, "tracking_id")
                .build();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("r1")))
                .build());

        assertThat(modelOutput.getResponse().containsKey(RandomDistributionModel.OUTPUT_TARGET_URLS)).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).isDefined()).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).get().size()).isEqualTo(1);
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS)
                .get().get(0).getStringArray()).isEqualTo(List.of("r1_url_3"));

        verify(randomDistributionFactory, times(1)).createRandomDistribution(eq(DistributionType.BETA),
                eq(DistributionResultType.RANDOM_SAMPLING), any());

        assertThat(betaInputsCaptor.getLastResult()).isNotNull();
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs()).isNotEmpty();
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs().size()).isEqualTo(3);
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs()).hasSameElementsAs(Arrays.asList(
                new BetaDistribution.BetaInput("r1_url_1", "tracking_id-r1-r1_url_1", 0., 1.0),
                new BetaDistribution.BetaInput("r1_url_2", "tracking_id-r1-r1_url_2", 0.5, 0.5),
                new BetaDistribution.BetaInput("r1_url_3", "tracking_id-r1-r1_url_3", 1., 0.)));
    }


    @Test
    void randomRestaurantImageModel_should_return_top2_for_r1() {
        val model = createRandomRestaurantImagesModel();
        createRestaurantImagesStoredFeatureValues(model, getDefaultRandomRestaurantImagesFeatureKeys());

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.TOP_N, 2)
                .add(RandomDistributionModel.TRACKING_ID, "tracking_id")
                .build();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("r1")))
                .build());

        assertThat(modelOutput.getResponse().containsKey(RandomDistributionModel.OUTPUT_TARGET_URLS)).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).isDefined()).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).get().size()).isEqualTo(1);
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS)
                .get().get(0).getStringArray()).isEqualTo(List.of("r1_url_3", "r1_url_2"));

        verify(randomDistributionFactory, times(1)).createRandomDistribution(eq(DistributionType.BETA),
                eq(DistributionResultType.RANDOM_SAMPLING), any());

        assertThat(betaInputsCaptor.getLastResult()).isNotNull();
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs()).isNotEmpty();
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs().size()).isEqualTo(3);
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs()).hasSameElementsAs(Arrays.asList(
                new BetaDistribution.BetaInput("r1_url_1", "tracking_id-r1-r1_url_1", 0., 1.0),
                new BetaDistribution.BetaInput("r1_url_2", "tracking_id-r1-r1_url_2", 0.5, 0.5),
                new BetaDistribution.BetaInput("r1_url_3", "tracking_id-r1-r1_url_3", 1., 0.)));
    }

    @Test
    void randomRestaurantImageModel_should_find_two_out_of_3_restaurants() {
        val model = createRandomRestaurantImagesModel();
        createRestaurantImagesStoredFeatureValues(model, getDefaultRandomRestaurantImagesFeatureKeys());

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.TOP_N, 2)
                .add(RandomDistributionModel.TRACKING_ID, "tracking_id")
                .build();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("r1", "r2", "r3")))
                .build());

        assertThat(modelOutput.getResponse().containsKey(RandomDistributionModel.OUTPUT_TARGET_URLS)).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).isDefined()).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).get().size()).isEqualTo(3);
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS)
                .get().get(0).getStringArray()).isEqualTo(List.of("r1_url_3", "r1_url_2"));
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).get().get(1).getStringArray())
                .isEqualTo(List.of("r2_url_3", "r2_url_2"));
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS)
                .get().get(2).getStringArray()).isEqualTo(List.empty());
    }

    @Test()
    void randomRestaurantImageModel_should_beConsistent_regardlessOfRestaurantIdsOrder_andInvocationTimes() {
        // repeat to exclude any probability inconsistencies
        val model = createRandomRestaurantImagesModel();
        createRestaurantImagesStoredFeatureValues(model, List.of(new BetaInputHolder("3374238",
                        new String[]{
                                "https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search",
                                "https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo",
                                "https://media-cdn.grubhub.com/image/upload/v1631224804/lzsmi835l8ge0cmz1acq.jpg?tag=menu",
                                "https://media-cdn.grubhub.com/image/upload/v1631224810/funvwrrtllzdngr94rrc.jpg?tag=menu",
                                "https://media-cdn.grubhub.com/image/upload/v1631224822/qvpdf62luv77uaugvi8h.jpg?tag=menu",
                                "https://media-cdn.grubhub.com/image/upload/v1631224824/cjdnb3vnyhwibkfmrnsa.jpg?tag=menu",
                                "https://media-cdn.grubhub.com/image/upload/v1631224827/ttoikcvhqnje2xvwtjh0.jpg?tag=menu",
                                "https://media-cdn.grubhub.com/image/upload/v1632516869/bulatimqakphxnmagojr.jpg?tag=menu"
                        },
                        new Double[]{16., 16., 0., 50., 41., 19., 3., 12.},
                        new Double[]{265., 265., 1., 813., 675., 329., 134., 240.}),
                new BetaInputHolder("273286",
                        new String[]{
                                "https://media-cdn.grubhub.com/image/upload/v1587132777/l6agvoukglqxuq6son7x.jpg?tag=menu",
                                "https://media-cdn.grubhub.com/image/upload/v1587132790/lylccmb4kdfvgjem8jki.jpg?tag=menu",
                                "https://media-cdn.grubhub.com/image/upload/v1587135296/dycgjsfxipw5ogb5okgf.jpg?tag=menu",
                                "https://media-cdn.grubhub.com/image/upload/v1601067738/iwcmgscbzoxvvoe1povk.png?tag=search",
                                "https://media-cdn.grubhub.com/image/upload/v1642705320/o5unqqh5tdlzu8vkpgyz.jpg?tag=menu",
                                "https://media-cdn.grubhub.com/image/upload/v1648574572/jkfetevqcref2odngmq4.jpg?tag=menu",
                                "https://res.cloudinary.com/grubhub/image/upload/v1506443084/b8j0p1kwl7eidddkesco.jpg?tag=menu",
                                "https://res.cloudinary.com/grubhub/image/upload/v1506443341/g1sknvxpsuha2bdtsigi.jpg?tag=menu",
                                "https://res.cloudinary.com/grubhub/image/upload/v1506443404/or7xmw4gef5o4nzyjtl1.jpg?tag=menu"},

                        new Double[]{1., 80., 8., 877., 1., 0., 40., 38., 10.},
                        new Double[]{59., 881., 163., 14213., 65., 1., 441., 506., 203.}
                )));

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.TRACKING_ID, "tacking_id_value")
                .add(RandomDistributionModel.SEED_MODE, "tracking_id")
                .add(RandomDistributionModel.TOP_N, 3)
                .build();

        Map<String, List<String>> idWithResults = new java.util.HashMap<>();

        idWithResults.put("3374238", List.of(
                "https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search",
                "https://media-cdn.grubhub.com/image/upload/v1631224822/qvpdf62luv77uaugvi8h.jpg?tag=menu",
                "https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo"));

        idWithResults.put("273286", List.of(
                "https://media-cdn.grubhub.com/image/upload/v1587132790/lylccmb4kdfvgjem8jki.jpg?tag=menu",
                "https://res.cloudinary.com/grubhub/image/upload/v1506443341/g1sknvxpsuha2bdtsigi.jpg?tag=menu",
                "https://res.cloudinary.com/grubhub/image/upload/v1506443084/b8j0p1kwl7eidddkesco.jpg?tag=menu"));

        for (int i = 0; i < 3; i++) {
            betaInputsCaptor.reset();
            java.util.List<ModelInferenceOutput> results = Stream.of(List.of("3374238", "273286"), List.of("273286", "3374238"))
                    .map(queryIds -> modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                            .builder()
                            .callerTrackingId("test")
                            .modelName(model.getModelName())
                            .globalFeatures(globalFeatures)
                            .features(ModelFactory.buildInvocationFeatures("restaurant_id", queryIds))
                            .build())).collect(Collectors.toList());


            assertThat(results.get(0).getResponse().containsKey(RandomDistributionModel.OUTPUT_TARGET_URLS)).isTrue();
            assertThat(results.get(0).getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).isDefined()).isTrue();
            assertThat(results.get(0).getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).get().size()).isEqualTo(2);
            assertThat(results.get(0).getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS)
                    .get().get(0).getStringArray()).isEqualTo(idWithResults.get("3374238"));
            assertThat(results.get(0).getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).get()
                    .get(1).getStringArray())
                    .isEqualTo(idWithResults.get("273286"));

            assertThat(results.get(1).getResponse().containsKey(RandomDistributionModel.OUTPUT_TARGET_URLS)).isTrue();
            assertThat(results.get(1).getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).isDefined()).isTrue();
            assertThat(results.get(1).getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).get().size()).isEqualTo(2);
            assertThat(results.get(1).getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS)
                    .get().get(0).getStringArray()).isEqualTo(idWithResults.get("273286"));
            assertThat(results.get(1).getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS)
                    .get().get(1).getStringArray())
                    .isEqualTo(idWithResults.get("3374238"));
        }
    }

    @Test
    void randomRestaurantImageModel_should_constructBetaInputsCorrectly_trackingIdMode() {
        val model = createRandomRestaurantImagesModel();
        createRestaurantImagesStoredFeatureValues(model, List.of(new BetaInputHolder("3374231",
                new String[]{
                        "https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search",
                        "https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo",
                        "https://media-cdn.grubhub.com/image/upload/v1631224804/lzsmi835l8ge0cmz1acq.jpg?tag=menu",
                },
                new Double[]{16., 16., 0.},
                new Double[]{265., 265., 1.})
        ));

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.TRACKING_ID, "tracking_id")
                .add(RandomDistributionModel.SEED_MODE, "tracking_id")
                .add(RandomDistributionModel.TOP_N, 3)
                .build();


        modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("3374231")))
                .build());

        verify(randomDistributionFactory, times(1)).createRandomDistribution(eq(DistributionType.BETA),
                eq(DistributionResultType.RANDOM_SAMPLING), any());

        assertThat(betaInputsCaptor.getLastResult()).isNotNull();
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs()).isNotEmpty();
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs().size()).isEqualTo(3);
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs()).hasSameElementsAs(Arrays.asList(
                new BetaDistribution.BetaInput("https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search",
                        "tracking_id-3374231-https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search", 16., 265.),
                new BetaDistribution.BetaInput("https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo",
                        "tracking_id-3374231-https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo", 16., 265.),
                new BetaDistribution.BetaInput("https://media-cdn.grubhub.com/image/upload/v1631224804/lzsmi835l8ge0cmz1acq.jpg?tag=menu",
                        "tracking_id-3374231-https://media-cdn.grubhub.com/image/upload/v1631224804/lzsmi835l8ge0cmz1acq.jpg?tag=menu", 0., 1.)));
    }

    @Test
    void randomRestaurantImageModel_should_constructBetaInputsCorrectly_dinerIdMode() {
        val model = createRandomRestaurantImagesModel();
        createRestaurantImagesStoredFeatureValues(model, List.of(new BetaInputHolder("3374232",
                new String[]{
                        "https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search",
                        "https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo",
                        "https://media-cdn.grubhub.com/image/upload/v1631224804/lzsmi835l8ge0cmz1acq.jpg?tag=menu",
                },
                new Double[]{16., 16., 0.},
                new Double[]{265., 265., 1.})
        ));

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.DINER_ID, "diner_id")
                .add(RandomDistributionModel.SEED_MODE, "diner_id")
                .add(RandomDistributionModel.TOP_N, 3)
                .build();


        modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("3374232")))
                .build());

        verify(randomDistributionFactory, times(1)).createRandomDistribution(eq(DistributionType.BETA),
                eq(DistributionResultType.RANDOM_SAMPLING), any());

        assertThat(betaInputsCaptor.getLastResult()).isNotNull();
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs()).isNotEmpty();
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs().size()).isEqualTo(3);
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs()).hasSameElementsAs(Arrays.asList(
                new BetaDistribution.BetaInput("https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search",
                        "diner_id-3374232-https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search", 16., 265.),
                new BetaDistribution.BetaInput("https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo",
                        "diner_id-3374232-https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo", 16., 265.),
                new BetaDistribution.BetaInput("https://media-cdn.grubhub.com/image/upload/v1631224804/lzsmi835l8ge0cmz1acq.jpg?tag=menu",
                        "diner_id-3374232-https://media-cdn.grubhub.com/image/upload/v1631224804/lzsmi835l8ge0cmz1acq.jpg?tag=menu", 0., 1.)));
    }

    @Test
    void randomRestaurantImageModel_should_constructBetaInputsCorrectly_defaultMode() {
        val model = createRandomRestaurantImagesModel();
        createRestaurantImagesStoredFeatureValues(model, List.of(new BetaInputHolder("3374233",
                new String[]{
                        "https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search",
                        "https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo",
                        "https://media-cdn.grubhub.com/image/upload/v1631224804/lzsmi835l8ge0cmz1acq.jpg?tag=menu",
                },
                new Double[]{16., 16., 0.},
                new Double[]{265., 265., 1.})
        ));

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.TRACKING_ID, "tracking_id")
                .add(RandomDistributionModel.TOP_N, 3)
                .build();


        modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("3374233")))
                .build());

        verify(randomDistributionFactory, times(1)).createRandomDistribution(eq(DistributionType.BETA),
                eq(DistributionResultType.RANDOM_SAMPLING), any());

        assertThat(betaInputsCaptor.getLastResult()).isNotNull();
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs()).isNotEmpty();
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs().size()).isEqualTo(3);
        assertThat(betaInputsCaptor.getLastResult().getBetaInputs()).hasSameElementsAs(Arrays.asList(
                new BetaDistribution.BetaInput("https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search",
                        "tracking_id-3374233-https://media-cdn.grubhub.com/image/upload/v1572987670/xihdxdrniirl7aej24yu.jpg?tag=search", 16., 265.),
                new BetaDistribution.BetaInput("https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo",
                        "tracking_id-3374233-https://media-cdn.grubhub.com/image/upload/v1549565529/vsqavaihvyckjfsmole1.png?tag=logo", 16., 265.),
                new BetaDistribution.BetaInput("https://media-cdn.grubhub.com/image/upload/v1631224804/lzsmi835l8ge0cmz1acq.jpg?tag=menu",
                        "tracking_id-3374233-https://media-cdn.grubhub.com/image/upload/v1631224804/lzsmi835l8ge0cmz1acq.jpg?tag=menu", 0., 1.)));
    }

    @Test
    void randomRestaurantImageModel_should_returnEmpty_onIllegalDistribution() {
        val model = createRandomRestaurantImagesModel();
        createRestaurantImagesStoredFeatureValues(model, getDefaultRandomRestaurantImagesFeatureKeys());

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.DISTRIBUTION, "wrong_hehe")
                .add(RandomDistributionModel.TOP_N, 2)
                .build();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("r1", "r2", "r3")))
                .build());

        assertThat(modelOutput.getResponse().containsKey(RandomDistributionModel.OUTPUT_TARGET_URLS)).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).isDefined()).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_TARGET_URLS).get().size()).isEqualTo(0);
    }

    @Test
    void randomRestaurantImageModel_should_return_ranking() {
        val model = createRandomRestaurantImagesModel();
        createRestaurantImagesStoredFeatureValues(model, getDefaultRandomRestaurantImagesFeatureKeys());

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.MODE, "ranking")
                .add(RandomDistributionModel.TRACKING_ID, "tracking_id")
                .add(RandomDistributionModel.TOP_N, 2)
                .build();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("r1")))
                .build());

        assertThat(modelOutput.getResponse().containsKey(RandomDistributionModel.OUTPUT_SCORES)).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).isDefined()).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().size()).isEqualTo(1);
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().get(0).getFloat()).isEqualTo(1.0f);
    }

    @Test
    void randomRestaurantImageModel_should_returnDifferentResults_differentSeedMode() {
        val model = createRandomRestaurantImagesModel();
        createRestaurantImagesStoredFeatureValues(model, List.of(new BetaInputHolder("key1",
                new String[]{"value1"},
                new Double[]{0.5},
                new Double[]{0.5})));

        val globalFeaturesTracking = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.MODE, "ranking")
                .add(RandomDistributionModel.TRACKING_ID, "tracking_id")
                .add(RandomDistributionModel.SEED_MODE, "tracking_id")
                .add(RandomDistributionModel.TOP_N, 1)
                .build();

        val modelOutputTracking = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeaturesTracking)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("key1")))
                .build());

        assertThat(modelOutputTracking.getResponse().containsKey(RandomDistributionModel.OUTPUT_SCORES)).isTrue();
        assertThat(modelOutputTracking.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).isDefined()).isTrue();
        assertThat(modelOutputTracking.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().size()).isEqualTo(1);

        val resultRankTracking = modelOutputTracking.getResponse().get(RandomDistributionModel.OUTPUT_SCORES)
                .get().get(0).getFloat();

        val globalFeaturesDiner = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.MODE, "ranking")
                .add(RandomDistributionModel.DINER_ID, "diner_id")
                .add(RandomDistributionModel.SEED_MODE, "diner_id")
                .add(RandomDistributionModel.TOP_N, 1)
                .build();

        val modelOutputDiner = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeaturesDiner)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("key1")))
                .build());

        assertThat(modelOutputDiner.getResponse().containsKey(RandomDistributionModel.OUTPUT_SCORES)).isTrue();
        assertThat(modelOutputDiner.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).isDefined()).isTrue();
        assertThat(modelOutputDiner.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().size()).isEqualTo(1);

        val resultRankDiner = modelOutputDiner.getResponse().get(RandomDistributionModel.OUTPUT_SCORES)
                .get().get(0).getFloat();

        assertScore(resultRankTracking);
        assertScore(resultRankDiner);
        assertThat(resultRankTracking).isNotEqualTo(resultRankDiner);
    }


    @Test
    void randomDistributionModel_rankingShouldBeDifferent_forSameAlphaBeta() {
        val model = createRandomRestaurantImagesModel();
        val features = List.of(
                new BetaInputHolder("r6",
                        new String[]{},
                        new Double[]{1.},
                        new Double[]{1.}),
                new BetaInputHolder("r7",
                        new String[]{},
                        new Double[]{1.},
                        new Double[]{1.}
                ));

        createRestaurantImagesStoredFeatureValues(model, features);

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.MODE, "ranking")
                .add(RandomDistributionModel.TRACKING_ID, "tacking_id_value")
                .add(RandomDistributionModel.SEED_MODE, "tracking_id")
                .add(RandomDistributionModel.TOP_N, 1)
                .build();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures("restaurant_id", List.of("r6", "r7")))
                .build());

        assertThat(modelOutput.getResponse().containsKey(RandomDistributionModel.OUTPUT_SCORES)).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).isDefined()).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().size()).isEqualTo(2);

        double score1 = modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().get(0).getFloat();
        double score2 = modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().get(1).getFloat();

        assertThat(score1).isNotEqualTo(score2);
        assertScore(score1);
        assertScore(score2);
    }

    @Test
    public void topicsMABRanker_should_correctlyComputeScores_anyOrder() {
        val model = createMABRankerModel();
        createMABRankerStoredFeatureValues(model);

        java.util.List<java.util.List<String>> inputs = Arrays.asList(
                Arrays.asList("lunch specials", "9rtr", "lunch"),
                Arrays.asList("discover neighborhood gems", "dph4", "dinner"),
                Arrays.asList("top rated", "dr8t", "breakfast"),
                Arrays.asList("top rated", "dq9c", "mid-day")
        );

        for (int i = 0; i < 3; i++) {
            val globalFeatures = ModelFactory.invocationFeatureBuilder()
                    .add(RandomDistributionModel.MODE, "ranking")
                    .add(RandomDistributionModel.TRACKING_ID, "tacking_id_value")
                    .add(RandomDistributionModel.SEED_MODE, "tracking_id")
                    .add(RandomDistributionModel.TOP_N, 4)
                    .build();

            Collections.shuffle(inputs);

            val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                    .builder()
                    .callerTrackingId("test")
                    .modelName(model.getModelName())
                    .globalFeatures(globalFeatures)
                    .features(
                            java.util.List.of(
                                    ModelFactory.invocationFeature("topic_name", inputs.get(0).get(0), "diner_geohash_4", inputs.get(0).get(1),
                                            "mealtime", inputs.get(0).get(2)),
                                    ModelFactory.invocationFeature("topic_name", inputs.get(1).get(0), "diner_geohash_4", inputs.get(1).get(1),
                                            "mealtime", inputs.get(1).get(2)),
                                    ModelFactory.invocationFeature("topic_name", inputs.get(2).get(0), "diner_geohash_4", inputs.get(2).get(1),
                                            "mealtime", inputs.get(2).get(2)),
                                    ModelFactory.invocationFeature("topic_name", inputs.get(3).get(0), "diner_geohash_4", inputs.get(3).get(1),
                                            "mealtime", inputs.get(3).get(2))
                            )
                    )
                    .build());

            assertThat(modelOutput.getResponse().containsKey(RandomDistributionModel.OUTPUT_SCORES)).isTrue();
            assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).isDefined()).isTrue();
            assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().size()).isEqualTo(4);
            assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().map(ModelInferenceOutputType::getFloat))
                    .hasSameElementsAs(Arrays.asList(0.36226222f, 0.18749094f, 0.24746996f, 0.019302702f));
        }
    }

    @Test
    public void topicsMABRanker_should_correctlyComputeScores_forFeaturesNotFound() {
        val model = createMABRankerModel();
        createMABRankerStoredFeatureValues(model);

        // all features which are not in the store
        java.util.List<java.util.List<String>> inputs = Arrays.asList(
                Arrays.asList("lunch specials inexistent", "9rtr", "lunch"),
                Arrays.asList("discover neighborhood gems indexistent", "dph4", "dinner"),
                Arrays.asList("top rated", "dr8t fake", "breakfast"),
                Arrays.asList("top rated", "dq9c", "mid-day yesterday")
        );

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.MODE, "ranking")
                .add(RandomDistributionModel.TRACKING_ID, "tacking_id_value")
                .add(RandomDistributionModel.SEED_MODE, "tracking_id")
                .add(RandomDistributionModel.TOP_N, 4)
                .build();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("topic_name", inputs.get(0).get(0), "diner_geohash_4", inputs.get(0).get(1),
                                        "mealtime", inputs.get(0).get(2)),
                                ModelFactory.invocationFeature("topic_name", inputs.get(1).get(0), "diner_geohash_4", inputs.get(1).get(1),
                                        "mealtime", inputs.get(1).get(2)),
                                ModelFactory.invocationFeature("topic_name", inputs.get(2).get(0), "diner_geohash_4", inputs.get(2).get(1),
                                        "mealtime", inputs.get(2).get(2)),
                                ModelFactory.invocationFeature("topic_name", inputs.get(3).get(0), "diner_geohash_4", inputs.get(3).get(1),
                                        "mealtime", inputs.get(3).get(2))
                        )
                )
                .build());

        assertThat(modelOutput.getResponse().containsKey(RandomDistributionModel.OUTPUT_SCORES)).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).isDefined()).isTrue();
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().size()).isEqualTo(4);
        assertThat(modelOutput.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().map(ModelInferenceOutputType::getFloat))
                .hasSameElementsAs(Arrays.asList(0.11592524f, 0.5427733f, 0.53201056f, 0.7886929f));

        verify(randomDistributionFactory, times(4)).createRandomDistribution(eq(DistributionType.BETA),
                eq(DistributionResultType.RANKING), any());

        assertThat(betaInputsCaptor.getResults()).isNotNull();
        assertThat(betaInputsCaptor.getResults()).isNotEmpty();
        assertThat(betaInputsCaptor.getResults().size()).isEqualTo(4);

        assertThat(betaInputsCaptor.getResults().get(0).getBetaInputs()).hasSameElementsAs(java.util.List.of(
                new BetaDistribution.BetaInput(null,
                        "tacking_id_value-lunch specials inexistent;9rtr;lunch-VERY_CONSTANT_STRING", 1., 1.)));

        assertThat(betaInputsCaptor.getResults().get(1).getBetaInputs()).hasSameElementsAs(java.util.List.of(
                new BetaDistribution.BetaInput(null,
                        "tacking_id_value-discover neighborhood gems indexistent;dph4;dinner-VERY_CONSTANT_STRING", 1., 1.)));

        assertThat(betaInputsCaptor.getResults().get(2).getBetaInputs()).hasSameElementsAs(java.util.List.of(
                new BetaDistribution.BetaInput(null,
                        "tacking_id_value-top rated;dr8t fake;breakfast-VERY_CONSTANT_STRING", 1., 1.)));

        assertThat(betaInputsCaptor.getResults().get(3).getBetaInputs()).hasSameElementsAs(java.util.List.of(
                new BetaDistribution.BetaInput(null,
                        "tacking_id_value-top rated;dq9c;mid-day yesterday-VERY_CONSTANT_STRING", 1., 1.)));
    }

    @Test
    public void topicsMABRanker_should_correctlyComputeScores_inexistentFeatures_forDifferentFeaturesOrder() {
        val model = createMABRankerModel();
        createMABRankerStoredFeatureValues(model);

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.MODE, "ranking")
                .add(RandomDistributionModel.TRACKING_ID, "tacking_id_value")
                .add(RandomDistributionModel.SEED_MODE, "tracking_id")
                .add(RandomDistributionModel.TOP_N, 4)
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("diner_geohash_4", "9rtr", "topic_name", "inexistent",
                                        "mealtime", "lunch"),
                                ModelFactory.invocationFeature("topic_name", "inexistent",
                                        "mealtime", "lunch", "diner_geohash_4", "9rtr")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(RandomDistributionModel.OUTPUT_SCORES)).isTrue();
        assertThat(modelOutput1.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().size()).isEqualTo(2);
        assertThat(modelOutput1.getResponse().get(RandomDistributionModel.OUTPUT_SCORES).get().map(ModelInferenceOutputType::getFloat))
                .hasSameElementsAs(Arrays.asList(0.0037181824f, 0.0037181824f));
    }

    @Test
    public void topicsMABRanker_should_correctlyComputeBetaInputs_featuresPresent() {
        val model = createMABRankerModel();
        createMABRankerStoredFeatureValues(model);

        // all features which are not in the store
        java.util.List<java.util.List<String>> inputs = Arrays.asList(
                Arrays.asList("lunch specials", "9rtr", "lunch"),
                Arrays.asList("discover neighborhood gems", "dph4", "dinner"),
                Arrays.asList("top rated", "dr8t", "breakfast")
        );

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(RandomDistributionModel.MODE, "ranking")
                .add(RandomDistributionModel.TRACKING_ID, "tacking_id_value")
                .add(RandomDistributionModel.SEED_MODE, "tracking_id")
                .add(RandomDistributionModel.TOP_N, 4)
                .build();

        modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("topic_name", inputs.get(0).get(0), "diner_geohash_4", inputs.get(0).get(1),
                                        "mealtime", inputs.get(0).get(2)),
                                ModelFactory.invocationFeature("topic_name", inputs.get(1).get(0), "diner_geohash_4", inputs.get(1).get(1),
                                        "mealtime", inputs.get(1).get(2)),
                                ModelFactory.invocationFeature("topic_name", inputs.get(2).get(0), "diner_geohash_4", inputs.get(2).get(1),
                                        "mealtime", inputs.get(2).get(2))
                        )
                )
                .build());

        verify(randomDistributionFactory, times(3)).createRandomDistribution(eq(DistributionType.BETA),
                eq(DistributionResultType.RANKING), any());

        assertThat(betaInputsCaptor.getResults()).isNotNull();
        assertThat(betaInputsCaptor.getResults()).isNotEmpty();
        assertThat(betaInputsCaptor.getResults().size()).isEqualTo(3);

        assertThat(betaInputsCaptor.getResults().get(0).getBetaInputs()).hasSameElementsAs(java.util.List.of(
                new BetaDistribution.BetaInput(null,
                        "tacking_id_value-lunch specials;9rtr;lunch-VERY_CONSTANT_STRING", 1., 6.)));

        assertThat(betaInputsCaptor.getResults().get(1).getBetaInputs()).hasSameElementsAs(java.util.List.of(
                new BetaDistribution.BetaInput(null,
                        "tacking_id_value-discover neighborhood gems;dph4;dinner-VERY_CONSTANT_STRING", 4., 15.)));

        assertThat(betaInputsCaptor.getResults().get(2).getBetaInputs()).hasSameElementsAs(java.util.List.of(
                new BetaDistribution.BetaInput(null,
                        "tacking_id_value-top rated;dr8t;breakfast-VERY_CONSTANT_STRING", 1., 3.)));
    }


    private void createRestaurantImagesStoredFeatureValues(ModelDTO model, List<BetaInputHolder> betaInputs) {
        List<FeatureValueDTO> featureValues = List.ofAll(betaInputs.toStream().map(inp ->
                ModelFactory.buildFeatureValue(
                        model, "search_restaurant_images_feature",
                        List.of(inp.featureKey),
                        HashMap.of(
                                "restaurant_id", inp.featureKey,
                                RandomDistributionModel.INTERNAL_TARGET_VALUES_KEY, inp.targetValues,
                                RandomDistributionModel.INTERNAL_ALPHA_PARAMS_KEY, inp.alphaParams,
                                RandomDistributionModel.INTERNAL_BETA_PARAMS_KEY, inp.betaParams
                        ))
        ).collect(Collectors.toList()));

        featureValueService.createOrUpdateMany(featureValues);
    }

    private ModelDTO createRandomRestaurantImagesModel() {
        val model = ModelFactory.createRestaurantRandomImagesModel();
        this.modelService.createOrUpdateModel(model);
        return model;
    }

    private ModelDTO createMABRankerModel() {
        val model = ModelFactory.createMABRankerModel();
        this.modelService.createOrUpdateModel(model);
        return model;
    }

    private void createMABRankerStoredFeatureValues(ModelDTO model) {
        List<List<String>> storedValues = List.of(
                List.of("lunch specials", "9rtr", "lunch", "1", "6"),
                List.of("discover neighborhood gems", "dph4", "dinner", "4", "15"),
                List.of("top rated", "dr8t", "breakfast", "1", "3"),
                List.of("top rated", "dq9c", "mid-day", "1", "12"),
                List.of("local favorites", "dinner", "mid-day", "2", "13")
        );
        List<FeatureValueDTO> featureValues = List.ofAll(storedValues.toStream().map(inp ->
                ModelFactory.buildFeatureValue(
                        model, "topics_mab_mp_ts_ranker_test_internal_feature",
                        List.of(inp.get(0), inp.get(1), inp.get(2)),
                        HashMap.of(
                                "topic_name", inp.get(0),
                                "diner_geohash_4", inp.get(1),
                                "mealtime", inp.get(2),
                                RandomDistributionModel.INTERNAL_ALPHA_PARAMS_KEY, new Double[]{Double.valueOf(inp.get(3))},
                                RandomDistributionModel.INTERNAL_BETA_PARAMS_KEY, new Double[]{Double.valueOf(inp.get(4))}
                        ))
        ).collect(Collectors.toList()));

        featureValueService.createOrUpdateMany(featureValues);
    }

    private List<BetaInputHolder> getDefaultRandomRestaurantImagesFeatureKeys() {
        return List.of(new BetaInputHolder("r1",
                        new String[]{"r1_url_1", "r1_url_2", "r1_url_3"},
                        new Double[]{0., 0.5, 1.},
                        new Double[]{1., 0.5, 0.}),
                new BetaInputHolder("r2",
                        new String[]{"r2_url_1", "r2_url_2", "r2_url_3"},
                        new Double[]{0., 0.5, 1.},
                        new Double[]{0.2, 0.5, 0.}
                ));
    }


    private void assertScore(double score) {
        assertThat(score).isLessThanOrEqualTo(1.);
        assertThat(score).isGreaterThanOrEqualTo(0.);
    }

    @AllArgsConstructor
    private static class BetaInputHolder {
        String featureKey;
        String[] targetValues;
        Double[] alphaParams;
        Double[] betaParams;
    }

    @Getter
    public class ResultCaptor<T> implements Answer {
        private List<T> results = List.empty();

        public T getLastResult() {
            return results.last();
        }

        public void reset() {
            results = List.empty();
        }

        @Override
        public T answer(InvocationOnMock invocationOnMock) throws Throwable {
            val result = (T) invocationOnMock.callRealMethod();
            results = results.append(result);
            return result;
        }
    }
}

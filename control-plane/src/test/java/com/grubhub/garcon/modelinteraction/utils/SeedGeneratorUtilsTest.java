package com.grubhub.garcon.modelinteraction.utils;

import com.grubhub.garcon.controlplane.services.common.utils.InternalFieldsUtils;
import com.grubhub.garcon.modelinteraction.RandomDistributionModel;
import lombok.val;
import org.junit.jupiter.api.Test;

import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;

public class SeedGeneratorUtilsTest {
    @Test
    public void computeBaseSeed_should_properlyChooseSeedMode_1() {
        val features = new HashMap<String, Object>();
        features.put(RandomDistributionModel.SEED_MODE, RandomDistributionModel.TRACKING_ID);
        features.put(RandomDistributionModel.TRACKING_ID, "tracking_id_value");
        features.put(RandomDistributionModel.DINER_ID, "diner_id");

        String result = SeedGeneratorUtils.computeSeed(features, null, null);
        assertThat(result).isEqualTo("tracking_id_value");
    }

    @Test
    public void computeBaseSeed_should_properlyChooseSeedMode_2() {
        val features = new HashMap<String, Object>();
        features.put(RandomDistributionModel.SEED_MODE, RandomDistributionModel.DINER_ID);
        features.put(RandomDistributionModel.TRACKING_ID, "tracking_id_value");
        features.put(RandomDistributionModel.DINER_ID, "diner_id");

        String result = SeedGeneratorUtils.computeSeed(features, null, null);
        assertThat(result).isEqualTo("diner_id");
    }

    @Test
    public void computeBaseSeed_should_properlyChooseDefaultSeed() {
        val features = new HashMap<String, Object>();
        features.put(RandomDistributionModel.TRACKING_ID, "tracking_id_value");
        features.put(RandomDistributionModel.DINER_ID, "diner_id");

        String result = SeedGeneratorUtils.computeSeed(features, RandomDistributionModel.TRACKING_ID, null);
        assertThat(result).isEqualTo("tracking_id_value");
    }

    @Test
    public void computeBaseSeed_should_properlyAddLookupKeys() {
        val features = new HashMap<String, Object>();
        features.put(RandomDistributionModel.TRACKING_ID, "tracking_id_value");
        features.put(RandomDistributionModel.DINER_ID, "diner_id");
        features.put(InternalFieldsUtils.getInternalLookupKeysField(0), "lookup_key#1;lookup_key#2");

        String result = SeedGeneratorUtils.computeSeed(features, RandomDistributionModel.TRACKING_ID, null);
        assertThat(result).isEqualTo("tracking_id_value-lookup_key#1;lookup_key#2");
    }
}

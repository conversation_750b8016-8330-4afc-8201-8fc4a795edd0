package com.grubhub.garcon.modelinteraction.computablefeatures;

import com.grubhub.garcon.modelinteraction.computablefeatures.textnormalizer.ControlPlaneTextNormalizer;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Same as the ControlPlaneTextNormalizerTest from search
 */
public class ControlPlaneTextNormalizerTest {

    @Test
    public void testTokenize_Wray() {
        Optional<String> actual = new ControlPlaneTextNormalizer("wray").tokenize();
        assertThat(actual.isPresent(), equalTo(true));
        assertThat(actual.get(), equalTo("wray"));
    }

    @Test
    public void testTokenizeStopword_Fried() {
        Optional<String> actual = new ControlPlaneTextNormalizer("Chicken Fried Steak").tokenize();
        assertThat(actual.isPresent(), equalTo(true));
        assertThat(actual.get(), equalTo("chicken fried steak"));
    }

    @Test
    public void testTokenize_Fry() {
        Optional<String> actual = new ControlPlaneTextNormalizer("stir fry").tokenize();
        assertThat(actual.isPresent(), equalTo(true));
        assertThat(actual.get(), equalTo("stir fri"));
    }

    @Test
    public void testTokenize_should_normalize_the_number_of_spaces_between_words() {
        Optional<String> actual = new ControlPlaneTextNormalizer("   Cheese    BurgEr ").tokenize();
        assertThat(actual.isPresent(), equalTo(true));
        assertThat(actual.get(), equalTo("chees burger"));
    }

    @Test
    public void testTokenize_should_remove_punctuation() {
        Optional<String> actual = new ControlPlaneTextNormalizer("Hello, world!").tokenize();
        assertThat(actual.isPresent(), equalTo(true));
        assertThat(actual.get(), equalTo("hello world"));
    }


    @Test
    public void testTokenize_should_stem_words() {
        Optional<String> actual = new ControlPlaneTextNormalizer("abandoned").tokenize();
        assertThat(actual.isPresent(), equalTo(true));
        assertThat(actual.get(), equalTo("abandon"));

        Optional<String> actual1 = new ControlPlaneTextNormalizer("abominably").tokenize();
        assertThat(actual1.isPresent(), equalTo(true));
        assertThat(actual1.get(), equalTo("abomin"));
    }

    @Test
    public void testTokenize_should_remove_possesion() {
        Optional<String> actual = new ControlPlaneTextNormalizer("carlito's").tokenize();
        assertThat(actual.isPresent(), equalTo(true));
        assertThat(actual.get(), equalTo("carlito"));
    }


    @Test
    public void testTokenize_should_tokenize_phrase() {
        Optional<String> actual = new ControlPlaneTextNormalizer("I want quick deliver of the best pizza in town").tokenize();
        assertThat(actual.isPresent(), equalTo(true));
        assertThat(actual.get(), equalTo("i want quick best pizza town"));
    }

    @Test
    public void testTokenize_should_consider_not_stemming_words() {
        Optional<String> actual = new ControlPlaneTextNormalizer("I'll cook the best fries in town").tokenize();
        assertThat(actual.isPresent(), equalTo(true));
        assertThat(actual.get(), equalTo("ill cook best fries town"));
    }

    @Test
    public void testTokenize_should_consider_special_case() {
        Optional<String> actual = new ControlPlaneTextNormalizer("Sandwiches").tokenize();
        assertThat(actual.isPresent(), equalTo(true));
        assertThat(actual.get(), equalTo("sandwich"));
    }

}

package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplane.services.ensembler.impl.BaseEnsembleTest;
import com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import io.vavr.collection.List;
import lombok.val;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class TextSimilarityModelTest extends BaseEnsembleTest {

    /**
     * TOP 50 intent restaurant/brand queries:
     * <p>
     * SELECT search_keyword, COUNT(1) total
     * FROM integrated_events.diner_search_impression_summary dsis
     * WHERE dsis.session_start_date BETWEEN DATE('2022-04-01') AND DATE('2022-04-18')
     * AND dsis.search_keyword IS NOT NULL
     * AND dsis.intent_context_type IN ('BRAND', 'RESTAURANT')
     * GROUP BY 1
     * ORDER BY 2 DESC
     * LIMIT 500;
     */

    private static final List<String> REMOVE_CHARS = List.of("'", "’", "`", "-", "#", "!", "+", ".", "&", "", "®", "@");

    @Test
    void test_case() {
        val model = createTextSimilarityModel();
        doQueryTest(model, 0.8, "taco bell", List.of("taco bell", "TaCo BeLl", "TACO BELL", "TACO bell"));
        doQueryTest(model, 0.8, "TACO BELL", List.of("taco bell", "TaCo BeLl", "TACO BELL", "TACO bell"));
    }

    @Test
    void test_whitespaces() {
        val model = createTextSimilarityModel();
        doQueryTest(model, 1.0, "  taco bell  ", List.of("taco bell", " taco bell ", "   taco bell", "taco bell   "));
        doQueryTest(model, 1.0, "taco bell  ", List.of("taco bell", " taco bell ", "   taco bell", "taco bell   "));
        doQueryTest(model, 1.0, "   taco bell", List.of("taco bell", " taco bell ", "   taco bell", "taco bell   "));
    }


    @Test
    void test_similarity() {
        val model = createTextSimilarityModel();

        doQueryTest(model, 0.8, false, false, List.empty(), false, "burger king",
                List.of(
                        "burger king",
                        "   burger king     ",
                        "BURGER KING",
                        "burgerking",
                        "BURGERKING",
                        "BurGeKIng",
                        "burger kin",
                        "urger king",
                        "burger ing",
                        "urger kin"
                ),
                List.of(
                        "burger",
                        "shake shack",
                        "king"
                )
        );

        doQueryTest(model, 0.7, "tacobel", List.of("taco bell"));
        doQueryTest(model, 0.8, "tacobell", List.of("taco bell"));
        doQueryTest(model, 0.8, "taco bel", List.of("taco bell"));
    }

    @Test
    void test_missing_characters() {
        val model = createTextSimilarityModel();
        doQueryTest(model, 0.8, "mcdonalds", List.of("McDonald's"));
        doQueryTest(model, 0.7, "mcdonald", List.of("McDonald's"));
        doQueryTest(model, 0.8, "mc donalds", List.of("McDonald's"));
        doQueryTest(model, 0.8, "mc donald's", List.of("McDonald's"));

        doQueryTest(model, 0.8, "chick fil a", List.of("Chick-fil-A"));
        doQueryTest(model, 0.7, "chickfila", List.of("Chick-fil-A"));
        doQueryTest(model, 0.6, "chick fil", List.of("Chick-fil-A"));
        doQueryTest(model, 0.8, "chick-fil a", List.of("Chick-fil-A"));
        doQueryTest(model, 0.8, "chick fila", List.of("Chick-fil-A"));
    }

    @Test
    void test_normalize() {
        val model = createTextSimilarityModel();
        doQueryTestNorm(model, 1.0, "mcdonalds", List.of("McDonald's"));
        doQueryTestNorm(model, 1.0, "mc donald's", List.of("McDonald's"));
        doQueryTestNorm(model, 0.8, "   mc    donald   '   s  ", List.of("McDonald's"));
        doQueryTestNorm(model, 1.0, "chickfila", List.of("Chick-fil-A"));
        doQueryTestNorm(model, 0.8, "chick-fil a", List.of("Chick-fil-A"));
        doQueryTestNorm(model, 0.8, "chick fil", List.of("Chick-fil-A"));
    }

    @Test
    void test_normalize_removeStopwordsAndSpecialCharacters() {
        val model = createTextSimilarityModel();
        // Remove stopword "and" and special character "&"
        doQueryTestNorm(model, 1.0, "Crisp and Green", List.of("Crisp & Green"));
    }

    @Test
    void test_invalid_queries() {
        val model = createTextSimilarityModel();
        doQueryTestNormInvalid(model, 0.9, "taco bell", List.of("taco", "bell"));
        doQueryTestNormInvalid(model, 0.9, "tacobell", List.of("taco", "bell"));
        doQueryTestNormInvalid(model, 0.9, "mcdonalds", List.of("burger", "shake shack"));
        doQueryTestNormInvalid(model, 0.9, "burger king", List.of("burger", "king"));
        doQueryTestNormInvalid(model, 0.9, "papa john", List.of("papa", "john"));
        doQueryTestNormInvalid(model, 0.9, "papajohn", List.of("papa", "john"));
        doQueryTestNormInvalid(model, 0.9, "olive garden", List.of("olive", "garden"));
        doQueryTestNormInvalid(model, 0.9, "olivegarden", List.of("olive", "garden"));
        doQueryTestNormInvalid(model, 0.9, "starbucks coffee", List.of("starbucks", "coffee"));
        doQueryTestNormInvalid(model, 0.9, "starbuckscoffee", List.of("starbucks", "coffee"));
    }

    @Test
    void invoke_AmeFontainesTestCase_returnMatchForThatRestaurant() {
        val model = createTextSimilarityModel();
        // Testing that only the first case should have score greather than 0.7
        doQueryTest(model, 0.7, "amy fontaine's", List.of("amy fontaine's"));
        doQueryTest(model, 0.0, "amy fontaine's", List.of("sfilatino italian gourmet"));
        doQueryTest(model, 0.0, "amy fontaine's", List.of("the hangover shop"));
    }

    @Test
    void invoke_AmeFontainesTestCase_returnScores() {
        val model = createTextSimilarityModel();
        List<Float> scores = getModelScores(model,
                0.8,
                true,
                true,
                REMOVE_CHARS,
                true,
                "amy fontaine's",
                List.of("amy fontaine's"),
                List.of("amy font", "sfilatino italian gourmet", "the hangover shop"));

        Assertions.assertNotNull(scores);
        Assertions.assertEquals(scores, List.of(1.0f, 0.0f, 0.0f, 0.0f));
    }


    private void doQueryTest(ModelDTO model, double cutOff, String queryText, List<String> validValues) {
        doQueryTest(model, cutOff, false, false, List.empty(), false, queryText, validValues, List.empty());
    }

    private void doQueryTestNorm(ModelDTO model, double cutOff, String queryText, List<String> validValues) {
        doQueryTest(model, cutOff, true, true, REMOVE_CHARS, true, queryText, validValues, List.empty());
    }

    private void doQueryTestNormInvalid(ModelDTO model, double cutOff, String queryText, List<String> invalidValues) {
        doQueryTest(model, cutOff, true, true, REMOVE_CHARS, true, queryText, List.empty(), invalidValues);
    }

    private void doQueryTest(ModelDTO model, double cutOff, boolean normalizeInput, boolean normalizeTarget,
                             List<String> removeChars, boolean removeBlanks, String queryText, List<String> validValues, List<String> invalidValues) {
        final List<Float> scores =
                getModelScores(model, cutOff, normalizeInput, normalizeTarget, removeChars, removeBlanks, queryText, validValues, invalidValues);

        val restaurantNames = List.ofAll(validValues).appendAll(invalidValues);
        assertThat(scores.size()).isEqualTo(restaurantNames.size());

        // Check scores of both lists
        restaurantNames.zip(scores).forEach(t -> {
            val restName = t._1;
            val score = t._2;
            if (validValues.contains(restName)) {
                assertThat(score).as("Score should > cutoff, query=%s, target=%s, score=%f, cutOff=%f", queryText, restName, score, cutOff)
                        .isGreaterThanOrEqualTo((float) cutOff);
            } else {
                assertThat(score).as("Score should be 0, query=%s, target=%s, score=%f, cutOff=%f", queryText, restName, score, cutOff)
                        .isEqualTo(0.0f);
            }
        });
    }

    private List<Float> getModelScores(ModelDTO model,
                                       double cutOff,
                                       boolean normalizeInput,
                                       boolean normalizeTarget,
                                       List<String> removeChars,
                                       boolean removeBlanks,
                                       String queryText,
                                       List<String> validValues,
                                       List<String> invalidValues) {
        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add(TextSimilarityModel.INPUT_TEXT, queryText)
                .add(TextSimilarityModel.CUT_OFF_THRESHOLD, cutOff)
                .add(TextSimilarityModel.TARGET_KEYS, "restaurant_name")
                .add(TextSimilarityModel.NORMALIZE_INPUT, normalizeInput)
                .add(TextSimilarityModel.NORMALIZE_TARGET, normalizeTarget)
                .add(TextSimilarityModel.REMOVE_CHARS, removeChars.mkString(","))
                .add(TextSimilarityModel.REMOVE_BLANKS, removeBlanks)
                .build();
        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(ModelFactory.buildInvocationFeatures("restaurant_name", List.ofAll(validValues).appendAll(invalidValues)))
                .build());
        return modelOutput.getFloatResponse();
    }

    private ModelDTO createTextSimilarityModel() {
        val model = ModelDTO
                .builder()
                .modelName("similarity")
                .modelDescription("similarity")
                .versioningModelName("similarity")
                .modelType(ModelType.TEXT_SIMILARITY.name())
                .status(ModelStatus.ENABLED.getName())
                .version("1.0")
                .versioningStrategy(ModelDTO.STATIC)
                .modelFeatures(List.of(
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr(TextSimilarityModel.INPUT_TEXT),
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr(TextSimilarityModel.TARGET_KEYS),
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr(TextSimilarityModel.CUT_OFF_THRESHOLD),
                        ModelFactory.buildRuntimeFeatureBoolean(TextSimilarityModel.NORMALIZE_INPUT, false),
                        ModelFactory.buildRuntimeFeatureBoolean(TextSimilarityModel.NORMALIZE_TARGET, false),
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr(TextSimilarityModel.REMOVE_CHARS),
                        ModelFactory.buildRuntimeFeatureBoolean(TextSimilarityModel.REMOVE_BLANKS, false),
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr("restaurant_name"),
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr("cuisines")
                ))
                .modelOutputs(List.of(ModelOutputDTO
                        .builder()
                        .outputName("output")
                        .outputType(ModelOutputType.FLOAT.name())
                        .outputShape(List.of(1))
                        .normalized(false)
                        .build()))
                .build();

        this.modelService.createOrUpdateModel(model);
        return model;
    }

}

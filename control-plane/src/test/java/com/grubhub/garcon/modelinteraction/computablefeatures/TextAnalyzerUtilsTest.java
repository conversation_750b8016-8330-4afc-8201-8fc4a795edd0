package com.grubhub.garcon.modelinteraction.computablefeatures;

import com.grubhub.garcon.modelinteraction.computablefeatures.textnormalizer.TextAnalyzerUtils;
import org.apache.lucene.analysis.en.EnglishAnalyzer;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

/**
 * Same as the AnalyzerUtilsTest from search-elastic
 */
public class TextAnalyzerUtilsTest {

    EnglishAnalyzer englishAnalyzer = new EnglishAnalyzer();

    @Test
    public void test_lower_uppercase() {
        String text = "Mein Soup";
        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer).orElse(null)).isEqualTo("mein soup");
    }

    @Test
    public void test_withDelemiter_success() {
        String text = "Mein Soups";

        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer, ",").orElse(null)).isEqualTo("mein,soup");
    }

    @Test
    public void test_asciifolding_success() {
        String text = "mein soup!";
        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer).orElse(null)).isEqualTo("mein soup");

        text = "mein     !soup!";
        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer).orElse(null)).isEqualTo("mein soup");

        text = "mein^ soup!";
        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer).orElse(null)).isEqualTo("mein soup");

        text = "&mein@ (soup!";
        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer).orElse(null)).isEqualTo("mein soup");
    }

    @Test
    public void test_stemming_simple_success() {
        String text = "Salad Wraps";

        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer).orElse(null)).isEqualTo("salad wrap");
    }

    @Test
    public void test_stemming_not_simple_success() {
        String text = "Spicy Beef";
        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer).orElse(null)).isEqualTo("spici beef");

        text = "Chocolate Mousse Cream Pie";
        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer).orElse(null)).isEqualTo("chocol mouss cream pie");

        text = "Housemade French Fries";
        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer).orElse(null)).isEqualTo("housemad french fri");
    }


    @Test
    public void test_combination_success() {
        String text = "HousEmade$$$ ((   French Fries!!!";

        assertThat(TextAnalyzerUtils.analyzeAndJoin(text, englishAnalyzer).orElse(null)).isEqualTo("housemad french fri");
    }

}

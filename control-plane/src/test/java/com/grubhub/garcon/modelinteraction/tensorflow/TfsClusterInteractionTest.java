package com.grubhub.garcon.modelinteraction.tensorflow;

import com.google.common.collect.ImmutableList;
import com.google.common.util.concurrent.Futures;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.grpc.EurekaHostAndPort;
import com.grubhub.garcon.grpc.GrpcChannelFactory;
import com.grubhub.garcon.grpc.GrpcConfiguration;
import com.grubhub.garcon.modelinteraction.PredictionClientFactory;
import io.grpc.ManagedChannel;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.vavr.Tuple;
import io.vavr.collection.List;
import io.vavr.collection.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import tensorflow.serving.GetModelStatus;

import java.time.Duration;
import java.util.Collections;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

@ExtendWith(MockitoExtension.class)
class TfsClusterInteractionTest {

    private static final int PORTS_UPPER_BOUND = 65535;
    private static final String LOCALHOST = "localhost";
    private static final ModelDTO model1 = ModelDTO.builder().modelName("model1")
            .version("123.123")
            .modelType("TENSOR_FLOW")
            .build();
    private static final ModelDTO model2 = ModelDTO.builder().modelName("model2")
            .version("123.124")
            .modelType("TENSOR_FLOW")
            .build();

    @Mock
    private ModelAwareClientProvider modelAwareClientProvider;
    @Mock
    private ModelService modelService;

    @Mock
    private FlowConfig flowConfig;

    @Mock
    private PredictionClientFactory predictionClientFactory;
    @Mock
    private GrpcChannelFactory grpcChannelFactory;

    TfsClusterInteraction tfsClusterInteraction;

    @BeforeEach
    public void setUp() {
        openMocks(this);
        TensorFlowModelConfiguration tensorFlowModelConfiguration = new TensorFlowModelConfiguration();
        tensorFlowModelConfiguration.setRefreshModelAwareTargeting(true);
        TfsClusterConfig tfsClusterConfig = new TfsClusterConfig();

        when(modelService.getAllModelsWithStatus("ENABLED")).thenReturn(List.of(model1, model2));
        String tfsClusterName = "searchTFS";
        when(flowConfig.getTfsClusterNameForModel(any())).thenReturn(tfsClusterName);
        tfsClusterInteraction = spy(
                new TfsClusterInteraction(
                        tfsClusterName,
                        tfsClusterConfig,
                        predictionClientFactory,
                        grpcChannelFactory,
                        tensorFlowModelConfiguration,
                        new SimpleMeterRegistry(),
                        modelAwareClientProvider,
                        modelService,
                        flowConfig,
                        new GrpcConfiguration()
                )
        );
    }

    @Test
    public void shouldRefreshAllStatuses() {
        Mockito.reset(modelAwareClientProvider);

        doReturn(ImmutableList.of(
                Futures.immediateFuture(Tuple.of(EurekaHostAndPort.builder().host("host1").port(8080).build(), fakeAvailableResponse())),
                Futures.immediateFuture(Tuple.of(EurekaHostAndPort.builder().host("host2").port(8080).build(), fakeAvailableResponse()))
        )).when(tfsClusterInteraction).getStatusRequests(requestWithModelName("model1"));

        doReturn(ImmutableList.of(
                Futures.immediateFuture(Tuple.of(EurekaHostAndPort.builder().host("host1").port(8080).build(), fakeAvailableResponse())),
                Futures.immediateFuture(Tuple.of(EurekaHostAndPort.builder().host("host2").port(8080).build(), fakeLoadingResponse()))
        )).when(tfsClusterInteraction).getStatusRequests(requestWithModelName("model2"));

        tfsClusterInteraction.refreshAllStatuses();

        verify(modelAwareClientProvider, times(1)).refreshModelStatus(eq(model1), argThat(
                t -> t.containsAll(ImmutableList.of(
                        EurekaHostAndPort.builder().host("host1").port(8080).build(),
                        EurekaHostAndPort.builder().host("host2").port(8080).build()
                ))));
        verify(modelAwareClientProvider, times(1))
                .refreshModelStatus(
                        eq(model2),
                        eq(ImmutableList.of(EurekaHostAndPort.builder().host("host1").port(8080).build()))
                );
    }

    @Test
    public void shouldRefreshStatus_whenOneModelFails() {
        Mockito.reset(modelAwareClientProvider);

        doReturn(ImmutableList.of(
                Futures.immediateFuture(Tuple.of(EurekaHostAndPort.builder().host("host1").port(8080).build(), fakeAvailableResponse())),
                Futures.immediateFuture(Tuple.of(EurekaHostAndPort.builder().host("host2").port(8080).build(), fakeAvailableResponse()))
        )).when(tfsClusterInteraction).getStatusRequests(requestWithModelName("model1"));

        doThrow(new RuntimeException("call failed"))
                .when(tfsClusterInteraction).getStatusRequests(requestWithModelName("model2"));

        tfsClusterInteraction.refreshAllStatuses();

        verify(modelAwareClientProvider, times(1)).refreshModelStatus(eq(model1), argThat(
                t -> t.containsAll(ImmutableList.of(
                        EurekaHostAndPort.builder().host("host1").port(8080).build(),
                        EurekaHostAndPort.builder().host("host2").port(8080).build()
                ))));
        verify(modelAwareClientProvider, never()).refreshModelStatus(eq(model2), any());
    }

    @Test
    public void shouldRefreshStatus_whenOneClientFutureFails() {
        Mockito.reset(modelAwareClientProvider);
        when(modelService.getAllModelsWithStatus(any())).thenReturn(List.of(model1, model2));
        doReturn(ImmutableList.of(
                Futures.immediateFuture(Tuple.of(EurekaHostAndPort.builder().host("host1").port(8080).build(), fakeAvailableResponse())),
                Futures.immediateFuture(Tuple.of(EurekaHostAndPort.builder().host("host2").port(8080).build(), fakeAvailableResponse()))
        )).when(tfsClusterInteraction).getStatusRequests(requestWithModelName("model1"));

        doReturn(ImmutableList.of(
                Futures.immediateFuture(null),
                Futures.immediateFuture(Tuple.of(EurekaHostAndPort.builder().host("host2").port(8080).build(), fakeLoadingResponse()))
        )).when(tfsClusterInteraction).getStatusRequests(requestWithModelName("model2"));

        tfsClusterInteraction.refreshAllStatuses();
        verify(modelAwareClientProvider, times(1)).refreshModelStatus(eq(model1), argThat(
                t -> t.containsAll(ImmutableList.of(
                        EurekaHostAndPort.builder().host("host1").port(8080).build(),
                        EurekaHostAndPort.builder().host("host2").port(8080).build()
                ))));
        verify(modelAwareClientProvider, times(1)).refreshModelStatus(eq(model2), eq(ImmutableList.of()));
    }


    private static GetModelStatus.GetModelStatusResponse fakeAvailableResponse() {
        return GetModelStatus.GetModelStatusResponse.newBuilder()
                .addModelVersionStatus(GetModelStatus.ModelVersionStatus.newBuilder().setState(GetModelStatus.ModelVersionStatus.State.AVAILABLE).build())
                .build();
    }


    private static GetModelStatus.GetModelStatusResponse fakeLoadingResponse() {
        return GetModelStatus.GetModelStatusResponse.newBuilder()
                .addModelVersionStatus(GetModelStatus.ModelVersionStatus.newBuilder().setState(GetModelStatus.ModelVersionStatus.State.LOADING).build())
                .build();
    }

    private static GetModelStatus.GetModelStatusRequest requestWithModelName(String modelName) {
        return ArgumentMatchers.argThat(request -> request.getModelSpec().getName().equals(modelName));
    }

    @Test
    void refreshFanOutClients() {
        ExecutorService executorService = Executors.newFixedThreadPool(10);

        when(tfsClusterInteraction.getStatusRequests(any())).thenReturn(Collections.emptyList());
        when(grpcChannelFactory.fetchMultiEurekaChannels(any()))
                .thenAnswer(invocation ->
                                Stream.continually(() ->
                                                EurekaHostAndPort.builder()
                                                .host(LOCALHOST)
                                                .port(ThreadLocalRandom.current().nextInt(PORTS_UPPER_BOUND))
                                                .build()
                                )
                                        .take(100)
                                        .toMap(k -> k, k -> mock(ManagedChannel.class))
                                        .toJavaMap()
                );

        IntStream.range(0, 100)
                .forEach(i ->
                    executorService.submit(() -> tfsClusterInteraction.refreshFanOutClients())
                );

        await()
                .atLeast(Duration.ofMillis(100))
                .atMost(Duration.ofSeconds(30))
                .with()
                .pollInterval(Duration.ofMillis(100))
                .until(() -> tfsClusterInteraction.getFanOutClients().size() == 100);

        executorService.shutdown();
        try {
            executorService.awaitTermination(500, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            executorService.shutdownNow();
        }
    }

    @Test
    void shouldNotRefreshFanOutClients_whenGrpcEnabledForDdmlTensorFlow() {
        TensorFlowModelConfiguration tensorFlowModelConfiguration = new TensorFlowModelConfiguration();
        tensorFlowModelConfiguration.setRefreshModelAwareTargeting(true);

        TfsClusterConfig tfsClusterConfig = new TfsClusterConfig();
        GrpcConfiguration grpcConfiguration = mock(GrpcConfiguration.class);

        when(grpcConfiguration.isGrpcEnabledForDdmlTensorFlow()).thenReturn(true);
        String tfsClusterName = "searchTFS";

        TfsClusterInteraction interactionSpy = Mockito.spy(
                new TfsClusterInteraction(
                        tfsClusterName,
                        tfsClusterConfig,
                        predictionClientFactory,
                        grpcChannelFactory,
                        tensorFlowModelConfiguration,
                        new SimpleMeterRegistry(),
                        modelAwareClientProvider,
                        modelService,
                        flowConfig,
                        grpcConfiguration
                )
        );

        verify(interactionSpy, never()).refreshFanOutClients();
    }

}

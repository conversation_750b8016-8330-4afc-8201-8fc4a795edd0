package com.grubhub.garcon.modelinteraction.computablefeatures;

import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;


class StringReplaceFunctionTest {

    private static StringReplaceFunction replaceFunction;

    @BeforeAll
    static void beforeAll() {
        replaceFunction = new StringReplaceFunction();
    }

    @Test
    void compute_whenValueExists_shouldReturnExpected() {
        Map<String, Object> inputs = HashMap.of(
                "str", "CUISINE",
                "mapping", "CUISINE=INTENT_CUISINES,RESTAURANT=INTENT_RESTAURANT",
                "default", "DEFAULT"
        );

        Map<String, Object> actual = replaceFunction.compute(inputs);
        assertThat(actual.get("str").get()).isEqualTo("INTENT_CUISINES");
    }

    @Test
    void compute_whenValueNotExists_shouldReturnDefault() {
        Map<String, Object> inputs = HashMap.of(
                "str", "CUISINE1",
                "mapping", "CUISINES=INTENT_CUISINES,RESTAURANT=INTENT_RESTAURANT",
                "default", "DEFAULT_CUISINE"
        );

        Map<String, Object> actual = replaceFunction.compute(inputs);
        assertThat(actual.get("str").get()).isEqualTo("DEFAULT_CUISINE");
    }

    @Test
    void compute_whenValueExists_shouldReturnTheSameValue() {
        Map<String, Object> inputs = HashMap.of(
                "str", "CUISINE1",
                "mapping", "CUISINES=INTENT_CUISINES,RESTAURANT=INTENT_RESTAURANT",
                "default", "DEFAULT_CUISINE"
        );
        Map<String, Object> actual = replaceFunction.compute(inputs);
        assertThat(actual.get("str").get()).isEqualTo("DEFAULT_CUISINE");
    }

    @Test
    void compute_whenMappingHasSpacesBetweenCommas_shouldReturnExpected() {
        Map<String, Object> inputs = HashMap.of(
                "str", "RESTAURANT",
                "mapping", "CUISINE=INTENT_CUISINES, RESTAURANT=INTENT_RESTAURANT",
                "default", "DEFAULT_CUISINE"
        );
        Map<String, Object> actual = replaceFunction.compute(inputs);
        assertThat(actual.get("str").get()).isEqualTo("INTENT_RESTAURANT");
    }

    @Test
    void compute_whenMappingHasSpaces_shouldReturnExpected() {
        Map<String, Object> inputs = HashMap.of(
                "str", "RESTAURANT",
                "mapping", "CUISINE = INTENT_CUISINES, RESTAURANT = INTENT_RESTAURANT",
                "default", "DEFAULT_CUISINE"
        );
        Map<String, Object> actual = replaceFunction.compute(inputs);
        assertThat(actual.get("str").get()).isEqualTo("INTENT_RESTAURANT");
    }
}

package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplanerpc.model.ModelType;
import lombok.val;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

public class ModelTypeTest {

    @Test
    void modelInferenceType_should_contain_the_same_values_as_ModelType() {
        val modelInferenceTypes = Arrays.stream(ModelInferenceType.values())
                .map(ModelInferenceType::name)
                .collect(Collectors.toList());
        val modelTypes = Arrays.stream(ModelType.values())
                .map(ModelType::name)
                .collect(Collectors.toList());
        assertThat(modelInferenceTypes).containsAll(modelTypes);
    }
}

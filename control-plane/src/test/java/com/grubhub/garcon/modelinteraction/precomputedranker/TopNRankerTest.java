package com.grubhub.garcon.modelinteraction.precomputedranker;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.*;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertIterableEquals;

class TopNRankerTest {
    private TopNRanker topNRanker;
    private ModelDTO testModel;
    @BeforeEach void setUp() {
        topNRanker = new TopNRanker();
        testModel = ModelDTO.builder().modelName("model1")
                .version("123.123")
                .modelType("PRECOMPUTED_RANKER")
                .modelOutputs(
                        List.of(
                                ModelOutputDTO.builder()
                                        .outputName("dummyOutput")
                                        .outputType("FLOAT")
                                        .build()
                        )
                )
                .build();
    }

    @Test void testProcessScores_top3() {
        var input = ModelInferenceInput.builder().model(testModel).build();
        List<Map<String, Object>> rows = List.of(
                HashMap.of(PrecomputedRankerModel.SCORE, 0.9),
                HashMap.of(PrecomputedRankerModel.SCORE, 0.7),
                HashMap.of(PrecomputedRankerModel.SCORE, 0.8),
                HashMap.of(PrecomputedRankerModel.SCORE, 0.6),
                HashMap.of(PrecomputedRankerModel.SCORE, 1.0)
        );
        var actualResult = topNRanker.processScores(input, rows, 3);
        var expectedResult = ModelInferenceResult.from(input, List.of(0.9f, 0.0f, 0.8f, 0.0f, 1.0f));
        assertIterableEquals(
                expectedResult.getOutput().get("dummyOutput").get()
                        .map(ModelInferenceOutputType::getFloat)
                        .toJavaList(),
                actualResult.getOutput().get("dummyOutput").get()
                        .map(ModelInferenceOutputType::getFloat)
                        .toJavaList()
        );

    }

    @Test void testProcessScores_top3_sameScore() {
        var input = ModelInferenceInput.builder().model(testModel).build();
        List<Map<String, Object>> rows = List.of(
                HashMap.of(PrecomputedRankerModel.SCORE, 0.9),
                HashMap.of(PrecomputedRankerModel.SCORE, 0.7),
                HashMap.of(PrecomputedRankerModel.SCORE, 0.7),
                HashMap.of(PrecomputedRankerModel.SCORE, 0.7),
                HashMap.of(PrecomputedRankerModel.SCORE, 0.7)
        );
        var actualResult = topNRanker.processScores(input, rows, 3);
        var expectedResult = ModelInferenceResult.from(input, List.of(0.9f, 0.7f, 0.7f, 0.0f, 0.0f));
        assertIterableEquals(
                expectedResult.getOutput().get("dummyOutput").get()
                        .map(ModelInferenceOutputType::getFloat)
                        .toJavaList(),
                actualResult.getOutput().get("dummyOutput").get()
                        .map(ModelInferenceOutputType::getFloat)
                        .toJavaList()
        );

    }

    @Test void testProcessScores_emptyList() {
        var input = ModelInferenceInput.builder().model(testModel).build();
        var rows = List.<Map<String, Object>>empty();
        var actualResult = topNRanker.processScores(input, rows, 3);
        var expectedResult = ModelInferenceResult.from(input, List.empty());
        assertEquals(expectedResult, actualResult);
    }
}

package com.grubhub.garcon.modelinteraction.adapter;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import com.google.inject.Key;
import com.google.inject.name.Names;
import com.grubhub.garcon.controlplane.services.ensembler.FeatureValueService;
import com.grubhub.garcon.controlplane.services.ensembler.impl.BaseEnsembleTest;
import com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureSourceType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelConfiguration;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.KNearestNeighbors;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.Neighbor;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.Normalization;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.QueryFilters;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.resources.SearchEmbeddingsRpcApi;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;

import java.util.Collections;

import static com.grubhub.garcon.controlplane.utils.InjectorProvider.INJECTOR;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class SearchEmbeddingsAdapterTest extends BaseEnsembleTest {

    @Mock
    private ModelInferenceInput modelInferenceInput;
    @Mock
    private TensorFlowModelConfiguration tensorFlowModelConfiguration;

    private SearchEmbeddingsAdapter searchEmbeddingsAdapter;
    private ListAppender<ILoggingEvent> appender;
    private final Logger searchEmbeddingsAdapterLogger = (Logger) LoggerFactory.getLogger((SearchEmbeddingsAdapter.class));
    private final SearchEmbeddingsRpcApi searchEmbeddingsRpcApi = INJECTOR.getInstance(Key.get(SearchEmbeddingsRpcApi.class,
            Names.named("SEARCH-EMBEDDINGS-rpc-client")));

    @BeforeEach
    void setup() {
        when(searchEmbeddingsRpcApi.getNearestNeighborsMultipleIndex(
                eq(List.of(1f, 1f, 1f, 1f, 1f).toJavaList()),
                eq(2), eq(6),
                eq(Collections.singletonList("test_knn_master_index")),
                any(Normalization.class),
                any(QueryFilters.class)))
                .thenReturn(new KNearestNeighbors(2, List.of(
                        new Neighbor("entity1", 1.0f, "master_index"),
                        new Neighbor("entity2", 0.9f, "master_index")
                ).toJavaList()));

        when(searchEmbeddingsRpcApi.getNearestNeighborsMultipleIndex(
                eq(List.of(2f, 2f, 2f, 2f, 2f).toJavaList()),
                eq(2),
                eq(6),
                eq(Collections.singletonList("test_knn_master_index")),
                any(Normalization.class),
                any(QueryFilters.class)))
                .thenReturn(new KNearestNeighbors(2, List.of(
                        new Neighbor("entity2", 0.85f, "master_index"),
                        new Neighbor("entity3", 0.8f, "master_index")
                ).toJavaList()));
        when(searchEmbeddingsRpcApi.getNearestNeighborsMultipleIndex(
                eq(List.of(3f, 3f, 3f, 3f, 3f).toJavaList()),
                eq(2),
                eq(6),
                eq(Collections.singletonList("test_knn_master_index")),
                any(Normalization.class),
                any(QueryFilters.class)))
                .thenReturn(new KNearestNeighbors(2, List.of(
                        new Neighbor("entity3", 0.7f, "master_index"),
                        new Neighbor("entity1", 0.6f, "master_index")
                ).toJavaList()));

        searchEmbeddingsAdapter = spy(new SearchEmbeddingsAdapter(
                searchEmbeddingsRpcApi,
                INJECTOR.getInstance(Key.get(FeatureValueService.class, Names.named("featureValueServiceWithCache"))),
                flowConfig,
                new SimpleMeterRegistry(),
                tensorFlowModelConfiguration,
                INJECTOR.getInstance(JavaDateTimeHelper.class)
        ));
    }

    @Test
    void getNearestNeighbors_returns_expected_entities_and_scores() {
        val data = buildAndInsertSearchEmbeddingsModel();
        val model = data.getModel();
        val queryVector = List.of(1f);
        val indexNameList = List.of("test_index");
        when(modelInferenceInput.getProcessedFeatures()).thenReturn(
                List.of(HashMap.of("k", 2, "search_k", 6, "query_vector", queryVector, "index_name_list", indexNameList)));
        when(modelInferenceInput.getModel()).thenReturn(model);
        when(modelInferenceInput.getModelName()).thenReturn(model.getModelName());

        when(searchEmbeddingsRpcApi.getNearestNeighborsMultipleIndex(anyList(), anyInt(), anyInt(), anyList(),
                any(Normalization.class), any(QueryFilters.class)))
                .thenReturn(new KNearestNeighbors(2, List.of(
                        new Neighbor("entity1", 1.0f, "master_index"),
                        new Neighbor("entity2", 0.9f, "master_index")
                ).toJavaList()));

        val nearestNeighborsMap = searchEmbeddingsAdapter.getNearestNeighbors(modelInferenceInput);
        val nearestNeighborsEntities = nearestNeighborsMap.get("entities").get();
        val nearestNeighborsScores = nearestNeighborsMap.get("scores").get();

        assertThat(nearestNeighborsEntities.length()).isEqualTo(1);
        assertThat(nearestNeighborsEntities.get(0).getStringArray()).contains("restaurant1", "restaurant2");
        assertThat(nearestNeighborsScores.length()).isEqualTo(1);
        assertThat(nearestNeighborsScores.get(0).getFloatArray()).contains(1.0f, 0.9f);
    }

    @Test
    void getNearestNeighbors_returns_expected_entities_and_scores_when_the_se_entity_mapping_is_enabled() {
        val data = buildAndInsertSearchEmbeddingsModel();
        val model = data.getModel();
        model.setModelFeatures(model.getModelFeatures().append(seMappingEntityFeature()));
        val queryVector = List.of(1f);
        val indexNameList = List.of("test_index");
        when(modelInferenceInput.getProcessedFeatures()).thenReturn(
                List.of(HashMap.of("k", 2, "search_k", 6, "query_vector", queryVector, "index_name_list", indexNameList, "se_entity_mapping_enabled", true)));
        when(modelInferenceInput.getModelName()).thenReturn(model.getModelName());

        when(searchEmbeddingsRpcApi.getNearestNeighborsMultipleIndex(anyList(), anyInt(), anyInt(), anyList(),
                any(Normalization.class), any(QueryFilters.class)))
                .thenReturn(new KNearestNeighbors(2, List.of(
                        new Neighbor("entity1", 1.0f, "master_index"),
                        new Neighbor("entity2", 0.9f, "master_index")
                ).toJavaList()));

        val nearestNeighborsMap = searchEmbeddingsAdapter.getNearestNeighbors(modelInferenceInput);
        val nearestNeighborsEntities = nearestNeighborsMap.get("entities").get();
        val nearestNeighborsScores = nearestNeighborsMap.get("scores").get();

        assertThat(nearestNeighborsEntities.length()).isEqualTo(1);
        assertThat(nearestNeighborsEntities.get(0).getStringArray()).contains("entity1", "entity2");
        assertThat(nearestNeighborsScores.length()).isEqualTo(1);
        assertThat(nearestNeighborsScores.get(0).getFloatArray()).contains(1.0f, 0.9f);
    }

    private FeatureDTO seMappingEntityFeature() {
        return FeatureDTO.builder()
                .featureName("se_mapping_entity_enabled")
                .featureSourceType("RUNTIME")
                .status("ENABLED")
                .featureOptional(true)
                .featureDefaultValue("true")
                .featureDefaultType("BOOLEAN")
                .skipFetching(false)
                .build();

    }

    @Test
    @SuppressWarnings("unchecked")
    @Disabled
    void getNearestNeighbors_logs_warnings_when_outputs_entities_or_scores_are_empty() {
        val model = createModelWithSkipFetchingFeature();
        when(modelInferenceInput.getProcessedFeatures()).thenReturn(List.empty());
        when(modelInferenceInput.getModel()).thenReturn(model);
        when(modelInferenceInput.getModelName()).thenReturn(model.getModelName());

        val emptyEntity = ModelInferenceOutputType.ofStringArray(List.empty());
        val emptyScore = ModelInferenceOutputType.ofStringArray(List.empty());
        doReturn(List.of(emptyEntity)).when(searchEmbeddingsAdapter).getEntitiesValues(any(List.class), any(Map.class));
        doReturn(List.of(emptyScore)).when(searchEmbeddingsAdapter).getScoresValues(any(List.class));

        appender = new ListAppender<>();
        appender.start();
        searchEmbeddingsAdapterLogger.addAppender(appender);

        searchEmbeddingsAdapter.getNearestNeighbors(modelInferenceInput);

        val entitiesWarningLog = appender.list.get(0);
        assertThat(entitiesWarningLog.getLevel().levelStr).isEqualTo(Level.DEBUG.toString());
        assertThat(entitiesWarningLog.getFormattedMessage()).startsWith(String.format("For model_name=%s, empty",
                model.getModelName()));
        val scoresWarningLog = appender.list.get(1);
        assertThat(scoresWarningLog.getLevel().levelStr).isEqualTo(Level.DEBUG.toString());
        assertThat(scoresWarningLog.getFormattedMessage()).startsWith(String.format("For model_name=%s, empty",
                model.getModelName()));

        searchEmbeddingsAdapterLogger.detachAppender(appender);
    }

    @Test
    @SuppressWarnings("unchecked")
    void getNearestNeighbors_throws_error_when_element_count_in_entities_and_scores_do_not_match() {
        val model = createModelWithSkipFetchingFeature();
        when(modelInferenceInput.getProcessedFeatures()).thenReturn(List.empty());
        when(modelInferenceInput.getModel()).thenReturn(model);
        when(modelInferenceInput.getModelName()).thenReturn(model.getModelName());

        val entityOne = ModelInferenceOutputType.ofStringArray(List.of("a", "b"));
        val entityTwo = ModelInferenceOutputType.ofStringArray(List.of("c", "d"));
        val nonEmptyScore = ModelInferenceOutputType.ofFloatArray(List.of(0.5f, 0.6f));
        val emptyScore = ModelInferenceOutputType.ofFloatArray(List.empty());
        doReturn(List.of(entityOne, entityTwo)).when(searchEmbeddingsAdapter).getEntitiesValues(any(List.class), any(Map.class));
        doReturn(List.of(nonEmptyScore, emptyScore)).when(searchEmbeddingsAdapter).getScoresValues(any(List.class));


        assertThatExceptionOfType(RuntimeException.class).isThrownBy(() -> searchEmbeddingsAdapter.getNearestNeighbors(modelInferenceInput))
                .withMessageContaining(
                        String.format("For model_name=%s, numbers of scores are not consistent in neighbors_map", model.getModelName()));

    }

    @Test
    void getNearestNeighbors_returns_mocked_indices_for_test_invocation() {
        val data = buildAndInsertSearchEmbeddingsModel();
        val model = data.getModel();
        val queryVector = List.of(0f);
        val indexNameList = List.of("test_index");
        when(modelInferenceInput.getProcessedFeatures()).thenReturn(
                List.of(HashMap.of("k", 2, "search_k", 6, "query_vector", queryVector, "index_name_list", indexNameList)));
        when(modelInferenceInput.getModelName()).thenReturn(model.getModelName());
        when(modelInferenceInput.isTestInvocation()).thenReturn(true);

        when(searchEmbeddingsRpcApi.getNearestNeighborsMultipleIndex(anyList(), anyInt(), anyInt(), anyList(),
                any(Normalization.class), any(QueryFilters.class)))
                .thenReturn(new KNearestNeighbors(1, List.of(
                        new Neighbor("mock_entity", 1.0f, "master_index_MOCK")
                ).toJavaList()));

        val nearestNeighborsMap = searchEmbeddingsAdapter.getNearestNeighbors(modelInferenceInput);
        val nearestNeighborsEntities = nearestNeighborsMap.get("entities").get();
        val nearestNeighborsScores = nearestNeighborsMap.get("scores").get();
        val nearestNeighborsMockedIndices = nearestNeighborsMap.get("mocked_indices").get();

        assertThat(nearestNeighborsEntities.length()).isEqualTo(1);
        assertThat(nearestNeighborsEntities.get(0).getStringArray()).contains("mock_entity");
        assertThat(nearestNeighborsScores.length()).isEqualTo(1);
        assertThat(nearestNeighborsScores.get(0).getFloatArray()).contains(1.0f);
        assertThat(nearestNeighborsMockedIndices.length()).isEqualTo(1);
        assertThat(nearestNeighborsMockedIndices.get(0).getStringArray()).contains("master_index_MOCK");
    }

    private ModelDTO createModelWithSkipFetchingFeature() {
        val feature = FeatureDTO
                .builder()
                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                .majorVersion("3")
                .minorVersion("1")
                .featureName("RestaurantFeature")
                .featureStoreFields(List.empty())
                .skipFetching(true)
                .build();

        val model = ModelDTO.builder()
                .modelDescription("Test Search Embedding")
                .modelName("search_embeddings_model")
                .modelType(ModelType.SEARCH_EMBEDDINGS.name())
                .versioningStrategy("DYNAMIC")
                .status("ENABLED")
                .location("")
                .servingLocation("")
                .version("3.32")
                .modelFeatures(List.of(feature))
                .modelOutputs(ModelFactory.buildModelOutputs())
                .modelGroup("search")
                .build();

        modelService.createOrUpdateModel(model);
        return model;
    }


}

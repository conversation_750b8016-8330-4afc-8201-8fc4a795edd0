package com.grubhub.garcon.modelinteraction.tensorflow;

import com.google.protobuf.InvalidProtocolBufferException;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.grpc.GrpcChannelFactory;
import com.grubhub.garcon.grpc.GrpcConfiguration;
import com.grubhub.garcon.modelinteraction.PredictionClientFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.tensorflow.example.Example;
import org.tensorflow.example.Features;
import org.tensorflow.framework.TensorProto;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.MockitoAnnotations.openMocks;

class TensorFlowModelAdaptorTest {

    @Mock private FlowConfig flowConfig;
    @Mock private PredictionClientFactory predictionClientFactory;
    @Mock private GrpcChannelFactory grpcChannelFactory;
    @Mock private MeterRegistry meterRegistry;
    @Mock private TensorFlowModelConfiguration tensorFlowModelConfiguration;
    @Mock private java.util.Map<String, TfsClusterInteraction> tfsClusterInteractions;
    @Mock private ModelService modelService;
    @Mock private GrpcConfiguration grpcConfiguration;

    private TensorFlowModelAdaptor subject;

    @BeforeEach
    public void setUp() {
        openMocks(this);
        subject = new TensorFlowModelAdaptor(
                predictionClientFactory,
                grpcChannelFactory,
                tensorFlowModelConfiguration,
                meterRegistry,
                flowConfig,
                modelService,
                grpcConfiguration
        );
    }

    @Test
    public void test_convertFeaturesToSerializedExamples() throws InvalidProtocolBufferException {
        Long longVal = (long) 9;
        Integer integerVal = 10;
        Short shortVal = (short) 11;
        Byte byteVal = (byte) 'a';
        Float floatVal = 12.0f;
        Double doubleVal = 13.0;
        String stringVal = "abc123";
        Collection<Long> longCollection = List.of(1L, 2L, 3L).toJavaList();
        Collection<Integer> integerCollection = List.of(1, 2, 3).toJavaList();
        Collection<Short> shortCollection = List.of((short) 4, (short) 5, (short) 6).toJavaList();
        Collection<Byte> byteCollection = List.of((byte) 'a', (byte) 'b', (byte) 'c').toJavaList();
        Collection<Float> floatCollection = List.of(10.0f, 11.0f, 12.0f).toJavaList();
        Collection<Double> doubleCollection = List.of(13.0, 14.0, 15.0).toJavaList();
        Collection<String> stringCollection = List.of("abc", "123", "def").toJavaList();
        List<Integer> vavrIntegerList = List.of(1, 2, 3);
        List<Float> vavrFloatList = List.of(10.0f, 11.0f, 12.0f);
        List<String> vavrStringList = List.of("abc", "123", "def");

        Map<String, Object> featureMap = HashMap.empty();
        featureMap = featureMap.put("longVal", longVal);
        featureMap = featureMap.put("integerVal", integerVal);
        featureMap = featureMap.put("shortVal", shortVal);
        featureMap = featureMap.put("byteVal", byteVal);
        featureMap = featureMap.put("floatVal", floatVal);
        featureMap = featureMap.put("doubleVal", doubleVal);
        featureMap = featureMap.put("stringVal", stringVal);
        featureMap = featureMap.put("longCollection", longCollection);
        featureMap = featureMap.put("integerCollection", integerCollection);
        featureMap = featureMap.put("shortCollection", shortCollection);
        featureMap = featureMap.put("byteCollection", byteCollection);
        featureMap = featureMap.put("floatCollection", floatCollection);
        featureMap = featureMap.put("doubleCollection", doubleCollection);
        featureMap = featureMap.put("stringCollection", stringCollection);
        featureMap = featureMap.put("vavrIntegerList", vavrIntegerList);
        featureMap = featureMap.put("vavrFloatList", vavrFloatList);
        featureMap = featureMap.put("vavrStringList", vavrStringList);
        List<Map<String, Object>> processedFeatures = List.of(featureMap);
        Map<String, TensorProto> result = subject.convertFeaturesToSerializedExamples("junit", processedFeatures);

        Features features = Example.parseFrom(result.get("junit").get().getStringVal(0)).getFeatures();

        assertEquals(1, features.getFeatureOrThrow("longVal").getInt64List().getValueCount());
        assertEquals(9, features.getFeatureOrThrow("longVal").getInt64List().getValue(0));

        assertEquals(1, features.getFeatureOrThrow("integerVal").getInt64List().getValueCount());
        assertEquals(10, features.getFeatureOrThrow("integerVal").getInt64List().getValue(0));

        assertEquals(1, features.getFeatureOrThrow("shortVal").getInt64List().getValueCount());
        assertEquals(11, features.getFeatureOrThrow("shortVal").getInt64List().getValue(0));

        assertEquals(1, features.getFeatureOrThrow("byteVal").getInt64List().getValueCount());
        assertEquals(97, features.getFeatureOrThrow("byteVal").getInt64List().getValue(0));

        assertEquals(1, features.getFeatureOrThrow("floatVal").getFloatList().getValueCount());
        assertEquals(12.0f, features.getFeatureOrThrow("floatVal").getFloatList().getValue(0));

        assertEquals(1, features.getFeatureOrThrow("doubleVal").getFloatList().getValueCount());
        assertEquals(13.0f, features.getFeatureOrThrow("doubleVal").getFloatList().getValue(0));

        assertEquals(1, features.getFeatureOrThrow("stringVal").getBytesList().getValueCount());
        assertEquals("abc123", features.getFeatureOrThrow("stringVal").getBytesList().getValue(0).toStringUtf8());

        assertEquals(3, features.getFeatureOrThrow("longCollection").getInt64List().getValueCount());
        assertEquals(1, features.getFeatureOrThrow("longCollection").getInt64List().getValue(0));
        assertEquals(2, features.getFeatureOrThrow("longCollection").getInt64List().getValue(1));
        assertEquals(3, features.getFeatureOrThrow("longCollection").getInt64List().getValue(2));

        assertEquals(3, features.getFeatureOrThrow("integerCollection").getInt64List().getValueCount());
        assertEquals(1, features.getFeatureOrThrow("integerCollection").getInt64List().getValue(0));
        assertEquals(2, features.getFeatureOrThrow("integerCollection").getInt64List().getValue(1));
        assertEquals(3, features.getFeatureOrThrow("integerCollection").getInt64List().getValue(2));

        assertEquals(3, features.getFeatureOrThrow("shortCollection").getInt64List().getValueCount());
        assertEquals(4, features.getFeatureOrThrow("shortCollection").getInt64List().getValue(0));
        assertEquals(5, features.getFeatureOrThrow("shortCollection").getInt64List().getValue(1));
        assertEquals(6, features.getFeatureOrThrow("shortCollection").getInt64List().getValue(2));

        assertEquals(3, features.getFeatureOrThrow("byteCollection").getInt64List().getValueCount());
        assertEquals(97, features.getFeatureOrThrow("byteCollection").getInt64List().getValue(0));
        assertEquals(98, features.getFeatureOrThrow("byteCollection").getInt64List().getValue(1));
        assertEquals(99, features.getFeatureOrThrow("byteCollection").getInt64List().getValue(2));

        assertEquals(3, features.getFeatureOrThrow("floatCollection").getFloatList().getValueCount());
        assertEquals(10.0f, features.getFeatureOrThrow("floatCollection").getFloatList().getValue(0));
        assertEquals(11.0f, features.getFeatureOrThrow("floatCollection").getFloatList().getValue(1));
        assertEquals(12.0f, features.getFeatureOrThrow("floatCollection").getFloatList().getValue(2));

        assertEquals(3, features.getFeatureOrThrow("doubleCollection").getFloatList().getValueCount());
        assertEquals(13.0f, features.getFeatureOrThrow("doubleCollection").getFloatList().getValue(0));
        assertEquals(14.0f, features.getFeatureOrThrow("doubleCollection").getFloatList().getValue(1));
        assertEquals(15.0f, features.getFeatureOrThrow("doubleCollection").getFloatList().getValue(2));

        assertEquals(3, features.getFeatureOrThrow("stringCollection").getBytesList().getValueCount());
        assertEquals("abc", features.getFeatureOrThrow("stringCollection").getBytesList().getValue(0).toStringUtf8());
        assertEquals("123", features.getFeatureOrThrow("stringCollection").getBytesList().getValue(1).toStringUtf8());
        assertEquals("def", features.getFeatureOrThrow("stringCollection").getBytesList().getValue(2).toStringUtf8());

        assertEquals(3, features.getFeatureOrThrow("vavrIntegerList").getInt64List().getValueCount());
        assertEquals(1, features.getFeatureOrThrow("vavrIntegerList").getInt64List().getValue(0));
        assertEquals(2, features.getFeatureOrThrow("vavrIntegerList").getInt64List().getValue(1));
        assertEquals(3, features.getFeatureOrThrow("vavrIntegerList").getInt64List().getValue(2));

        assertEquals(3, features.getFeatureOrThrow("vavrFloatList").getFloatList().getValueCount());
        assertEquals(10.0f, features.getFeatureOrThrow("vavrFloatList").getFloatList().getValue(0));
        assertEquals(11.0f, features.getFeatureOrThrow("vavrFloatList").getFloatList().getValue(1));
        assertEquals(12.0f, features.getFeatureOrThrow("vavrFloatList").getFloatList().getValue(2));

        assertEquals(3, features.getFeatureOrThrow("vavrStringList").getBytesList().getValueCount());
        assertEquals("abc", features.getFeatureOrThrow("vavrStringList").getBytesList().getValue(0).toStringUtf8());
        assertEquals("123", features.getFeatureOrThrow("vavrStringList").getBytesList().getValue(1).toStringUtf8());
        assertEquals("def", features.getFeatureOrThrow("vavrStringList").getBytesList().getValue(2).toStringUtf8());
    }

}

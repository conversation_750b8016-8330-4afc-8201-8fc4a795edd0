package com.grubhub.garcon.modelinteraction.computablefeatures;

import com.grubhub.garcon.modelinteraction.computablefeatures.stringconcat.StringConcatenateFourFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.stringconcat.StringConcatenateFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.stringconcat.StringConcatenateThreeFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.textnormalizer.TextNormalizerFunction;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import lombok.val;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class FunctionFeatureTest {

    @Test
    public void coordinatesToGeohash_compute_should_return_geohash_as_output() {
        Map<String, Object> inputs = HashMap.of(
                CoordinatesToGeohashFunction.LATITUDE, "40.7128",
                CoordinatesToGeohashFunction.LONGITUDE, "-74.0060",
                CoordinatesToGeohashFunction.PRECISION, "5"
        );
        CoordinatesToGeohashFunction function = new CoordinatesToGeohashFunction();

        val functionOutput = function.compute(inputs);

        assertThat(functionOutput.size()).isEqualTo(1);
        assertThat(functionOutput.get(CoordinatesToGeohashFunction.GEOHASH).get()).isEqualTo("dr5re");
    }

    @Test
    public void geohashToCoordinates_compute_should_return_geohash_as_output() {
        Map<String, Object> input = HashMap.of(GeohashToCoordinatesFunction.GEOHASH, "dr5re");
        GeohashToCoordinatesFunction function = new GeohashToCoordinatesFunction();

        val functionOutput = function.compute(input);

        assertThat(functionOutput.size()).isEqualTo(2);
        assertThat(functionOutput.get(GeohashToCoordinatesFunction.LATITUDE).get()).matches(lat -> String.valueOf(lat).startsWith("40.7"));
        assertThat(functionOutput.get(GeohashToCoordinatesFunction.LONGITUDE).get()).matches(lng -> String.valueOf(lng).startsWith("-74.0"));
    }

    @Test
    public void stringToLowerCase_compute_should_return_lowercase_string_as_output() {
        Map<String, Object> input = HashMap.of(StringToLowerCaseFunction.INPUT_STRING, "A#hIjk&_iqu525PMLD");
        StringToLowerCaseFunction function = new StringToLowerCaseFunction();

        val functionOutput = function.compute(input);

        assertThat(functionOutput.size()).isEqualTo(1);
        assertThat(functionOutput.get(StringToLowerCaseFunction.OUTPUT_STRING).get()).isEqualTo("a#hijk&_iqu525pmld");
    }

    @Test
    public void stringToLowerCase_compute_should_return_empty_string_if_input_is_null_or_empty() {
        Map<String, Object> nullInput = HashMap.of(StringToLowerCaseFunction.INPUT_STRING, null);
        Map<String, Object> blankInput = HashMap.of(StringToLowerCaseFunction.INPUT_STRING, "");
        StringToLowerCaseFunction function = new StringToLowerCaseFunction();

        val functionOutputNull = function.compute(nullInput);
        assertThat(functionOutputNull.get(StringToLowerCaseFunction.OUTPUT_STRING).get()).isEqualTo("");

        val functionOutputBlank = function.compute(blankInput);
        assertThat(functionOutputBlank.get(StringToLowerCaseFunction.OUTPUT_STRING).get()).isEqualTo("");
    }

    @Test
    public void textNormalizer_compute_should_normalize_text() {
        Map<String, Object> input = HashMap.of(TextNormalizerFunction.INPUT_STRING, "I,   want quick deliver of the best Joe's pizzas in towns!!");
        TextNormalizerFunction function = new TextNormalizerFunction();

        val functionOutput = function.compute(input);
        assertThat(functionOutput.get(TextNormalizerFunction.OUTPUT_STRING).get()).isEqualTo("i want quick best joe pizza town");
    }

    @Test
    public void stringCoalesce_compute_should_return_first_if_not_null_or_empty() {
        val testInput = "test*string";
        Map<String, Object> inputs = HashMap.of(
                StringCoalesceFunction.INPUT_STRING_ONE, testInput,
                StringCoalesceFunction.INPUT_STRING_TWO, "stringTwo",
                StringCoalesceFunction.INPUT_STRING_DEFAULT, "default"
        );
        StringCoalesceFunction function = new StringCoalesceFunction();

        val functionOutput = function.compute(inputs);
        assertThat(functionOutput.get(StringCoalesceFunction.OUTPUT_STRING).get()).isEqualTo(testInput);
    }

    @Test
    public void stringCoalesce_compute_should_return_second_if_first_is_null_or_empty_and_second_is_not() {
        val testInput = "test*string";
        Map<String, Object> inputsNullFirst = HashMap.of(
                StringCoalesceFunction.INPUT_STRING_ONE, null,
                StringCoalesceFunction.INPUT_STRING_TWO, testInput,
                StringCoalesceFunction.INPUT_STRING_DEFAULT, "default"
        );
        Map<String, Object> inputsEmptyFirst = HashMap.of(
                StringCoalesceFunction.INPUT_STRING_ONE, "",
                StringCoalesceFunction.INPUT_STRING_TWO, testInput,
                StringCoalesceFunction.INPUT_STRING_DEFAULT, "default"
        );

        StringCoalesceFunction function = new StringCoalesceFunction();

        val functionOutputNullFirst = function.compute(inputsNullFirst);
        assertThat(functionOutputNullFirst.get(StringCoalesceFunction.OUTPUT_STRING).get()).isEqualTo(testInput);

        val functionOutputEmptyFirst = function.compute(inputsEmptyFirst);
        assertThat(functionOutputEmptyFirst.get(StringCoalesceFunction.OUTPUT_STRING).get()).isEqualTo(testInput);
    }

    @Test
    public void stringCoalesce_compute_should_return_default_if_first_and_second_are_null_or_empty() {
        val testInput = "test*string";
        Map<String, Object> inputsWithNormalDefault = HashMap.of(
                StringCoalesceFunction.INPUT_STRING_ONE, null,
                StringCoalesceFunction.INPUT_STRING_TWO, "",
                StringCoalesceFunction.INPUT_STRING_DEFAULT, testInput
        );
        Map<String, Object> inputsWithNullDefault = HashMap.of(
                StringCoalesceFunction.INPUT_STRING_ONE, null,
                StringCoalesceFunction.INPUT_STRING_TWO, "",
                StringCoalesceFunction.INPUT_STRING_DEFAULT, null
        );

        StringCoalesceFunction function = new StringCoalesceFunction();

        val functionOutputNormalDefault = function.compute(inputsWithNormalDefault);
        assertThat(functionOutputNormalDefault.get(StringCoalesceFunction.OUTPUT_STRING).get()).isEqualTo(testInput);

        val functionOutputNullDefault = function.compute(inputsWithNullDefault);
        assertThat(functionOutputNullDefault.get(StringCoalesceFunction.OUTPUT_STRING).get()).isEqualTo("");
    }

    @Test
    public void stringConcat_compute_should_return_concatenated_string() {
        val stringOne = "hello";
        val stringTwo = "world";
        val separator = "##";
        Map<String, Object> inputs = HashMap.of(
                StringConcatenateFunction.INPUT_STRING_ONE, stringOne,
                StringConcatenateFunction.INPUT_STRING_TWO, stringTwo,
                StringConcatenateFunction.INPUT_SEPARATOR, separator
        );
        StringConcatenateFunction function = new StringConcatenateFunction();

        val functionOutput = function.compute(inputs);
        assertThat(functionOutput.get(StringConcatenateFunction.OUTPUT_STRING).get()).isEqualTo(stringOne + separator + stringTwo);
    }

    @Test
    public void stringConcat_compute_should_use_empty_string_in_place_of_null_values() {
        val stringTwo = "world";
        val separator = "##";
        Map<String, Object> inputs = HashMap.of(
                StringConcatenateFunction.INPUT_STRING_ONE, null,
                StringConcatenateFunction.INPUT_STRING_TWO, stringTwo,
                StringConcatenateFunction.INPUT_SEPARATOR, separator
        );
        StringConcatenateFunction function = new StringConcatenateFunction();

        val functionOutput = function.compute(inputs);
        assertThat(functionOutput.get(StringConcatenateFunction.OUTPUT_STRING).get()).isEqualTo(separator + stringTwo);
    }

    @Test
    public void stringConcat3_compute_should_return_concatenated_string() {
        val stringOne = "manners";
        val stringTwo = "maketh";
        val stringThree = "man";
        val separator = "##";
        Map<String, Object> inputs = HashMap.of(
                StringConcatenateThreeFunction.INPUT_STRING_ONE, stringOne,
                StringConcatenateThreeFunction.INPUT_STRING_TWO, stringTwo,
                StringConcatenateThreeFunction.INPUT_STRING_THREE, stringThree,
                StringConcatenateThreeFunction.INPUT_SEPARATOR, separator
        );
        StringConcatenateThreeFunction function = new StringConcatenateThreeFunction();

        val functionOutput = function.compute(inputs);
        assertThat(functionOutput.get(StringConcatenateFunction.OUTPUT_STRING).get()).isEqualTo(
                stringOne + separator + stringTwo + separator + stringThree);
    }

    @Test
    public void stringConcat3_compute_should_use_empty_string_in_place_of_null_values() {
        val stringOne = "manners";
        val stringThree = "man";
        val separator = "##";
        Map<String, Object> inputs = HashMap.of(
                StringConcatenateThreeFunction.INPUT_STRING_ONE, stringOne,
                StringConcatenateThreeFunction.INPUT_STRING_TWO, null,
                StringConcatenateThreeFunction.INPUT_STRING_THREE, stringThree,
                StringConcatenateThreeFunction.INPUT_SEPARATOR, separator
        );
        StringConcatenateThreeFunction function = new StringConcatenateThreeFunction();

        val functionOutput = function.compute(inputs);
        assertThat(functionOutput.get(StringConcatenateFunction.OUTPUT_STRING).get()).isEqualTo(
                stringOne + separator + separator + stringThree);
    }

    @Test
    public void stringConcat4_compute_should_return_concatenated_string() {
        val stringOne = "I";
        val stringTwo = "am";
        val stringThree = "your";
        val stringFour = "father";
        val separator = "##";
        Map<String, Object> inputs = HashMap.of(
                StringConcatenateFourFunction.INPUT_STRING_ONE, stringOne,
                StringConcatenateFourFunction.INPUT_STRING_TWO, stringTwo,
                StringConcatenateFourFunction.INPUT_STRING_THREE, stringThree,
                StringConcatenateFourFunction.INPUT_STRING_FOUR, stringFour,
                StringConcatenateFourFunction.INPUT_SEPARATOR, separator
        );
        StringConcatenateFourFunction function = new StringConcatenateFourFunction();

        val functionOutput = function.compute(inputs);
        assertThat(functionOutput.get(StringConcatenateFunction.OUTPUT_STRING).get()).isEqualTo(
                stringOne + separator + stringTwo + separator + stringThree + separator + stringFour);
    }

    @Test
    public void stringConcat4_compute_should_use_empty_string_in_place_of_null_values() {
        val stringOne = "I";
        val stringThree = "your";
        val separator = "##";
        Map<String, Object> inputs = HashMap.of(
                StringConcatenateFourFunction.INPUT_STRING_ONE, stringOne,
                StringConcatenateFourFunction.INPUT_STRING_TWO, null,
                StringConcatenateFourFunction.INPUT_STRING_THREE, stringThree,
                StringConcatenateFourFunction.INPUT_STRING_FOUR, null,
                StringConcatenateFourFunction.INPUT_SEPARATOR, separator
        );
        StringConcatenateFourFunction function = new StringConcatenateFourFunction();

        val functionOutput = function.compute(inputs);
        assertThat(functionOutput.get(StringConcatenateFunction.OUTPUT_STRING).get()).isEqualTo(
                stringOne + separator + separator + stringThree + separator);
    }
}

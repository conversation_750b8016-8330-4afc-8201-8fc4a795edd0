package com.grubhub.garcon.modelinteraction.distribution;

import com.grubhub.garcon.modelinteraction.RandomDistributionModel;
import lombok.val;
import name.falgout.jeffrey.testing.junit.guice.GuiceExtension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith({MockitoExtension.class, GuiceExtension.class})
public class RandomDistributionFactoryTest {
    private final RandomDistributionFactory target = new RandomDistributionFactory();

    @Test()
    public void invalidAlphaBeta_shouldReturnEmpty() {
        val featuresMap = new HashMap<String, Object>();
        featuresMap.put(RandomDistributionModel.INTERNAL_ALPHA_PARAMS_KEY, new Double[]{1., 7., 2.});
        featuresMap.put(RandomDistributionModel.INTERNAL_BETA_PARAMS_KEY, new Double[]{2., 5.});
        featuresMap.put(RandomDistributionModel.INTERNAL_TARGET_VALUES_KEY, new String[]{"v1", "v2", "v3"});
        featuresMap.put(RandomDistributionModel.TRACKING_ID, "tracking_id");

        val distribution = DistributionType.BETA;
        val distributionResult = DistributionResultType.RANDOM_SAMPLING;

        BaseDistribution imageDistribution = target
                .createRandomDistribution(distribution, distributionResult, featuresMap);

        assertThat(imageDistribution.getClass()).isEqualTo(BetaDistribution.class);
        assertThat(((BetaDistribution) imageDistribution).getBetaInputs()).isEmpty();
    }

    @Test()
    public void validAlphaBeta_shouldCorrectlyConstruct() {
        val featuresMap = new HashMap<String, Object>();
        featuresMap.put(RandomDistributionModel.INTERNAL_ALPHA_PARAMS_KEY, new Double[]{1., 7., 2.});
        featuresMap.put(RandomDistributionModel.INTERNAL_BETA_PARAMS_KEY, new Double[]{2., 5., 3.});
        featuresMap.put(RandomDistributionModel.INTERNAL_TARGET_VALUES_KEY, new String[]{"v1", "v2", "v3"});
        featuresMap.put(RandomDistributionModel.TRACKING_ID, "tracking_id");

        val distribution = DistributionType.BETA;
        val distributionResult = DistributionResultType.RANDOM_SAMPLING;


        BaseDistribution imageDistribution = target
                .createRandomDistribution(distribution, distributionResult, featuresMap);

        assertThat(imageDistribution.getClass()).isEqualTo(BetaDistribution.class);
        assertThat(((BetaDistribution) imageDistribution).getBetaInputs()).isNotEmpty();
        assertThat(((BetaDistribution) imageDistribution).getBetaInputs().size()).isEqualTo(3);
    }

    @Test()
    public void validAlphaBeta_shouldCorrectlyConstruct_values() {
        val featuresMap = new HashMap<String, Object>();
        featuresMap.put(RandomDistributionModel.INTERNAL_ALPHA_PARAMS_KEY, new Double[]{1., 7., 2.});
        featuresMap.put(RandomDistributionModel.INTERNAL_BETA_PARAMS_KEY, new Integer[]{2, 5, 3});
        featuresMap.put(RandomDistributionModel.INTERNAL_TARGET_VALUES_KEY, new String[]{"v1", "v2", "v3"});
        featuresMap.put(RandomDistributionModel.TRACKING_ID, "tracking_id");

        val distribution = DistributionType.BETA;
        val distributionResult = DistributionResultType.RANDOM_SAMPLING;


        BaseDistribution imageDistribution = target
                .createRandomDistribution(distribution, distributionResult, featuresMap);

        assertThat(imageDistribution.getClass()).isEqualTo(BetaDistribution.class);
        assertThat(((BetaDistribution) imageDistribution).getBetaInputs()).isNotEmpty();
        assertThat(((BetaDistribution) imageDistribution).getBetaInputs().size()).isEqualTo(3);
    }
}

package com.grubhub.garcon.modelinteraction.queryexpansionmodel;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ModelPromptTest {
    private ModelPromptLoader promptLoader;
    private ModelPrompt modelPrompt;

    @BeforeEach
    public void setUp() {
        promptLoader = new ResourceModelPromptLoader("models/LLM/test_prompt.txt");
        modelPrompt = promptLoader.loadPrompt();
    }

    @Test
    public void loadPrompt_withResourceLoader_success() {
        assertNotNull(modelPrompt);
        assertNotNull(modelPrompt.getPromptTemplate());
    }

    @Test
    public void loadPrompt_withResourceLoader_invalidPath() {
        promptLoader = new ResourceModelPromptLoader("invalid/path");
        Exception exception = assertThrows(RuntimeException.class, () -> promptLoader.loadPrompt());
        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("Failed loading prompt with Resource Loader at promptPath="));
    }

    @Test
    public void resolvePromptWithQuery_success() {
        String query = "chinese";
        String promptWithQuery = modelPrompt.resolvePromptWithQuery(query);

        assertNotNull(promptWithQuery);
        assertTrue(promptWithQuery.contains(query), "The prompt should contain the query text.");
    }


}

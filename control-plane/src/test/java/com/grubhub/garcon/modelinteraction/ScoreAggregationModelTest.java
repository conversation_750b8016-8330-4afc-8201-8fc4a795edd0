package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplane.services.ensembler.impl.BaseEnsembleTest;
import com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory;
import com.grubhub.garcon.controlplane.services.ensembler.rules.FeatureDefaultValueType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import lombok.val;
import org.junit.jupiter.api.Test;

import static com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory.buildRuntimeFeature;
import static org.assertj.core.api.Assertions.assertThat;

class ScoreAggregationModelTest extends BaseEnsembleTest {

    @Test
    void invoke_shouldReturnExpectedForMax() {
        // Three feature maps (i.e. topics).
        // Restaurants: "a", "b", "c", "d", "e"
        // Restaurant scores: 10, 15, 3, 20, 9 (for instance, from M1).
        // Calculate the MAX score in each topic.

        val model = createScoreAggregationModel();
        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("config_agg_function", "MAX")
                .add("config_item_ids", List.of("a", "b", "c", "d", "e").toJavaList())
                .add("config_item_scores", List.of(10, 15, 3, 20, 9).toJavaList())
                .build();
        java.util.Map<String, Object> features1 = HashMap.of(
                "items", (Object) List.of("a", "b").toJavaList(),
                "item_weights", (Object) List.of(10, 15).toJavaList()).toJavaMap();
        java.util.Map<String, Object> features2 = HashMap.of(
                "items", (Object) List.of("a", "c").toJavaList(),
                "item_weights", (Object) List.of(10, 3).toJavaList()
        ).toJavaMap();
        java.util.Map<String, Object> features3 = HashMap.of(
                "items", (Object) List.of("d", "e").toJavaList(),
                "item_weights", (Object) List.of(20, 9).toJavaList()
        ).toJavaMap();
        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(List.of(
                        features1,
                        features2,
                        features3
                ).toJavaList())
                .build());

        assertThat(modelOutput.getFloatResponse()).contains(15.00f, 10.00f, 20.00f);
    }

    @Test
    void invoke_shouldReturnExpectedForMin() {
        // Three feature maps (i.e. topics).
        // Restaurants: "a", "b", "c", "d", "e"
        // Restaurant scores: 10, 15, 3, 20, 9 (for instance, from M1).
        // Calculate the MIN score in each topic.

        val model = createScoreAggregationModel();
        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("config_agg_function", "MIN")
                .add("config_item_ids", List.of("a", "b", "c", "d", "e").toJavaList())
                .add("config_item_scores", List.of(10, 15, 3, 20, 9).toJavaList())
                .build();
        java.util.Map<String, Object> features1 = HashMap.of(
                "items", (Object) List.of("a", "b").toJavaList(),
                "item_weights", (Object) List.of(10, 15).toJavaList()).toJavaMap();
        java.util.Map<String, Object> features2 = HashMap.of(
                "items", (Object) List.of("a", "c").toJavaList(),
                "item_weights", (Object) List.of(10, 3).toJavaList()
        ).toJavaMap();
        java.util.Map<String, Object> features3 = HashMap.of(
                "items", (Object) List.of("d", "e").toJavaList(),
                "item_weights", (Object) List.of(20, 9).toJavaList()
        ).toJavaMap();
        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(List.of(
                        features1,
                        features2,
                        features3
                ).toJavaList())
                .build());


        assertThat(modelOutput.getFloatResponse()).contains(10.00f, 3.00f, 9.00f);
    }


    @Test
    void invoke_shouldReturnExpectedForMin_nonExistentTopics() {
        // It should return 0 for each of the item list sent as empty.

        val model = createScoreAggregationModelForInternalInvocation();
        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("config_agg_function", "MIN")
                .add("config_item_ids", "[]")
                .add("config_item_scores", "[]")
                .build();

        java.util.Map<String, Object> features1 = HashMap.of(
                "items", "[]",
                "item_weights", (Object) "[]").toJavaMap();

        java.util.Map<String, Object> features2 = HashMap.of(
                "items", "[]",
                "item_weights", (Object) "[]"
        ).toJavaMap();

        java.util.Map<String, Object> features3 = HashMap.of(
                "items", "[]",
                "item_weights", (Object) "[]"
        ).toJavaMap();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(List.of(
                        features1,
                        features2,
                        features3
                ).toJavaList())
                .build());


        assertThat(modelOutput.getFloatResponse().size()).isEqualTo(3);
        assertThat(modelOutput.getFloatResponse()).isEqualTo(List.of(0f, 0f, 0f));
    }


    @Test
    void invoke_shouldReturnExpectedForAvg() {
        // Three feature maps (i.e. topics).
        // Restaurants: "a", "b", "c", "d", "e"
        // Restaurant scores: 10, 15, 3, 20, 9 (for instance, from M1).
        // Calculate the AVG score in each topic.

        val model = createScoreAggregationModel();
        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("config_agg_function", "AVG")
                .add("config_item_ids", List.of("a", "b", "c", "d", "e").toJavaList())
                .add("config_item_scores", List.of(10, 15, 3, 20, 9).toJavaList())
                .build();
        java.util.Map<String, Object> features1 = HashMap.of(
                "items", (Object) List.of("a", "b").toJavaList(),
                "item_weights", (Object) List.of(10, 15).toJavaList()).toJavaMap();
        java.util.Map<String, Object> features2 = HashMap.of(
                "items", (Object) List.of("a", "c").toJavaList(),
                "item_weights", (Object) List.of(10, 3).toJavaList()
        ).toJavaMap();
        java.util.Map<String, Object> features3 = HashMap.of(
                "items", (Object) List.of("d", "e").toJavaList(),
                "item_weights", (Object) List.of(20, 9).toJavaList()
        ).toJavaMap();
        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(List.of(
                        features1,
                        features2,
                        features3
                ).toJavaList())
                .build());

        assertThat(modelOutput.getFloatResponse()).contains(12.5f, 6.5f, 14.5f);
    }

    @Test
    void invoke_shouldReturnExpectedForAvg_oneFeatureMapDoesNotHaveData_returnZero() {
        val model = createScoreAggregationModel();
        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("config_agg_function", "AVG")
                .add("config_item_ids", List.of("a", "b", "c", "d", "e").toJavaList())
                .add("config_item_scores", List.of(10, 15, 3, 20, 9).toJavaList())
                .build();
        java.util.Map<String, Object> features1 = HashMap.of(
                "items", (Object) List.of("a", "b").toJavaList(),
                "item_weights", (Object) List.of(10, 15).toJavaList()).toJavaMap();
        java.util.Map<String, Object> features2 = HashMap.of(
                "items", "[]",
                "item_weights", (Object) "[]"
        ).toJavaMap();
        java.util.Map<String, Object> features3 = HashMap.of(
                "items", (Object) List.of("d", "e").toJavaList(),
                "item_weights", (Object) List.of(20, 9).toJavaList()
        ).toJavaMap();
        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(List.of(
                        features1,
                        features2,
                        features3
                ).toJavaList())
                .build());

        assertThat(modelOutput.getFloatResponse().size()).isEqualTo(3);
        assertThat(modelOutput.getFloatResponse()).isEqualTo(List.of(12.5f, 0.0f, 14.5f));
    }


    @Test
    void invoke_shouldReturnExpectedForWeightedAvg() {
        // Three feature maps (i.e. topics).
        // Restaurants: "a", "b", "c", "d", "e"
        // Restaurant scores: 10, 15, 3, 20, 9 (for instance, from M1).
        // Calculate the WEIGHTED_AVG score in each topic, this is the M1 scores multiplied by the times that a restaurant appears in the topic.

        val model = createScoreAggregationModel();
        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("config_agg_function", "WEIGHTED_AVG")
                .add("config_item_ids", List.of("a", "b", "c", "d", "e").toJavaList())
                .add("config_item_scores", List.of(10, 15, 3, 20, 9).toJavaList())
                .build();
        java.util.Map<String, Object> features1 = HashMap.of(
                "items", (Object) List.of("a", "b").toJavaList(),
                "item_weights", (Object) List.of(1, 1).toJavaList()).toJavaMap();
        java.util.Map<String, Object> features2 = HashMap.of(
                "items", (Object) List.of("a", "c").toJavaList(),
                "item_weights", (Object) List.of(1, 1).toJavaList()
        ).toJavaMap();
        java.util.Map<String, Object> features3 = HashMap.of(
                "items", (Object) List.of("d", "e").toJavaList(),
                "item_weights", (Object) List.of(2, 1).toJavaList()
        ).toJavaMap();
        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(List.of(
                        features1,
                        features2,
                        features3
                ).toJavaList())
                .build());

        assertThat(modelOutput.getFloatResponse()).isEqualTo(List.of(25.0f, 13.0f, 49.0f));
    }

    @Test
    void invoke_shouldReturnExpectedForWeightedAvg_oneFeatureMapDoesNotHaveData_returnZero() {
        val model = createScoreAggregationModel();
        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("config_agg_function", "WEIGHTED_AVG")
                .add("config_item_ids", List.of("a", "b", "c", "d", "e").toJavaList())
                .add("config_item_scores", List.of(10, 15, 3, 20, 9).toJavaList())
                .build();
        java.util.Map<String, Object> features1 = HashMap.of(
                "items", (Object) List.of("a", "b").toJavaList(),
                "item_weights", (Object) List.of(1, 1).toJavaList()).toJavaMap();
        java.util.Map<String, Object> features2 = HashMap.of(
                "items", "[]",
                "item_weights", (Object) "[]"
        ).toJavaMap();
        java.util.Map<String, Object> features3 = HashMap.of(
                "items", (Object) List.of("d", "e").toJavaList(),
                "item_weights", (Object) List.of(1, 1).toJavaList()
        ).toJavaMap();
        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(List.of(
                        features1,
                        features2,
                        features3
                ).toJavaList())
                .build());

        assertThat(modelOutput.getFloatResponse().size()).isEqualTo(3);
        assertThat(modelOutput.getFloatResponse()).contains(25.0f, 0.0f, 29.0f);
    }

    @Test
    void invoke_shouldReturnExpectedForTopNAvg() {
        // Three feature maps (i.e. topics).
        // Restaurants: "a", "b", "c", "d", "e", "f", "g", "h"
        // Restaurant scores: 10, 15, 3, 20, 9, 1, 1, 1 (for instance, from M1).
        // Calculate the TOP N AVG score in each topic. If top_n_elements_for_avg = 2, it's going to take the 2 restaurants
        // with higher M1 score and calculate the average.
        // Example: for the first topic: "a", "b", "f" have scores 10, 15, 1. It will calculate the average between 10 and 15 (top 2).

        val model = createScoreAggregationModel();
        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("top_n_elements_for_avg", 2)
                .add("config_agg_function", "TOP_N_AVG")
                .add("config_item_ids", List.of("a", "b", "c", "d", "e", "f", "g", "h").toJavaList())
                .add("config_item_scores", List.of(10, 15, 3, 20, 9, 1, 1, 1).toJavaList())
                .build();
        java.util.Map<String, Object> features1 = HashMap.of(
                "items", (Object) List.of("a", "b", "f").toJavaList(),
                "item_weights", (Object) List.of(10, 15, 10).toJavaList()).toJavaMap();
        java.util.Map<String, Object> features2 = HashMap.of(
                "items", (Object) List.of("a", "c", "g").toJavaList(),
                "item_weights", (Object) List.of(10, 3, 10).toJavaList()
        ).toJavaMap();
        java.util.Map<String, Object> features3 = HashMap.of(
                "items", (Object) List.of("d", "e", "h").toJavaList(),
                "item_weights", (Object) List.of(20, 9, 10).toJavaList()
        ).toJavaMap();
        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(List.of(
                        features1,
                        features2,
                        features3
                ).toJavaList())
                .build());

        assertThat(modelOutput.getFloatResponse()).contains(12.5f, 6.5f, 14.5f);
    }

    @Test
    void invoke_shouldReturnExpectedForTopNAvg_oneFeatureMapDoesNotHaveData_returnZero() {
        // Three feature maps (i.e. topics).
        // Restaurants: "a", "b", "c", "d", "e", "f", "g", "h"
        // Restaurant scores: 10, 15, 3, 20, 9, 1, 1, 1 (for instance, from M1).
        // Calculate the TOP N AVG score in each topic. If top_n_elements_for_avg = 2, it's going to take the 2 restaurants
        // with higher M1 score and calculate the average.
        // Example: for the first topic: "a", "b", "f" have scores 10, 15, 1. It will calculate the average between 10 and 15 (top 2).

        val model = createScoreAggregationModel();
        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("top_n_elements_for_avg", 2)
                .add("config_agg_function", "TOP_N_AVG")
                .add("config_item_ids", List.of("a", "b", "c", "d", "e", "f", "g", "h").toJavaList())
                .add("config_item_scores", List.of(10, 15, 3, 20, 9, 1, 1, 1).toJavaList())
                .build();
        java.util.Map<String, Object> features1 = HashMap.of(
                "items", (Object) List.of("a", "b", "f").toJavaList(),
                "item_weights", (Object) List.of(10, 15, 10).toJavaList()).toJavaMap();
        java.util.Map<String, Object> features2 = HashMap.of(
                "items", "[]",
                "item_weights", (Object) "[]"
        ).toJavaMap();
        java.util.Map<String, Object> features3 = HashMap.of(
                "items", (Object) List.of("d", "e", "h").toJavaList(),
                "item_weights", (Object) List.of(20, 9, 10).toJavaList()
        ).toJavaMap();
        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(List.of(
                        features1,
                        features2,
                        features3
                ).toJavaList())
                .build());

        assertThat(modelOutput.getFloatResponse()).contains(12.5f, 0f, 14.5f);
    }

    private ModelDTO createScoreAggregationModel() {
        val model = ModelDTO
                .builder()
                .modelName("score_aggregation")
                .modelDescription("score_aggregation")
                .versioningModelName("score_aggregation")
                .modelType(ModelType.SCORE_AGGREGATION.name())
                .status(ModelStatus.ENABLED.getName())
                .version("1.0")
                .versioningStrategy(ModelDTO.STATIC)
                .modelFeatures(List.of(
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr("config_item_ids"),
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr("config_item_scores"),
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr("config_agg_function"),
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr("items"),
                        ModelFactory.buildRuntimeFeatureDefaultEmptyStr("item_weights"),
                        buildRuntimeFeature("top_n_elements_for_avg", true, "0", FeatureDefaultValueType.INTEGER)
                ))
                .modelOutputs(List.of(ModelOutputDTO
                        .builder()
                        .outputName("outputs")
                        .outputType(ModelOutputType.FLOAT.name())
                        .outputShape(List.of(1))
                        .normalized(false)
                        .build()))
                .build();

        this.modelService.createOrUpdateModel(model);
        return model;
    }

    private ModelDTO createScoreAggregationModelForInternalInvocation() {
        val model = ModelDTO
                .builder()
                .modelName("score_aggregation")
                .modelDescription("score_aggregation")
                .versioningModelName("score_aggregation")
                .modelType(ModelType.SCORE_AGGREGATION.name())
                .status(ModelStatus.ENABLED.getName())
                .version("1.0")
                .versioningStrategy(ModelDTO.STATIC)
                .modelFeatures(List.of(
                        buildRuntimeFeature("config_item_ids", true, "[]", FeatureDefaultValueType.STRING_ARRAY),
                        buildRuntimeFeature("config_item_scores", true, "[]", FeatureDefaultValueType.FLOAT_ARRAY),
                        buildRuntimeFeature("config_agg_function", true, "WEIGHTED_AVG", FeatureDefaultValueType.STRING),
                        buildRuntimeFeature("items", true, "[]", FeatureDefaultValueType.STRING_ARRAY),
                        buildRuntimeFeature("item_weights", true, "[]", FeatureDefaultValueType.FLOAT_ARRAY)
                ))
                .modelOutputs(List.of(ModelOutputDTO
                        .builder()
                        .outputName("outputs")
                        .outputType(ModelOutputType.FLOAT.name())
                        .outputShape(List.of(1))
                        .normalized(false)
                        .build()))
                .build();

        this.modelService.createOrUpdateModel(model);
        return model;
    }
}

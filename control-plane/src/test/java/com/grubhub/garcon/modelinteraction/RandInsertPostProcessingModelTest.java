package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplane.services.ensembler.impl.BaseEnsembleTest;
import com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.Test;

import static com.grubhub.garcon.modelinteraction.RankingPostProcessingModel.OUTPUT_ENTITIES;
import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
public class RandInsertPostProcessingModelTest extends BaseEnsembleTest {

    @Test
    public void randInsert_should_correctlyPositionPinnedEntity_randomPosition3_sameOrder() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed")
                .add("strategy", "RAND_INSERT")
                .add("pin_entity_id", "id_4")
                .add("max_ranking_slots", 4)
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_1"),
                                ModelFactory.invocationFeature("entity_id", "id_2"),
                                ModelFactory.invocationFeature("entity_id", "id_3"),
                                ModelFactory.invocationFeature("entity_id", "id_4")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(4);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_1", "id_4", "id_2", "id_3"));
    }

    @Test
    public void randInsert_should_correctlyPositionPinnedEntity_randomPosition3_differentOrder() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_butDifferent")
                .add("strategy", "RAND_INSERT")
                .add("pin_entity_id", "id_4")
                .add("max_ranking_slots", 4)
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_4"),
                                ModelFactory.invocationFeature("entity_id", "id_1"),
                                ModelFactory.invocationFeature("entity_id", "id_2"),
                                ModelFactory.invocationFeature("entity_id", "id_3")
                        )
                )
                .build());

        // id_4 is the pinned entity. Random position is 3. Processing should move id_4 to the random position.
        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(4);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_1", "id_2", "id_4", "id_3"));
    }

    @Test
    public void randInsert_should_correctlyPositionPinnedEntity_randomPosition6() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_butDifferent_again")
                .add("strategy", "RAND_INSERT")
                .add("pin_entity_id", "id_4")
                .add("max_ranking_slots", 8)
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_1"),
                                ModelFactory.invocationFeature("entity_id", "id_4"),
                                ModelFactory.invocationFeature("entity_id", "id_2"),
                                ModelFactory.invocationFeature("entity_id", "id_3"),
                                ModelFactory.invocationFeature("entity_id", "id_5"),
                                ModelFactory.invocationFeature("entity_id", "id_6"),
                                ModelFactory.invocationFeature("entity_id", "id_7"),
                                ModelFactory.invocationFeature("entity_id", "id_8")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(8);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().toJavaArray())
                .containsExactly("id_1", "id_2", "id_3", "id_5", "id_4", "id_6", "id_7", "id_8");
    }

    @Test
    public void randInsert_should_correctly_use_max_ranking_slots() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_butDifferent_w_max")
                .add("strategy", "RAND_INSERT")
                .add("pin_entity_id", "id_3")
                .add("max_ranking_slots", 3)
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_1"),
                                ModelFactory.invocationFeature("entity_id", "id_2"),
                                ModelFactory.invocationFeature("entity_id", "id_dont_move_1"),
                                ModelFactory.invocationFeature("entity_id", "id_dont_move_2"),
                                ModelFactory.invocationFeature("entity_id", "id_dont_move_3"),
                                ModelFactory.invocationFeature("entity_id", "id_dont_move_4"),
                                ModelFactory.invocationFeature("entity_id", "id_3")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(7);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_1", "id_3", "id_2", "id_dont_move_1",
                        "id_dont_move_2", "id_dont_move_3", "id_dont_move_4"));
    }

    @Test
    public void randInsert_should_correctly_use_max_ranking_slots_one_slot() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_butDifferent_w_max")
                .add("strategy", "RAND_INSERT")
                .add("pin_entity_id", "id_3")
                .add("max_ranking_slots", 1)
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_dont_move_6"),
                                ModelFactory.invocationFeature("entity_id", "id_dont_move_5"),
                                ModelFactory.invocationFeature("entity_id", "id_dont_move_1"),
                                ModelFactory.invocationFeature("entity_id", "id_dont_move_2"),
                                ModelFactory.invocationFeature("entity_id", "id_dont_move_3"),
                                ModelFactory.invocationFeature("entity_id", "id_dont_move_4"),
                                ModelFactory.invocationFeature("entity_id", "id_3")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(7);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_3", "id_dont_move_6", "id_dont_move_5",
                        "id_dont_move_1", "id_dont_move_2", "id_dont_move_3", "id_dont_move_4"));
    }

    @Test
    public void randInsert_should_handle_invalid_max_ranking_slots_zero() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_butDifferent_w_max")
                .add("strategy", "RAND_INSERT")
                .add("pin_entity_id", "id_2")
                .add("max_ranking_slots", 0)
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_1"),
                                ModelFactory.invocationFeature("entity_id", "id_2")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(2);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_2", "id_1"));

    }

    @Test
    public void randInsert_should_handle_invalid_max_ranking_slots() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_butDifferent_w_max")
                .add("strategy", "RAND_INSERT")
                .add("pin_entity_id", "id_2")
                .add("max_ranking_slots", -1)
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_1"),
                                ModelFactory.invocationFeature("entity_id", "id_2")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(2);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_2", "id_1"));

    }

    @Test
    public void randInsert_should_correctlyHandleEmptyList() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_3")
                .add("strategy", "RAND_INSERT")
                .add("pin_entity_id", "id_4")
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of()
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isFalse();
    }

    @Test
    public void randInsert_should_correctlyHandleOneElementEntries() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_3")
                .add("strategy", "RAND_INSERT")
                .add("pin_entity_id", "id_4")
                .add("max_ranking_slots", 1)
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_4")
                        )
                )
                .build());
        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray().size()).isEqualTo(1);
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().get().getStringArray())
                .isEqualTo(io.vavr.collection.List.of("id_4"));
    }

    @Test
    public void randInsert_should_correctlyHandleMissingPinEntry() {
        val model = createRankPostProcessingModel();

        val globalFeatures = ModelFactory.invocationFeatureBuilder()
                .add("random_seed", "randomSeed_3")
                .add("strategy", "RAND_INSERT")
                .build();

        val modelOutput1 = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(globalFeatures)
                .features(
                        java.util.List.of(
                                ModelFactory.invocationFeature("entity_id", "id_1")
                        )
                )
                .build());

        assertThat(modelOutput1.getResponse().containsKey(OUTPUT_ENTITIES)).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).isDefined()).isTrue();
        assertThat(modelOutput1.getResponse().get(OUTPUT_ENTITIES).get().size()).isEqualTo(0);
    }


    private ModelDTO createRankPostProcessingModel() {
        val model = ModelFactory.createRankPostProcessing();
        this.modelService.createOrUpdateModel(model);
        return model;
    }
}

package com.grubhub.garcon.modelinteraction.searchembeddings;

import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.Normalization;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

public class NormalizationProviderTest {

    private NormalizationProvider subject;

    @BeforeEach
    public void setUp() {
        final Map<String, Object> inputFeatures = HashMap.of(
                "se_normalization_method", "IDENTITY",
                "se_min_score_threshold", 0.6F
        );
        final String modelName = "knn";

        subject = new NormalizationProvider(inputFeatures, modelName);
    }

    @Test
    public void get_allProperties_success() {
        final Normalization normalization = subject.get();

        assertNotNull(normalization);
        assertEquals(normalization.getMethod(), "IDENTITY");
        assertEquals(normalization.getMinScoreThreshold(), 0.6F);
    }

    @Test
    public void get_noProperties_success() {
        subject = new NormalizationProvider(HashMap.empty(), null);
        final Normalization normalization = subject.get();

        assertNotNull(normalization);
        assertNull(normalization.getMethod());
        assertNull(normalization.getMinScoreThreshold());
    }

    @Test
    public void get_errorReadingThreshold_success() {
        final Map<String, Object> inputFeatures = HashMap.of(
                "se_normalization_method", "IDENTITY",
                "se_min_score_threshold", "a"
        );
        final String modelName = "knn";

        subject = new NormalizationProvider(inputFeatures, modelName);
        final Normalization normalization = subject.get();

        assertNotNull(normalization);
        assertEquals(normalization.getMethod(), "IDENTITY");
        assertEquals(normalization.getMinScoreThreshold(), Float.NEGATIVE_INFINITY);
    }

}

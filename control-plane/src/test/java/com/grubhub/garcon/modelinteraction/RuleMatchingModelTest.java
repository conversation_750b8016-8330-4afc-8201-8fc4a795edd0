package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.services.controlplane.sequential.ExpressionExecutor;
import com.grubhub.garcon.controlplane.services.controlplane.sequential.JexlExpressionExecutorImpl;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.ddml.util.ControlPlaneTestUtils;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Iterator;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
public class RuleMatchingModelTest {
    private static final String JEXL_EXPRESSION_CREATION = "jexlExpressionCreation";
    private static final String JEXL_EXPRESSION_EVALUATION = "jexlExpressionEvaluation";
    private static final String TAG_MODEL_NAME = "model_name";
    private static final String TAG_MODEL_VERSION = "model_version";

    private RuleMatchingModel subject;

    @Mock
    private MeterRegistry meterRegistry;
    @Mock
    private Timer jexlExpressionCreationTimer;
    @Mock
    private Timer jexlExpressionEvaluationTimer;

    @BeforeEach
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        MockitoAnnotations.openMocks(this);

        when(jexlExpressionCreationTimer.record(any(Supplier.class))).thenAnswer(invocation -> {
            Supplier<?> supplier = invocation.getArgument(0);
            return supplier.get();
        });
        when(jexlExpressionEvaluationTimer.record(any(Supplier.class))).thenAnswer(invocation -> {
            Supplier<?> supplier = invocation.getArgument(0);
            return supplier.get();
        });

        ExpressionExecutor jexlExpressionExecutor = new JexlExpressionExecutorImpl();
        when(meterRegistry.timer(Metrics.name(RuleMatchingModel.class, JEXL_EXPRESSION_CREATION), Tags.of(TAG_MODEL_NAME, TAG_MODEL_VERSION)))
                .thenReturn(jexlExpressionCreationTimer);
        when(meterRegistry.timer(Metrics.name(RuleMatchingModel.class, JEXL_EXPRESSION_EVALUATION), Tags.of(TAG_MODEL_NAME, TAG_MODEL_VERSION)))
                .thenReturn(jexlExpressionEvaluationTimer);

        subject = new RuleMatchingModel(jexlExpressionExecutor, meterRegistry);
    }

    @ParameterizedTest
    @MethodSource("featuresProvider")
    void invoke_success(List<Map<String, Object>> featureRowValues,
                        Map<String, List<ModelInferenceOutputType>> expectedResult) {
        ModelInferenceInput modelInferenceInput = buildModelInferenceInput(featureRowValues);

        ModelInferenceResult modelInferenceResult = subject.invoke(modelInferenceInput);

        assertNotNull(modelInferenceResult);
        assertMapsEqual(expectedResult, modelInferenceResult.getOutput());
    }

    @Test
    void invoke_rules_feature_missing() {
        List<Map<String, Object>> featureRowValues = List.of(HashMap.of(
                "merchant_id", "1",
                "merchant_types", "CONVENIENCE",
                "menu_items_count", 300,
                "menu_items_image_coverage", 90,
                "categories_image_coverage", 95));

        ModelInferenceInput modelInferenceInput = buildModelInferenceInput(featureRowValues);
        // remove the rules feature
        ModelInferenceInput finalModelInferenceInput = modelInferenceInput.toBuilder()
                .processedFeatures(modelInferenceInput.getProcessedFeatures().map(map -> map.remove("rules")))
                .build();
        Exception exception = assertThrows(RuntimeException.class, () -> subject.invoke(finalModelInferenceInput));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("Missing rules Feature featureRow="));
    }

    @Test
    void invoke_rules_feature_invalid() {
        List<Map<String, Object>> featureRowValues = List.of(HashMap.of(
                "merchant_id", "1",
                "merchant_types", "CONVENIENCE",
                "menu_items_count", 300,
                "menu_items_image_coverage", 90,
                "categories_image_coverage", 95));

        ModelInferenceInput modelInferenceInput = buildModelInferenceInput(featureRowValues);
        // set invalid value for rules feature
        ModelInferenceInput finalModelInferenceInput = modelInferenceInput.toBuilder()
                .processedFeatures(modelInferenceInput.getProcessedFeatures().map(map -> map.put("rules", "   ")))
                .build();
        Exception exception = assertThrows(RuntimeException.class, () -> subject.invoke(finalModelInferenceInput));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("Value of rules feature is invalid rulesFeatureValue="));
    }

    @Test
    void invoke_rules_json_invalid() {
        List<Map<String, Object>> featureRowValues = List.of(HashMap.of(
                "merchant_id", "1",
                "merchant_types", "CONVENIENCE",
                "menu_items_count", 300,
                "menu_items_image_coverage", 90,
                "categories_image_coverage", 95));

        ModelInferenceInput modelInferenceInput = buildModelInferenceInput(featureRowValues);
        // set invalid value for rules feature
        ModelInferenceInput finalModelInferenceInput = modelInferenceInput.toBuilder()
                .processedFeatures(modelInferenceInput.getProcessedFeatures().map(map -> map.put("rules", "{")))
                .build();
        Exception exception = assertThrows(RuntimeException.class, () -> subject.invoke(finalModelInferenceInput));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("Failed deserializing JSON rules"));
    }

    @Test
    void invoke_rules_jexl_expression_invalid() {
        List<Map<String, Object>> featureRowValues = List.of(HashMap.of(
                "merchant_id", "1",
                "merchant_types", "CONVENIENCE",
                "menu_items_count", 300,
                "menu_items_image_coverage", 90,
                "categories_image_coverage", 95));

        ModelInferenceInput modelInferenceInput = buildModelInferenceInput(featureRowValues);
        // update rule with ids not found in context
        modelInferenceInput = modelInferenceInput.toBuilder()
                .processedFeatures(modelInferenceInput.getProcessedFeatures().map(map -> map.put("rules", "{\"RULE_ID\":\"not_mapped || && smth\"}")))
                .build();

        ModelInferenceInput finalModelInferenceInput = modelInferenceInput;
        Exception exception = assertThrows(RuntimeException.class, () -> subject.invoke(finalModelInferenceInput));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("Failed creating jexl rule expressions"));
    }

    @Test
    void invoke_rules_jexl_evaluation_failed() {
        List<Map<String, Object>> featureRowValues = List.of(HashMap.of(
                "merchant_id", "1",
                "merchant_types", "CONVENIENCE",
                "menu_items_count", 250,
                "menu_items_image_coverage", 90,
                "categories_image_coverage", 95));

        ModelInferenceInput modelInferenceInput = buildModelInferenceInput(featureRowValues);
        ModelInferenceInput finalModelInferenceInput = modelInferenceInput.toBuilder()
                .processedFeatures(modelInferenceInput.getProcessedFeatures().map(map -> map.put("rules", "{\"RULE_ID\":\"id_not_mapped\"}")))
                .build();
        ;
        Exception exception = assertThrows(RuntimeException.class, () -> subject.invoke(finalModelInferenceInput));

        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains("Failed evaluating JEXL rule expression="));
    }

    private ModelInferenceInput buildModelInferenceInput(List<Map<String, Object>> processedFeatures) {
        var model = buildRuleMatchingModel();
        for (FeatureDTO featureDTO : model.getModelFeatures()) {
            processedFeatures = processedFeatures.map(map -> map.computeIfAbsent(featureDTO.getFeatureName(),
                    mappingFunction -> featureDTO.getFeatureDefaultValue())._2);
        }
        return ModelInferenceInput.builder()
                .model(model)
                .processedFeatures(processedFeatures)
                .build();
    }

    @SneakyThrows
    private ModelDTO buildRuleMatchingModel() {
        return ControlPlaneTestUtils.readRuleMatchingModel();
    }

    private static Iterator<Object[]> featuresProvider() {
        return List.of(
                new Object[]{List.of(HashMap.of(
                                "merchant_id", "1",
                                "merchant_types", "CONVENIENCE",
                                "menu_items_count", 300,
                                "menu_items_image_coverage", 90,
                                "categories_image_coverage", 95),
                        HashMap.of(
                                "merchant_id", "2",
                                "merchant_types", "CONVENIENCE",
                                "menu_items_count", 300,
                                "menu_items_image_coverage", 80,
                                "categories_image_coverage", 80),
                        HashMap.of(
                                "merchant_id", "3",
                                "merchant_types", "CONVENIENCE",
                                "menu_items_count", 300,
                                "menu_items_image_coverage", 79,
                                "categories_image_coverage", 79),
                        HashMap.of(
                                "merchant_id", "4",
                                "merchant_types", "CONVENIENCE",
                                "menu_items_count", 300,
                                "menu_items_image_coverage", 80,
                                "categories_image_coverage", 79),
                        HashMap.of(
                                "merchant_id", "5",
                                "merchant_types", "CONVENIENCE",
                                "menu_items_count", 300,
                                "menu_items_image_coverage", 79,
                                "categories_image_coverage", 90)),
                        HashMap.of("HAS_MULTI_PAGE_EXPERIENCE", List.of(ModelInferenceOutputType.of("true"),
                                        ModelInferenceOutputType.of("true"),
                                        ModelInferenceOutputType.of("false"),
                                        ModelInferenceOutputType.of("true"),
                                        ModelInferenceOutputType.of("false")),
                                "ILLUSTRATIVE_MENU_EXPERIENCE", List.of(ModelInferenceOutputType.of("true"),
                                        ModelInferenceOutputType.of("true"),
                                        ModelInferenceOutputType.of("false"),
                                        ModelInferenceOutputType.of("false"),
                                        ModelInferenceOutputType.of("false")))},
                new Object[]{List.of(HashMap.of(
                        "merchant_id", "1",
                        "merchant_types", "CONVENIENCE",
                        "menu_items_count", 300,
                        "menu_items_image_coverage", 100,
                        "categories_image_coverage", 0)),
                        HashMap.of("HAS_MULTI_PAGE_EXPERIENCE", List.of(ModelInferenceOutputType.of("true")),
                                "ILLUSTRATIVE_MENU_EXPERIENCE", List.of(ModelInferenceOutputType.of("false")))}
        ).iterator();
    }

    public static void assertMapsEqual(Map<String, List<ModelInferenceOutputType>> expected, Map<String, List<ModelInferenceOutputType>> actual) {
        assertEquals(expected.size(), actual.size());

        assertEquals(expected.keySet(), actual.keySet());

        for (String key : expected.keySet()) {
            List<ModelInferenceOutputType> expectedList = expected.get(key).getOrElse(List.empty());
            List<ModelInferenceOutputType> actualList = actual.get(key).getOrElse(List.empty());

            assertEquals(expectedList.size(), actualList.size());
            for (int i = 0; i < expectedList.size(); i++) {
                assertEquals(expectedList.get(i).getValue(), actualList.get(i).getValue());
            }
        }
    }
}

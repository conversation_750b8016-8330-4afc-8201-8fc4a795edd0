package com.grubhub.garcon.modelinteraction.vectorsimilaritymodel;

import io.vavr.collection.List;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class CosineSimilarityCalculatorTest {

    private static final double DELTA = 1e-5;

    @Test
    public void cosineSimilarity_identity_success() {
        List<Float> u = List.of(0.1f, 0.1f);
        float similarity = CosineSimilarityCalculator.cosineSimilarity(u, u);

        assertEquals(similarity, 1f, DELTA);
        assertEquals(CosineSimilarityCalculator.cosineToAngularNormalization(similarity), 1f, DELTA);
    }

    @Test
    public void cosineSimilarity_zeroVector_returnMinimumValue() {
        List<Float> u = List.of(0f, 0f);
        List<Float> v = List.of(2f, 2f);
        float similarity = CosineSimilarityCalculator.cosineSimilarity(u, v);

        assertEquals(similarity, -1f, DELTA);
        assertEquals(CosineSimilarityCalculator.cosineToAngularNormalization(similarity), 0f, DELTA);
    }

    @Test
    public void cosineSimilarity_proportional_success() {
        List<Float> u = List.of(1f, 1f);
        List<Float> v = List.of(2f, 2f);
        float similarity = CosineSimilarityCalculator.cosineSimilarity(u, v);

        assertEquals(similarity, 1f, DELTA);
        assertEquals(CosineSimilarityCalculator.cosineToAngularNormalization(similarity), 1f, DELTA);
    }

    @Test
    public void cosineSimilarity_perpendicular_success() {
        List<Float> u = List.of(1f, 1f);
        List<Float> v = List.of(1f, -1f);
        float similarity = CosineSimilarityCalculator.cosineSimilarity(u, v);

        assertEquals(similarity, 0.0f, DELTA);
        assertEquals(CosineSimilarityCalculator.cosineToAngularNormalization(similarity), 0.5f, DELTA);
    }

    @Test
    public void cosineSimilarity_opposite_success() {
        List<Float> u = List.of(1f, 1f);
        List<Float> v = List.of(-1f, -1f);
        float similarity = CosineSimilarityCalculator.cosineSimilarity(u, v);

        assertEquals(similarity, -1f, DELTA);
        assertEquals(CosineSimilarityCalculator.cosineToAngularNormalization(similarity), 0f, DELTA);
    }

    @Test
    public void cosineSimilarity_verySimilar_success() {
        List<Float> u = List.of(1f, 1f);
        List<Float> v = List.of(1f, 0.95f);
        float similarity = CosineSimilarityCalculator.cosineSimilarity(u, v);

        assertEquals(similarity, 0.99967146f, DELTA);
        assertEquals(CosineSimilarityCalculator.cosineToAngularNormalization(similarity), 0.99184f, DELTA);
    }

    @Test
    public void cosineSimilarity_veryDissimilar_success() {
        List<Float> u = List.of(1f, 1f);
        List<Float> v = List.of(-1f, -0.95f);
        float similarity = CosineSimilarityCalculator.cosineSimilarity(u, v);

        assertEquals(similarity, -0.999671f, DELTA);
        assertEquals(CosineSimilarityCalculator.cosineToAngularNormalization(similarity), 0.008159f, DELTA);
    }
}

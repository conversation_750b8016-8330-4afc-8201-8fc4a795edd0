package com.grubhub.garcon.modelinteraction;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.grubhub.garcon.controlplane.services.ensembler.impl.BaseEnsembleTest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.ddml.util.ControlPlaneTestUtils;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Iterator;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Slf4j
public class RuleMatchingModelEnsembleTest extends BaseEnsembleTest {

    @BeforeEach
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        super.setUp();
    }

    @ParameterizedTest
    @MethodSource("featuresProvider")
    void ruleMatchingModel_success(java.util.List<java.util.Map<String, Object>> featureRowValues,
                                   io.vavr.collection.Map<String, List<ModelInferenceOutputType>> expectedResult) {
        val model = buildRuleMatchingModel();

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test_ruleMatchingModel_success")
                .modelName(model.getModelName())
                .features(featureRowValues)
                .build());

        assertMapsEqual(expectedResult, modelOutput.getResponse());
    }

    private static Iterator<Object[]> featuresProvider() {
        return Lists.newArrayList(
                new Object[]{ImmutableList.of(ImmutableMap.of(
                                "merchant_id", "1",
                                "merchant_types", "CONVENIENCE",
                                "menu_items_count", 300,
                                "menu_items_image_coverage", 90,
                                "categories_image_coverage", 95),
                        ImmutableMap.of(
                                "merchant_id", "2",
                                "merchant_types", "CONVENIENCE",
                                "menu_items_count", 300,
                                "menu_items_image_coverage", 80,
                                "categories_image_coverage", 80),
                        ImmutableMap.of(
                                "merchant_id", "3",
                                "merchant_types", "CONVENIENCE",
                                "menu_items_count", 300,
                                "menu_items_image_coverage", 79,
                                "categories_image_coverage", 79),
                        ImmutableMap.of(
                                "merchant_id", "4",
                                "merchant_types", "CONVENIENCE",
                                "menu_items_count", 300,
                                "menu_items_image_coverage", 80,
                                "categories_image_coverage", 79),
                        ImmutableMap.of(
                                "merchant_id", "5",
                                "merchant_types", "CONVENIENCE",
                                "menu_items_count", 300,
                                "menu_items_image_coverage", 79,
                                "categories_image_coverage", 90)),
                        HashMap.of("HAS_MULTI_PAGE_EXPERIENCE", List.of(ModelInferenceOutputType.of("true"),
                                        ModelInferenceOutputType.of("true"),
                                        ModelInferenceOutputType.of("false"),
                                        ModelInferenceOutputType.of("true"),
                                        ModelInferenceOutputType.of("false")),
                                "ILLUSTRATIVE_MENU_EXPERIENCE", List.of(ModelInferenceOutputType.of("true"),
                                        ModelInferenceOutputType.of("true"),
                                        ModelInferenceOutputType.of("false"),
                                        ModelInferenceOutputType.of("false"),
                                        ModelInferenceOutputType.of("false")))},
                new Object[]{ImmutableList.of(ImmutableMap.of(
                        "merchant_id", "1",
                        "merchant_types", "CONVENIENCE",
                        "menu_items_count", 300,
                        "menu_items_image_coverage", 100,
                        "categories_image_coverage", 0)),
                        HashMap.of("HAS_MULTI_PAGE_EXPERIENCE", List.of(ModelInferenceOutputType.of("true")),
                                "ILLUSTRATIVE_MENU_EXPERIENCE", List.of(ModelInferenceOutputType.of("false")))}
        ).iterator();
    }

    public static void assertMapsEqual(Map<String, List<ModelInferenceOutputType>> expected, Map<String, List<ModelInferenceOutputType>> actual) {
        assertEquals(expected.size(), actual.size());

        assertEquals(expected.keySet(), actual.keySet());

        for (String key : expected.keySet()) {
            List<ModelInferenceOutputType> expectedList = expected.get(key).getOrElse(List.empty());
            List<ModelInferenceOutputType> actualList = actual.get(key).getOrElse(List.empty());

            assertEquals(expectedList.size(), actualList.size());
            for (int i = 0; i < expectedList.size(); i++) {
                assertEquals(expectedList.get(i).getValue(), actualList.get(i).getValue());
            }
        }
    }

    @SneakyThrows
    private ModelDTO buildRuleMatchingModel() {
        val model = ControlPlaneTestUtils.readRuleMatchingModel();
        this.modelService.createOrUpdateModel(model);
        return model;
    }
}

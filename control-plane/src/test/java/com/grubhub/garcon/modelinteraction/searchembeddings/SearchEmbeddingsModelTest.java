package com.grubhub.garcon.modelinteraction.searchembeddings;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.modelinteraction.adapter.SearchEmbeddingsAdapter;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.val;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SearchEmbeddingsModelTest {

    @Mock
    private SearchEmbeddingsAdapter searchEmbeddingsAdapter;

    @InjectMocks
    private SearchEmbeddingsModel searchEmbeddingsModel;

    @Test
    void invoke_should_call_adapter_with_right_parameters() {
        String key = "neighbor1";
        Map<String, List<ModelInferenceOutputType>> nearestNeighbors = HashMap.of(key, List.of(ModelInferenceOutputType.of(2)));
        val mockedModelInferenceInput = mock(ModelInferenceInput.class);
        when(searchEmbeddingsAdapter.getNearestNeighbors(mockedModelInferenceInput))
                .thenReturn(nearestNeighbors);

        ModelInferenceResult inferenceResult = searchEmbeddingsModel.invoke(mockedModelInferenceInput);
        Map<String, List<ModelInferenceOutputType>> actualNeighbors = inferenceResult.getOutput();

        verify(searchEmbeddingsAdapter, times(1)).getNearestNeighbors(eq(mockedModelInferenceInput));
        assertThat(actualNeighbors.get(key).isDefined()).isTrue();
        actualNeighbors.get(key)
                .peek(output ->
                        assertThat(output).isNotEmpty()
                );
    }

    @Test
    void activateIndexes_should_call_underlying_adapter() {
        doNothing().when(searchEmbeddingsAdapter).activateIndexes(any(List.class));

        searchEmbeddingsModel.activateIndexes(List.of(mock(ModelDTO.class), mock(ModelDTO.class)));

        verify(searchEmbeddingsAdapter, times(1))
                .activateIndexes(any(List.class));
    }

    @Test
    void indexesStatus_should_call_underlying_adapter() {
        when(searchEmbeddingsAdapter.indexesStatus(any(ModelDTO.class)))
                .thenReturn(ModelStatus.AVAILABLE);

        val actualStatus = searchEmbeddingsModel.indexesStatus(mock(ModelDTO.class));
        verify(searchEmbeddingsAdapter, times(1))
                .indexesStatus(any(ModelDTO.class));
        assertThat(actualStatus).isEqualTo(ModelStatus.AVAILABLE);
    }
}

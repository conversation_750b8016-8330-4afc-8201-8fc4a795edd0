package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplane.services.ensembler.impl.BaseEnsembleTest;
import com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.ddml.random.SamplingMode;
import com.grubhub.garcon.ddml.random.SeedMode;

import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;

import lombok.val;
import org.assertj.core.util.Sets;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;

import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput.DEFAULT_OUTPUT_NAME;
import static com.grubhub.garcon.ddml.random.RandomUtil.CONFIG_CUSTOM_SEED;
import static com.grubhub.garcon.ddml.random.RandomUtil.CONFIG_SEED_MODE;

import static com.grubhub.garcon.modelinteraction.RandomSampleModel.CONFIG_SAMPLING_MODE;
import static com.grubhub.garcon.modelinteraction.RandomSampleModel.CONFIG_TOTAL_SAMPLE;
import static com.grubhub.garcon.modelinteraction.RandomSampleModel.INPUT_LIST;
import static com.grubhub.garcon.modelinteraction.RandomSampleModel.INPUT_PROBABILITY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

class RandomSampleModelTest extends BaseEnsembleTest {

    List<Float> weights;

    @Captor
    ArgumentCaptor<java.util.List<String>> inputListCaptor;

    @Test
    void should_return_2_random_items_per_list() {
        val model = ModelFactory.createRandomSampleModel();
        this.modelService.createOrUpdateModel(model);

        Map<String, Object> config = HashMap.of(
                CONFIG_SEED_MODE, SeedMode.CUSTOM_SEED.name(),
                CONFIG_TOTAL_SAMPLE, 2, // return sample lists of size 2
                CONFIG_CUSTOM_SEED, 4L
        );

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(config.toJavaMap())
                .features(ModelFactory.buildInvocationFeatures(INPUT_LIST,
                        List.of(getInputList(), getInputList(), getInputList()))) //provide 3 lists
                .build());

        Map<String, List<ModelInferenceOutputType>> response = modelOutput.getResponse();
        List<List<String>> results = ModelInferenceOutputType.toStringArrayList(response.get(DEFAULT_OUTPUT_NAME).get());
        assertEquals(3, results.size());
        for (List<String> result : results) {
            assertEquals(2, result.size());
            assertEquals(2, Sets.newHashSet(result).size());
        }

        verify(randomPicker, times(3)).pick(inputListCaptor.capture(), eq(2),
                eq(SeedMode.CUSTOM_SEED), eq(4L));
        assertTrue(getInputList().containsAll(inputListCaptor.getValue()));
    }

    @Test
    void should_return_1_item_per_list_with_default_values() {
        val model = ModelFactory.createRandomSampleModel();
        this.modelService.createOrUpdateModel(model);

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .features(ModelFactory.buildInvocationFeatures(INPUT_LIST, List.of(getInputList(), getInputList())))
                .build());

        Map<String, List<ModelInferenceOutputType>> response = modelOutput.getResponse();
        List<List<String>> results = ModelInferenceOutputType.toStringArrayList(response.get(DEFAULT_OUTPUT_NAME).get());
        assertEquals(2, results.size());
        for (List<String> result : results) {
            assertEquals(1, result.size());
        }
    }

    @Test
    void should_return_1_item_per_list_with_weighted_list() {
        val model = ModelFactory.createRandomSampleModel();
        this.modelService.createOrUpdateModel(model);

        val modelOutput = modelService.invokeModelInferenceMultiOutput(ModelInferenceRequest
                .builder()
                .callerTrackingId("test")
                .modelName(model.getModelName())
                .globalFeatures(ModelFactory.invocationFeature(CONFIG_SAMPLING_MODE, SamplingMode.NON_UNIFORM.name()))
                .features(List.of(
                        ModelFactory.invocationFeature(INPUT_LIST, getInputList(), INPUT_PROBABILITY, getWeightsList()),
                        ModelFactory.invocationFeature(INPUT_LIST, getInputList(), INPUT_PROBABILITY, getWeightsList())
                ).toJavaList())
                .build());

        Map<String, List<ModelInferenceOutputType>> response = modelOutput.getResponse();
        List<List<String>> results = ModelInferenceOutputType.toStringArrayList(response.get(DEFAULT_OUTPUT_NAME).get());
        assertEquals(2, results.size());
        for (List<String> result : results) {
            assertEquals(1, result.size());
        }
        verify(randomPicker, times(2)).weightedPick(eq(getInputList().toJavaList()), eq(getWeightsList().toJavaList()));
    }

    private List<String> getInputList() {
        return List.of("item_0", "item_1", "item_2", "item_3");
    }

    private List<Float> getWeightsList() {
        if (weights == null) {
            weights = List.of(0.1f, 0.4f, 0.2f, 0.3f);
        }
        return weights;
    }
}

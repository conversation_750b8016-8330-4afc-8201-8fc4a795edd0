package com.grubhub.garcon.modelinteraction.adapter;

import com.google.common.collect.ImmutableList;
import com.google.inject.Key;
import com.google.inject.name.Names;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.services.ensembler.FeatureValueService;
import com.grubhub.garcon.controlplane.utils.ContainersInitializer;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelConfiguration;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.IndexInfo;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.IndexesStatus;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import com.grubhub.roux.api.responses.UpdateResponse;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.IntStream;

import static com.grubhub.garcon.controlplane.utils.InjectorProvider.INJECTOR;
import static com.grubhub.garcon.ddml.util.TestResourceTemplate.compile;
import static java.util.stream.Collectors.toList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SearchEmbeddingsAdapterIT extends ContainersInitializer {

    @Mock
    private FlowConfig flowConfig;
    @Mock
    private TensorFlowModelConfiguration tensorFlowModelConfiguration;

    private final JavaDateTimeHelper dateTimeHelper = INJECTOR.getInstance(JavaDateTimeHelper.class);
    private SearchEmbeddingsAdapter searchEmbeddingsAdapter;
    private ModelDTO mockedModel;

    private final String mockedModelName = "test_knn";

    @BeforeEach
    void setUp() {
        searchEmbeddingsAdapter = new SearchEmbeddingsAdapter(
                MOCK_SEARCH_EMBEDDINGS_RPC_API,
                INJECTOR.getInstance(Key.get(FeatureValueService.class, Names.named("featureValueServiceWithCache"))),
                flowConfig,
                new SimpleMeterRegistry(),
                tensorFlowModelConfiguration,
                dateTimeHelper
        );
        mockedModel = mock(ModelDTO.class);
        lenient().when(mockedModel.getModelName()).thenReturn(mockedModelName);
    }

    @Test
    @Disabled("Unreliable because of NIO errors.")
    void activateIndexes_should_return_successfully() {
        ArgumentCaptor<List<IndexInfo>> cacheKeyCaptor = ArgumentCaptor.forClass(List.class);
        when(MOCK_SEARCH_EMBEDDINGS_RPC_API.activateIndexes(anyBoolean(), anyList()))
                .thenReturn(UpdateResponse.builder().build());

        searchEmbeddingsAdapter.activateIndexes(io.vavr.collection.List.ofAll(models()));

        verify(MOCK_SEARCH_EMBEDDINGS_RPC_API, times(1))
                .activateIndexes(eq(true), cacheKeyCaptor.capture());
        List<IndexInfo> capturedIndexesInfos = cacheKeyCaptor.getValue();
        assertThat(capturedIndexesInfos).isNotEmpty();

        IndexInfo capturedIndexInfo = capturedIndexesInfos.get(0);
        assertThat(capturedIndexInfo.getIndexName()).isEqualTo(mockedModelName + "_search-embeddings");
        assertThat(capturedIndexInfo.getS3Path())
                .isEqualTo("s3://vnwre0qnbk1qy9zw-ddml-models/modelserving/roux-model-serving" +
                        "/ddml_control_plane/models/test_knn/model/1/0/search-embeddings.annoy");
        assertThat(capturedIndexInfo.getFileInfo().getFileSizeKb()).isEqualTo(0L);
        assertThat(capturedIndexInfo.getModelName()).isEqualTo(mockedModelName);
        assertThat(capturedIndexInfo.getMetadataPath())
                .isEqualTo("s3://vnwre0qnbk1qy9zw-ddml-models/modelserving/roux-model-serving" +
                        "/ddml_control_plane/models/test_knn/model/1/0/search-embeddings_metadata.dat");
    }

    @Disabled("Unreliable because of NIO errors.")
    @Test()
    void activateIndexes_should_show_error_message_when_exception_occurs() {
        when(MOCK_SEARCH_EMBEDDINGS_RPC_API.activateIndexes(anyBoolean(), anyList()))
                .thenThrow(new RuntimeException());

        assertThatExceptionOfType(RuntimeException.class).isThrownBy(
                        () -> searchEmbeddingsAdapter.activateIndexes(io.vavr.collection.List.ofAll(models())))
                .withMessageContaining(
                        String.format("Something wrong occurred when trying to activate the indexes for models=%s",
                                io.vavr.collection.List.of(mockedModelName)));

        reset(MOCK_SEARCH_EMBEDDINGS_RPC_API);
    }

    @SneakyThrows
    private List<ModelDTO> models() {
        return ImmutableList.of(
                ObjectMapperHelper.INSTANCE.readValue(
                        compile("/models/search-embeddings-model.json", mockedModel).getBytes(StandardCharsets.UTF_8),
                        ModelDTO.class
                )
        );
    }

    @SneakyThrows
    private List<IndexesStatus> indexesStatuses() {
        return IntStream.range(0, 5)
                .mapToObj(i -> new IndexesStatus(
                        "search-embeddings-" + i,
                        2,
                        5,
                        1f,
                        ImmutableList.of("localhost1", "localhost2")
                ))
                .collect(toList());
    }

    @Test
    @Disabled("Unreliable because of NIO errors.")
    void indexesStatus_should_return_available() {
        when(MOCK_SEARCH_EMBEDDINGS_RPC_API.getIndexesStatus(anyList()))
                .thenReturn(indexesStatuses());
        when(tensorFlowModelConfiguration.getSearchEmbeddingsInstancesActiveRatio())
                .thenReturn(1f);

        ModelStatus modelStatus = searchEmbeddingsAdapter.indexesStatus(models().get(0));

        assertThat(modelStatus).isEqualTo(ModelStatus.AVAILABLE);
    }

    @Test
    @Disabled("Unreliable because of NIO errors.")
    void indexesStatus_should_return_unavailable() {
        List<IndexesStatus> indexesStatuses = indexesStatuses();
        indexesStatuses.get(4).setActiveProportion(0.9f);
        when(MOCK_SEARCH_EMBEDDINGS_RPC_API.getIndexesStatus(anyList()))
                .thenReturn(indexesStatuses);

        when(tensorFlowModelConfiguration.getSearchEmbeddingsInstancesActiveRatio())
                .thenReturn(1f);

        ModelStatus modelStatus = searchEmbeddingsAdapter.indexesStatus(models().get(0));

        assertThat(modelStatus).isEqualTo(ModelStatus.UNAVAILABLE);
    }

}

package com.grubhub.garcon.modelinteraction.tensorflow;

import com.google.common.collect.ImmutableSet;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.grpc.EurekaHostAndPort;
import io.grpc.Channel;
import io.grpc.ManagedChannel;
import io.grpc.stub.AbstractStub;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.testcontainers.shaded.com.google.common.collect.ImmutableList;
import org.testcontainers.shaded.com.google.common.collect.ImmutableMap;
import tensorflow.serving.PredictionServiceGrpc;

import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

class ModelAwareClientProviderTest {
    private ModelAwareClientProvider provider;

    @BeforeEach
    public void setUp() {
        provider = new ModelAwareClientProvider(new SimpleMeterRegistry());
    }

    @Test
    void shouldRefreshModelStatus() {
        ModelDTO modelDTO1 = ModelDTO.builder()
                .modelName("testModel1")
                .build();
        List<EurekaHostAndPort> hosts1 = ImmutableList.of(
                EurekaHostAndPort.builder().host("host1").port(8080).build(),
                EurekaHostAndPort.builder().host("host2").port(8080).build()
        );

        ModelDTO modelDTO2 = ModelDTO.builder()
                .modelName("testModel2")
                .build();
        List<EurekaHostAndPort> hosts2 = ImmutableList.of(EurekaHostAndPort.builder().host("host").port(8080).build());

        provider.refreshModelStatus(modelDTO1, hosts1);
        assertEquals(provider.getModelHosts(), ImmutableMap.of("testModel1", hosts1));

        provider.refreshModelStatus(modelDTO2, hosts2);
        assertEquals(provider.getModelHosts(), ImmutableMap.of("testModel1", hosts1,
                "testModel2", hosts2));

        provider.refreshModelStatus(modelDTO1, hosts2);
        assertEquals(provider.getModelHosts(), ImmutableMap.of("testModel1", hosts2,
                "testModel2", hosts2));
    }

    @Test
    public void shouldRefreshPredictionClients() {
        Map<EurekaHostAndPort, ManagedChannel> channels1 = ImmutableMap.of(
                EurekaHostAndPort.builder()
                        .host("host1")
                        .port(8080)
                        .build(),
                mock(ManagedChannel.class),   EurekaHostAndPort.builder()
                        .host("host2")
                        .port(8080)
                        .build(), mock(ManagedChannel.class)
        );

        Map<EurekaHostAndPort, ManagedChannel> channels2 = ImmutableMap.of(EurekaHostAndPort.builder()
                .host("host1")
                .port(8080)
                .build(), mock(ManagedChannel.class), EurekaHostAndPort.builder()
                .host("host3")
                .port(8080)
                .build(), mock(ManagedChannel.class));

        provider.refreshPredictionClients(channels1);
        assertEquals(provider.getPredictionClients().keySet(),
                ImmutableSet.of(
                    EurekaHostAndPort.builder()
                            .host("host1")
                            .port(8080)
                            .build(),
                    EurekaHostAndPort.builder()
                            .host("host2")
                            .port(8080)
                            .build()
                )
        );

        provider.refreshPredictionClients(channels2);
        assertEquals(provider.getPredictionClients().keySet(),
                ImmutableSet.of(
                    EurekaHostAndPort.builder()
                            .host("host1")
                            .port(8080)
                            .build(),
                     EurekaHostAndPort.builder()
                            .host("host3")
                            .port(8080)
                            .build()
                    )
        );
    }

    @Test
    public void shouldGetNextClientConcurrently_whenModelHostsMatchClientList() throws Exception {
        int iters = 1000;
        int threads = 5;
        ModelDTO modelDTO1 = ModelDTO.builder()
                .modelName("testModel1")
                .build();
        EurekaHostAndPort host1 = EurekaHostAndPort.builder()
                .host("host1")
                .port(8080)
                .build();
        EurekaHostAndPort host2 = EurekaHostAndPort.builder()
                .host("host2")
                .port(8080)
                .build();

        ManagedChannel channel1 = mock(ManagedChannel.class);
        ManagedChannel channel2 = mock(ManagedChannel.class);

        provider.refreshPredictionClients(ImmutableMap.of(host1, channel1, host2, channel2));
        provider.refreshModelStatus(modelDTO1, ImmutableList.of(host1, host2));


        ExecutorService service = Executors.newFixedThreadPool(threads);
        CountDownLatch latch = new CountDownLatch(threads);
        Queue<PredictionServiceGrpc.PredictionServiceBlockingStub> clients = new ConcurrentLinkedQueue<>();

        for (int i = 0; i < threads; ++i) {
            service.submit(() -> {
                for (int c = 0; c < iters; c++) {
                    clients.add(provider.nextClient("testModel1").getPredictionServiceBlockingStub());
                }

                latch.countDown();
            });
        }

        latch.await();

        assertEquals(clients.size(), threads * iters);
        Map<Channel, List<PredictionServiceGrpc.PredictionServiceBlockingStub>> clientsByChannel = clients.stream()
                .collect(Collectors.groupingBy(AbstractStub::getChannel));

        assertEquals(clientsByChannel.get(channel1).size(), threads * iters / 2);
        assertEquals(clientsByChannel.get(channel2).size(), threads * iters / 2);
    }


    @Test
    public void shouldGetNextCLient_whenModelHostSubsetOfClients() {
        ModelDTO modelDTO1 = ModelDTO.builder()
                .modelName("testModel1")
                .build();
        EurekaHostAndPort host1 = EurekaHostAndPort.builder()
                .host("host1")
                .port(8080)
                .build();
        EurekaHostAndPort host2 = EurekaHostAndPort.builder()
                .host("host2")
                .port(8080)
                .build();
        EurekaHostAndPort host3 = EurekaHostAndPort.builder()
                .host("host3")
                .port(8080)
                .build();

        ManagedChannel channel1 = mock(ManagedChannel.class);
        ManagedChannel channel2 = mock(ManagedChannel.class);
        ManagedChannel channel3 = mock(ManagedChannel.class);

        provider.refreshPredictionClients(ImmutableMap.of(host1, channel1, host2, channel2, host3, channel3));
        provider.refreshModelStatus(modelDTO1, ImmutableList.of(host1, host2));

        assertEquals(provider.nextClient("testModel1").getChannel(), channel1);
        assertEquals(provider.getIndex().get(), 0);

        assertEquals(provider.nextClient("testModel1").getChannel(), channel2);
        assertEquals(provider.getIndex().get(), 1);

        assertTrue(ImmutableSet.of(channel1, channel2).contains(provider.nextClient("testModel1").getChannel()));
        assertEquals(provider.getIndex().get(), 2);

        assertEquals(provider.nextClient("testModel1").getChannel(), channel1);
        assertEquals(provider.getIndex().get(), 0);
    }

    @Test
    public void shouldGetNextClient_whenModelHostContainsNonexistentClient() {
        ModelDTO modelDTO1 = ModelDTO.builder()
                .modelName("testModel1")
                .build();
        EurekaHostAndPort host1 = EurekaHostAndPort.builder()
                .host("host1")
                .port(8080)
                .build();
        EurekaHostAndPort host2 = EurekaHostAndPort.builder()
                .host("host2")
                .port(8080)
                .build();
        EurekaHostAndPort host3 = EurekaHostAndPort.builder()
                .host("host3")
                .port(8080)
                .build();

        ManagedChannel channel1 = mock(ManagedChannel.class);
        ManagedChannel channel2 = mock(ManagedChannel.class);

        provider.refreshPredictionClients(ImmutableMap.of(host1, channel1, host2, channel2));
        provider.refreshModelStatus(modelDTO1, ImmutableList.of(host1, host3, host2));

        assertEquals(provider.nextClient("testModel1").getChannel(), channel1);
        assertEquals(provider.getIndex().get(), 0);

        // Fallback should be invoked when trying to get the client at index 1
        assertTrue(ImmutableSet.of(channel1, channel2).contains(provider.nextClient("testModel1").getChannel()));
        assertEquals(provider.getIndex().get(), 1);

        assertEquals(provider.nextClient("testModel1").getChannel(), channel1);
        assertEquals(provider.getIndex().get(), 0);

        // Fallback should be invoked when trying to get the client at index 1
        assertTrue(ImmutableSet.of(channel1, channel2).contains(provider.nextClient("testModel1").getChannel()));
        assertEquals(provider.getIndex().get(), 1);
    }

    @Test
    public void shouldReturnNull_whenModelHostContainsNoValidClients() {
        ModelDTO modelDTO1 = ModelDTO.builder()
                .modelName("testModel1")
                .build();
        EurekaHostAndPort host1 = EurekaHostAndPort.builder()
                .host("host1")
                .port(8080)
                .build();
        EurekaHostAndPort host2 = EurekaHostAndPort.builder()
                .host("host2")
                .port(8080)
                .build();

        ManagedChannel channel2 = mock(ManagedChannel.class);

        provider.refreshPredictionClients(ImmutableMap.of(host2, channel2));
        provider.refreshModelStatus(modelDTO1, ImmutableList.of(host1));

        assertNull(provider.nextClient("testModel1"));
    }

    @Test
    public void shouldReturnNull_whenModelHostContainsNoClients() {
        EurekaHostAndPort host1 = EurekaHostAndPort.builder()
                .host("host1")
                .port(8080)
                .build();
        ManagedChannel channel1 = mock(ManagedChannel.class);

        provider.refreshPredictionClients(ImmutableMap.of(host1, channel1));

        assertNull(provider.nextClient("testModel1"));
    }
}

package com.grubhub.garcon.modelinteraction.vectorsimilaritymodel;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputDTO;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput.DEFAULT_OUTPUT_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class VectorSimilarityModelTest {

    private VectorSimilarityModel subject;

    private final List<Float> inputVector = List.of(0.1f, 0.1f);
    private final String targetKeys = "menu_item_1,menu_item_2,menu_item_3";
    private final double multiplier = 1;
    private final double cutOff = 0;
    private final boolean normalizationEnabled = true;
    private ModelInferenceInput defaultInput;

    @BeforeEach
    public void setUp() {
        subject = new VectorSimilarityModel();

        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, multiplier, cutOff, normalizationEnabled);
        defaultInput = ModelInferenceInput.builder()
                .model(ModelDTO.builder()
                        .modelName("VECTOR_COSINE_SIMILARITY")
                        .modelOutputs(List.of(
                                ModelOutputDTO.builder()
                                        .outputName(DEFAULT_OUTPUT_NAME)
                                        .outputType("FLOAT")
                                        .build()
                        ))
                        .build())
                .processedFeatures(processedFeatures)
                .build();
    }

    @Test
    public void invoke_validInputNormalizedWithoutCutOff_success() {
        final ModelInferenceResult inferenceResult = subject.invoke(defaultInput);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), defaultInput.getProcessedFeatures().size());

        assertEquals(result.get(0), 1f);
        assertEquals(result.get(1), 0f);
        assertEquals(result.get(2), 0.8524164f);
        assertEquals(result.get(3), 0.5f);
    }

    @Test
    public void invoke_validInputNormalizedWithCutOff_success() {
        final double cutOff = 0.6;

        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, multiplier, cutOff, normalizationEnabled);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), defaultInput.getProcessedFeatures().size());

        assertEquals(result.get(0), 1f);
        assertEquals(result.get(1), 0f);
        assertEquals(result.get(2), 0.8524164f);
        assertEquals(result.get(3), 0f); // Set to 0 due to cut-off.
    }

    @Test
    public void invoke_nullInputVector_returnDefaultResponse() {
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(null, targetKeys, multiplier, cutOff, normalizationEnabled);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), defaultInput.getProcessedFeatures().size());

        assertEquals(result.get(0), 0f);
        assertEquals(result.get(1), 0f);
        assertEquals(result.get(2), 0f);
        assertEquals(result.get(3), 0f);
    }

    @Test
    public void invoke_emptyProcessedFeatures_returnDefaultResponse() {
        ModelInferenceInput input = defaultInput.withProcessedFeatures(List.empty());

        Throwable t = assertThrows(RuntimeException.class, () -> subject.invoke(input));
        assertTrue(t.getMessage().contains("Processed feature list is null or empty"));
    }

    @Test
    public void invoke_nullTargetKeys_returnDefaultResponseWithNormalizationEnabled() {
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, null, multiplier, cutOff, normalizationEnabled);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), processedFeatures.size());
        assertEquals(result, List.of(0.0f, 0.0f, 0.0f, 0.0f));
    }

    @Test
    public void invoke_nullTargetKeys_returnDefaultResponseWithNormalizationDisabled() {
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, null, multiplier, cutOff, false);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), processedFeatures.size());
        assertEquals(result, List.of(-1.0f, -1.0f, -1.0f, -1.0f));
    }

    @Test
    public void invoke_nonExistentTargetKeys_returnDefaultResponse() {
        String targetKeys = "menu_item_6,menu_item_7";
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, multiplier, cutOff, normalizationEnabled);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), processedFeatures.size());
        assertEquals(result, List.of(0.0f, 0.0f, 0.0f, 0.0f));
    }

    @Test
    public void invoke_onlyOneKeyExists_success() {
        String targetKeys = "menu_item_1,menu_item_6,menu_item_7";
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, multiplier, cutOff, normalizationEnabled);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), processedFeatures.size());
        assertEquals(result, List.of(1.0f, 0.0f, 0.7820438f, 0.5f));
    }

    @Test
    public void invoke_nullMultiplierDefaultToOne_success() {
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, null, cutOff, normalizationEnabled);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), defaultInput.getProcessedFeatures().size());

        assertEquals(result.get(0), 1f);
        assertEquals(result.get(1), 0f);
        assertEquals(result.get(2), 0.8524164f);
        assertEquals(result.get(3), 0.5f);
    }

    @Test
    public void invoke_nullMultiplierTen_success() {
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, 10d, cutOff, normalizationEnabled);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), defaultInput.getProcessedFeatures().size());

        assertEquals(result.get(0), 10f);
        assertEquals(result.get(1), 0f);
        assertEquals(result.get(2), 8.524164f);
        assertEquals(result.get(3), 5f);
    }

    @Test
    public void invoke_nullCutOffDefaultToZero_success() {
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, multiplier, null, normalizationEnabled);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), defaultInput.getProcessedFeatures().size());

        assertEquals(result.get(0), 1f);
        assertEquals(result.get(1), 0f);
        assertEquals(result.get(2), 0.8524164f);
        assertEquals(result.get(3), 0.5f);
    }

    @Test
    public void invoke_nullNormalizationDefaultToTrue_success() {
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, multiplier, cutOff, normalizationEnabled);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), defaultInput.getProcessedFeatures().size());

        assertEquals(result.get(0), 1f);
        assertEquals(result.get(1), 0f);
        assertEquals(result.get(2), 0.8524164f);
        assertEquals(result.get(3), 0.5f);
    }

    @Test
    public void invoke_normalizationDisabled_success() {
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, multiplier, cutOff, false);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), defaultInput.getProcessedFeatures().size());

        assertEquals(result.get(0), 1f);
        assertEquals(result.get(1), -1f);
        assertEquals(result.get(2), 0.8944272f);
        assertEquals(result.get(3), 0f);
    }

    @Test
    public void invoke_normalizationDisabledHighCutOff_success() {
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, multiplier, 0.9, false);
        ModelInferenceInput input = defaultInput.withProcessedFeatures(processedFeatures);

        final ModelInferenceResult inferenceResult = subject.invoke(input);
        final List<Float> result = getFloatList(inferenceResult);

        assertNotNull(result);
        assertEquals(result.size(), defaultInput.getProcessedFeatures().size());

        assertEquals(result.get(0), 1f);
        assertEquals(result.get(1), -1f);
        assertEquals(result.get(2), -1f);
        assertEquals(result.get(3), -1f);
    }

    private List<Float> getFloatList(ModelInferenceResult result) {
        return result.getOutput().get(DEFAULT_OUTPUT_NAME).get().map(ModelInferenceOutputType::getFloat);
    }

    @Test
    public void invoke_invalidLengthInputVector_success() {
        List<Float> inputVector = List.of(0.1f, 0.1f, 0.1f);
        final List<Map<String, Object>> processedFeatures = buildProcessedFeatures(inputVector, targetKeys, multiplier, 0.9, false);
        defaultInput = ModelInferenceInput.builder()
                .processedFeatures(processedFeatures)
                .build();

        Throwable t = assertThrows(RuntimeException.class, () -> subject.invoke(defaultInput));
        assertTrue(t.getMessage().contains("Input length=3, Target length=2"));
    }

    private List<Map<String, Object>> buildProcessedFeatures(List<Float> inputVector,
                                                             String targetKeys,
                                                             Double multiplier,
                                                             Double cutOff,
                                                             Boolean normalizationEnabled) {
        Map<String, Object> commonEntries = HashMap.of(
                "input_vec", inputVector,
                "config_target_keys", targetKeys,
                "config_multiplier", multiplier,
                "config_cut_off", cutOff,
                "normalization_enabled", normalizationEnabled
        );

        Map<String, Object> restaurant1 = HashMap.<String, Object>of(
                "restaurant_id", "1",
                "menu_item_1", List.of(0.1f, 0.1f),
                "menu_item_2", List.of(0.1f, 0.2f),
                "menu_item_3", List.empty()
        ).merge(commonEntries);

        Map<String, Object> restaurant2 = HashMap.<String, Object>of(
                "restaurant_id", "2",
                "menu_item_1", List.of(-1f, -1f),
                "menu_item_2", List.of(-1f, -1f),
                "menu_item_3", List.of(-1f, -1f)
        ).merge(commonEntries);

        Map<String, Object> restaurant3 = HashMap.<String, Object>of(
                "restaurant_id", "3",
                "menu_item_1", List.of(0.99f, 0.1f),
                "menu_item_2", null,
                "menu_item_3", List.of(0.9f, 0.3f)
        ).merge(commonEntries);

        Map<String, Object> restaurant4 = HashMap.<String, Object>of(
                "restaurant_id", "3",
                "menu_item_1", List.of(0.5f, -0.5f),
                "menu_item_2", List.of(-1f, 0.5f),
                "menu_item_3", List.of(0.0f, -0.1f)
        ).merge(commonEntries);

        return List.of(restaurant1, restaurant2, restaurant3, restaurant4);
    }

}

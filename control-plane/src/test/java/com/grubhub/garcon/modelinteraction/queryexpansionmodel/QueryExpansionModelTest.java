package com.grubhub.garcon.modelinteraction.queryexpansionmodel;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.*;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput.DEFAULT_OUTPUT_NAME;
import static com.grubhub.garcon.modelinteraction.queryexpansionmodel.QueryExpansionModel.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class QueryExpansionModelTest {

    @Mock
    private LLMClient queryExpansionClient;

    @Mock
    private ModelPrompt modelPrompt;

    @Mock
    private BedrockConfiguration bedrockConfiguration;

    @Mock
    private BedrockModelConfig bedrockModelConfig;

    @InjectMocks
    private QueryExpansionModel subject;

    private ModelInferenceInput defaultInput;

    @BeforeEach
    public void setUp() {
        final List<Map<String, Object>> processedFeatures = List.of(HashMap.of("query_text", "chinese"));

        defaultInput = ModelInferenceInput.builder()
                .model(ModelDTO.builder()
                        .modelName("QUERY_EXPANSION_LLM")
                        .modelType("QUERY_EXPANSION_LLM")
                        .modelOutputs(List.of(
                                ModelOutputDTO.builder()
                                        .outputName(DEFAULT_OUTPUT_NAME)
                                        .outputType("STRING_ARRAY")
                                        .build()
                        ))
                        .build())
                .processedFeatures(processedFeatures)
                .build();
    }

    @Test
    public void invoke_getQueryExpansionMock_success() {
        QueryExpansionModel mockSubject = new QueryExpansionModel(
                new QueryExpansionModelConfig(),
                new BedrockConfiguration(),
                new QueryExpansionClientMock(),
                new ModelPrompt("test prompt")
        );

        ModelInferenceResult result = mockSubject.invoke(defaultInput);

        assertNotNull(result.getOutput());

        String queryExpansions = String.join(", ",
                result.getOutput()
                        .get("query_expansion")
                        .map(ModelInferenceOutputType::toStringList)
                        .get()
        );

        assertEquals("vegan test prompt, gluten-free test prompt, test prompt combos, healthy test prompt, low-calorie test prompt", queryExpansions);
    }


    @ParameterizedTest
    @MethodSource("queryExpansionModelInvokeArguments")
    public void invoke_getQueryExpansions_success(List<Map<String, Object>> processedTestFeatures, String modelResponse) {

        String query = processedTestFeatures.head().getOrElse(QUERY_TEXT, "").toString();
        String modelName = "claude";
        String testPromptWithQuery = "This is a test prompt with query: " + query;
        String testPromptJson = "This is a test prompt json with query: " + query;

        when(modelPrompt.resolvePromptWithQuery(query)).thenReturn(testPromptWithQuery);
        when(modelPrompt.resolvePromptToJson(testPromptWithQuery)).thenReturn(testPromptJson);
        when(bedrockConfiguration.getBedrockModelConfig()).thenReturn(bedrockModelConfig);
        when(bedrockConfiguration.getBedrockModelConfig().getModelName()).thenReturn(modelName);
        when(queryExpansionClient.invoke(modelName, testPromptJson)).thenReturn(modelResponse);

        ModelInferenceInput testInput = defaultInput.withProcessedFeatures(processedTestFeatures);
        ModelInferenceResult result = subject.invoke(testInput);


        verify(modelPrompt).resolvePromptWithQuery(query);
        verify(modelPrompt).resolvePromptToJson(testPromptWithQuery);
        verify(queryExpansionClient).invoke(modelName, testPromptJson);

        assertNotNull(result);

        assertTrue(result.getOutput().containsKey(QUERY_EXPANSION_OUTPUT_NAME));
        List<ModelInferenceOutputType> output = result.getOutput().get(QUERY_EXPANSION_OUTPUT_NAME).get();
        assertNotNull(output);


        assertEquals(modelResponse.split(DELIMITER).length, output.size());
    }

    @Test
    public void invoke_emptyProcessedFeatures_throwsException() {
        defaultInput = defaultInput.withProcessedFeatures(List.empty());

        RuntimeException exception = assertThrows(RuntimeException.class, () -> subject.invoke(defaultInput));
        assertEquals("Processed feature list is null or empty when calling QueryExpansionModel.", exception.getMessage());

    }

    @Test
    public void invoke_emptyQueryOrModelName_returnsDefaultOutput() {
        List<Map<String, Object>> processedFeatures = List.of(HashMap.of("query_text", "", "modelName", "claude"));
        String modelName = processedFeatures.head().getOrElse(BEDROCK_MODEL_NAME, "").toString();

        ModelInferenceInput testInput = defaultInput.withProcessedFeatures(processedFeatures);

        when(bedrockConfiguration.getBedrockModelConfig()).thenReturn(bedrockModelConfig);
        when(bedrockConfiguration.getBedrockModelConfig().getModelName()).thenReturn(modelName);

        ModelInferenceResult result = subject.invoke(testInput);

        assertNotNull(result.getOutput());
        assertTrue(result.getOutput().containsKey(QUERY_EXPANSION_OUTPUT_NAME));
        List<ModelInferenceOutputType> output = result.getOutput().get(QUERY_EXPANSION_OUTPUT_NAME).get();
        assertNotNull(output);
        assertTrue(output.isEmpty());

    }


    private static Stream<Arguments> queryExpansionModelInvokeArguments() {
        return Stream.of(
                // Normal cases
                Arguments.of(
                        List.of(HashMap.of("query_text", "chinese")),
                        "vegan chinese, gluten-free chinese, chinese combos, healthy chinese, low-calorie chinese"
                ),
                Arguments.of(
                        List.of(HashMap.of("query_text", "pizza")),
                        "vegan pizza, gluten-free pizza, pizza combos, healthy pizza, low-calorie pizza"
                ),
                Arguments.of(
                        List.of(HashMap.of("query_text", "burger")),
                        "vegan burger, gluten-free burger, burger combos, healthy burger, low-calorie burger"
                ),
                // Edge case: Very long query or model name
                Arguments.of(
                        List.of(HashMap.of("query_text", "a".repeat(1000))),
                        "long expansion results"
                ),

                // Edge case: Query with multiple commas
                Arguments.of(
                        List.of(HashMap.of("query_text", "chinese, italian, mexican")),
                        "vegan chinese, gluten-free italian, mexican combos, healthy chinese, low-calorie italian"
                )
        );
    }








  
}

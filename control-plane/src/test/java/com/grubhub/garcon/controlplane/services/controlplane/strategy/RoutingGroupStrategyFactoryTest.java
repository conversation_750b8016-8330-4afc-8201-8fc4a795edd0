package com.grubhub.garcon.controlplane.services.controlplane.strategy;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.dto.EnrichedResolveFlowRequest;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.CompositeRandomRoutingGroupStrategy;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.GeohashRandomRoutingGroupStrategy;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.RoutingGroupStrategy;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.RoutingGroupStrategyFactory;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.RoutingGroupStrategyInput;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.SelectFirstRoutingGroupStrategy;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.UuidRandomRoutingGroupStrategy;
import com.grubhub.garcon.controlplaneapi.models.BucketingMode;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.search.experiment.ExperimentBranchManager;
import io.vavr.collection.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static com.grubhub.garcon.controlplaneapi.models.BucketingMode.CALLER_TRACKING_ID;
import static com.grubhub.garcon.controlplaneapi.models.BucketingMode.DINER;
import static com.grubhub.garcon.controlplaneapi.models.BucketingMode.DINER_AND_CLIENT_ENTITY_ID;
import static com.grubhub.garcon.controlplaneapi.models.BucketingMode.GEOHASH;
import static com.grubhub.garcon.controlplaneapi.models.BucketingMode.TRACKING_ID;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RoutingGroupStrategyFactoryTest {

    @Mock
    private EnrichedResolveFlowRequest enrichedResolveFlowRequest;
    @Mock
    private FlowConfig flowConfig;
    @Mock
    private FlowDTO flow;
    @Mock
    private ControlPlaneMapper controlPlaneMapper;
    @Mock
    private ExperimentBranchManager experimentBranchManager;

    @Test
    public void pickFlowRoutingGroupStrategy_should_return_selectFirstRoutingGroupStrategy() {
        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(List.of(mock(FlowRoutingGroupV2.class)))
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        final RoutingGroupStrategy routingGroupStrategy = RoutingGroupStrategyFactory.pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        assertThat(routingGroupStrategy).isInstanceOf(SelectFirstRoutingGroupStrategy.class);
    }

    @Test
    public void pickFlowRoutingGroupStrategy_null_bucketing_mode_should_return_geohashRandomRoutingGroupStrategy() {
        final List<FlowRoutingGroupV2> flowRoutingGroups = List.of(mock(FlowRoutingGroupV2.class), mock(FlowRoutingGroupV2.class));
        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(flowRoutingGroups)
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        final RoutingGroupStrategy routingGroupStrategy = RoutingGroupStrategyFactory.pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        assertThat(routingGroupStrategy).isInstanceOf(GeohashRandomRoutingGroupStrategy.class);
    }

    @Test
    public void pickFlowRoutingGroupStrategy_should_return_geohashRandomRoutingGroupStrategy() {
        final List<FlowRoutingGroupV2> flowRoutingGroups = List.of(mock(FlowRoutingGroupV2.class), mock(FlowRoutingGroupV2.class));
        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(flowRoutingGroups)
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        when(flow.getRoutingGroupsBucketingMode()).thenReturn(GEOHASH);

        final RoutingGroupStrategy routingGroupStrategy = RoutingGroupStrategyFactory.pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        assertThat(routingGroupStrategy).isInstanceOf(GeohashRandomRoutingGroupStrategy.class);
    }

    @ParameterizedTest
    @MethodSource("provideCompositeBucketingModes")
    public void pickFlowRoutingGroupStrategy_should_return_CompositeRandomRoutingGroupStrategy(final BucketingMode bucketingMode) {
        final List<FlowRoutingGroupV2> flowRoutingGroups = List.of(mock(FlowRoutingGroupV2.class), mock(FlowRoutingGroupV2.class));
        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(flowRoutingGroups)
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        when(flow.getRoutingGroupsBucketingMode()).thenReturn(bucketingMode);


        final RoutingGroupStrategy routingGroupStrategy = RoutingGroupStrategyFactory.pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        assertThat(routingGroupStrategy).isInstanceOf(CompositeRandomRoutingGroupStrategy.class);
    }

    @ParameterizedTest
    @MethodSource("provideUuidBucketingModes")
    public void pickFlowRoutingGroupStrategy_should_return_uuidRandomRoutingGroupStrategy(final BucketingMode bucketingMode) {
        final List<FlowRoutingGroupV2> flowRoutingGroups = List.of(mock(FlowRoutingGroupV2.class), mock(FlowRoutingGroupV2.class));
        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(flowRoutingGroups)
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        when(flow.getRoutingGroupsBucketingMode()).thenReturn(bucketingMode);

        final RoutingGroupStrategy routingGroupStrategy = RoutingGroupStrategyFactory.pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        assertThat(routingGroupStrategy).isInstanceOf(UuidRandomRoutingGroupStrategy.class);
    }

    private static Stream<Arguments> provideUuidBucketingModes() {
        return Stream.of(Arguments.of(DINER), Arguments.of(CALLER_TRACKING_ID), Arguments.of(TRACKING_ID));
    }

    private static Stream<Arguments> provideCompositeBucketingModes() {
        return Stream.of(Arguments.of(DINER_AND_CLIENT_ENTITY_ID));
    }

}

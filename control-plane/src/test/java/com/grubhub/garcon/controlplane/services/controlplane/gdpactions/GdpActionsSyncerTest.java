package com.grubhub.garcon.controlplane.services.controlplane.gdpactions;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.grubhub.garcon.controlplane.config.GdpDataTransferConfig;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionResultDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.vavr.collection.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

public class GdpActionsSyncerTest {

    private static final ObjectMapper OBJECT_MAPPER = ObjectMapperHelper.INSTANCE;
    private GdpActionsSyncer subject;

    @Mock
    private GdpDataTransferConfig gdpDataTransferConfig;
    @Mock
    private ModelService modelService;
    @Mock
    private MeterRegistry meterRegistry;
    @Mock
    private Counter mockedCounter;
    @Captor
    private ArgumentCaptor<ModelDTO> disabledModelsCaptor;

    // Stored models
    ModelDTO modelA110 = createModel("model_a", "1.10");
    ModelDTO modelA111 = createModel("model_a", "1.11");
    ModelDTO modelB230 = createModel("model_b", "2.30");
    ModelDTO modelB301 = createModel("model_b", "3.01");

    // Models coming in action files.
    ModelDTO modelA112 = createModel("model_a", "1.12");
    ModelDTO modelB302 = createModel("model_b", "3.02");

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        subject = new GdpActionsSyncer(gdpDataTransferConfig, modelService, meterRegistry);

        when(gdpDataTransferConfig.getAllowedNumberOfPendingVersionByModel()).thenReturn(1);
        when(meterRegistry.counter(anyString(), anyIterable())).thenReturn(mockedCounter);
        when(modelService.getAllModelsWithStatus(ModelStatus.PENDING.getName())).thenReturn(createPendingModelList());
    }

    @Test
    public void disableOlderModels_emptyActions_emptyPendingModels() {
        when(modelService.getAllModelsWithStatus(ModelStatus.PENDING.getName())).thenReturn(List.empty());

        subject.disableOlderModels(List.empty());

        verify(modelService, never()).createOrUpdateModel(any(ModelDTO.class));
        verify(meterRegistry, never()).counter(anyString(), anyIterable());
    }

    @Test
    public void disableOlderModels_noNewActions_disablingSomeAlreadyStoredModels() {
        subject.disableOlderModels(List.empty());

        verify(modelService, times(2)).createOrUpdateModel(disabledModelsCaptor.capture());

        java.util.List<ModelDTO> disabledModels = disabledModelsCaptor.getAllValues();
        // The models sent to update were the two oldest with status DISABLED.
        assertEquals(disabledModels.size(), 2);
        assertTrue(disabledModels.contains(modelA110));
        assertTrue(disabledModels.contains(modelB230));
        assertEquals(modelA110.getStatus(), ModelStatus.DISABLED.getName());
        assertEquals(modelB230.getStatus(), ModelStatus.DISABLED.getName());

        verify(meterRegistry, times(1))
                .counter("ddml.ensembler.GdpActionsSyncer.DisabledModels", Tags.of("model_name", modelA110.getModelName()));
        verify(meterRegistry, times(1))
                .counter("ddml.ensembler.GdpActionsSyncer.DisabledModels", Tags.of("model_name", modelB230.getModelName()));
    }

    @Test
    public void disableOlderModels_actionsAlreadyStored_disablingSomeAlreadyStoredModels() throws JsonProcessingException {
        // These models are already stored in cassandra, testing de-duplication.
        GdpActionResultDTO action1 = createActionResultFromModel(modelA111);
        GdpActionResultDTO action2 = createActionResultFromModel(modelB301);

        subject.disableOlderModels(List.of(action1, action2));

        verify(modelService, times(2)).createOrUpdateModel(disabledModelsCaptor.capture());

        java.util.List<ModelDTO> disabledModels = disabledModelsCaptor.getAllValues();
        // The models sent to update were the two oldest with status DISABLED.
        assertEquals(disabledModels.size(), 2);
        assertTrue(disabledModels.contains(modelA110));
        assertTrue(disabledModels.contains(modelB230));
        assertEquals(modelA110.getStatus(), ModelStatus.DISABLED.getName());
        assertEquals(modelB230.getStatus(), ModelStatus.DISABLED.getName());

        verify(meterRegistry, times(1))
                .counter("ddml.ensembler.GdpActionsSyncer.DisabledModels", Tags.of("model_name", modelA110.getModelName()));
        verify(meterRegistry, times(1))
                .counter("ddml.ensembler.GdpActionsSyncer.DisabledModels", Tags.of("model_name", modelB230.getModelName()));
    }

    @Test
    public void disableOlderModels_newActions_disablingAllAlreadyStoredModels() throws JsonProcessingException {
        GdpActionResultDTO action1 = createActionResultFromModel(modelA112);
        GdpActionResultDTO action2 = createActionResultFromModel(modelB302);

        subject.disableOlderModels(List.of(action1, action2));

        verify(modelService, times(4)).createOrUpdateModel(disabledModelsCaptor.capture());

        java.util.List<ModelDTO> disabledModels = disabledModelsCaptor.getAllValues();
        // The models sent to update were the 4 oldest with status DISABLED.
        assertEquals(disabledModels.size(), 4);
        assertTrue(disabledModels.contains(modelA110));
        assertTrue(disabledModels.contains(modelA111));
        assertTrue(disabledModels.contains(modelB230));
        assertTrue(disabledModels.contains(modelB301));
        assertEquals(modelA110.getStatus(), ModelStatus.DISABLED.getName());
        assertEquals(modelA111.getStatus(), ModelStatus.DISABLED.getName());
        assertEquals(modelB230.getStatus(), ModelStatus.DISABLED.getName());
        assertEquals(modelB301.getStatus(), ModelStatus.DISABLED.getName());

        verify(meterRegistry, times(1))
                .counter("ddml.ensembler.GdpActionsSyncer.DisabledModels", Tags.of("model_name", modelA110.getModelName()));
        verify(meterRegistry, times(1))
                .counter("ddml.ensembler.GdpActionsSyncer.DisabledModels", Tags.of("model_name", modelA111.getModelName()));
        verify(meterRegistry, times(1))
                .counter("ddml.ensembler.GdpActionsSyncer.DisabledModels", Tags.of("model_name", modelB230.getModelName()));
        verify(meterRegistry, times(1))
                .counter("ddml.ensembler.GdpActionsSyncer.DisabledModels", Tags.of("model_name", modelB301.getModelName()));
    }

    @Test
    public void disableOlderModels_newActions_changeNumberOfPendingModelsKept() throws JsonProcessingException {
        when(gdpDataTransferConfig.getAllowedNumberOfPendingVersionByModel()).thenReturn(2); // Main change here.

        GdpActionResultDTO action1 = createActionResultFromModel(modelA112);
        GdpActionResultDTO action2 = createActionResultFromModel(modelB302);

        subject.disableOlderModels(List.of(action1, action2));

        verify(modelService, times(2)).createOrUpdateModel(disabledModelsCaptor.capture());

        java.util.List<ModelDTO> disabledModels = disabledModelsCaptor.getAllValues();
        assertEquals(disabledModels.size(), 2);
        assertTrue(disabledModels.contains(modelA110));
        assertTrue(disabledModels.contains(modelB230));
        assertEquals(modelA110.getStatus(), ModelStatus.DISABLED.getName());
        assertEquals(modelB230.getStatus(), ModelStatus.DISABLED.getName());

        verify(meterRegistry, times(1))
                .counter("ddml.ensembler.GdpActionsSyncer.DisabledModels", Tags.of("model_name", modelA110.getModelName()));
        verify(meterRegistry, times(1))
                .counter("ddml.ensembler.GdpActionsSyncer.DisabledModels", Tags.of("model_name", modelB230.getModelName()));
    }

    @Test
    public void disableOlderModels_functionalityDisabled() throws JsonProcessingException {
        when(gdpDataTransferConfig.getAllowedNumberOfPendingVersionByModel()).thenReturn(0); // Main change here.

        GdpActionResultDTO action1 = createActionResultFromModel(modelA112);
        GdpActionResultDTO action2 = createActionResultFromModel(modelB302);

        subject.disableOlderModels(List.of(action1, action2));

        verify(modelService, never()).createOrUpdateModel(any(ModelDTO.class));
        verify(meterRegistry, never()).counter(anyString(), any(Tags.class));
    }

    @Test
    public void disableOlderModels_storedLastVersion_nothingShouldBeDisabled() {
        when(modelService.getAllModelsWithStatus(ModelStatus.PENDING.getName())).thenReturn(List.of(modelA111, modelB301));

        subject.disableOlderModels(List.empty());

        verify(modelService, never()).createOrUpdateModel(disabledModelsCaptor.capture());

        verify(modelService, never()).createOrUpdateModel(any(ModelDTO.class));
        verify(meterRegistry, never()).counter(anyString(), any(Tags.class));
    }

    private List<ModelDTO> createPendingModelList() {
        return List.of(modelA110, modelA111, modelB230, modelB301);
    }

    private static ModelDTO createModel(String modelVersioningName, String version) {
        return ModelDTO.builder()
                .modelName(modelVersioningName + version)
                .versioningModelName(modelVersioningName)
                .status(ModelStatus.PENDING.getName())
                .build()
                .withVersion(version);
    }

    private static GdpActionResultDTO createActionResultFromModel(ModelDTO modelDTO) throws JsonProcessingException {
        String actionDate = "2023-07-11";

        GdpActionDTO action = GdpActionDTO.builder()
                .actionDate(actionDate)
                .actionName("create_model_" + modelDTO.getModelName())
                .actionType(GdpActionType.CREATE_MODEL)
                .actionGroup("search_group")
                .sequenceNumber(1)
                .filePath("s3://grubhub-gdp-data-transfer-prod/" + actionDate + "/create_model_" + modelDTO.getModelName() + ".json")
                .createdDateTime(actionDate + "10:30:00")
                .actionContent(OBJECT_MAPPER.writeValueAsString(modelDTO))
                .build();

        return GdpActionResultDTO.builder()
                .action(action)
                .ok(true)
                .resultMsg("")
                .loadedDateTime(actionDate + "10:35:00")
                .processedDateTime(actionDate + "10:35:00")
                .build();
    }
}

package com.grubhub.garcon.controlplane.resources;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.grubhub.garcon.controlplane.mapper.VavrMapper;
import com.grubhub.garcon.controlplaneapi.models.BucketingMode;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupApi;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import com.grubhub.roux.rest.jersey.JacksonMessageBodyProvider;
import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.config.ClientConfig;
import com.sun.jersey.api.client.config.DefaultClientConfig;
import io.vavr.collection.HashMap;
import lombok.experimental.UtilityClass;
import lombok.val;

import javax.validation.Validation;
import java.util.*;

import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.ROUTING_GROUPS_ENSEMBLE_NAME;

@UtilityClass
public class ITFactory {

    public static Client getClient() {
        ClientConfig clientConfig = new DefaultClientConfig();
        clientConfig.getSingletons().add(new JacksonMessageBodyProvider(ObjectMapperHelper.INSTANCE, Validation.buildDefaultValidatorFactory().getValidator()));
        return Client.create(clientConfig);
    }

    private static Map<String, List<FlowRoutingGroupApi>> buildManyRoutingGroupsForOneCriteria(String flowId) {
        val firstRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .flowId(flowId)
                .routingPercentage(0.15f)
                .groupName("m1")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        val secondRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .flowId(flowId)
                .routingPercentage(0.2f)
                .groupName("m2")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        val thirdRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .flowId(flowId)
                .routingPercentage(0.3f)
                .groupName("m3")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        val fourthRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .flowId(flowId)
                .routingPercentage(0.35f)
                .groupName("m4")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        return Map.of("default", List.of(
                firstRoutingGroupCriteria,
                secondRoutingGroupCriteria,
                thirdRoutingGroupCriteria,
                fourthRoutingGroupCriteria));
    }

    public static FlowApi createFlowWithMultipleRoutingGroups() {
        String flowId = String.valueOf(UUID.randomUUID());

        return FlowApi.builder()
                .flowId(flowId)
                .flowSet("Search")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(ImmutableSet.of("DELIVERY", "CATERING"))
                .matchingQueryTypes(ImmutableSet.of("INTENTFUL"))
                .matchingApplications(ImmutableSet.of("umami", "iOS Native"))
                .matchingQueryTokens(ImmutableList.of("pizza", "pasta"))
                .locationMarkets(ImmutableSet.of("manhattan"))
                .matchingDinerTypes(ImmutableSet.of("UNAUTHENTICATED"))
                .routingGroupsBucketingMode(BucketingMode.GEOHASH)
                .routingGroupsCriteria(buildManyRoutingGroupsForOneCriteria(flowId))
                .priority(1)
                .build();
    }

    public static FlowApi createNYCFlow() {
        String flowId = UUID.randomUUID().toString();
        return FlowApi.builder()
                .flowId(flowId)
                .flowSet("Search")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(ImmutableSet.of("DELIVERY", "CATERING"))
                .matchingQueryTypes(ImmutableSet.of("INTENTFUL"))
                .matchingApplications(ImmutableSet.of("umami", "iOS Native"))
                .matchingQueryTokens(ImmutableList.of("pizza", "pasta"))
                .locationMarkets(ImmutableSet.of("manhattan"))
                .matchingDinerTypes(ImmutableSet.of("UNAUTHENTICATED"))
                .routingGroupsCriteria(buildRoutingGroupsCriteria())
                .priority(1)
                .build();
    }

    private static Map<String, List<FlowRoutingGroupApi>> buildRoutingGroupsCriteria() {
        return Map.of("default", buildRoutingGroups());
    }

    private static List<FlowRoutingGroupApi> buildRoutingGroups() {
        val firstRoutingGroup = FlowRoutingGroupApi
                .builder()
                .groupName("group_50")
                .routingPercentage(0.4f)
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .routingGroupCriteria("default")
                .build();
        val secondRoutingGroup = FlowRoutingGroupApi
                .builder()
                .groupName("default_group")
                .routingPercentage(0.6f)
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .routingGroupCriteria("default")
                .build();

        return ImmutableList.of(firstRoutingGroup, secondRoutingGroup);
    }

    public static EnsembleDTO createEnsemble() {
        return EnsembleDTO.builder()
                .models(VavrMapper.toVavrSet(ImmutableSet.of(ROUTING_GROUPS_ENSEMBLE_NAME)))
                .versioningStrategy("DYNAMIC")
                .version("123.123")
                .status("ENABLED")
                .ensembleName("search_m1_m2")
                .ensembleWeights(io.vavr.collection.HashMap.of("default", HashMap.of("restaurant_id", 0.7f)))
                .ensembleDescription("LTR model")
                .build();
    }

}

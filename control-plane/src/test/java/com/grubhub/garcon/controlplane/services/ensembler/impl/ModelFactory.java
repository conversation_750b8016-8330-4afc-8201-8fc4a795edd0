package com.grubhub.garcon.controlplane.services.ensembler.impl;

import com.grubhub.garcon.controlplane.services.ensembler.rules.FeatureDefaultValueType;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.*;
import com.grubhub.garcon.controlplaneapi.models.ensembler.normalization.ModelOutputNormalization;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import com.grubhub.garcon.ddml.random.RandomUtil;
import com.grubhub.garcon.ddml.random.SamplingMode;
import com.grubhub.garcon.ddml.random.SeedMode;
import com.grubhub.garcon.ensembler.cassandra.models.EntityCollection;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import com.grubhub.garcon.modelinteraction.*;
import com.grubhub.garcon.modelinteraction.precomputedranker.PrecomputedRankerModel;
import com.grubhub.garcon.modelinteraction.queryexpansionmodel.QueryExpansionModel;
import com.grubhub.garcon.modelinteraction.sagemaker.SageMakerModel;
import com.grubhub.garcon.modelinteraction.searchembeddings.SearchEmbeddingsModel;
import com.grubhub.garcon.modelinteraction.vectorsimilaritymodel.VectorSimilarityModel;
import io.vavr.Tuple;
import io.vavr.collection.HashMap;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.experimental.UtilityClass;
import lombok.val;

import static com.grubhub.garcon.controlplane.utils.InjectorProvider.INJECTOR;
import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput.DEFAULT_OUTPUT_NAME;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;

@UtilityClass
public class ModelFactory {

    public static final String BASE_MODEL_LOCATION = "s3://grubhub-gdp-data-transfer-dev/roux-model-serving/ddml_control_plane/models/";
    public static final String BASE_SERVING_LOCATION = "s3://vnwre0qnbk1qy9zw-ddml-models/modelserving/roux-model-serving/ddml_control_plane/models/";

    public static ModelDTO createModel() {
        return ModelDTO.builder()
                .modelDescription("M1 - Order Max - Click LTR model")
                .modelName("search_m1")
                .modelType(ModelType.TENSOR_FLOW.name())
                .versioningStrategy("DYNAMIC")
                .status("ENABLED")
                .location("s3://gdp-data-transfer-prod/roux-model-serving/model/ClickLTR")
                .servingLocation("s3://dizwyxupgkmgbg-search-jobs/roux-model-serving/model/ClickLTR")
                .version("3.32")
                .modelFeatures(buildModelFeatures())
                .modelOutputs(buildModelOutputs())
                .processedFeaturesFilter(HashSet.empty())
                .modelGroup("search")
                .build();
    }

    public static ModelDTO createSELoadedModel() {
        return ModelDTO.builder()
                .modelDescription("M1 - Order Max - Click LTR model")
                .modelName("search_m1")
                .modelType(ModelType.SEARCH_EMBEDDINGS.name())
                .versioningStrategy("DYNAMIC")
                .searchEmbeddingsVectorDimension(5)
                .status("LOADED")
                .location("s3://gdp-data-transfer-prod/roux-model-serving/model/ClickLTR")
                .servingLocation("s3://dizwyxupgkmgbg-search-jobs/roux-model-serving/model/ClickLTR")
                .version("3.32")
                .modelFeatures(buildModelFeatures())
                .modelOutputs(buildModelOutputs())
                .processedFeaturesFilter(HashSet.empty())
                .modelGroup("search")
                .build();
    }

    public static ModelDTO createM2Model() {
        return ModelDTO.builder()
                .modelDescription("M2 - Order Max - Click LTR model")
                .modelName("search_m2")
                .versioningStrategy("DYNAMIC")
                .modelType(ModelType.TENSOR_FLOW.name())
                .status("ENABLED")
                .location("s3://gdp-data-transfer-prod/roux-model-serving/model/ClickLTR")
                .servingLocation("s3://dizwyxupgkmgbg-search-jobs/roux-model-serving/model/ClickLTR")
                .version("3.32")
                .modelFeatures(buildModelFeatures())
                .modelOutputs(buildModelOutputs())
                .processedFeaturesFilter(HashSet.empty())
                .build();
    }


    public static ModelDTO createModelForFeatureStoreExternalIT() {
        return ModelDTO.builder()
                .modelDescription("M1 - Order Max - Click LTR model")
                .modelName("half_plus_two")
                .version("68.68")
                .modelType(ModelType.TENSOR_FLOW.name())
                .versioningStrategy("DYNAMIC")
                .location("s3://gdp-data-transfer-prod/roux-model-serving/model/ClickLTR")
                .servingLocation("s3://dizwyxupgkmgbg-search-jobs/roux-model-serving/model/ClickLTR")
                .version("123.123")
                .status("ENABLED")
                .modelFeatures(List.of(FeatureDTO
                        .builder()
                        .featureName("x")
                        .featureSourceType(FeatureSourceType.RUNTIME.name())
                        .status("ENABLED")
                        .build()))
                .modelOutputs(List.of(ModelOutputDTO
                        .builder()
                        .outputName("y")
                        .outputType(ModelOutputType.FLOAT.name())
                        .outputShape(List.of(1))
                        .build()
                ))
                .processedFeaturesFilter(HashSet.empty())
                .build();
    }

    public static ModelDTO createModelForFeatureStoreRuntime() {
        return ModelDTO.builder()
                .modelDescription("M1 - Order Max - Runtime model")
                .modelName("search_m1")
                .modelType(ModelType.FUNCTION.name())
                .versioningModelName("testVersioningName_search_m1")
                .versioningStrategy("DYNAMIC")
                .status("ENABLED")
                .location("s3://gdp-data-transfer-prod/roux-model-serving/model/ClickLTR")
                .servingLocation("s3://dizwyxupgkmgbg-search-jobs/roux-model-serving/model/ClickLTR")
                .version("3.32")
                .modelFeatures(buildModelFeaturesWithRuntimeType())
                .modelOutputs(buildModelOutputs())
                .processedFeaturesFilter(HashSet.empty())
                .build();
    }


    public static ModelDTO createModelWithName(String modelName) {
        return ModelDTO.builder()
                .modelDescription("M1 - Order Max - Click LTR model")
                .modelName(modelName)
                .modelType(ModelType.TENSOR_FLOW.name())
                .version("68.68")
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .location("s3://gdp-data-transfer-prod/roux-model-serving/model/ClickLTR")
                .servingLocation("s3://dizwyxupgkmgbg-search-jobs/roux-model-serving/model/ClickLTR")
                .version("3.32")
                .modelFeatures(buildModelFeatures())
                .modelOutputs(buildModelOutputs())
                .processedFeaturesFilter(HashSet.empty())
                .modelGroup("search")
                .build();
    }

    public static List<ModelOutputDTO> buildModelOutputs() {
        return List.of(ModelOutputDTO.builder()
                .outputName("output")
                .outputType(ModelOutputType.FLOAT.name())
                .outputShape(List.of(1))
                .normalized(false)
                .build()
        );
    }

    public static List<FeatureDTO> buildModelFeatures() {
        val firstFeature = FeatureDTO
                .builder()
                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                .majorVersion("3")
                .minorVersion("1")
                .featureName("clickLTRRestaurantFeature")
                .featureStoreFields(List.of("ratings_avg"))
                .build();

        val secondFeature = FeatureDTO
                .builder()
                .featureSourceType(FeatureSourceType.RUNTIME.name())
                .featureName("runtime")
                .featureStoreFields(List.of("ratings_avg"))
                .build();

        return List.of(firstFeature, secondFeature);
    }

    public static List<FeatureDTO> buildModelFeaturesWithRuntimeType() {
        val firstFeature = FeatureDTO
                .builder()
                .featureSourceType(FeatureSourceType.RUNTIME.name())
                .majorVersion("3")
                .minorVersion("1")
                .featureName(FeatureSourceType.RUNTIME.name())
                .build();

        return List.of(firstFeature);
    }

    public static List<ModelDTO> buildModelsWithDuplicateFeatureNames() {
        val firstModel = ModelDTO.builder()
                .modelType(ModelType.TENSOR_FLOW.name())
                .location("s3://gdp-data-transfer-prod/roux-model-serving/model/ClickLTR")
                .servingLocation("s3://dizwyxupgkmgbg-search-jobs/roux-model-serving/model/ClickLTR")
                .modelDescription("M1 - Order Max - Click LTR model")
                .modelName("search_m1")
                .version("3.32")
                .versioningStrategy("DYNAMIC")
                .status("ENABLED")
                .modelFeatures(buildModelFeaturesWithDuplicateNames())
                .modelOutputs(buildModelOutputs())
                .processedFeaturesFilter(HashSet.empty())
                .build();
        return List.of(firstModel);
    }

    public static List<ModelDTO> buildModelsWithDuplicateFeatureKeys() {
        val firstModel = ModelDTO.builder()
                .modelType(ModelType.TENSOR_FLOW.name())
                .location("s3://gdp-data-transfer-prod/roux-model-serving/model/ClickLTR")
                .servingLocation("s3://dizwyxupgkmgbg-search-jobs/roux-model-serving/model/ClickLTR")
                .modelDescription("M1 - Order Max - Click LTR model")
                .modelName("search_m1")
                .version("3.32")
                .versioningStrategy("DYNAMIC")
                .status("ENABLED")
                .modelFeatures(buildModelFeaturesWithDuplicateFeatureKeys())
                .modelOutputs(buildModelOutputs())
                .processedFeaturesFilter(HashSet.empty())
                .build();
        return List.of(firstModel);
    }

    public static List<FeatureDTO> buildModelFeaturesWithDuplicateNames() {
        val firstFeature = FeatureDTO
                .builder()
                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                .majorVersion("3")
                .minorVersion("1")
                .featureName("clickLTRRestaurantFeature")
                .build();

        val secondFeature = FeatureDTO
                .builder()
                .featureSourceType(FeatureSourceType.RUNTIME.name())
                .featureName("clickLTRRestaurantFeature")
                .build();

        return List.of(firstFeature, secondFeature);
    }

    public static List<FeatureDTO> buildModelFeaturesWithDuplicateFeatureKeys() {
        val firstFeature = FeatureDTO
                .builder()
                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                .majorVersion("3")
                .minorVersion("1")
                .featureName("clickLTRRestaurantFeature")
                .featureSourceType("EXTERNAL")
                .build();

        val secondFeature = FeatureDTO
                .builder()
                .featureSourceType(FeatureSourceType.RUNTIME.name())
                .majorVersion("3")
                .minorVersion("1")
                .featureName("clickLTRRestaurantFeature")
                .featureSourceType("INTERNAL")
                .build();

        return List.of(firstFeature, secondFeature);
    }

    public static Map<String, ModelDTO> buildM1M2M3Models(int major, int minor) {
        return HashMap.of(
                "m1", buildM1Model(major, minor),
                "m2", buildM2Model(major, minor),
                "m3", buildM3Model(major, minor)
        );
    }

    public static ModelDTO buildM1Model(int major, int minor) {
        val baseModelName = "search_m1";
        return getBaseModelBuilder(baseModelName, major, minor, ModelType.TENSOR_FLOW)
                .processedFeaturesFilter(HashSet.of(
                        "query_text",
                        "cuisine_filter",
                        "user_search_tokens",
                        "user_click_history",
                        "user_order_history",
                        "user_gotos",
                        "geohash",
                        "mealtime",
                        "app",
                        "name",
                        "cuisines",
                        "rest_geohash",
                        "rid"
                ))
                .tfUseExamplesSerialization(true)
                .tfExamplesSerializationName("inputs")
                .modelFeatures(List.of(
                        buildInternalFeature(baseModelName, "search_m1_restaurant_feature", major, minor, 0, false,
                                List.of("RESTAURANT_ID"),
                                HashMap.of(
                                        "rid", "",
                                        "name", "",
                                        "cuisines", "",
                                        "rest_geohash", "",
                                        "modified_cbsa_name", ""
                                )),
                        buildInternalFeature(baseModelName, "search_m1_diner_feature", major, minor, 0, false,
                                List.of("DINER_ID"),
                                HashMap.of(
                                        "diner_id", "",
                                        "user_search_tokens", List.empty(),
                                        "user_click_history", List.empty(),
                                        "user_order_history", List.empty(),
                                        "user_gotos", List.empty()
                                )),
                        buildRuntimeFeatureRequired("RESTAURANT_ID"),
                        buildRuntimeFeatureDefaultEmptyStr("DINER_ID"),
                        buildRuntimeFeatureDefaultEmptyStr("query_text"),
                        buildRuntimeFeatureDefaultEmptyStr("geohash"),
                        buildRuntimeFeatureDefaultEmptyStr("mealtime"),
                        buildRuntimeFeatureDefaultEmptyStr("app"),
                        buildRuntimeFeatureDefaultNull("cuisine_filter")
                ))
                .modelOutputs(List.of(
                                ModelOutputDTO.builder()
                                        .outputName("outputs")
                                        .outputType(ModelOutputType.FLOAT.name())
                                        .outputShape(List.of(1))
                                        .normalized(true)
                                        .normalizedType(ModelOutputNormalization.STANDARD.name())
                                        .normalizedAvg(268.2796677)
                                        .normalizedStd(1.55)
                                        .build()
                        )
                )
                .modelGroup("search")
                .build();
    }

    public static ModelDTO buildM2Model(int major, int minor) {
        val baseModelName = "search_m2";
        return getBaseModelBuilder(baseModelName, major, minor, ModelType.LINEAR_COMBINATION)
                .processedFeaturesFilter(HashSet.of(
                        "gh_delivery_ind",
                        "has_promo",
                        "gh_funded_promo",
                        "self_funded_promo",
                        "avg_order_cost",
                        "avg_order_revenue",
                        "avg_delivery_cost",
                        "avg_delivery_fnc",
                        "ad_fee",
                        "weight_gh_delivery_ind",
                        "weight_has_promo",
                        "weight_gh_funded_promo",
                        "weight_self_funded_promo",
                        "weight_avg_order_cost",
                        "weight_avg_order_revenue",
                        "weight_avg_delivery_cost",
                        "weight_avg_delivery_fnc",
                        "weight_ad_fee"
                ))
                .modelFeatures(List.of(
                        buildRuntimeFeatureRequired("RESTAURANT_ID"),
                        buildRuntimeFeatureDefaultStr("diner_geohash_7", "Global"),
                        buildRuntimeFeatureDefaultStr("location_mode", "DELIVERY"),
                        buildRuntimeFeatureDefaultStr("order_type", "STANDARD"),
                        buildInternalFeature(baseModelName, "search_m2_area_feature", major, minor, 1, false,
                                List.of("diner_geohash_7"),
                                HashMap.of(
                                        "area", "Global",
                                        "diner_geohash_7", "Global"
                                )),
                        buildInternalFeature(baseModelName, "search_m2_weights_feature", major, minor, 2, false,
                                List.of("area"),
                                HashMap.of(
                                        "area", "Global",
                                        "weight_gh_delivery_ind", 1.0,
                                        "weight_has_promo", 1.0,
                                        "weight_gh_funded_promo", 1.0,
                                        "weight_self_funded_promo", 1.0,
                                        "weight_avg_order_cost", 1.0,
                                        "weight_avg_order_revenue", 1.0,
                                        "weight_avg_delivery_fnc", 1.0,
                                        "weight_avg_delivery_cost", 1.0,
                                        "weight_ad_fee", 1.0
                                )),
                        buildInternalFeature(baseModelName, "search_m2_restaurant_feature", major, minor, 3, false,
                                List.of("RESTAURANT_ID", "location_mode", "order_type", "area"),
                                HashMap.ofEntries(
                                        Tuple.of("RESTAURANT_ID", ""),
                                        Tuple.of("area", "Global"),
                                        Tuple.of("diner_type", "diner"),
                                        Tuple.of("delivery_method", "delivery"),
                                        Tuple.of("order_type", "STANDARD"),
                                        Tuple.of("location_mode", "DELIVERY"),
                                        Tuple.of("gh_delivery_ind", 1.0),
                                        Tuple.of("has_promo", 1.0),
                                        Tuple.of("gh_funded_promo", 1.0),
                                        Tuple.of("self_funded_promo", 1.0),
                                        Tuple.of("avg_order_cost", 1.0),
                                        Tuple.of("avg_order_revenue", 1.0),
                                        Tuple.of("avg_delivery_fnc", 1.0),
                                        Tuple.of("avg_delivery_cost", 1.0),
                                        Tuple.of("ad_fee", 1.0)
                                ))
                ))
                .modelOutputs(buildModelOutputs())
                .modelGroup("search")
                .build();
    }

    public static ModelDTO buildM3Model(int major, int minor) {
        val baseModelName = "search_m3";
        return getBaseModelBuilder(baseModelName, major, minor, ModelType.DISTANCE_FALLOFF)
                .processedFeaturesFilter(HashSet.of(
                        "diner_latitude",
                        "diner_longitude",
                        "restaurant_latitude",
                        "restaurant_longitude",
                        "onion_powder",
                        "distance_threshold",
                        "decay_rate",
                        "traffic_level_multiplier",
                        "min_weight",
                        "threshold_scalar",
                        "diner_bias",
                        "min_bias_weight",
                        "min_bias",
                        "quality",
                        "quality_exponent",
                        "distance_exponent"
                ))
                .modelFeatures(List.of(
                        buildRuntimeFeatureRequired("RESTAURANT_ID"),
                        buildRuntimeFeatureDefaultStr("location_mode", "DELIVERY"),
                        buildRuntimeFeatureDefaultEmptyStr("diner_geohash_6"),
                        buildRuntimeFeatureDefaultStr("diner_latitude", "0.0"),
                        buildRuntimeFeatureDefaultStr("diner_longitude", "0.0"),
                        buildInternalFeature(baseModelName, "search_m3_geohash_feature", major, minor, 0, false,
                                List.of("diner_geohash_6", "location_mode"),
                                HashMap.of(
                                        "diner_geohash_6", "",
                                        "location_mode", "DELIVERY",
                                        "distance_threshold", 3.0
                                )),
                        buildInternalFeature(baseModelName, "search_m3_restaurant_feature", major, minor, 0, false,
                                List.of("RESTAURANT_ID"),
                                HashMap.of(
                                        "RESTAURANT_ID", "",
                                        "restaurant_latitude", 0.0,
                                        "restaurant_longitude", 0.0,
                                        "onion_powder", 0.5
                                )),
                        buildRuntimeFeatureDefaultStr("decay_rate", "0.8"),
                        buildRuntimeFeatureDefaultStr("traffic_level_multiplier", "0.0"),
                        buildRuntimeFeatureDefaultStr("min_weight", "0.01"),
                        buildRuntimeFeatureDefaultStr("threshold_scalar", "0.001"),
                        buildRuntimeFeatureDefaultStr("diner_bias", "0.5"),
                        buildRuntimeFeatureDefaultStr("min_bias_weight", "0.0"),
                        buildRuntimeFeatureDefaultStr("min_bias", "0.8"),
                        buildRuntimeFeatureDefaultStr("quality", "1.0"),
                        buildRuntimeFeatureDefaultStr("quality_exponent", "0.0"),
                        buildRuntimeFeatureDefaultStr("distance_exponent", "1.0")
                ))
                .modelOutputs(buildModelOutputs())
                .modelGroup("search")
                .build();
    }

    public static FeatureDTO buildInternalFeature(String baseModelName,
                                                  String featureName,
                                                  int major,
                                                  int minor,
                                                  int order,
                                                  boolean exposeKeysAsAdditionalFeature,
                                                  List<String> featureStoreFields, Map<String, ?> defaultValue) {
        return FeatureDTO.builder()
                .featureName(featureName)
                .majorVersion(String.valueOf(major))
                .minorVersion(String.valueOf(minor))
                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                .featureStoreFields(featureStoreFields)
                .featureOptional(defaultValue != null && defaultValue.nonEmpty())
                .featureDefaultValue(toJsonStr(defaultValue))
                .status("ENABLED")
                .featureLocation(BASE_MODEL_LOCATION + getModelName(baseModelName, major, minor) + "/" + featureName + "/")
                .featureOrder(order)
                .exposeFeatureStoreFieldsAsFeature(exposeKeysAsAdditionalFeature)
                .build();
    }

    public static ModelDTO.ModelDTOBuilder getBaseModelBuilder(String baseModelName, int major, int minor,
                                                               ModelType modelType) {
        val modelName = getModelName(baseModelName, major, minor);
        return ModelDTO.builder()
                .modelName(modelName)
                .modelDescription(baseModelName)
                .modelType(modelType.name())
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningModelName(baseModelName)
                .location(BASE_MODEL_LOCATION + modelName + "/model/")
                .servingLocation(BASE_SERVING_LOCATION + modelName + "/" + major + "/")
                .version(major + "." + minor);
    }

    public static String getModelName(String baseModelName, int major, int minor) {
        return baseModelName + "_v" + major + "." + minor;
    }

    public static FeatureDTO buildFunctionFeature(String featureName, String functionName, int order, Map<String, String> inputs,
                                                  Map<String, String> outputs) {
        return FeatureDTO.builder()
                .featureName(featureName)
                .featureSourceType(FeatureSourceType.FUNCTION.name())
                .status("ENABLED")
                .featureOrder(order)
                .functionName(functionName)
                .functionInputs(inputs.toJavaMap())
                .functionOutputs(outputs.toJavaMap())
                .build();
    }

    public static FeatureDTO buildRuntimeFeature(String featureName, boolean isOptional,
                                                 String defaultValue, FeatureDefaultValueType defValueType) {
        return FeatureDTO.builder()
                .featureName(featureName)
                .featureSourceType(FeatureSourceType.RUNTIME.name())
                .status("ENABLED")
                .featureOptional(isOptional)
                .featureDefaultValue(defaultValue)
                .majorVersion("1")
                .minorVersion("1")
                .featureDefaultType(isOptional ? defValueType.name() : null)
                .build();
    }

    public static FeatureDTO buildRuntimeFeatureRequired(String featureName) {
        return buildRuntimeFeature(featureName, false, null, null);
    }

    public static FeatureDTO buildRuntimeFeatureDefaultEmptyStr(String featureName) {
        return buildRuntimeFeature(featureName, true, "", FeatureDefaultValueType.STRING);
    }

    public static FeatureDTO buildRuntimeFeatureDefaultStr(String featureName, String defaultValue) {
        return buildRuntimeFeature(featureName, true, defaultValue, FeatureDefaultValueType.STRING);
    }

    public static FeatureDTO buildRuntimeFeatureDefaultNull(String featureName) {
        return buildRuntimeFeature(featureName, true, null, FeatureDefaultValueType.NULL);
    }

    public static FeatureDTO buildRuntimeFeatureBoolean(String featureName, boolean defaultValue) {
        return buildRuntimeFeature(featureName, true, String.valueOf(defaultValue), FeatureDefaultValueType.BOOLEAN);
    }

    public static FeatureDTO buildRuntimeFeatureDefaultInt(String featureName, String defaultValue){
        return buildRuntimeFeature(featureName, true, String.valueOf(defaultValue), FeatureDefaultValueType.INTEGER);
    }

    public static FeatureValueDTO buildFeatureValue(FeatureDTO feature, List<String> keyComponents, Map<String, ?> value) {
        return FeatureValueDTO.builder()
                .featureName(feature.getFeatureName())
                .majorVersion(Integer.parseInt(feature.getMajorVersion()))
                .minorVersion(Integer.parseInt(feature.getMinorVersion()))
                .featureKey(keyComponents.mkString(";"))
                .featureValue(toJsonStr(value))
                .build();
    }

    public static FeatureValueDTO buildFeatureValue(ModelDTO model, String featureName, List<String> keyComponents,
                                                    Map<String, ?> value) {
        return buildFeatureValue(getFeature(model, featureName), keyComponents, value);
    }

    public static FeatureDTO getFeature(ModelDTO model, String featureName) {
        return model.getModelFeatures().find(f -> f.getFeatureName().equals(featureName)).get();
    }

    public static java.util.List<java.util.Map<String, Object>> buildInvocationFeatures(String featureName, List<?> values) {
        return values.map(value -> {
            java.util.Map<String, Object> map = new java.util.HashMap<>();
            map.put(featureName, value);
            return map;
        }).toJavaList();
    }

    public static java.util.List<java.util.Map<String, Object>> buildInvocationFeatures(String featureName1, List<?> values1,
                                                                                        String featureName2, List<?> values2) {
        return values1.zip(values2).map(t -> {
            java.util.Map<String, Object> map = new java.util.HashMap<>();
            map.put(featureName1, t._1);
            map.put(featureName2, t._2);
            return map;
        }).toJavaList();
    }

    public static java.util.Map<String, Object> invocationFeature(String featureName, Object value) {
        return InvocationFeatureBuilder.create()
                .add(featureName, value)
                .build();
    }

    public static java.util.Map<String, Object> invocationFeature(String featureName1, Object value1,
                                                                  String featureName2, Object value2) {
        return InvocationFeatureBuilder.create()
                .add(featureName1, value1)
                .add(featureName2, value2)
                .build();
    }

    public static java.util.Map<String, Object> invocationFeature(String featureName1, Object value1,
                                                                  String featureName2, Object value2,
                                                                  String featureName3, Object value3) {
        return InvocationFeatureBuilder.create()
                .add(featureName1, value1)
                .add(featureName2, value2)
                .add(featureName3, value3)
                .build();
    }

    public static InvocationFeatureBuilder invocationFeatureBuilder() {
        return InvocationFeatureBuilder.create();
    }

    public static class InvocationFeatureBuilder {

        private final java.util.Map<String, Object> map;

        public static InvocationFeatureBuilder create() {
            return new InvocationFeatureBuilder();
        }

        public InvocationFeatureBuilder() {
            this.map = new java.util.HashMap<>();
        }

        public InvocationFeatureBuilder add(String featureName, Object value) {
            map.put(featureName, value);
            return this;
        }

        public java.util.Map<String, Object> build() {
            return this.map;
        }
    }

    public static EntitiesAliasDTO buildModelAlias(String aliasName, ModelDTO model) {
        return EntitiesAliasDTO.builder()
                .collectionName(EntityCollection.Constants.COLLECTION_MODEL)
                .aliasName(aliasName)
                .entityId(model.getModelName())
                .build();
    }

    public static void compareModelOutput(List<Float> output, List<Float> expectedOutput) {
        assertThat(output).hasSize(expectedOutput.size());
        for (int i = 0; i < output.size(); i++) {
            val v1 = output.get(0);
            val v2 = expectedOutput.get(0);
            assertThat(v1).as("Index: " + i).isEqualTo(v2, within(0.01f));
        }
    }

    public static void compareLists(List<?> output, List<?> expectedOutput) {
        assertThat(output).hasSize(expectedOutput.size());
        for (int i = 0; i < output.size(); i++) {
            val v1 = output.get(0);
            val v2 = expectedOutput.get(0);
            assertThat(v1).as("Index: " + i).isEqualTo(v2);
        }
    }

    public static void compareEnsembleOutput(java.util.List<Float> output, List<Float> expectedOutput) {
        compareModelOutput(List.ofAll(output), expectedOutput);
    }

    public static List<Float> combineOutputsWithWeights(EnsembleDTO ensemble, String weights, Map<String, List<Float>> modelOutputs) {
        return combineOutputsWithWeights(ensemble.getEnsembleWeights().get(weights).get(), modelOutputs);
    }

    public static List<Float> combineSingleOutputWithWeights(EnsembleDTO ensemble, String weights, Map<String, Float> modelOutputs) {
        val modelOutputsWithList = modelOutputs.map(i -> Tuple.of(i._1, List.of(i._2))).toMap(i -> i);
        return combineOutputsWithWeights(ensemble.getEnsembleWeights().get(weights).get(), modelOutputsWithList);
    }

    public static List<Float> combineOutputsWithWeights(Map<String, Float> weights, Map<String, List<Float>> modelOutputs) {
        if (weights.size() != modelOutputs.size()) {
            throw new RuntimeException("Size doesn't match, weights: " + weights.size() + ", modelOutputs: " + modelOutputs.size());
        }
        val fistModelOutput = modelOutputs.head()._2;
        modelOutputs.forEach(i -> {
            if (i._2.size() != fistModelOutput.size()) {
                throw new RuntimeException("Model: " + i._1 + " has invalid output size: " + i._2.size());
            }
        });
        return List.range(0, fistModelOutput.size()).map(idx ->
                weights.map(w -> w._2 * modelOutputs.get(w._1).get().get(idx)).sum().floatValue());
    }

    public static ModelDTO disableNormalization(ModelDTO model) {
        val outputInfo = model.getFirstFloatOutputType().get()
                .withNormalized(false);
        return model.withModelOutputs(List.of(outputInfo));
    }

    public static String toJsonStr(Map<String, ?> map) {
        try {
            return ObjectMapperHelper.INSTANCE.writeValueAsString(map);
        } catch (Exception e) {
            throw new RuntimeException("Error converting to map json str: " + map);
        }
    }

    public ModelDTO createSimpleFunctionModel(String modelName, String featureName) {
        return createSimpleFunctionModel(modelName, featureName, "", 68, 68, false, ModelStatus.ENABLED);
    }

    public ModelDTO createSimpleFunctionModel(String modelName, String featureName, String featureDefValue,
                                              int majorVersion, int minorVersion, boolean isDynamic, ModelStatus status) {
        val modelNameVersioned = getModelName(modelName, majorVersion, minorVersion);
        return ModelDTO
                .builder()
                .modelName(modelNameVersioned)
                .modelDescription(modelNameVersioned)
                .versioningModelName(modelName)
                .modelType(ModelType.FUNCTION.name())
                .status(status.getName())
                .version(majorVersion + "." + minorVersion)
                .versioningStrategy(isDynamic ? ModelDTO.DYNAMIC : ModelDTO.STATIC)
                .modelFeatures(List.of(FeatureDTO.builder()
                        .featureName(featureName)
                        .featureSourceType("RESTAURANT_ID")
                        .featureSourceType(FeatureSourceType.RUNTIME.name())
                        .status("ENABLED")
                        .featureOptional(true)
                        .featureDefaultValue(featureDefValue)
                        .build()
                ))
                .modelOutputs(List.of(ModelOutputDTO
                        .builder()
                        .outputName("output")
                        .outputType(ModelOutputType.FLOAT.name())
                        .outputShape(List.of(1))
                        .normalized(false)
                        .build()))
                .build();
    }

    public static JavaModels getJavaModels() {
        return new JavaModels(
                INJECTOR.getInstance(DistanceFalloffModel.class),
                INJECTOR.getInstance(LinearCombinationModel.class),
                INJECTOR.getInstance(FeaturesFetchModel.class),
                INJECTOR.getInstance(RandomSortModel.class),
                INJECTOR.getInstance(SearchEmbeddingsModel.class),
                INJECTOR.getInstance(TextSimilarityModel.class),
                INJECTOR.getInstance(VectorSimilarityModel.class),
                INJECTOR.getInstance(RandomSampleModel.class),
                INJECTOR.getInstance(RandomDistributionModel.class),
                INJECTOR.getInstance(RankingPostProcessingModel.class),
                INJECTOR.getInstance(ScoreAggregationModel.class),
                INJECTOR.getInstance(RuleMatchingModel.class),
                INJECTOR.getInstance(QueryExpansionModel.class),
                INJECTOR.getInstance(PrecomputedRankerModel.class),
                INJECTOR.getInstance(SageMakerModel.class)
        );
    }

    public static ModelDTO createRandomSortModel() {
        return ModelDTO
                .builder()
                .modelName("randomSort")
                .modelDescription("randomSort")
                .versioningModelName("randomSort")
                .modelType(ModelType.RANDOM_SORT.name())
                .status(ModelStatus.ENABLED.getName())
                .version("1.0")
                .versioningStrategy(ModelDTO.STATIC)
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName(RandomUtil.CONFIG_CUSTOM_SEED)
                                .featureSourceType("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("0")
                                .build(),
                        FeatureDTO.builder()
                                .featureName(RandomUtil.CONFIG_SEED_MODE)
                                .featureSourceType("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue(SeedMode.CUSTOM_SEED.name())
                                .build(),
                        FeatureDTO.builder()
                                .featureName(RandomSortModel.CONFIG_FIXED_POSITION)
                                .featureSourceType("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue(String.valueOf(RandomSortModel.DEFAULT_FIXED_POSITION))
                                .build()
                ))
                .modelOutputs(List.of(ModelOutputDTO
                        .builder()
                        .outputName("output")
                        .outputType(ModelOutputType.FLOAT.name())
                        .outputShape(List.of(1))
                        .normalized(false)
                        .build()))
                .build();
    }

    // Model to retrieve a list of randomized lists of image ids.
    public static ModelDTO createRandomSampleModel() {
        return ModelDTO
                .builder()
                .modelName("randomSample")
                .modelDescription("randomSample")
                .versioningModelName("randomSample")
                .modelType(ModelType.RANDOM_SAMPLE.name())
                .status(ModelStatus.ENABLED.getName())
                .version("1.0")
                .versioningStrategy(ModelDTO.STATIC)
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName(RandomUtil.CONFIG_CUSTOM_SEED)
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("1")
                                .build(),
                        FeatureDTO.builder()
                                .featureName(RandomUtil.CONFIG_SEED_MODE)
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue(SeedMode.DAILY.name())
                                .build(),
                        FeatureDTO.builder()
                                .featureName(RandomSampleModel.CONFIG_TOTAL_SAMPLE)
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("1")
                                .build(),
                        FeatureDTO.builder()
                                .featureName(RandomSampleModel.CONFIG_SAMPLING_MODE)
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue(SamplingMode.UNIFORM.name())
                                .build(),
                        FeatureDTO.builder()
                                .featureName(RandomSampleModel.INPUT_LIST)
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .build(),
                        FeatureDTO.builder()
                                .featureName(RandomSampleModel.INPUT_PROBABILITY)
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .build()
                ))
                .modelOutputs(List.of(ModelOutputDTO
                        .builder()
                        .outputName(DEFAULT_OUTPUT_NAME)
                        .outputType(ModelOutputType.STRING_ARRAY.name())
                        .outputShape(List.of(1))
                        .normalized(false)
                        .build()))
                .build();
    }

    public static ModelDTO createRestaurantRandomImagesModel() {
        return ModelDTO
                .builder()
                .modelName("search_img_distr_rand_v1.0")
                .modelDescription("search_img_distr_rand_v1.0")
                .versioningModelName("search_img_distr_rand")
                .modelType(ModelType.RANDOM_DISTRIBUTION.name())
                .status(ModelStatus.ENABLED.getName())
                .version("1.0")
                .versioningStrategy(ModelDTO.STATIC)
                .modelFeatures(List.of(
                        buildRuntimeFeatureDefaultNull("restaurant_id"),
                        buildRuntimeFeatureDefaultNull(RandomDistributionModel.DINER_ID),
                        buildRuntimeFeatureDefaultNull(RandomDistributionModel.TRACKING_ID),
                        buildRuntimeFeatureDefaultStr(RandomDistributionModel.TOP_N, "1"),
                        buildRuntimeFeatureDefaultStr(RandomDistributionModel.MODE, "random_sampling"),
                        buildRuntimeFeatureDefaultStr(RandomDistributionModel.SEED_MODE, "tracking_id"),
                        buildRuntimeFeatureDefaultStr(RandomDistributionModel.DISTRIBUTION, "beta"),
                        buildInternalFeature("search_img_distr_rand", "search_restaurant_images_feature", 1, 1, 0,
                                true, // Keys will be exposed as internal feature to be able to create the seed for the model.
                                List.of("restaurant_id"),
                                HashMap.of(
                                        RandomDistributionModel.INTERNAL_TARGET_VALUES_KEY, new String[]{},
                                        RandomDistributionModel.INTERNAL_ALPHA_PARAMS_KEY, new Double[]{},
                                        RandomDistributionModel.INTERNAL_BETA_PARAMS_KEY, new Double[]{}
                                )
                        )))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("image_urls")
                                .outputType(ModelOutputType.STRING_ARRAY.name())
                                .outputShape(List.of(1))
                                .normalized(false)
                                .build(),
                        ModelOutputDTO
                                .builder()
                                .outputName("scores")
                                .outputType(ModelOutputType.DOUBLE_ARRAY.name())
                                .outputShape(List.of(1))
                                .normalized(false)
                                .build()
                ))
                .build();
    }

    public static ModelDTO createRankPostProcessing() {
        return ModelDTO
                .builder()
                .modelName("search_rank_postprocessing_v1.0")
                .modelDescription("search_rank_postprocessing_v1.0")
                .versioningModelName("search_rank_postprocessing")
                .modelType(ModelType.RANKING_POST_PROCESSING.name())
                .status(ModelStatus.ENABLED.getName())
                .version("1.0")
                .versioningStrategy(ModelDTO.STATIC)
                .modelFeatures(List.of(
                        buildRuntimeFeatureDefaultNull("strategy"),
                        buildRuntimeFeatureDefaultNull("random_seed"),
                        buildRuntimeFeatureDefaultNull("entity_id"),
                        buildRuntimeFeatureDefaultStr("pin_entity_id", ""),
                        buildRuntimeFeatureDefaultInt("max_ranking_slots", "3"))
                )
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("entities")
                                .outputType(ModelOutputType.STRING_ARRAY.name())
                                .outputShape(List.of(1))
                                .normalized(false)
                                .build()
                ))
                .build();
    }

    public static ModelDTO createMABRankerModel() {
        return ModelDTO
                .builder()
                .modelName("topics_mab_mp_ts_ranker_test_v1.2")
                .modelDescription("topics_mab_mp_ts_ranker_test")
                .versioningModelName("topics_mab_mp_ts_ranker_test")
                .modelType(ModelType.RANDOM_DISTRIBUTION.name())
                .status(ModelStatus.ENABLED.getName())
                .version("1.0")
                .versioningStrategy(ModelDTO.STATIC)
                .modelFeatures(List.of(
                        buildRuntimeFeatureDefaultNull("topic_name"),
                        buildRuntimeFeatureDefaultNull("diner_geohash_4"),
                        buildRuntimeFeatureDefaultNull("mealtime"),
                        buildRuntimeFeatureDefaultNull(RandomDistributionModel.DINER_ID),
                        buildRuntimeFeatureDefaultNull(RandomDistributionModel.TRACKING_ID),
                        buildRuntimeFeatureDefaultStr(RandomDistributionModel.TOP_N, "1"),
                        buildRuntimeFeatureDefaultStr(RandomDistributionModel.MODE, "random_sampling"),
                        buildRuntimeFeatureDefaultStr(RandomDistributionModel.SEED_MODE, "tracking_id"),
                        buildRuntimeFeatureDefaultStr(RandomDistributionModel.DISTRIBUTION, "beta"),
                        buildInternalFeature("topics_mab_mp_ts_ranker_test_v1.2", "topics_mab_mp_ts_ranker_test_internal_feature", 1, 1, 0,
                                true, // Keys will be exposed as internal feature to be able to create the seed for the model.
                                List.of("topic_name", "diner_geohash_4", "mealtime"),
                                HashMap.of(
                                        "topic_name", "",
                                        "diner_geohash_4", "",
                                        "mealtime", "",
                                        RandomDistributionModel.INTERNAL_ALPHA_PARAMS_KEY, new Double[]{1.},
                                        RandomDistributionModel.INTERNAL_BETA_PARAMS_KEY, new Double[]{1.}
                                )
                        )))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("scores")
                                .outputType(ModelOutputType.FLOAT_ARRAY.name())
                                .outputShape(List.of(1))
                                .normalized(false)
                                .build()
                ))
                .build();
    }
}

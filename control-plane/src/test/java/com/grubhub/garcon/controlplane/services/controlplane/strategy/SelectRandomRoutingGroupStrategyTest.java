package com.grubhub.garcon.controlplane.services.controlplane.strategy;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.dto.EnrichedResolveFlowRequest;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.CompositeRandomRoutingGroupStrategy;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.GeohashRandomRoutingGroupStrategy;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.RoutingGroupStrategy;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.RoutingGroupStrategyInput;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.SelectFirstRoutingGroupStrategy;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.UuidRandomRoutingGroupStrategy;
import com.grubhub.garcon.controlplaneapi.models.BucketingMode;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.search.experiment.ExperimentBranchManager;
import com.grubhub.garcon.search.experiment.api.CutoffProvider;
import io.vavr.collection.List;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.UUID;
import java.util.stream.Stream;

import static com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.RoutingGroupStrategyFactory.pickFlowRoutingGroupStrategy;
import static com.grubhub.garcon.controlplane.utils.InjectorProvider.INJECTOR;
import static com.grubhub.roux.api.datetime.JavaDateTimeHelper.utc;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SelectRandomRoutingGroupStrategyTest {

    @Spy
    private final ControlPlaneMapper controlPlaneMapper = INJECTOR.getInstance(ControlPlaneMapper.class);
    @Mock
    private EnrichedResolveFlowRequest enrichedResolveFlowRequest;
    @Mock
    private FlowConfig flowConfig;
    @Mock
    private FlowDTO flow;

    private ExperimentBranchManager experimentBranchManager;

    private RoutingGroupStrategy routingGroupStrategy;

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.openMocks(this);

        when(enrichedResolveFlowRequest.getFlowConfig()).thenReturn(flowConfig);
        experimentBranchManager = new ExperimentBranchManager(new CutoffProvider.Simple());
        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(List.empty())
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();
        routingGroupStrategy = pickFlowRoutingGroupStrategy(routingGroupStrategyInput);
    }

    @Test
    public void selectRoutingGroup_should_return_first_routing_group() {
        val routingGroup = FlowRoutingGroupV2
                .builder()
                .variation("variation 1")
                .ensembleName("search_m1_m2")
                .ensembleWeight("default")
                .routingPercentage(0.6f)
                .groupName("search")
                .groupOrder(0)
                .flowId(UUID.randomUUID())
                .build();

        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(List.of(routingGroup))
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        routingGroupStrategy = pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        val routingGroupResponse = routingGroupStrategy.selectRoutingGroup();
        assertThat(routingGroupStrategy).isInstanceOf(SelectFirstRoutingGroupStrategy.class);
        assertThat(routingGroupResponse.getRoutingRand()).isEqualTo(1d);
        assertThat(routingGroupResponse.getRoutingPercentage()).isEqualTo(0.6f);
        assertThat(routingGroupResponse.getEnsembleName()).isEqualTo("search_m1_m2");
    }

    @Test
    public void selectRoutingGroup_should_return_last_one_routing_when_seed_is_not_provided() {
        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(build10RoutingGroups())
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        when(flowConfig.getRoutingGroupsGeohashPrecision()).thenReturn(5);
        when(enrichedResolveFlowRequest.getDinerId()).thenReturn(null);
        when(enrichedResolveFlowRequest.getTrackingId()).thenReturn(UUID.randomUUID().toString());

        routingGroupStrategy = pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        val routingGroupResponse = routingGroupStrategy.selectRoutingGroup();
        assertThat(routingGroupStrategy).isInstanceOf(GeohashRandomRoutingGroupStrategy.class);
        assertThat(routingGroupResponse.getRoutingRand()).isEqualTo(1.00);
        assertThat(routingGroupResponse.getGroupName()).isEqualTo("search 10");
    }

    @Test
    public void selectRoutingGroup_should_return_random_routing_when_many_routing_groups_exists() {
        when(flow.getRoutingGroupsSeed()).thenReturn(0);

        final FlowConfig flowConfig = new FlowConfig();
        flowConfig.setRoutingGroupsSeed(1);

        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(build10RoutingGroups())
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        routingGroupStrategy = pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        val routingGroupResponse = routingGroupStrategy.selectRoutingGroup();
        assertThat(routingGroupStrategy).isInstanceOf(GeohashRandomRoutingGroupStrategy.class);
        assertThat(routingGroupResponse.getRoutingRand()).isEqualTo(1.00);
        assertThat(routingGroupResponse.getGroupName()).isEqualTo("search 5");
    }

    @Test
    public void selectRoutingGroup_should_use_seed_provided_by_flow_instead_of_config_when_flow_has_seed_value() {
        when(flow.getRoutingGroupsSeed()).thenReturn(3);

        final FlowConfig flowConfig = new FlowConfig();
        flowConfig.setRoutingGroupsSeed(1);

        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(build10RoutingGroups())
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        routingGroupStrategy = pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        val routingGroupResponse = routingGroupStrategy.selectRoutingGroup();
        assertThat(routingGroupStrategy).isInstanceOf(GeohashRandomRoutingGroupStrategy.class);
        assertThat(routingGroupResponse.getRoutingRand()).isEqualTo(1.00);
        assertThat(routingGroupResponse.getGroupName()).isEqualTo("search 4");
    }

    @Test
    public void selectRoutingGroup_should_return_random_routing_with_probability_2_groups() {
        val builder = FlowRoutingGroupV2
                .builder()
                .variation("variation 1")
                .ensembleName("search_m1_m2")
                .ensembleWeight("default")
                .groupOrder(0)
                .flowId(UUID.randomUUID());

        val groups = Stream.of(
                builder.routingPercentage(0.5f).groupName("search 1").build(),
                builder.routingPercentage(0.5f).groupName("search 2").build()
        ).collect(List.collector());

        final FlowConfig flowConfig = new FlowConfig();
        flowConfig.setRoutingGroupsGeohashPrecision(9);
        flowConfig.setRoutingGroupsSeed(1);

        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(groups)
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        when(enrichedResolveFlowRequest.getDinerId()).thenReturn(null);
        when(enrichedResolveFlowRequest.getTrackingId()).thenReturn(UUID.randomUUID().toString());

        when(enrichedResolveFlowRequest.getLat()).thenReturn(42D);
        when(enrichedResolveFlowRequest.getLng()).thenReturn(-118D);
        when(enrichedResolveFlowRequest.getFlowConfig()).thenReturn(flowConfig);

        when(flow.getRoutingGroupsBucketingMode()).thenReturn(BucketingMode.GEOHASH);
        when(flow.getRoutingGroupsSeed()).thenReturn(0);

        routingGroupStrategy = pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        val routingGroupResponse = routingGroupStrategy.selectRoutingGroup();
        assertThat(routingGroupStrategy).isInstanceOf(GeohashRandomRoutingGroupStrategy.class);
        assertThat(routingGroupResponse.getRoutingRand()).isEqualTo(1.00);
        assertThat(routingGroupResponse.getGroupName()).isEqualTo("search 2");
    }

    @Test
    public void selectRoutingGroup_should_return_random_routing_with_probability_2_groups_and_combinedBucketingMode() {
        val builder = FlowRoutingGroupV2
                .builder()
                .variation("variation 1")
                .ensembleName("search_m1_m2")
                .ensembleWeight("default")
                .groupOrder(0)
                .flowId(UUID.randomUUID());

        val groups = Stream.of(
                builder.routingPercentage(0.5f).groupName("search 1").build(),
                builder.routingPercentage(0.5f).groupName("search 2").build()
        ).collect(List.collector());

        final FlowConfig flowConfig = new FlowConfig();
        flowConfig.setRoutingGroupsSeed(1);

        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(groups)
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        when(enrichedResolveFlowRequest.getDinerId()).thenReturn("951a1879-d832-459b-b9d6-1fb87c11d157");
        when(enrichedResolveFlowRequest.getClientEntityId()).thenReturn("4e81b2a1-85f9-42d3-8219-cbbd0a446d10");
        when(enrichedResolveFlowRequest.getTrackingId()).thenReturn(UUID.randomUUID().toString());

        when(enrichedResolveFlowRequest.getFlowConfig()).thenReturn(flowConfig);

        when(flow.getRoutingGroupsBucketingMode()).thenReturn(BucketingMode.DINER_AND_CLIENT_ENTITY_ID);
        when(flow.getRoutingGroupsSeed()).thenReturn(0);

        routingGroupStrategy = pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        val routingGroupResponse = routingGroupStrategy.selectRoutingGroup();
        assertThat(routingGroupStrategy).isInstanceOf(CompositeRandomRoutingGroupStrategy.class);
        assertThat(routingGroupResponse.getRoutingRand()).isEqualTo(1.00);
        assertThat(routingGroupResponse.getGroupName()).isEqualTo("search 2");
    }

    @ParameterizedTest
    @MethodSource("provideUuidRoutingFlowTestArguments")
    public void selectRoutingGroup_should_return_random_routing_with_probability_2_groups_and_uuidBucketingMode(
            final EnrichedResolveFlowRequest enrichedResolveFlowRequest, final BucketingMode bucketingMode
    ) {
        val builder = FlowRoutingGroupV2
                .builder()
                .variation("variation 1")
                .ensembleName("search_m1_m2")
                .ensembleWeight("default")
                .groupOrder(0)
                .flowId(UUID.randomUUID());

        val groups = Stream.of(
                builder.routingPercentage(0.5f).groupName("search 1").build(),
                builder.routingPercentage(0.5f).groupName("search 2").build()
        ).collect(List.collector());

        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(groups)
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        when(flow.getRoutingGroupsBucketingMode()).thenReturn(bucketingMode);
        when(flow.getRoutingGroupsSeed()).thenReturn(0);

        routingGroupStrategy = pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        assertThat(routingGroupStrategy).isInstanceOf(UuidRandomRoutingGroupStrategy.class);

        val routingGroupResponse = routingGroupStrategy.selectRoutingGroup();
        assertThat(routingGroupResponse.getRoutingRand()).isEqualTo(1.00);
        assertThat(routingGroupResponse.getGroupName()).isEqualTo("search 2");
    }

    @ParameterizedTest
    @MethodSource("provideUuidRoutingFlowTestArguments")
    public void selectRoutingGroup_should_return_same_routing_when_called_twice_with_probability_2_groups_and_uuidBucketingMode(
            final EnrichedResolveFlowRequest enrichedResolveFlowRequest, final BucketingMode bucketingMode
    ) {
        val builder = FlowRoutingGroupV2
                .builder()
                .variation("variation 1")
                .ensembleName("search_m1_m2")
                .ensembleWeight("default")
                .groupOrder(0)
                .flowId(UUID.randomUUID());

        val groups = Stream.of(
                builder.routingPercentage(0.5f).groupName("search 1").build(),
                builder.routingPercentage(0.5f).groupName("search 2").build()
        ).collect(List.collector());

        final RoutingGroupStrategyInput routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flow)
                .flowConfig(flowConfig)
                .flowRoutingGroups(groups)
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(enrichedResolveFlowRequest)
                .controlPlaneMapper(controlPlaneMapper).build();

        when(flow.getRoutingGroupsBucketingMode()).thenReturn(bucketingMode);
        when(flow.getRoutingGroupsSeed()).thenReturn(0);

        routingGroupStrategy = pickFlowRoutingGroupStrategy(routingGroupStrategyInput);

        val firstRoutingGroupResponse = routingGroupStrategy.selectRoutingGroup();
        val secondRoutingGroupResponse = routingGroupStrategy.selectRoutingGroup();

        assertThat(routingGroupStrategy).isInstanceOf(UuidRandomRoutingGroupStrategy.class);

        assertThat(firstRoutingGroupResponse.getRoutingRand()).isEqualTo(1.00);
        assertThat(firstRoutingGroupResponse.getGroupName()).isEqualTo("search 2");

        assertThat(secondRoutingGroupResponse.getRoutingRand()).isEqualTo(1.00);
        assertThat(secondRoutingGroupResponse.getGroupName()).isEqualTo("search 2");
    }

    private static Stream<Arguments> provideUuidRoutingFlowTestArguments() {
        final FlowConfig flowConfig = new FlowConfig();
        flowConfig.setRoutingGroupsSeed(1);

        return Stream.of(
                Arguments.of(EnrichedResolveFlowRequest.builder()
                                .dinerId("951a1879-d832-459b-b9d6-1fb87c11d157")
                                .trackingId(UUID.randomUUID().toString())
                                .flowConfig(flowConfig).build(),
                        BucketingMode.DINER),
                Arguments.of(EnrichedResolveFlowRequest.builder()
                                .callerTrackingId("6fd1661a-1e43-4503-a1ba-0e748fcc7ac2")
                                .trackingId(UUID.randomUUID().toString())
                                .flowConfig(flowConfig).build(),
                        BucketingMode.CALLER_TRACKING_ID),
                Arguments.of(EnrichedResolveFlowRequest.builder()
                                .trackingId("0f961a78-0a25-4ed1-bc05-a1b0b947a637")
                                .flowConfig(flowConfig).build(),
                        BucketingMode.TRACKING_ID)
        );
    }

    private List<FlowRoutingGroupV2> build10RoutingGroups() {
        val builder = FlowRoutingGroupV2
                .builder()
                .variation("variation 1")
                .ensembleName("search_m1_m2")
                .ensembleWeight("default")
                .groupOrder(0)
                .flowId(UUID.randomUUID());

        return Stream.of(
                        builder.routingPercentage(0.05f).groupName("search 1").build(),
                        builder.routingPercentage(0.06f).groupName("search 2").build(),
                        builder.routingPercentage(0.05f).groupName("search 3").build(),
                        builder.routingPercentage(0.05f).groupName("search 4").build(),
                        builder.routingPercentage(0.18f).groupName("search 5").build(),
                        builder.routingPercentage(0.16f).groupName("search 6").build(),
                        builder.routingPercentage(0.15f).groupName("search 7").build(),
                        builder.routingPercentage(0.13f).groupName("search 8").build(),
                        builder.routingPercentage(0.08f).groupName("search 9").build(),
                        builder.routingPercentage(0.12f).groupName("search 10").build()
                )
                .collect(List.collector());
    }

    private Instant getInstant() {
        return utc(2021, 2, 17, 13, 21, 56, 90).toInstant();
    }

}

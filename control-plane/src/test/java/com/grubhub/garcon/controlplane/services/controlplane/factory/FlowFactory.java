package com.grubhub.garcon.controlplane.services.controlplane.factory;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupDTO;
import io.vavr.collection.HashMap;
import lombok.experimental.UtilityClass;
import lombok.val;
import io.vavr.collection.HashSet;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

@UtilityClass
public class FlowFactory {

    public static final String FLOW_ID_AS_STRING = String.valueOf(UUID.randomUUID());
    public static final String ROUTING_GROUPS_ENSEMBLE_NAME = "search_m1_m2";

    public static FlowApi createFlowWithEmptyLocationMarkets() {
        return FlowApi.builder()
                .flowId(FLOW_ID_AS_STRING)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(Collections.emptySet())
                .matchingDinerTypes(ImmutableSet.of("dish"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .build();
    }

    public static FlowApi createUnauthenticatedFlow() {
        return FlowApi.builder()
                .flowId(FLOW_ID_AS_STRING)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("chicago"))
                .matchingDinerTypes(ImmutableSet.of("unauthenticated"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .build();
    }

    public static FlowApi createNewDinerFlow() {
        return FlowApi.builder()
                .flowId(FLOW_ID_AS_STRING)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("chicago"))
                .matchingDinerTypes(ImmutableSet.of("new_diner"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .build();
    }

    public static FlowApi createNewDinerFlowWithDinerTypeOrdersThreshold(int dinerTypeOrdersThreshold) {
        return FlowApi.builder()
                .flowId(FLOW_ID_AS_STRING)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("chicago"))
                .matchingDinerTypes(ImmutableSet.of("new_diner"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .dinerTypeOrdersThreshold(dinerTypeOrdersThreshold)
                .build();
    }

    public static FlowApi createExistingDinerFlow() {
        return FlowApi.builder()
                .flowId(FLOW_ID_AS_STRING)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("chicago"))
                .matchingDinerTypes(ImmutableSet.of("existing_diner"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .build();
    }

    public static FlowApi createExistingDinerFlowWithDinerTypeOrdersThreshold(int dinerTypeOrdersThreshold) {
        return FlowApi.builder()
                .flowId(FLOW_ID_AS_STRING)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("chicago"))
                .matchingDinerTypes(ImmutableSet.of("existing_diner"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .dinerTypeOrdersThreshold(dinerTypeOrdersThreshold)
                .build();
    }

    public static FlowApi createFlowForVariationInput() {
        return FlowApi.builder()
                .flowId(FLOW_ID_AS_STRING)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("chicago"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .build();
    }

    public static FlowApi createFlow() {
        String flowId = String.valueOf(UUID.randomUUID());
        return FlowApi.builder()
                .flowId(flowId)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(ImmutableSet.of("DELIVERY", "CATERING"))
                .matchingMealtime(ImmutableSet.of("breakfast", "dinner"))
                .matchingQueryTypes(ImmutableSet.of("INTENTFUL"))
                .matchingApplications(ImmutableSet.of("umami", "iOS Native"))
                .matchingQueryTokens(ImmutableList.of("pizza", "pasta"))
                .locationMarkets(ImmutableSet.of("chicago"))
                .matchingDinerTypes(ImmutableSet.of("dish"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .build();
    }

    public static FlowApi createFlowWithMultipleRoutingGroups() {
        String flowId = String.valueOf(UUID.randomUUID());
        return FlowApi.builder()
                .flowId(flowId)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(ImmutableSet.of("DELIVERY", "CATERING"))
                .matchingMealtime(ImmutableSet.of("breakfast", "dinner"))
                .matchingQueryTypes(ImmutableSet.of("INTENTFUL"))
                .matchingApplications(ImmutableSet.of("umami", "iOS Native"))
                .matchingQueryTokens(ImmutableList.of("pizza", "pasta"))
                .locationMarkets(ImmutableSet.of("chicago"))
                .matchingDinerTypes(ImmutableSet.of("dish"))
                .routingGroupsCriteria(buildManyRoutingGroupsForOneCriteria(flowId))
                .priority(1)
                .build();
    }

    public static FlowApi createFlowWithMultipleRoutingGroupsAndManyCriterias() {
        String flowId = String.valueOf(UUID.randomUUID());
        return FlowApi.builder()
                .flowId(flowId)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(ImmutableSet.of("DELIVERY", "CATERING"))
                .matchingMealtime(ImmutableSet.of("breakfast", "dinner"))
                .matchingQueryTypes(ImmutableSet.of("INTENTFUL"))
                .matchingApplications(ImmutableSet.of("umami", "iOS Native"))
                .matchingQueryTokens(ImmutableList.of("pizza", "pasta"))
                .locationMarkets(ImmutableSet.of("chicago"))
                .matchingDinerTypes(ImmutableSet.of("dish"))
                .routingGroupsCriteria(buildManyRoutingGroupsCriteria(flowId))
                .priority(1)
                .build();
    }

    public static FlowApi createFlowForCriteria() {
        String flowId = String.valueOf(UUID.randomUUID());
        return FlowApi.builder()
                .flowId(flowId)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(ImmutableSet.of("DELIVERY", "CATERING"))
                .matchingMealtime(ImmutableSet.of("breakfast", "dinner"))
                .matchingQueryTypes(ImmutableSet.of("INTENTFUL"))
                .matchingQueryTokens(ImmutableList.of("pizza", "pasta"))
                .locationMarkets(ImmutableSet.of("chicago"))
                .matchingDinerTypes(ImmutableSet.of("dish"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .build();
    }

    public static FlowDTO createDTOFlow() {
        return FlowDTO.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(HashSet.of("DELIVERY", "CATERING"))
                .matchingMealtime(HashSet.of("breakfast", "dinner"))
                .matchingQueryTypes(HashSet.of("INTENTFUL"))
                .matchingApplications(HashSet.of("umami", "iOS Native"))
                .matchingQueryTokens(io.vavr.collection.List.of("pizza", "pasta"))
                .locationMarkets(HashSet.of("chicago"))
                .matchingDinerTypes(HashSet.of("dish"))
                .routingGroupsCriteria(HashMap.ofAll(buildDefaultDTORoutingGroupCriteria()))
                .priority(1)
                .build();
    }

    public static FlowApi createOverlapFlow() {
        return FlowApi.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("OVERLAP SEARCH")
                .flowName("OVERLAP SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("overlap chicago"))
                .matchingDinerTypes(ImmutableSet.of("existing_diner"))
                .matchingOrderTypes(ImmutableSet.of("PICKUP", "CATERING"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .build();
    }

    public static FlowApi createFlowWithMealTimes() {
        return FlowApi.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("overlap chicago"))
                .matchingDinerTypes(ImmutableSet.of("existing_diner"))
                .matchingOrderTypes(ImmutableSet.of("PICKUP", "CATERING"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .matchingMealtime(ImmutableSet.of("breakfast", "lunch", "diner"))
                .priority(1)
                .build();
    }

    public static FlowApi createFlowWithMatchingApplications() {
        return FlowApi.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("overlap chicago"))
                .matchingDinerTypes(ImmutableSet.of("existing_diner"))
                .matchingOrderTypes(ImmutableSet.of("PICKUP", "CATERING"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .matchingMealtime(ImmutableSet.of("breakfast", "lunch", "diner"))
                .matchingApplications(ImmutableSet.of("Android Native", "umami"))
                .priority(1)
                .build();
    }

    public static FlowApi createFlowWithMatchingClientEntityId() {
        return FlowApi.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .matchingMealtime(ImmutableSet.of("breakfast", "lunch", "diner"))
                .matchingApplications(ImmutableSet.of("Android Native", "umami"))
                .matchingClientEntityIds(ImmutableSet.of("test_client_id"))
                .priority(1)
                .build();
    }

    public static FlowApi createFlowWithMatchingBrand() {
        return FlowApi.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .matchingMealtime(ImmutableSet.of("breakfast", "lunch", "diner"))
                .matchingApplications(ImmutableSet.of("Android Native", "umami"))
                .matchingBrands(ImmutableSet.of("Grubhub"))
                .priority(1)
                .build();
    }

    public static FlowApi createFlowWithMatchingQueryTypes() {
        return FlowApi.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("overlap chicago"))
                .matchingDinerTypes(ImmutableSet.of("existing_diner"))
                .matchingOrderTypes(ImmutableSet.of("PICKUP", "CATERING"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .matchingMealtime(ImmutableSet.of("breakfast", "lunch", "diner"))
                .matchingApplications(ImmutableSet.of("Android Native", "umami"))
                .matchingQueryTypes(ImmutableSet.of("DEFAULT"))
                .priority(1)
                .build();
    }

    public static FlowApi createFlowWithMatchingRequestTypes() {
        return FlowApi.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("overlap chicago"))
                .matchingDinerTypes(ImmutableSet.of("existing_diner"))
                .matchingOrderTypes(ImmutableSet.of("PICKUP", "CATERING"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .matchingMealtime(ImmutableSet.of("breakfast", "lunch", "diner"))
                .matchingApplications(ImmutableSet.of("Android Native", "umami"))
                .matchingQueryTypes(ImmutableSet.of("DEFAULT"))
                .matchingRequestTags(ImmutableSet.of("first", "second", "latest"))
                .priority(1)
                .build();
    }

    public static FlowApi createFlowWithQueryKeywords(Set<String> keywords) {
        return FlowApi.builder()
                .flowId(FLOW_ID_AS_STRING)
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(ImmutableSet.of("chicago"))
                .matchingDinerTypes(ImmutableSet.of("existing_diner"))
                .routingGroupsCriteria(buildDefaultRoutingGroupCriteria())
                .priority(1)
                .matchingQueryKeywords(keywords)
                .build();
    }

    private static List<FlowRoutingGroupDTO> buildRoutingDTOGroups() {
        val firstRoutingGroup = FlowRoutingGroupDTO
                .builder()
                .groupName("group_50")
                .routingPercentage(0.4f)
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .routingGroupCriteria("default")
                .build();

        val secondRoutingGroup = FlowRoutingGroupDTO
                .builder()
                .groupName("default_group")
                .routingPercentage(0.6f)
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .routingGroupCriteria("default")
                .build();

        return ImmutableList.of(firstRoutingGroup, secondRoutingGroup);
    }

    private static List<FlowRoutingGroupApi> buildRoutingGroups() {
        val firstRoutingGroup = FlowRoutingGroupApi
                .builder()
                .groupName("group_50")
                .routingPercentage(0.4f)
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .routingGroupCriteria("default")
                .build();

        val secondRoutingGroup = FlowRoutingGroupApi
                .builder()
                .groupName("default_group")
                .routingGroupCriteria("default")
                .routingPercentage(0.6f)
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        return ImmutableList.of(firstRoutingGroup, secondRoutingGroup);
    }

    private static Map<String, List<FlowRoutingGroupApi>> buildDefaultRoutingGroupCriteria() {
        return Map.of("default", buildRoutingGroups());
    }

    private static Map<String, io.vavr.collection.List<FlowRoutingGroupDTO>> buildDefaultDTORoutingGroupCriteria() {
        return Map.of("default", io.vavr.collection.List.ofAll(buildRoutingDTOGroups()));
    }

    private static Map<String, List<FlowRoutingGroupApi>> buildManyRoutingGroupsForOneCriteria(String flowId) {
        val firstRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .flowId(flowId)
                .routingPercentage(0.15f)
                .groupName("m1")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        val secondRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .flowId(flowId)
                .routingPercentage(0.2f)
                .groupName("m2")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        val thirdRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .flowId(flowId)
                .routingPercentage(0.3f)
                .groupName("m3")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        val fourthRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .flowId(flowId)
                .routingPercentage(0.35f)
                .groupName("m4")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        return Map.of("default", List.of(
                firstRoutingGroupCriteria,
                secondRoutingGroupCriteria,
                thirdRoutingGroupCriteria,
                fourthRoutingGroupCriteria));
    }

    private static Map<String, List<FlowRoutingGroupApi>> buildManyRoutingGroupsCriteria(String flowId) {
        val firstRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .routingGroupCriteria("default")
                .flowId(flowId)
                .routingPercentage(0.5f)
                .groupName("m1")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        val firstRoutingGroupCriteriaBis = FlowRoutingGroupApi
                .builder()
                .routingGroupCriteria("default")
                .flowId(flowId)
                .routingPercentage(0.5f)
                .groupName("m1")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        val secondRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .routingGroupCriteria("umami")
                .flowId(flowId)
                .routingPercentage(1.0f)
                .groupName("m2")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        val thirdRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .routingGroupCriteria("mobile_web")
                .flowId(flowId)
                .routingPercentage(1.0f)
                .groupName("m3")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        val fourthRoutingGroupCriteria = FlowRoutingGroupApi
                .builder()
                .routingGroupCriteria("mobile")
                .flowId(flowId)
                .routingPercentage(1.0f)
                .groupName("m4")
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .build();

        return Map.of(firstRoutingGroupCriteria.getRoutingGroupCriteria(), List.of(firstRoutingGroupCriteria, firstRoutingGroupCriteriaBis),
                secondRoutingGroupCriteria.getRoutingGroupCriteria(), List.of(secondRoutingGroupCriteria),
                thirdRoutingGroupCriteria.getRoutingGroupCriteria(), List.of(thirdRoutingGroupCriteria),
                fourthRoutingGroupCriteria.getRoutingGroupCriteria(), List.of(fourthRoutingGroupCriteria));
    }
}

package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.grubhub.garcon.controlplane.cassandra.dao.GdpActionResultDao;
import com.grubhub.garcon.controlplane.config.GdpDataTransferConfig;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.s3.S3ClientProvider;
import com.grubhub.garcon.controlplane.s3.S3Utils;
import com.grubhub.garcon.controlplane.services.controlplane.gdpactions.GdpActionsSyncer;
import com.grubhub.garcon.controlplane.services.ensembler.FeatureValueService;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionResultDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.service.ensembler.EnsembleService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import com.grubhub.roux.api.datetime.JavaDateTimeHelperBuilder;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.services.s3.S3Client;

import java.util.Map;
import java.util.function.Consumer;

import static com.grubhub.roux.api.datetime.JavaDateTimeHelper.utc;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@ExtendWith({MockitoExtension.class})
public class GdpActionsServiceImplTest {

    private GdpActionsServiceImpl subject;

    @Mock
    private S3ClientProvider s3ClientProvider;
    @Mock
    private GdpDataTransferConfig gdpDataTransferConfig;
    @Mock
    private ModelService modelService;
    @Mock
    private EnsembleService ensembleService;
    private JavaDateTimeHelper dateTimeHelper = JavaDateTimeHelperBuilder
            .buildMockWithSupplier(() -> utc(2021, 2, 17, 13, 21, 56, 90).toInstant());

    @Mock
    private GdpActionResultDao gdpActionResultDao;
    @Mock
    private ControlPlaneMapper controlPlaneMapper;
    @Mock
    private FeatureValueService featureValueService;

    private MeterRegistry meterRegistry = new SimpleMeterRegistry();
    @Mock
    private GdpActionsSyncer gdpActionsSyncer;

    private static final ObjectMapper OBJECT_MAPPER = ObjectMapperHelper.INSTANCE;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        subject = spy(new GdpActionsServiceImpl(
                s3ClientProvider,
                gdpDataTransferConfig,
                modelService,
                ensembleService,
                dateTimeHelper,
                gdpActionResultDao,
                controlPlaneMapper,
                featureValueService,
                meterRegistry,
                gdpActionsSyncer));
    }

        @Test
    public void testProcessPendingActions_PendingActions () throws JsonProcessingException {
        ModelDTO modelDTO1 = ModelDTO.builder().modelName("model1").version("1").modelType("type")
                .versioningModelName("versioning").modelFeatures(List.empty()).build();
        ModelDTO modelDTO2 = ModelDTO.builder().modelName("model2").version("1").modelType("type")
                .versioningModelName("versioning").modelFeatures(List.empty()).build();
        GdpActionDTO action1 = createAction(GdpActionType.CREATE_MODEL, "create_model_x_1",
                "models_x", 1, OBJECT_MAPPER.writeValueAsString(modelDTO1));
        GdpActionDTO action2 = createAction(GdpActionType.CREATE_MODEL, "create_model_x_2",
                "models_x2", 3, OBJECT_MAPPER.writeValueAsString(modelDTO2));


        //s3 mocking
        S3Client s3Client = mock(S3Client.class);
        when(s3ClientProvider.get()).thenReturn(s3Client);
        String s3ActionsPath = "s3://testBucket/testActionsPrefix";
        String s3ActionsDatePath = "s3://testBucket/testActionsPrefix/2023-08-28/";

        when(gdpDataTransferConfig.getBucket()).thenReturn("testBucket");
        when(gdpDataTransferConfig.getActionsPrefix()).thenReturn("testActionsPrefix");


        MockedStatic<S3Utils> s3Mocked = Mockito.mockStatic(S3Utils.class);
        s3Mocked.when(() -> S3Utils.listObjectsInPrefix(s3Client, s3ActionsPath)).thenReturn(List.empty());
        when(S3Utils.getLastDirNameInPrefix(s3Client, s3ActionsPath)).thenReturn("2023-08-28");

        // Mock the behavior of S3Utils.listFilesInPrefix() to return a list of files
        when(S3Utils.listFilesInPrefix(eq(s3Client), eq(s3ActionsDatePath), eq(HashSet.of("json"))))
                .thenReturn(List.of());


        when(gdpActionResultDao.selectAll("2023-08-28")).thenReturn(List.of());
        doNothing().when(subject).doCreateModel(any());

        s3Mocked.when(() -> S3Utils.path(any(), any())).thenReturn(S3Utils.S3Path.from(s3ActionsPath));
        s3Mocked.when(() -> S3Utils.listFilesInPrefix(any(), any(), any()))
                .thenReturn(List.of("file1.json", "file2.json"));
        s3Mocked.when((() -> S3Utils.loadJsonObject(s3Client, "file1.json", GdpActionDTO.class))).thenReturn(action1);
        s3Mocked.when((() -> S3Utils.loadJsonObject(s3Client, "file2.json", GdpActionDTO.class))).thenReturn(action2);

        List<GdpActionResultDTO> results = subject.processPendingActions();

        // Assertions
        assertEquals(2, results.size());
        assertEquals(results.get(0).getAction().getActionName(), "create_model_x_1");
        assertEquals(results.get(1).getAction().getActionName(), "create_model_x_2");
        // Verify relevant method calls on mocked dependencies
        verify(subject, times(2)).loadActionFile(eq(s3Client), anyString(), eq("2023-08-28"));
        verify(subject, times(2)).processAction(any(GdpActionResultDTO.class), any(Consumer.class));
        verify(subject, times(2)).doCreateModel(any(GdpActionDTO.class));
        }

    @Test
    public void groupPendingActions_testGrouping() {
        GdpActionResultDTO result1 = createActionResultFromModel(GdpActionType.CREATE_MODEL, "create_model_x_1", "models_x", 1);
        GdpActionResultDTO result3 = createActionResultFromModel(GdpActionType.CREATE_MODEL, "create_model_x_1", "models_x", 3);
        GdpActionResultDTO result7 = createActionResultFromModel(GdpActionType.CREATE_ENSEMBLE, "create_ensemble_xy_1", "emsemble_xy", 2);
        GdpActionResultDTO result5 = createActionResultFromModel(GdpActionType.CREATE_MODEL, "create_model_y_1", "models_y", 2);
        GdpActionResultDTO result8 = createActionResultFromModel(GdpActionType.INGEST_FEATURES, "ingest_features_x_1", "models_x", 1);
        GdpActionResultDTO result6 = createActionResultFromModel(GdpActionType.CREATE_ENSEMBLE, "create_ensemble_xy_1", "emsemble_xy", 1);
        GdpActionResultDTO result2 = createActionResultFromModel(GdpActionType.CREATE_MODEL, "create_model_x_1", "models_x", 2);
        GdpActionResultDTO result4 = createActionResultFromModel(GdpActionType.CREATE_MODEL, "create_model_y_1", "models_y", 1);

        List<GdpActionResultDTO> resultList = List.of(result1, result2, result3, result4, result5, result6, result7, result8);

        Map<GdpActionType, List<GdpActionResultDTO>> groupedActions = subject.groupPendingActions(resultList);

        assertNotNull(groupedActions);
        assertEquals(groupedActions.size(), 3);

        // Testing sorting by GdpActionType ingestion order.
        val firstEntry = groupedActions.entrySet().iterator().next();
        val secondEntry = groupedActions.entrySet().stream().skip(1).findFirst().orElse(null);
        val thirdEntry = groupedActions.entrySet().stream().skip(2).findFirst().orElse(null);

        assertNotNull(firstEntry);
        assertNotNull(secondEntry);
        assertNotNull(thirdEntry);

        assertEquals(firstEntry.getKey(), GdpActionType.CREATE_MODEL);
        assertEquals(secondEntry.getKey(), GdpActionType.CREATE_ENSEMBLE);
        assertEquals(thirdEntry.getKey(), GdpActionType.INGEST_FEATURES);

        // Testing lists inside each action type
        assertEquals(firstEntry.getValue(), List.of(result1, result2, result3, result4, result5));
        assertEquals(secondEntry.getValue(), List.of(result6, result7));
        assertEquals(thirdEntry.getValue(), List.of(result8));
    }

    private static GdpActionResultDTO createActionResultFromModel(GdpActionType type, String name, String group, int sequence) {
        GdpActionDTO action = GdpActionDTO.builder()
                .actionDate("2023-08-28")
                .actionName(name)
                .actionType(type)
                .actionGroup(group)
                .sequenceNumber(sequence)
                .build();

        return GdpActionResultDTO.builder()
                .action(action)
                .build();
    }

    private static GdpActionDTO createAction(GdpActionType type, String name, String group, int sequence, String content) {
        GdpActionDTO action = GdpActionDTO.builder()
                .actionDate("2023-08-28")
                .actionName(name)
                .actionType(type)
                .actionGroup(group)
                .sequenceNumber(sequence)
                .actionContent(content)
                .build();

        return action;
    }

    @Test
    public void testProcessAction_Success() {

        GdpActionDTO action = GdpActionDTO.builder()
                .actionDate("2023-08-28")
                .actionName("create_model")
                .actionType(GdpActionType.CREATE_MODEL)
                .actionGroup("models_x")
                .sequenceNumber(1)
                .build();

        GdpActionResultDTO exampleResult = createActionResultFromModel(GdpActionType.CREATE_MODEL, "create_model", "models_x", 1);

        Consumer<GdpActionDTO> createFunction = mock(Consumer.class);
        lenient().doNothing().when(createFunction).accept(action);
        GdpActionResultDTO result = subject.processAction(exampleResult, createFunction);

        assertTrue(result.isOk());
        verify(createFunction, times(1)).accept(action);
        verify(gdpActionResultDao, times(1)).insert(controlPlaneMapper.toModel(result));
        assertEquals(result.getAction(), action);

    }
    @Test
    public void testProcessAction_Failure_NoActionName() {

        GdpActionDTO action = GdpActionDTO.builder()
                .actionDate("2023-07-11")
                .actionName("")
                .actionType(GdpActionType.CREATE_MODEL)
                .actionGroup("models_x")
                .sequenceNumber(1)
                .build();

        GdpActionResultDTO exampleResult = createActionResultFromModel(GdpActionType.CREATE_MODEL, "create_model", "models_x", 1);

        Consumer<GdpActionDTO> createFunction = mock(Consumer.class);
        doNothing().when(createFunction).accept(action);
        GdpActionResultDTO result = subject.processAction(exampleResult, createFunction);

        assertFalse(result.isOk());

    }
}

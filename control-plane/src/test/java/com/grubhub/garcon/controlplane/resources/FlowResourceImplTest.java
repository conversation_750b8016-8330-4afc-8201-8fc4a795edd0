package com.grubhub.garcon.controlplane.resources;

import com.grubhub.garcon.controlplane.services.controlplane.impl.FlowServiceImpl;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowSummaryDTO;
import com.grubhub.roux.api.responses.GetResponse;
import com.grubhub.roux.uuid.UuidUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.Mockito.*;

public class FlowResourceImplTest {

    private FlowResourceImpl subject;

    @Mock
    private FlowServiceImpl flowService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        subject = new FlowResourceImpl(flowService);
    }

    @Test
    public void testFindFlowWithCache() {
        boolean retrieveFromCache = true;
        String flowId = UUID.randomUUID().toString();
        FlowApi mockFlow = new FlowApi();

        when(flowService.getFlow(UuidUtil.decode(flowId))).thenReturn(Optional.of(mockFlow));

        GetResponse<FlowApi> result = subject.findFlow(flowId, retrieveFromCache);

        Assertions.assertEquals(GetResponse.success(mockFlow), result);
        verify(flowService, times(1)).getFlow(UuidUtil.decode(flowId));
    }

    @Test
    public void testFindFlowWithoutCache() {
        boolean retrieveFromCache = false;
        String flowId = UUID.randomUUID().toString();
        FlowApi mockFlow = new FlowApi();

        when(flowService.getFlowWithoutCache(UuidUtil.decode(flowId))).thenReturn(Optional.of(mockFlow));

        GetResponse<FlowApi> result = subject.findFlow(flowId, retrieveFromCache);

        Assertions.assertEquals(GetResponse.success(mockFlow), result);
        verify(flowService, times(1)).getFlowWithoutCache(UuidUtil.decode(flowId));
    }

    @Test
    public void testGetAllFlowsDetailed() {
        FlowSummaryDTO mockFlow = new FlowSummaryDTO();
        when(flowService.getAllFlowsDetailed(null)).thenReturn(List.of(mockFlow));

        GetResponse<List<FlowSummaryDTO>> result = subject.getAllDetailed(null);

        Assertions.assertEquals(GetResponse.success(List.of(mockFlow)), result);
        verify(flowService, times(1)).getAllFlowsDetailed(null);
    }

    @Test
    public void testGetAllFlowsDetailedForSpeficifFlowSet() {
        FlowSummaryDTO mockFlow = new FlowSummaryDTO();
        when(flowService.getAllFlowsDetailed("test")).thenReturn(List.of(mockFlow));

        GetResponse<List<FlowSummaryDTO>> result = subject.getAllDetailed("test");

        Assertions.assertEquals(GetResponse.success(List.of(mockFlow)), result);
        verify(flowService, times(1)).getAllFlowsDetailed("test");
    }

}

package com.grubhub.garcon.controlplane.services.controlplane.geo;

import io.vavr.collection.HashSet;
import lombok.val;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.grubhub.garcon.controlplane.services.controlplane.geo.GeoUtils.geoPolygonToGeoHashes;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test GeoUtils
 */
public class GeoUtilsTest {

    @Test
    public void testPolygonToGeoHashesConversion() {

        List<GeoPolygon> polygons = Stream.of(
                "{\"type\":\"Polygon\",\"coordinates\":[[[-73.93799,40.87848],[-74.040985,40.700684]," +
                        "[-73.97026,40.69756],[-73.9246,40.800816],[-73.93799,40.87848]]]}",
                "{\"type\":\"Polygon\",\"coordinates\":[[[-87.798615,41.95847],[-87.757416,41.76312]," +
                        "[-87.561035,41.77746],[-87.64618,41.964596],[-87.798615,41.95847]]]}",
                "{\"type\":\"Polygon\",\"coordinates\":[[[-122.57446,37.796764],[-122.54562,37.60553]," +
                        "[-122.283325,37.60988],[-122.39456,37.839073],[-122.57446,37.796764]]]}"
        ).map(GeoUtils::createPolygon).collect(Collectors.toList());

        List<Set<String>> expectedGeoHashes = Stream.of(
                toSet("dr72m, dr72j, dr5rs, dr5re, dr5ru"),
                toSet("dp3wd, dp3we, dp3wm, dp3wn, dp3te, dp3wh, dp3tg, dp3wj, dp3wk, dp3wt, dp3w5, " +
                        "dp3ts, dp3w6, dp3w7, dp3ws, dp3ty, dp3tu, dp3tv"),
                toSet("9q8yp, 9q8yn, 9q8ym, 9q8zn, 9q8yk, 9q8yj, 9q8zj, 9q8yx, 9q8y7, 9q8yw, 9q8yv, " +
                        "9q8y5, 9q8yu, 9q8yt, 9q8ys, 9q8yr, 9q8yq, 9q9n2, 9q9n0, 9q8yz, 9q8yy, 9q8yh, 9q8yg, 9q8ye")
        ).collect(Collectors.toList());

        for (int i = 0; i < expectedGeoHashes.size(); i++) {
            GeoPolygon poly = polygons.get(i);
            Set<String> expGeoHashes = expectedGeoHashes.get(i);
            Set<String> generatedGeoHashes = geoPolygonToGeoHashes(poly, 5, true);
            assertEquals(expGeoHashes, generatedGeoHashes);
        }
    }

    @Disabled
    @Test
    public void testUpperManhattanPolygon() {
        /*
        GDP query to get polygon:
            select modified_cbsa_name area, region_name, region_bounds polygon
            from finance_presto_etl.dim_ghd_region
            where region_name = 'NY - Manhattan (North)';
         */

        // Concave polygon, it doesn't work
        val polyStr = "{\"type\": \"Polygon\",\n" +
                " \"coordinates\": [[[-73.97214889526367, 40.80645536364472],\n" +
                "  [-73.97300720214844, 40.80353178030703],\n" +
                "  [-73.9654524207939, 40.8003673443581],\n" +
                "  [-73.96039009094238, 40.79833398063371],\n" +
                "  [-73.96043755811752, 40.7982667922723],\n" +
                "  [-73.96013259887695, 40.79813905542078],\n" +
                "  [-73.96009888469973, 40.7981803763596],\n" +
                "  [-73.9600988846997, 40.7981803763596],\n" +
                "  [-73.95833015441895, 40.80034817642438],\n" +
                "  [-73.94983291625977, 40.79690451460294],\n" +
                "  [-73.94948959350586, 40.79735934810169],\n" +
                "  [-73.93232345581055, 40.7898217052053],\n" +
                "  [-73.92751693725586, 40.79735934810169],\n" +
                "  [-73.92889022827148, 40.80353178030703],\n" +
                "  [-73.93301010131836, 40.80814448642528],\n" +
                "  [-73.93318176269531, 40.82061667742242],\n" +
                "  [-73.93404006958008, 40.83211240217454],\n" +
                "  [-73.92631530761719, 40.84880038610132],\n" +
                "  [-73.90846252441406, 40.87106612992601],\n" +
                "  [-73.9310359954834, 40.87645292570517],\n" +
                "  [-73.95137786865234, 40.84133351968966],\n" +
                "  [-73.97214889526367, 40.80645536364472]]]}";

        val geoPoly = GeoUtils.createPolygon(polyStr);
        val geohashes = HashSet.ofAll(GeoUtils.geoPolygonToGeoHashes(geoPoly, 6, true));
        val expectedGeohashes = HashSet.of("dr72m", "dr72k", "dr72h", "dr72q", "dr72w");
        assertFalse(geohashes.isEmpty());
        assertTrue(geohashes.diff(expectedGeohashes).isEmpty());
    }

    private Set<String> toSet(String s) {
        return Arrays.stream(s.split(",")).map(String::trim).collect(Collectors.toSet());
    }


}

package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableSet;
import com.grubhub.garcon.controlplane.cassandra.models.DinerType;
import com.grubhub.garcon.controlplane.eventhub.event.ResolveFlowEvent;
import com.grubhub.garcon.controlplane.mapper.VavrMapper;
import com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory;
import com.grubhub.garcon.controlplane.services.ensembler.impl.EnsembleFactory;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FeatureApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowResponseApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.ResolveFlowRequest;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.RoutingGroupResponseDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import com.grubhub.garcon.shared.CassandraTestCase;
import com.grubhub.roux.json.RouxObjectMapper;
import io.vavr.Tuple;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.FLOW_ID_AS_STRING;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.ROUTING_GROUPS_ENSEMBLE_NAME;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createExistingDinerFlow;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createExistingDinerFlowWithDinerTypeOrdersThreshold;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowForVariationInput;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowWithMatchingApplications;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowWithMatchingBrand;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowWithMatchingClientEntityId;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowWithMatchingQueryTypes;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowWithMatchingRequestTypes;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowWithMealTimes;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createNewDinerFlow;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createNewDinerFlowWithDinerTypeOrdersThreshold;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createOverlapFlow;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createUnauthenticatedFlow;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.MarketFactory.buildChicagoMarket;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.MarketFactory.buildOverlapChicagoMarket;
import static com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory.createModel;
import static com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory.createModelWithName;
import static com.grubhub.garcon.controlplaneapi.models.BucketingMode.GEOHASH;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.within;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
@CassandraTestCase
public class FlowServiceImplResolveFlowTest extends FlowServiceImplTest {

    static final double CHICAGO_LAT = 41.94110578381595;
    static final double CHICAGO_LNG = -87.66265869140625;

    @Test
    public void resolveFlow_should_return_successfully_for_unauthenticated_diner_type_when_diner_id_is_empty() {
        when(sessionAttributeProvider.resolveTrackingId()).thenReturn(UUID.randomUUID().toString());
        mockConfig();
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val resolveFlowRequest = ResolveFlowRequest
                .builder()
                .flowSet("SEARCH")
                .dinerId("")
                .applicationId(UUID.randomUUID().toString())
                .lat(41.888476931243254)
                .lng(-87.69973754882812)
                .orderType("DELIVERY")
                .whenFor(dateTimeHelper.atZone(ZoneId.of("UTC")).toString())
                .build();

        val flow = createUnauthenticatedFlow();
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        flowService.createOrUpdateFlow(flow);
        val flowResponse = flowService.resolveFlow(resolveFlowRequest);
        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getGeohash()).isEqualTo("dp3wk");
        });
    }

    @Test
    public void resolveFlow_should_return_successfully_for_new_diner_type() {
        mockConfig();
        mockTotalOrders();
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 3, "DELIVERY");
        val flow = createNewDinerFlow();
        flowService.createOrUpdateFlow(flow);
        val flowResponse = flowService.resolveFlow(resolveFlowRequest);
        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getGeohash()).isEqualTo("dp3wt");
        });
    }


    @Test
    public void resolveFlow_should_return_successfully_for_existing_diner_type() {
        mockConfig();
        mockTotalOrders();
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "DELIVERY");
        val flow = createExistingDinerFlow();
        flowService.createOrUpdateFlow(flow);

        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getGeohash()).isEqualTo("dp3wt");
        });
    }

    @Test
    public void resolveFlow_should_return_successfully_for_new_diner_type_with_total_orders_threshold() {
        mockConfig();
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 3, "DELIVERY");
        val flow = createNewDinerFlowWithDinerTypeOrdersThreshold(10);
        flowService.createOrUpdateFlow(flow);
        val flowResponse = flowService.resolveFlow(resolveFlowRequest);
        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getGeohash()).isEqualTo("dp3wt");
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getDinerType()).isEqualTo(DinerType.NEW_DINER.getType());
        });
    }

    @Test
    public void resolveFlow_should_return_successfully_for_existing_diner_type_with_total_orders_threshold() {
        mockConfig();
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "DELIVERY");
        val flow = createExistingDinerFlowWithDinerTypeOrdersThreshold(49);
        flowService.createOrUpdateFlow(flow);
        val flowResponse = flowService.resolveFlow(resolveFlowRequest);
        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getGeohash()).isEqualTo("dp3wt");
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getDinerType()).isEqualTo(DinerType.EXISTING_DINER.getType());
        });
    }

    @Test
    public void resolveFlow_should_fail_for_existing_diner_type_when_total_orders_threshold_is_too_high() {
        mockConfig();
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "DELIVERY");
        val flow = createExistingDinerFlowWithDinerTypeOrdersThreshold(1000);
        flowService.createOrUpdateFlow(flow);

        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isFalse();
    }

    @Test
    public void resolveFlow_should_return_first_routing_group_when_only_one_exists() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "DELIVERY");
        val flow = createExistingDinerFlow();
        flowService.createOrUpdateFlow(flow);
        flowService.createOrUpdateFlow(createOverlapFlow());

        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getGeohash()).isEqualTo("dp3wt");
        });
    }

    @Test
    public void resolveFlow_should_return_random_routing_group() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "DELIVERY");
        val flow = createExistingDinerFlow();
        flowService.createOrUpdateFlow(flow);
        flowService.createOrUpdateFlow(createOverlapFlow());

        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getGeohash()).isEqualTo("dp3wt");
            assertThat(resolvedFlow.getRoutingGroup().getGroupName()).isEqualTo("default_group");
            assertThat(resolvedFlow.getRoutingGroup().getRoutingPercentage()).isEqualTo(0.6f);
            assertThat(resolvedFlow.getRoutingGroup().getEnsembleName()).isEqualTo(ROUTING_GROUPS_ENSEMBLE_NAME);
            assertThat(resolvedFlow.getRoutingGroup().getEnsembleWeight()).isEqualTo("default");
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getDinerType()).isEqualTo("EXISTING_DINER");
        });
    }

    @Test
    public void resolveFlow_should_filter_out_disabled_flows() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("OVERLAP SEARCH", 50, "PICKUP");
        val flow = createExistingDinerFlow();
        flow.setEnabled(false);
        flowService.createOrUpdateFlow(flow);
        val overlapFlow = createOverlapFlow();
        flowService.createOrUpdateFlow(overlapFlow);

        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(overlapFlow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(overlapFlow.getFlowSet());
        });
    }

    @Test
    public void resolveFlow_with_variation_id() {
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val resolveFlowRequest = ResolveFlowRequest
                .builder()
                .flowSet("test flow set")
                .applicationId("82")
                .variationId("SEARCH|" + FLOW_ID_AS_STRING + "|group_50")
                .build();
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        val flow = createFlowForVariationInput();
        flowService.createOrUpdateFlow(flow);
        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getVariationId()).isEqualTo("SEARCH|" + FLOW_ID_AS_STRING + "|group_50");
            assertThat(resolvedFlow.getMatchingStrategy().getType()).isEqualTo("DEBUG");
            assertThat(resolvedFlow.getRoutingGroup().getRoutingRand()).isEqualTo(1.0d);
            assertThat(resolvedFlow.getRoutingGroup().getGroupName()).isEqualTo("group_50");
        });
    }

    @Test
    public void resolveFlow_should_throw_validation_exception() {
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val resolveFlowRequest = ResolveFlowRequest
                .builder()
                .variationId("SEARCH|" + FLOW_ID_AS_STRING + "|group_50")
                .build();
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        val flow = createFlowForVariationInput();
        flowService.createOrUpdateFlow(flow);
        assertThatThrownBy(() -> flowService.resolveFlow(resolveFlowRequest))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("Constraint Validations:");
    }

    @Test
    public void resolveFlow_should_match_request_order_type() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("OVERLAP SEARCH", 50, "PICKUP");
        val flow = createExistingDinerFlow();
        flowService.createOrUpdateFlow(flow);
        val overlapFlow = createOverlapFlow();
        flowService.createOrUpdateFlow(overlapFlow);

        val flowResponseAsOptional = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponseAsOptional.isPresent()).isTrue();
        flowResponseAsOptional.ifPresent(flowResponse -> {
            assertThat(flowResponse.getMatchingStrategy().getMatch().getDinerType()).isEqualTo("EXISTING_DINER");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getOrderType()).isEqualTo("PICKUP");
        });
    }


    @Test
    public void resolveFlow_should_not_fail_for_unknown_order_type() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("OVERLAP SEARCH", 50, "WEIRD_ORDER_TYPE");
        val flow = createExistingDinerFlow();
        flowService.createOrUpdateFlow(flow);
        val overlapFlow = createOverlapFlow();
        flowService.createOrUpdateFlow(overlapFlow);

        val flowResponseAsOptional = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponseAsOptional.isPresent()).isFalse();
    }

    @Test
    public void resolveFlow_should_match_meal_time() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildChicagoMarket());
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "PICKUP");
        val flow = createFlowWithMealTimes();
        flowService.createOrUpdateFlow(flow);

        val flowResponseAsOptional = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponseAsOptional.isPresent()).isTrue();
        flowResponseAsOptional.ifPresent(flowResponse -> {
            assertThat(flowResponse.getMatchingStrategy().getMatch().getDinerType()).isEqualTo("EXISTING_DINER");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getOrderType()).isEqualTo("PICKUP");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getMealtime()).isEqualTo("lunch");
        });
    }

    @Test
    public void resolveFlow_should_match_application() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "PICKUP");
        val flow = createFlowWithMatchingApplications();
        flowService.createOrUpdateFlow(flow);

        val flowResponseAsOptional = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponseAsOptional.isPresent()).isTrue();
        flowResponseAsOptional.ifPresent(flowResponse -> {
            assertThat(flowResponse.getMatchingStrategy().getMatch().getDinerType()).isEqualTo("EXISTING_DINER");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getOrderType()).isEqualTo("PICKUP");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getMealtime()).isEqualTo("lunch");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getApplicationType()).isEqualTo("umami");

        });
    }

    @Test
    public void resolveFlow_should_return_successfully_for_client_entity_id() {
        mockConfig();
        mockTotalOrders();

        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 3, "DELIVERY");
        resolveFlowRequest = resolveFlowRequest.toBuilder()
                .clientEntityId("test_client_id")
                .build();
        val flow = createFlowWithMatchingClientEntityId();
        flowService.createOrUpdateFlow(flow);

        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getGeohash()).isEqualTo("GLOBAL");
        });
    }

    @Test
    public void resolveFlow_should_return_successfully_for_brand_id() {
        mockConfig();
        mockTotalOrders();

        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "DELIVERY");
        val flow = createFlowWithMatchingBrand();
        flowService.createOrUpdateFlow(flow);

        when(sessionAttributeProvider.resolveBrand()).thenReturn("grubhub");

        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getBrand()).isEqualTo("Grubhub");
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getGeohash()).isEqualTo("GLOBAL");
        });
    }

    @Test
    public void resolveFlow_should_match_query_type() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "PICKUP");
        val flow = createFlowWithMatchingQueryTypes();
        flowService.createOrUpdateFlow(flow);

        val flowResponseAsOptional = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponseAsOptional.isPresent()).isTrue();
        flowResponseAsOptional.ifPresent(flowResponse -> {
            assertThat(flowResponse.getMatchingStrategy().getMatch().getDinerType()).isEqualTo("EXISTING_DINER");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getOrderType()).isEqualTo("PICKUP");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getMealtime()).isEqualTo("lunch");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getApplicationType()).isEqualTo("umami");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getQueryType()).isEqualTo("DEFAULT");

        });
    }


    @Test
    public void resolveFlow_should_match_requested_tags() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildChicagoMarket());
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        val resolveFlowRequest = ResolveFlowRequest
                .builder()
                .flowSet("SEARCH")
                .dinerId(UUID.randomUUID().toString())
                .applicationId("82")
                .lat(CHICAGO_LAT)
                .lng(CHICAGO_LNG)
                .totalOrders(50)
                .whenFor(dateTimeHelper.atZone(ZoneId.of("UTC")).toString())
                .orderType("PICKUP")
                .requestTags(ImmutableSet.of("latest"))
                .build();

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        val flow = createFlowWithMatchingRequestTypes();
        flowService.createOrUpdateFlow(flow);

        val flowResponseAsOptional = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponseAsOptional.isPresent()).isTrue();
        flowResponseAsOptional.ifPresent(flowResponse -> {
            assertThat(flowResponse.getMatchingStrategy().getMatch().getDinerType()).isEqualTo("EXISTING_DINER");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getOrderType()).isEqualTo("PICKUP");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getMealtime()).isEqualTo("lunch");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getApplicationType()).isEqualTo("umami");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getQueryType()).isEqualTo("DEFAULT");
            assertThat(flowResponse.getMatchingStrategy().getMatch().getRequestTags()).contains("latest");

        });
    }

    @Test
    public void resolveFlow_should_return_non_empty_features_list() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "DELIVERY");
        val flow = createExistingDinerFlow();
        flowService.createOrUpdateFlow(flow);
        flowService.createOrUpdateFlow(createOverlapFlow());

        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getGeohash()).isEqualTo("dp3wt");
            assertThat(resolvedFlow.getRoutingGroup().getGroupName()).isEqualTo("default_group");
            assertThat(resolvedFlow.getRoutingGroup().getRoutingPercentage()).isEqualTo(0.6f);
            assertThat(resolvedFlow.getRoutingGroup().getEnsembleName()).isEqualTo(ROUTING_GROUPS_ENSEMBLE_NAME);
            assertThat(resolvedFlow.getRoutingGroup().getEnsembleWeight()).isEqualTo("default");
            assertThat(resolvedFlow.getMatchingStrategy().getMatch().getDinerType()).isEqualTo("EXISTING_DINER");
            assertThat(resolvedFlow.getFeatures()).isNotEmpty();
            assertThat(resolvedFlow.getFeatures().stream().map(FeatureApi::getFeatureName).collect(Collectors.toList()))
                    .containsExactlyInAnyOrder("runtime");
        });
    }

    @Test
    public void resolveFlow_shouldLogEvent() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "PICKUP");
        val flow = createFlowWithMatchingQueryTypes();
        flowService.createOrUpdateFlow(flow);

        val flowResponseAsOptional = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponseAsOptional.isPresent()).isTrue();

        verify(controlPlaneLogger, times(1)).logResolveFlowRequest(any(ResolveFlowEvent.class));
    }

    @Test
    public void resolveFlow_globalMarket() {
        mockTotalOrders();

        val resolveFlowRequest = ResolveFlowRequest
                .builder()
                .flowSet("Search")
                .dinerId("16500000xxxxx0")
                .applicationId("3")
                .lat(CHICAGO_LAT)
                .lng(CHICAGO_LNG)
                .totalOrders(50)
                .build();

        val flow = FlowApi.builder()
                .flowId("********-0000-0000-0000-************")
                .flowSet("Search")
                .flowName("Search-All")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .locationMarkets(Collections.emptySet())
                .routingGroupsCriteria(getDefaultRoutingGroups())
                .build();
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsembleWithName());
        flowService.createOrUpdateFlow(flow);

        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getRoutingGroup().getGroupName()).isEqualTo(flow.getRoutingGroupsCriteria().get("default").get(0).getGroupName());
            assertThat(resolvedFlow.getRoutingGroup().getVariation()).isEqualTo(flow.getRoutingGroupsCriteria().get("default").get(0).getVariation());
            assertThat(resolvedFlow.getRoutingGroup().getEnsembleName()).isEqualTo(flow.getRoutingGroupsCriteria().get("default").get(0).getEnsembleName());
            assertThat(resolvedFlow.getRoutingGroup().getEnsembleWeight()).isEqualTo(flow.getRoutingGroupsCriteria().get("default").get(0).getEnsembleWeight());
        });
    }

    @Test
    public void resolveFlowShouldContainStrategyAndFunction() {
        when(flowConfig.getPrecision()).thenReturn(5, 5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true, true);
        mockTotalOrders();
        marketService.createOrUpdateMarket(buildOverlapChicagoMarket());
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 50, "DELIVERY");
        val flow = createExistingDinerFlow();
        FlowRoutingGroupApi flowRoutingGroupApi = FlowRoutingGroupApi
                .builder()
                .groupName("group_50")
                .routingPercentage(0.4f)
                .ensembleName(ROUTING_GROUPS_ENSEMBLE_NAME)
                .ensembleWeight("default")
                .ensembleFunction("A + B + C")
                .ensembleStrategy("function")
                .routingGroupCriteria("default")
                .build();

        Map<String, java.util.List<FlowRoutingGroupApi>> flowRoutingGroupsCriteria = Map.of("default", java.util.List.of(flowRoutingGroupApi));

        flow.setRoutingGroupsCriteria(flowRoutingGroupsCriteria);
        flowService.createOrUpdateFlow(flow);

        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        flowResponse.ifPresent(resolvedFlow -> {
            assertThat(resolvedFlow.getFlowId()).isEqualTo(flow.getFlowId());
            assertThat(resolvedFlow.getFlowSet()).isEqualTo(flow.getFlowSet());
            assertThat(resolvedFlow.getRoutingGroup().getEnsembleStrategy()).isEqualTo("function");
            assertThat(resolvedFlow.getRoutingGroup().getEnsembleFunction()).isEqualTo("A + B + C");
        });

    }


    @NotNull
    private static Map<String, java.util.List<FlowRoutingGroupApi>> getDefaultRoutingGroups() {
        java.util.List<FlowRoutingGroupApi> routingGroupApis = Collections.singletonList(FlowRoutingGroupApi
                .builder()
                .groupName("default")
                .routingPercentage(1)
                .variation("exploreExploit")
                .ensembleName("test_function_simple_model")
                .ensembleWeight("default")
                .routingGroupCriteria("default")
                .build());

        Map<String, java.util.List<FlowRoutingGroupApi>> defaultGroup = new java.util.HashMap<>();
        defaultGroup.put("default", routingGroupApis);
        return defaultGroup;
    }

    @Test
    @SneakyThrows
    public void resolveFlow_shouldReturnRouxDeserializableResponse() {
        mockConfig();
        mockTotalOrders();
        ResolveFlowRequest resolveFlowRequest = createModelAndFlowAndGetResolveFlowRequest("SEARCH", 3, "DELIVERY");
        val flow = createNewDinerFlow();
        flowService.createOrUpdateFlow(flow);
        val flowResponse = flowService.resolveFlow(resolveFlowRequest);

        String serializedResponse = ObjectMapperHelper.INSTANCE.writeValueAsString(flowResponse);

        ObjectMapper rouxObjectMapper = RouxObjectMapper.newStandard();
        val returnType = rouxObjectMapper.constructType(flowService.getClass().getDeclaredMethod("resolveFlow", ResolveFlowRequest.class)
                .getGenericReturnType());
        @SuppressWarnings("unchecked")
        val reserializedFlowResponse = (Optional<FlowResponseApi>) rouxObjectMapper.readValue(serializedResponse, returnType);

        assertThat(flowResponse).isPresent();
        assertThat(reserializedFlowResponse).isPresent();
        assertThat(reserializedFlowResponse.get().getFlowId()).isEqualTo(flowResponse.get().getFlowId());
        assertThat(reserializedFlowResponse.get().getFlowSet()).isEqualTo(flowResponse.get().getFlowSet());
        assertThat(reserializedFlowResponse.get().getRoutingGroup().getGroupName()).isEqualTo(flowResponse.get().getRoutingGroup().getGroupName());
        assertThat(reserializedFlowResponse.get().getRoutingGroup().getVariation()).isEqualTo(flowResponse.get().getRoutingGroup().getVariation());
        assertThat(reserializedFlowResponse.get().getRoutingGroup().getEnsembleName()).isEqualTo(flowResponse.get().getRoutingGroup().getEnsembleName());
        assertThat(reserializedFlowResponse.get().getRoutingGroup().getEnsembleWeight()).isEqualTo(flowResponse.get().getRoutingGroup().getEnsembleWeight());
    }

    @Test
    void testRoutingGroupsWithGeohash() {
        when(flowConfig.getRoutingGroupsGeohashPrecision()).thenReturn(9);
        when(flowConfig.getRoutingGroupsSeed()).thenReturn(123);
        val ensembleName = "search_ensemble_m1_m2_m3_STATIC";
        val ensembleWeight = "default";
        val variation = "exploreExploit";
        val firstRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("group1")
                .variation(variation)
                .routingPercentage(0.2f)
                .ensembleName(ensembleName)
                .ensembleWeight(ensembleWeight)
                .build();
        val secondRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("group2")
                .variation(variation)
                .routingPercentage(0.5f)
                .ensembleName(ensembleName)
                .ensembleWeight(ensembleWeight)
                .build();
        val thirdRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("group3")
                .variation(variation)
                .routingPercentage(0.3f)
                .ensembleName(ensembleName)
                .ensembleWeight(ensembleWeight)
                .build();
        val routingGroups = List.of(firstRoutingGroup, secondRoutingGroup, thirdRoutingGroup);
        val flow = FlowApi.builder()
                .flowSet("TestFlowSet")
                .flowId("********-9998-0000-0000-************")
                .flowName("Test geohash flow")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(1)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroups))
                .build();
        modelService.createOrUpdateModel(createModel());
        ensembleService.createOrUpdateEnsemble(EnsembleFactory.createEnsembleWithName(ensembleName));
        flowService.createOrUpdateFlow(flow);

        //val invocations = generateResolveFlowForManyGeohashes(39.0d, -74.0d, 5, 0.1d);
        val invocations = generateResolveFlowForManyGeohashes(39.0d, -74.0d, 2, 0.1d);
        val totalInvocations = invocations.size();
        val frequency = invokeAllResolveFlowManyGeohashes(invocations);
        val expectedFrequency = HashMap.ofEntries(routingGroups.map(g -> Tuple.of(g.getGroupName(), g.getRoutingPercentage() * totalInvocations)));
        log.info("Total invocations = {}, expected frequency = {}, obtained frequency = {}", invocations, expectedFrequency, frequency);
        frequency.forEach(i -> assertThat(i._2.floatValue()).isEqualTo(expectedFrequency.get(i._1).get(), within(10f)));
    }

    @Test
    void resolveFlow_GeoHashBucketingOrder() {
        when(flowConfig.getRoutingGroupsGeohashPrecision()).thenReturn(9);
        when(flowConfig.getRoutingGroupsSeed()).thenReturn(6345789);

        final String flowSet = "Search";
        final String ensembleName = "search_ensemble_m1_m2_m3_STATIC";

        FlowRoutingGroupApi m1RoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("M1")
                .variation("exploreExploit")
                .routingPercentage(0.67f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        FlowRoutingGroupApi ensembleRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("Ensemble")
                .variation("exploreExploit")
                .routingPercentage(0.33f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        final List<FlowRoutingGroupApi> routingGroups = List.of(m1RoutingGroup, ensembleRoutingGroup);

        final FlowApi flow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test geohash bucketing order")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(1)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroups))
                .build();

        modelService.createOrUpdateModel(createModel());
        ensembleService.createOrUpdateEnsemble(EnsembleFactory.createEnsembleWithName(ensembleName));
        flowService.createOrUpdateFlow(flow);

        assertResolveFlowRequestFromLatAndLong(flowSet, 0, 0, "M1");
        assertResolveFlowRequestFromLatAndLong(flowSet, 0.00, 0.00, "M1");
        assertResolveFlowRequestFromLatAndLong(flowSet, 42.27, -83.73, "Ensemble");
        assertResolveFlowRequestFromLatAndLong(flowSet, 32.88, -117.21, "M1");
    }

    @Test
    void resolveFlow_UserEmailDomainMatching() {

        final String flowSet = "Recommender";
        final String ensembleName = "topics_ensemble_indentity_ranker";

        FlowRoutingGroupApi convenienceRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("convenience")
                .variation("convenience")
                .routingPercentage(1.0f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        FlowRoutingGroupApi metsovoRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("metsovo")
                .variation("metsovo")
                .routingPercentage(1.00f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        final List<FlowRoutingGroupApi> routingGroupsEmployee = List.of(convenienceRoutingGroup);
        final List<FlowRoutingGroupApi> routingGroupsNoEmployee = List.of(metsovoRoutingGroup);
        Set<String> matchingDomainSet = new HashSet<>();
        matchingDomainSet.add("grubhub.com");
        matchingDomainSet.add("justeattakeaway.com");


        final FlowApi employeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test employee domain")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(1)
                .matchingUserDomains(matchingDomainSet)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsEmployee))
                .build();

        final FlowApi noEmployeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test outsider domain")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(500)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsNoEmployee))
                .build();

        modelService.createOrUpdateModel(createModel());
        ensembleService.createOrUpdateEnsemble(EnsembleFactory.createEnsembleWithName(ensembleName));
        flowService.createOrUpdateFlow(employeeFlow);
        flowService.createOrUpdateFlow(noEmployeeFlow);
        when(sessionAttributeProvider.resolveUserEmail()).thenReturn("<EMAIL>");
        assertResolveFlowRequestFromLatAndLong(flowSet, 32.88, -117.21, "convenience");
        when(sessionAttributeProvider.resolveUserEmail()).thenReturn("<EMAIL>");
        assertResolveFlowRequestFromLatAndLong(flowSet, 32.88, -117.21, "convenience");

    }

    @Test
    void resolveFlow_UserEmailDomainNoMatch() {

        final String flowSet = "Recommender";
        final String ensembleName = "topics_ensemble_indentity_ranker";

        FlowRoutingGroupApi convenienceRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("convenience")
                .variation("convenience")
                .routingPercentage(1.0f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        FlowRoutingGroupApi metsovoRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("metsovo")
                .variation("metsovo")
                .routingPercentage(1.00f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        final List<FlowRoutingGroupApi> routingGroupsEmployee = List.of(convenienceRoutingGroup);
        final List<FlowRoutingGroupApi> routingGroupsNoEmployee = List.of(metsovoRoutingGroup);
        Set<String> matchingDomainSet = new HashSet<>();
        matchingDomainSet.add("grubhub.com");
        matchingDomainSet.add("justeattakeaway.com");

        final FlowApi employeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test employee domain")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(1)
                .matchingUserDomains(matchingDomainSet)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsEmployee))
                .build();

        final FlowApi noEmployeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test outsider domain")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(500)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsNoEmployee))
                .build();

        modelService.createOrUpdateModel(createModel());
        ensembleService.createOrUpdateEnsemble(EnsembleFactory.createEnsembleWithName(ensembleName));
        flowService.createOrUpdateFlow(employeeFlow);
        flowService.createOrUpdateFlow(noEmployeeFlow);
        when(sessionAttributeProvider.resolveUserEmail()).thenReturn("<EMAIL>");
        assertResolveFlowRequestFromLatAndLong(flowSet, 32.88, -117.21, "metsovo");
    }

    @NotNull
    private java.util.Map<String, java.util.List<FlowRoutingGroupApi>> getDefaultRoutingGroupCriteria(List<FlowRoutingGroupApi> routingGroupsNoEmployee) {
        return new java.util.HashMap<>() {{
            put("default", routingGroupsNoEmployee.asJava());
        }};
    }

    @Test
    void resolveFlow_UserEmailMatching() {

        final String flowSet = "Recommender";
        final String ensembleName = "topics_ensemble_indentity_ranker";

        FlowRoutingGroupApi convenienceRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("convenience")
                .variation("convenience")
                .routingPercentage(1.0f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        FlowRoutingGroupApi metsovoRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("metsovo")
                .variation("metsovo")
                .routingPercentage(1.00f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        final List<FlowRoutingGroupApi> routingGroupsEmployee = List.of(convenienceRoutingGroup);
        final List<FlowRoutingGroupApi> routingGroupsNoEmployee = List.of(metsovoRoutingGroup);
        Set<String> matchingEmailSet = new HashSet<>();
        matchingEmailSet.add("<EMAIL>");
        matchingEmailSet.add("<EMAIL>");


        final FlowApi employeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test email match")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(1)
                .matchingUserEmails(matchingEmailSet)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsEmployee))
                .build();

        final FlowApi noEmployeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test no email match")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(500)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsNoEmployee))
                .build();

        modelService.createOrUpdateModel(createModel());
        ensembleService.createOrUpdateEnsemble(EnsembleFactory.createEnsembleWithName(ensembleName));
        flowService.createOrUpdateFlow(employeeFlow);
        flowService.createOrUpdateFlow(noEmployeeFlow);
        when(sessionAttributeProvider.resolveUserEmail()).thenReturn("<EMAIL>");
        assertResolveFlowRequestFromLatAndLong(flowSet, 32.88, -117.21, "convenience");
        when(sessionAttributeProvider.resolveUserEmail()).thenReturn("<EMAIL>");
        assertResolveFlowRequestFromLatAndLong(flowSet, 32.88, -117.21, "convenience");
        when(sessionAttributeProvider.resolveUserEmail()).thenReturn("<EMAIL>");
        assertResolveFlowRequestFromLatAndLong(flowSet, 32.88, -117.21, "metsovo");

    }

    @Test
    void resolveFlow_ApplicationVersionMatch() {

        final String flowSet = "Recommender";
        final String ensembleName = "topics_ensemble_indentity_ranker";

        FlowRoutingGroupApi convenienceRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("convenience")
                .variation("convenience")
                .routingPercentage(1.0f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        FlowRoutingGroupApi metsovoRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("metsovo")
                .variation("metsovo")
                .routingPercentage(1.00f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        final List<FlowRoutingGroupApi> routingGroupsVersionMatch = List.of(convenienceRoutingGroup);
        final List<FlowRoutingGroupApi> routingGroupsVersionNoMatch = List.of(metsovoRoutingGroup);
        Set<String> matchingVersionSet = new HashSet<>();
        matchingVersionSet.add("2021.8.6");
        matchingVersionSet.add("2022.2.1");
        matchingVersionSet.add("2022.3.0");
        Set<String> matchingAppSet = new HashSet<>();
        matchingAppSet.add("iOS Native");


        final FlowApi employeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test application version")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(1)
                .matchingApplications(matchingAppSet)
                .matchingApplicationVersions(matchingVersionSet)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsVersionMatch))
                .build();

        final FlowApi noEmployeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test no application version")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(500)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsVersionNoMatch))
                .build();

        modelService.createOrUpdateModel(createModel());
        ensembleService.createOrUpdateEnsemble(EnsembleFactory.createEnsembleWithName(ensembleName));
        flowService.createOrUpdateFlow(employeeFlow);
        flowService.createOrUpdateFlow(noEmployeeFlow);
        assertResolveFlowRequestFromLatAndLong(flowSet, 32.88, -117.21, "convenience");
    }

    @Test
    void resolveFlow_ApplicationVersionNoMatch() {

        final String flowSet = "Recommender";
        final String ensembleName = "topics_ensemble_indentity_ranker";

        FlowRoutingGroupApi convenienceRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("convenience")
                .variation("convenience")
                .routingPercentage(1.0f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        FlowRoutingGroupApi metsovoRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("metsovo")
                .variation("metsovo")
                .routingPercentage(1.00f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        final List<FlowRoutingGroupApi> routingGroupsVersionMatch = List.of(convenienceRoutingGroup);
        final List<FlowRoutingGroupApi> routingGroupsVersionNoMatch = List.of(metsovoRoutingGroup);
        Set<String> matchingVersionSet = new HashSet<>();
        matchingVersionSet.add("2021.8.6");
        matchingVersionSet.add("2022.3.0");
        Set<String> matchingAppSet = new HashSet<>();
        matchingAppSet.add("iOS Native");


        final FlowApi employeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test application version")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(1)
                .matchingApplications(matchingAppSet)
                .matchingApplicationVersions(matchingVersionSet)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsVersionMatch))
                .build();

        final FlowApi noEmployeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test application version")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(500)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsVersionNoMatch))
                .build();

        modelService.createOrUpdateModel(createModel());
        ensembleService.createOrUpdateEnsemble(EnsembleFactory.createEnsembleWithName(ensembleName));
        flowService.createOrUpdateFlow(employeeFlow);
        flowService.createOrUpdateFlow(noEmployeeFlow);
        assertResolveFlowRequestFromLatAndLong(flowSet, 32.88, -117.21, "metsovo");
    }

    @Test
    void resolveFlow_ApplicationVersionGreaterThanMinimum() {

        final String flowSet = "Recommender";
        final String ensembleName = "topics_ensemble_indentity_ranker";

        FlowRoutingGroupApi convenienceRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("convenience")
                .variation("convenience")
                .routingPercentage(1.0f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        FlowRoutingGroupApi metsovoRoutingGroup = FlowRoutingGroupApi.builder()
                .groupName("metsovo")
                .variation("metsovo")
                .routingPercentage(1.00f)
                .ensembleName(ensembleName)
                .ensembleWeight("default")
                .build();

        final List<FlowRoutingGroupApi> routingGroupsVersionMatch = List.of(convenienceRoutingGroup);
        final List<FlowRoutingGroupApi> routingGroupsVersionNoMatch = List.of(metsovoRoutingGroup);
        Set<String> matchingVersionSet = new HashSet<>();
        matchingVersionSet.add("2022.2.+");
        Set<String> matchingAppSet = new HashSet<>();
        matchingAppSet.add("iOS Native");


        final FlowApi employeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test application version")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(1)
                .matchingApplications(matchingAppSet)
                .matchingApplicationVersions(matchingVersionSet)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsVersionMatch))
                .build();

        final FlowApi noEmployeeFlow = FlowApi.builder()
                .flowSet(flowSet)
                .flowId("********-0000-0000-0000-************")
                .flowName("Test application version")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .priority(500)
                .routingGroupsBucketingMode(GEOHASH)
                .routingGroupsCriteria(getDefaultRoutingGroupCriteria(routingGroupsVersionNoMatch))
                .build();

        modelService.createOrUpdateModel(createModel());
        ensembleService.createOrUpdateEnsemble(EnsembleFactory.createEnsembleWithName(ensembleName));
        flowService.createOrUpdateFlow(employeeFlow);
        flowService.createOrUpdateFlow(noEmployeeFlow);
        assertResolveFlowRequestFromLatAndLong(flowSet, 32.88, -117.21, "convenience");

    }

    @Test
    public void resolveFlow_should_return_response_with_flows_when_target_keywords_are_in_query_text() {
        mockConfig();
        Set<String> queryKeywords = ImmutableSet.of("test", "7-11", "Arby's");
        marketService.createOrUpdateMarket(buildChicagoMarket());
        modelService.createOrUpdateModel(createModelWithName(ROUTING_GROUPS_ENSEMBLE_NAME));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        val flow = FlowFactory.createFlowWithQueryKeywords(queryKeywords);
        flowService.createOrUpdateFlow(flow);

        val requestWithKeyword = ResolveFlowRequest
                .builder()
                .flowSet("SEARCH")
                .dinerId(UUID.randomUUID().toString())
                .applicationId(UUID.randomUUID().toString())
                .lat(CHICAGO_LAT)
                .lng(CHICAGO_LNG)
                .totalOrders(20)
                .queryText("finding next 7-11 location")
                .build();

        val withKeywordResponse = flowService.resolveFlow(requestWithKeyword);

        val requestWithoutKeyword = ResolveFlowRequest
                .builder()
                .flowSet("SEARCH")
                .dinerId(UUID.randomUUID().toString())
                .applicationId(UUID.randomUUID().toString())
                .lat(CHICAGO_LAT)
                .lng(CHICAGO_LNG)
                .totalOrders(20)
                .queryText("where is the nearest Arbys")
                .build();

        val withoutKeywordResponse = flowService.resolveFlow(requestWithoutKeyword);

        assertThat(withKeywordResponse.isPresent()).isTrue();
        assertThat(withKeywordResponse.get().getFlowName()).isEqualTo(flow.getFlowName());
        val keywordFromMatch = withKeywordResponse.get().getMatchingStrategy().getMatch().getQueryKeyword();
        assertThat(keywordFromMatch).isEqualTo("7-11");

        assertThat(withoutKeywordResponse.isPresent()).isFalse();
    }

    private void mockTotalOrders() {
        when(flowConfig.getTotalOrdersMax()).thenReturn(5);
    }

    private void mockConfig() {
        when(flowConfig.getPrecision()).thenReturn(5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true);
    }

    private EnsembleDTO createEnsembleWithName() {
        return EnsembleDTO.builder()
                .models(VavrMapper.toVavrSet(ImmutableSet.of("search_m1_m2")))
                .versioningStrategy("DYNAMIC")
                .version("123.123")
                .status("ENABLED")
                .ensembleName("test_function_simple_model")
                .ensembleWeights(HashMap.of("default", HashMap.of("restaurant_id", 0.7f)))
                .ensembleDescription("LTR model")
                .build();
    }

    private void assertResolveFlowRequestFromLatAndLong(String flowSet, double lat, double lng, String expectedGroupName) {
        ResolveFlowRequest resolveFlowRequest = ResolveFlowRequest
                .builder()
                .callerTrackingId("bucketing_order_test")
                .dinerId("********-13e4-11eb-874d-637bf9657209")
                .applicationId("3")
                .applicationVersion("2022.2.1")
                .flowSet(flowSet)
                .lat(lat)
                .lng(lng)
                .totalOrders(20)
                .build();

        Optional<FlowResponseApi> flowResponse = flowService.resolveFlow(resolveFlowRequest);

        assertThat(flowResponse.isPresent()).isTrue();
        assertThat(flowResponse.get().getRoutingGroup().getGroupName()).isEqualTo(expectedGroupName);
    }

    private io.vavr.collection.Map<String, Long> invokeAllResolveFlowManyGeohashes(List<ResolveFlowRequest> invocations) {
        return HashMap.ofAll(invocations.toStream()
                .map(flowService::resolveFlow)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(FlowResponseApi::getRoutingGroup)
                .collect(Collectors.groupingBy(RoutingGroupResponseDTO::getGroupName, Collectors.counting())));
    }

    private List<ResolveFlowRequest> generateResolveFlowForManyGeohashes(double startLat, double startLng, int degreesSpan, double step) {
        val resolveFlowRequestBuilder = ResolveFlowRequest
                .builder()
                .callerTrackingId("matias_test_geohash_routing")
                .dinerId("********-13e4-11eb-874d-637bf9657209")
                .applicationId("3")
                .flowSet("TestFlowSet")
                .lat(0)
                .lng(0)
                .totalOrders(20);

        val invocations = new ArrayList<ResolveFlowRequest>();
        for (double lat = startLat; lat <= startLat + degreesSpan; lat += step) {
            for (double lng = startLng; lng <= startLng + degreesSpan; lng += step) {
                invocations.add(resolveFlowRequestBuilder
                        .lat(lat)
                        .lng(lng)
                        .build());
            }
        }
        return List.ofAll(invocations);
    }

    private ResolveFlowRequest createModelAndFlowAndGetResolveFlowRequest(String flowSet, int totalOrders, String orderType) {
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val resolveFlowRequest = ResolveFlowRequest
                .builder()
                .flowSet(flowSet)
                .dinerId(UUID.randomUUID().toString())
                .applicationId("82")
                .lat(CHICAGO_LAT)
                .lng(CHICAGO_LNG)
                .totalOrders(totalOrders)
                .whenFor(dateTimeHelper.atZone(ZoneId.of("UTC")).toString())
                .orderType(orderType)
                .build();

        modelService.createOrUpdateModel(createModelWithName(ROUTING_GROUPS_ENSEMBLE_NAME));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        return resolveFlowRequest;
    }
}

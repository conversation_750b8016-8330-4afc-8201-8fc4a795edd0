package com.grubhub.garcon.controlplane.services.ensembler.impl;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.eventhub.ControlPlaneLogger;
import com.grubhub.garcon.controlplane.guice.ControlTestModule;
import com.grubhub.garcon.controlplane.services.common.service.CachedConfigurationService;
import com.grubhub.garcon.controlplane.services.controlplane.FlowByGeoHashService;
import com.grubhub.garcon.controlplane.services.controlplane.MarketService;
import com.grubhub.garcon.controlplane.services.controlplane.sequential.JexlExpressionExecutorImpl;
import com.grubhub.garcon.controlplane.services.ensembler.FeatureValueService;
import com.grubhub.garcon.controlplane.util.SessionAttributeProvider;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EnsembleInvocationRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EntitiesAliasDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureSourceType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputType;
import com.grubhub.garcon.controlplaneapi.service.controlplane.FlowService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.AliasService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.EnsembleService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import com.grubhub.garcon.ensembler.cassandra.dao.EntityCollectionDao;
import com.grubhub.garcon.ensembler.config.ModelGroupConfig;
import com.grubhub.garcon.ensembler.custom.FeatureValueContentParser;
import com.grubhub.garcon.grpc.ApplicationNodeFinder;
import com.grubhub.garcon.guice.PersistenceTestModule;
import com.grubhub.garcon.modelinteraction.ModelInferenceClient;
import com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelConfiguration;
import com.grubhub.garcon.shared.CassandraTestCase;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.test.cassandra.EmbeddedCassandraExt;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.micrometer.core.instrument.MeterRegistry;
import io.vavr.Tuple;
import io.vavr.collection.HashMap;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import lombok.val;
import name.falgout.jeffrey.testing.junit.guice.GuiceExtension;
import name.falgout.jeffrey.testing.junit.guice.IncludeModule;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import org.tensorflow.framework.TensorProto;
import tensorflow.serving.Predict;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;

import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static com.grubhub.garcon.controlplane.services.ensembler.impl.EnsembleFactory.createEnsemble;
import static com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory.createModel;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.DATA;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.initCasserole;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith({MockitoExtension.class, GuiceExtension.class})
@IncludeModule({PersistenceTestModule.class, ControlTestModule.class})
@CassandraTestCase
class EnsembleServiceImplTest {

    @Inject
    private FlowConfig flowConfig;
    @Inject
    private ControlPlaneLogger controlPlaneLogger;
    @Inject
    private ModelService modelService;
    @Inject
    private ModelInferenceInvoker modelInferenceInvoker;
    @Inject
    private EnsembleService ensembleService;
    @Inject
    private AliasService aliasService;
    @Inject
    @Named("featureValueService")
    private FeatureValueService featureValueService;
    @Inject
    private SessionAttributeProvider sessionAttributeProvider;
    @Inject
    private ApplicationNodeFinder applicationNodeFinder;

    @Inject
    private JexlExpressionExecutorImpl jexlExpressionExecutor;
    @Mock
    private Predict.PredictResponse predictResponse;
    @Inject
    private TensorFlowModelConfiguration tensorFlowModelConfiguration;
    @Inject
    private MeterRegistry meterRegistry;
    @Inject
    private EntityCollectionDao entityCollectionDao;
    @Inject
    private FlowService flowService;
    @Inject
    private MarketService marketService;
    @Inject
    private FlowByGeoHashService flowByGeohashService;
    @Inject
    private CachedConfigurationService cachedConfigurationService;
    @Inject
    private ModelInferenceClient modelInferenceClient;
    @Inject
    private MetricService metricService;

    @RegisterExtension
    public static final EmbeddedCassandraExt EMBEDDED_CASSANDRA = new EmbeddedCassandraExt(DATA);
    private static Casserole casserole;

    @BeforeAll
    static void beforeClass() {
        casserole = initCasserole(EMBEDDED_CASSANDRA.getPort());
    }

    @BeforeEach
    void setUp() {
        reset(flowConfig, controlPlaneLogger, sessionAttributeProvider, applicationNodeFinder, predictResponse);
        lenient().when(flowConfig.getFlowSetAsStream()).thenReturn(Stream.empty());
        lenient().when(flowConfig.getModelGroupConfigs()).thenReturn(HashMap.of("search", ModelGroupConfig.builder()
                .tfsClusterName("search")
                .internalFeaturesCacheroleName("internalFeatures")
                .build()).toJavaMap());
        cachedConfigurationService.cleanAllCache();
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }

    @AfterEach
    void afterEach() {
        cachedConfigurationService.cleanAllCache();
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }

    @AfterAll
    static void afterClass() {
        casserole.shutdown();
    }

    @Test
    void getEnsemble_should_return_successfully() {
        val ensemble = createEnsemble();
        val models = createModelsForEnsemble();
        ensembleService.createOrUpdateEnsemble(ensemble);

        val ensembleAsOption = ensembleService.getEnsemble(ensemble.getEnsembleName());
        assertThat(ensembleAsOption.isDefined()).isTrue();
        ensembleAsOption.peek(existingEnsemble -> {
            assertThat(existingEnsemble.getEnsembleWeights()).hasSize(1);
            assertThat(existingEnsemble.getEnsembleWeights().keySet()).containsExactly("search_m1_weight");
            assertThat(existingEnsemble.getEnsembleWeights().values()).containsExactly(HashMap.of("search_m1", 0.7f));
            assertThat(existingEnsemble.getModelList().size()).isEqualTo(models.size());
            assertThat(
                    existingEnsemble.getModelList().toStream().map(ModelDTO::getModelName).collect(List.collector())
            )
                    .containsExactlyInAnyOrderElementsOf(
                            models.toStream().map(ModelDTO::getModelName).collect(List.collector())
                    );
        });
    }

    private java.util.List<java.util.Map<String, Object>> createFeatures() {
        String value = "{" +
                "\"restaurant_id\":" + "88" + "," +
                "\"orders_last_month\":" + "500" + "," +
                "\"ratings_avg\":" + "7.5" +
                "}";
        val sample = ImmutableMap.of("clickLTRRestaurantFeature", (Object) value, "runtime", 1.5f);
        return ImmutableList.of(sample);
    }

    private List<ModelDTO> createModelsForEnsemble() {
        val searchM1Model = createModel();
        modelService.createOrUpdateModel(searchM1Model);
        return modelService
                .getModel(searchM1Model.getModelName())
                .toList();
    }

    @Test
    void createEnsemble_should_execute_successfully() {
        val ensemble = createEnsemble();
        val model = createModel();
        modelService.createOrUpdateModel(model);
        ensembleService.createOrUpdateEnsemble(ensemble);
    }

    @Test
    void createEnsemble_should_fail_when_model_is_not_found_in_database() {
        val ensemble = EnsembleDTO.builder()
                .ensembleName("search_m1")
                .versioningStrategy("DYNAMIC")
                .ensembleWeights(HashMap.of("search_m1_weight", HashMap.of("search_m1", 0.7f)))
                .ensembleDescription("Test ensemble")
                .status("ENABLED")
                .models(HashSet.of("search_m2"))
                .version("1.0")
                .build();

        assertThatThrownBy(() ->  ensembleService.createOrUpdateEnsemble(ensemble))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("For ensemble search_m1 the following models/ensembles are not found in database List(search_m2)");

    }

    @Test
    void createEnsemble_should_fail_when_strategy_is_function_not_found(){
        val ensemble = EnsembleDTO.builder()
                .ensembleName("search_m1")
                .versioningStrategy("DYNAMIC")
                .ensembleWeights(HashMap.of("search_m1_weight", HashMap.of("search_m1", 0.7f)))
                .ensembleDescription("Test ensemble")
                .status("ENABLED")
                .models(HashSet.of("search_m1"))
                .ensembleStrategy("function")
                .version("1.0")
                .build();
        val model = createModel();
        modelService.createOrUpdateModel(model);
        assertThatThrownBy(() ->  ensembleService.createOrUpdateEnsemble(ensemble))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("Error invoking ensemble=search_m1, the specified ensemble with ensembleStrategy=function " +
                        "did not have the correct fields for the specified strategy");

    }

    @Test
    void updateEnsemble_should_execute_successfully() {
        val ensemble = createEnsemble();
        val model = createModel();
        modelService.createOrUpdateModel(model);
        ensembleService.createOrUpdateEnsemble(ensemble);

        val updatedEnsemble =
                ensemble.withModels(ensemble.getModels().add("test_model"))
                .withEnsembleWeights(HashMap
                        .of(
                                "search_m1_weight", HashMap.of("search_m1", 0.2f),
                                "test_model_weight", HashMap.of("test_model", 0.8f)
                        )
                )
                .withEnsembleDescription("Updated description");

        ensembleService.updateEnsemble(updatedEnsemble);

        val existingEnsemble = ensembleService.getEnsemble(updatedEnsemble.getEnsembleName());
        assertThat(existingEnsemble.isDefined()).isTrue();
        existingEnsemble.peek(theEnsemble -> {
            assertThat(theEnsemble.getModels()).containsExactlyInAnyOrder("search_m1", "test_model");
            assertThat(theEnsemble.getEnsembleDescription()).isEqualTo("Updated description");
            assertThat(theEnsemble.getEnsembleWeights().keySet()).containsExactlyInAnyOrder("search_m1_weight", "test_model_weight");
            assertThat(theEnsemble.getEnsembleWeights().values()).containsExactlyInAnyOrder(HashMap.of("search_m1", 0.2f), HashMap.of("test_model", 0.8f));
        });
    }

    @Test
    @Disabled
    void test_m1_model() throws IOException {
        val searchM1Model = ModelDTO.builder()
                .modelName("search_m1")
                .modelDescription("M1 - Order Max - Click LTR model")
                .modelType(ModelType.TENSOR_FLOW.name())
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m1")
                .location("s3://vnwre0qnbk1qy9zw-ddml-models/tensorflow/model/ClickLTR/3/")
                .servingLocation("s3://vnwre0qnbk1qy9zw-ddml-models/tensorflow/model/ClickLTR/3/")
                .version("68.68")
                .processedFeaturesFilter(HashSet.of(
                        "query_text",
                        "cuisine_filter",
                        "user_search_tokens",
                        "user_click_history",
                        "user_order_history",
                        "user_gotos",
                        "geohash",
                        "mealtime",
                        "app",
                        "name",
                        "cuisines",
                        "rest_geohash",
                        "rid"
                ))
                .tfUseExamplesSerialization(true)
                .tfExamplesSerializationName("inputs")
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("search_m1_restaurant_feature")
                                .majorVersion("3")
                                .minorVersion("2")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID"))
                                .status("ENABLED")
                                .featureLocation("s3://grubhub-gdp-data-transfer-dev/roux-model-serving/feature/search_m1/restaurant_feature/")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m1_diner_feature")
                                .majorVersion("3")
                                .minorVersion("2")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("DINER_ID"))
                                .status("ENABLED")
                                .featureLocation("s3://grubhub-gdp-data-transfer-dev/roux-model-serving/feature/search_m1/diner_feature/")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("query_text")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("geohash")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("mealtime")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("app")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("cuisine_filter")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO.builder()
                                .outputName("outputs")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                            .build()
                        )
                )
                .build();

        modelService.createOrUpdateModel(searchM1Model);

        val ensemble = EnsembleDTO.builder()
                .ensembleName("search_ensemble_m1")
                .ensembleWeights(HashMap.of("default", HashMap.of("search_m1", 1.0f)))
                .ensembleDescription("Ensemble for Order Max (M1) model alone")
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningEnsembleName("search_ensemble_m1")
                .models(HashSet.of("search_m1"))
                .version("1.0")
                .build();

        ensembleService.createOrUpdateEnsemble(ensemble);

        Map<String, Object> featureOne = new java.util.HashMap<>();
        featureOne.put("DINER_ID", "00004120-13e4-11eb-874d-637bf9657209");
        featureOne.put("query_text", "");
        featureOne.put("geohash", "dp3w");
        featureOne.put("mealtime", "breakfast");
        featureOne.put("app", "iOS Native");
        featureOne.put("cuisine_filter", "");

        val ensembleInvocationRequest = EnsembleInvocationRequest.builder()
                .callerTrackingId("some_id")
                .ensembleName("search_ensemble_m1")
                .ensembleWeight("default")
                .globalFeatures(featureOne)
                .features(Lists.newArrayList(
                        ImmutableMap.of("RESTAURANT_ID", "187"),
                        ImmutableMap.of("RESTAURANT_ID", "188"),
                        ImmutableMap.of("RESTAURANT_ID", "189")
                ))
                .build();


        when(modelInferenceClient.inferWithTensorFlow(any())).thenReturn(ModelInferenceResult.builder()
                .output(HashMap.ofEntries(Tuple.of(
                        "outputs",
                        ModelInferenceOutputType.ofFloatList(Lists.newArrayList(207.08072f, 206.4449f, 206.06284f)))
                )).build()
        );

        ingestFeatures();
        val ensembleScores = ensembleService.invokeEnsemble(ensembleInvocationRequest);

        assertThat(ensembleScores).isNotEmpty();
        assertThat(ensembleScores).containsExactlyInAnyOrder(207.08072f, 206.4449f, 206.06284f);
    }


    @Test
    @org.junit.jupiter.api.Disabled
    void test_m1_model_supports_default_json_for_internal_features() throws IOException {
        val searchM1Model = ModelDTO.builder()
                .modelName("search_m1")
                .modelDescription("M1 - Order Max - Click LTR model")
                .modelType(ModelType.TENSOR_FLOW.name())
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m1")
                .location("s3://vnwre0qnbk1qy9zw-ddml-models/tensorflow/model/ClickLTR/3/")
                .servingLocation("s3://vnwre0qnbk1qy9zw-ddml-models/tensorflow/model/ClickLTR/3/")
                .version("68.68")
                .processedFeaturesFilter(HashSet.of(
                        "query_text",
                        "cuisine_filter",
                        "user_search_tokens",
                        "user_click_history",
                        "user_order_history",
                        "user_gotos",
                        "geohash",
                        "mealtime",
                        "app",
                        "name",
                        "cuisines",
                        "rest_geohash",
                        "rid",
                        "modified_cbsa_name"
                ))
                .tfUseExamplesSerialization(true)
                .tfExamplesSerializationName("inputs")
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("search_m1_restaurant_feature")
                                .majorVersion("3")
                                .minorVersion("2")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID"))
                                .featureOptional(true)
                                .featureDefaultValue("" +
                                        "{\"modified_cbsa_name\": \"\"," +
                                        "\"cuisines\": \"\"," +
                                        "\"name\": \"\"," +
                                        "\"rid\": \"\"," +
                                        "\"rest_geohash\": " +
                                        "\"\"}")
                                .status("ENABLED")
                                .featureLocation("s3://grubhub-gdp-data-transfer-dev/roux-model-serving/feature/search_m1/restaurant_feature/")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m1_diner_feature")
                                .majorVersion("3")
                                .minorVersion("2")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("DINER_ID"))
                                .status("ENABLED")
                                .featureLocation("s3://grubhub-gdp-data-transfer-dev/roux-model-serving/feature/search_m1/diner_feature/")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("query_text")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("geohash")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("mealtime")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("app")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("cuisine_filter")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO.builder()
                                .outputName("outputs")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                                .build()
                        )
                )
                .build();

        modelService.createOrUpdateModel(searchM1Model);

        val ensemble = EnsembleDTO.builder()
                .ensembleName("search_ensemble_m1")
                .ensembleWeights(HashMap.of("default", HashMap.of("search_m1", 1.0f)))
                .ensembleDescription("Ensemble for Order Max (M1) model alone")
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningEnsembleName("search_ensemble_m1")
                .models(HashSet.of("search_m1"))
                .version("1.0")
                .build();

        ensembleService.createOrUpdateEnsemble(ensemble);

        Map<String, Object> featureOne = new java.util.HashMap<>();
        featureOne.put("DINER_ID", "00004120-13e4-11eb-874d-637bf9657209");
        featureOne.put("query_text", "");
        featureOne.put("geohash", "dp3w");
        featureOne.put("mealtime", "breakfast");
        featureOne.put("app", "iOS Native");
        featureOne.put("cuisine_filter", "");

        val ensembleInvocationRequest = EnsembleInvocationRequest.builder()
                .callerTrackingId("some_id")
                .ensembleName("search_ensemble_m1")
                .ensembleWeight("default")
                .globalFeatures(featureOne)
                .features(Lists.newArrayList(
                        ImmutableMap.of("RESTAURANT_ID", "2077")
                ))
                .build();

        java.util.Map<String, TensorProto> result = new java.util.HashMap<>();
        result.put("outputs", TensorProto.newBuilder()
                .addAllFloatVal(Lists.newArrayList(207.08072f, 206.4449f, 206.06284f))
                .build());
        when(predictResponse.getOutputsMap()).thenReturn(result);

        ingestFeatures();
        EnsembleServiceImpl spyEnsembleService = (EnsembleServiceImpl) spy(ensembleService);

        ResultCaptor<io.vavr.collection.Map<String, ModelInferenceOutput>> invokeRecursivelyResult = new ResultCaptor<>();
        doAnswer(invokeRecursivelyResult).when(spyEnsembleService).invokeRecursively(eq(ensembleInvocationRequest), any(EnsembleDTO.class),
                any(ConcurrentMap.class), any(CountDownLatch.class), eq(false));

        assertThat(invokeRecursivelyResult.getResult()).isNotNull();
        assertThat(invokeRecursivelyResult.getResult().get("search_m1").get().getProcessedFeatures().toStream()
        .flatMap(Map::keySet)).contains("modified_cbsa_name", "cuisines", "name", "rid", "rest_geohash");

        List<String> featureKeys = List.of("modified_cbsa_name", "cuisines", "name", "rid", "rest_geohash");
        assertThat(invokeRecursivelyResult.getResult().get("search_m1").get().getProcessedFeatures().toStream()
                .flatMap(Map::entrySet)
                .filter(entry -> featureKeys.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .map(String.class::cast)
                .collect(List.collector())
                .asJava()
        )
                .allMatch(String::isEmpty);
    }

    private static class ResultCaptor<T> implements Answer {
        private T result = null;
        public T getResult() {
            return result;
        }

        @Override
        public T answer(InvocationOnMock invocationOnMock) throws Throwable {
            result = (T) invocationOnMock.callRealMethod();
            return result;
        }
    }

    private void ingestFeatures() throws IOException {
        ingestFeatures("m1_sample_restaurant_features.csv", "search_m1_restaurant_feature", 3, 2);
        ingestFeatures("m1_sample_diner_features.csv", "search_m1_diner_feature", 3, 2);

    }

    @SuppressFBWarnings("PATH_TRAVERSAL_IN")
    private void ingestFeatures(String fileName, String featureName, int majorVersion, int minorVersion) throws IOException {
        val classLoader = getClass().getClassLoader();
        val file = new File(classLoader.getResource(FilenameUtils.getName(fileName)).getFile());
        val dinerFeatures = FileUtils.readFileToString(file, "UTF-8");

        val values = new FeatureValueContentParser(featureName, majorVersion, minorVersion, dinerFeatures).parseValues();
        featureValueService.createOrUpdateMany(values);
    }


    @Test
    void test_m2_model() throws IOException {
        val searchM2GhDeliveryInd = ModelDTO
                .builder()
                .modelName("search_m2_ghDeliveryInd")
                .modelDescription("M2 - ghDeliveryInd: Lever used to favor restaurants whose orders are delivered by GH")
                .modelType(ModelType.FUNCTION.name())
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m2_ghDeliveryInd")
                .processedFeaturesFilter(HashSet.of("gh_delivery_ind"))
                .version("1.0")
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("order_type")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("STANDARD")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("location_mode")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("DELIVERY")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m2_restaurant_feature")
                                .majorVersion("1")
                                .minorVersion("0")
                                .status("ENABLED")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID", "location_mode", "order_type"))
                                .featureLocation("s3://some_path/")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("output")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                                .build()
                ))
                .build();

        val searchM2HasPromo = ModelDTO
                .builder()
                .modelName("search_m2_hasPromo")
                .modelDescription("M2 - hasPromo: Lever to favor restaurants with an available promo for a diner")
                .modelType(ModelType.FUNCTION.name())
                .version("1.0")
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m2_hasPromo")
                .processedFeaturesFilter(HashSet.of("has_promo"))
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("order_type")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("STANDARD")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("location_mode")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("DELIVERY")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m2_restaurant_feature")
                                .majorVersion("1")
                                .minorVersion("0")
                                .status("ENABLED")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID", "location_mode", "order_type"))
                                .featureLocation("s3://some_path/")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("output")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                                .build()
                ))
                .build();

        val searchM2GhFundedPromo = ModelDTO
                .builder()
                .modelName("search_m2_ghFundedPromo")
                .modelDescription("M2 - ghFundedPromo: Lever to favor restaurants with a promo that is funded by GH\"")
                .modelType(ModelType.FUNCTION.name())
                .version("1.0")
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m2_ghFundedPromo")
                .processedFeaturesFilter(HashSet.of("gh_funded_promo"))
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("order_type")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("STANDARD")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("location_mode")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("DELIVERY")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m2_restaurant_feature")
                                .majorVersion("1")
                                .minorVersion("0")
                                .status("ENABLED")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID", "location_mode", "order_type"))
                                .featureLocation("s3://some_path/")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("output")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                                .build()
                ))
                .build();

        val searchM2RestaurantFeature = ModelDTO
                .builder()
                .modelName("search_m2_selfFundedPromo")
                .modelDescription("M2 - ghFundedPromo: Lever to favor restaurants with a promo that is funded by GH\"")
                .modelType(ModelType.FUNCTION.name())
                .status("ENABLED")
                .version("1.0")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m2_selfFundedPromo")
                .processedFeaturesFilter(HashSet.of("self_funded_promo"))
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("order_type")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("STANDARD")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("location_mode")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("DELIVERY")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m2_restaurant_feature")
                                .majorVersion("1")
                                .minorVersion("0")
                                .status("ENABLED")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID", "location_mode", "order_type"))
                                .featureLocation("s3://some_path/")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("output")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                                .build()
                ))
                .build();

        val searchM2AvgOrderCost = ModelDTO
                .builder()
                .modelName("search_m2_avgOrderCost")
                .modelDescription("M2 - avgOrderCost: Lever to favor a restaurant by how much it costs GH when an order is placed")
                .modelType(ModelType.FUNCTION.name())
                .version("1.0")
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m2_avgOrderCost")
                .processedFeaturesFilter(HashSet.of("avg_order_cost"))
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("order_type")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("STANDARD")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("location_mode")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("DELIVERY")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m2_restaurant_feature")
                                .majorVersion("1")
                                .minorVersion("0")
                                .status("ENABLED")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID", "location_mode", "order_type"))
                                .featureLocation("s3://some_path/")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("output")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                                .build()
                ))
                .build();

        val searchM2AvgOrderRevenue = ModelDTO
                .builder()
                .modelName("search_m2_avgOrderRevenue")
                .modelDescription("M2 - avgOrderRevenue: Lever to favor a restaurant by how much it revenue GH makes when an order is placed")
                .modelType(ModelType.FUNCTION.name())
                .version("1.0")
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m2_avgOrderRevenue")
                .processedFeaturesFilter(HashSet.of("avg_order_revenue"))
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("order_type")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("STANDARD")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("location_mode")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("DELIVERY")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m2_restaurant_feature")
                                .majorVersion("1")
                                .minorVersion("0")
                                .status("ENABLED")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID", "location_mode", "order_type"))
                                .featureLocation("s3://some_path/")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("output")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                                .build()
                ))
                .build();

        val searchM2AvgDeliveryFnc = ModelDTO
                .builder()
                .modelName("search_m2_avgDeliveryFnc")
                .modelDescription("M2 - avgDeliveryFnc: Lever to favor restaurants by how much in fees are paid to GH when GH does a delivery")
                .modelType(ModelType.FUNCTION.name())
                .version("1.0")
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m2_avgDeliveryFnc")
                .processedFeaturesFilter(HashSet.of("avg_delivery_fnc"))
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("order_type")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("STANDARD")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("location_mode")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("DELIVERY")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m2_restaurant_feature")
                                .majorVersion("1")
                                .minorVersion("0")
                                .status("ENABLED")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID", "location_mode", "order_type"))
                                .featureLocation("s3://some_path/")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("output")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                                .build()
                ))
                .build();

        val searchM2AvgDeliveryCost = ModelDTO
                .builder()
                .modelName("search_m2_avgDeliveryCost")
                .modelDescription("M2 - avgDeliveryCost: Lever to favor a restaurant by how much it costs GH to delivery an order from there")
                .modelType(ModelType.FUNCTION.name())
                .version("1.0")
                .status("ENABLED")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m2_avgDeliveryCost")
                .processedFeaturesFilter(HashSet.of("avg_delivery_cost"))
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("order_type")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("STANDARD")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("location_mode")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("DELIVERY")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m2_restaurant_feature")
                                .majorVersion("1")
                                .minorVersion("0")
                                .status("ENABLED")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID", "location_mode", "order_type"))
                                .featureLocation("s3://some_path/")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("output")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                                .build()
                ))
                .build();

        val searchM2DailyAdFee = ModelDTO
                .builder()
                .modelName("search_m2_adFee")
                .modelDescription("M2 - adFee: Lever to favor a restaurant by what percentage of ads fee it pays")
                .modelType(ModelType.FUNCTION.name())
                .status("ENABLED")
                .version("1.0")
                .versioningStrategy("DYNAMIC")
                .versioningModelName("search_m2_adFee")
                .processedFeaturesFilter(HashSet.of("ad_fee"))
                .modelFeatures(List.of(
                        FeatureDTO.builder()
                                .featureName("RESTAURANT_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("order_type")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("STANDARD")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("location_mode")
                                .featureSourceType("DINER_ID")
                                .featureSourceType(FeatureSourceType.RUNTIME.name())
                                .status("ENABLED")
                                .featureOptional(true)
                                .featureDefaultValue("DELIVERY")
                                .build(),
                        FeatureDTO.builder()
                                .featureName("search_m2_restaurant_feature")
                                .majorVersion("1")
                                .minorVersion("0")
                                .status("ENABLED")
                                .featureSourceType(FeatureSourceType.FEATURE_STORE_INTERNAL.name())
                                .featureStoreFields(List.of("RESTAURANT_ID", "location_mode", "order_type"))
                                .featureLocation("s3://some_path/")
                                .build()
                ))
                .modelOutputs(List.of(
                        ModelOutputDTO
                                .builder()
                                .outputName("output")
                                .outputType(ModelOutputType.FLOAT.name())
                                .outputShape(List.of(1))
                                .build()
                ))
                .build();

        List.of(
                searchM2GhDeliveryInd,
                searchM2HasPromo,
                searchM2GhFundedPromo,
                searchM2RestaurantFeature,
                searchM2AvgOrderCost,
                searchM2AvgOrderRevenue,
                searchM2AvgDeliveryFnc,
                searchM2AvgDeliveryCost,
                searchM2DailyAdFee
        )
                .forEach(modelService::createOrUpdateModel);

        val searchEnsembleM2 = EnsembleDTO
                .builder()
                .ensembleName("search_ensemble_m2")
                .ensembleDescription("Ensemble for M2 - Incentives model")
                .status("ENABLED")
                .version("1.0")
                .versioningStrategy("DYNAMIC")
                .versioningEnsembleName("search_ensemble_m2")
                .models(HashSet.of(
                        "search_m2_ghDeliveryInd",
                        "search_m2_hasPromo",
                        "search_m2_ghFundedPromo",
                        "search_m2_selfFundedPromo",
                        "search_m2_avgOrderCost",
                        "search_m2_avgOrderRevenue",
                        "search_m2_avgDeliveryFnc",
                        "search_m2_avgDeliveryCost",
                        "search_m2_adFee"
                ))
                .ensembleWeights(HashMap.of(
                        "default", HashMap.of(
                                "search_m2_ghDeliveryInd", 1.0f,
                                "search_m2_hasPromo", 1.0f,
                                "search_m2_ghFundedPromo", 1.0f,
                                "search_m2_selfFundedPromo", 1.0f,
                                "search_m2_avgOrderCost", 1.0f,
                                "search_m2_avgOrderRevenue", 1.0f,
                                "search_m2_avgDeliveryFnc", 1.0f,
                                "search_m2_avgDeliveryCost", 1.0f,
                                "search_m2_adFee", 1.0f
                        ),
                        "market_1", HashMap.of(
                                "search_m2_ghDeliveryInd", 9.0f,
                                "search_m2_hasPromo", 5.0f,
                                "search_m2_ghFundedPromo", 5.0f,
                                "search_m2_selfFundedPromo", 5.0f,
                                "search_m2_avgOrderCost", 5.0f,
                                "search_m2_avgOrderRevenue", 3.0f,
                                "search_m2_avgDeliveryFnc", 3.0f,
                                "search_m2_avgDeliveryCost", 3.0f,
                                "search_m2_adFee", 3.0f
                        ),
                        "market_2", HashMap.of(
                                "search_m2_ghDeliveryInd", 3.0f,
                                "search_m2_hasPromo", 3.0f,
                                "search_m2_ghFundedPromo", 3.0f,
                                "search_m2_selfFundedPromo", 5.0f,
                                "search_m2_avgOrderCost", 5.0f,
                                "search_m2_avgOrderRevenue", 5.0f,
                                "search_m2_avgDeliveryFnc", 5.0f,
                                "search_m2_avgDeliveryCost", 5.0f,
                                "search_m2_adFee", 9.0f
                        ),
                        "only_revenue", HashMap.of(
                                "search_m2_ghDeliveryInd", 0f,
                                "search_m2_hasPromo", 0f,
                                "search_m2_ghFundedPromo", 0f,
                                "search_m2_selfFundedPromo", 0f,
                                "search_m2_avgOrderCost", 0f,
                                "search_m2_avgOrderRevenue", 1f,
                                "search_m2_avgDeliveryFnc", 0f,
                                "search_m2_avgDeliveryCost", 0f,
                                "search_m2_adFee", 0f
                        ),
                        "2_revenue_1cost", HashMap.of(
                                "search_m2_ghDeliveryInd", 0f,
                                "search_m2_hasPromo", 0f,
                                "search_m2_ghFundedPromo", 0f,
                                "search_m2_selfFundedPromo", 0f,
                                "search_m2_avgOrderCost", 1f,
                                "search_m2_avgOrderRevenue", 2f,
                                "search_m2_avgDeliveryFnc", 0f,
                                "search_m2_avgDeliveryCost", 0f,
                                "search_m2_adFee", 0f
                        )
                ))
                .build();

        ensembleService.createOrUpdateEnsemble(searchEnsembleM2);
        val searchEnsembleM2InvocationRequest = EnsembleInvocationRequest
                .builder()
                .callerTrackingId("some_id")
                .ensembleName("search_ensemble_m2")
                .ensembleWeight("default")
                .globalFeatures(ImmutableMap.of("location_mode", "DELIVERY", "order_type", "STANDARD"))
                .features(Lists.newArrayList(
                        ImmutableMap.of("RESTAURANT_ID", "rid_1"),
                        ImmutableMap.of("RESTAURANT_ID", "rid_2"),
                        ImmutableMap.of("RESTAURANT_ID", "rid_3")
                ))
                .build();

        ingestFeatures("m2_sample_features.csv", "search_m2_restaurant_feature", 1, 0);

        val m2EnsembleScores = ensembleService.invokeEnsemble(searchEnsembleM2InvocationRequest);
        verify(metricService, times(18)).gauge(any(), any(), any(), any());

        assertThat(m2EnsembleScores).isNotEmpty();
        assertThat(m2EnsembleScores).containsExactlyInAnyOrder(28.29f, 40.219997f, 27.16f);

        val searchEnsembleM2InvocationRequestWithOnlyRevenue = EnsembleInvocationRequest
                .builder()
                .callerTrackingId("some_id")
                .ensembleName("search_ensemble_m2")
                .ensembleWeight("only_revenue")
                .globalFeatures(ImmutableMap.of("location_mode", "DELIVERY", "order_type", "STANDARD"))
                .features(Lists.newArrayList(
                        ImmutableMap.of("RESTAURANT_ID", "rid_1"),
                        ImmutableMap.of("RESTAURANT_ID", "rid_2"),
                        ImmutableMap.of("RESTAURANT_ID", "rid_3")
                ))
                .build();
        val m2EnsembleScoresWithOnlyRevenue = ensembleService.invokeEnsemble(searchEnsembleM2InvocationRequestWithOnlyRevenue);

        assertThat(m2EnsembleScoresWithOnlyRevenue).isNotEmpty();
        assertThat(m2EnsembleScoresWithOnlyRevenue).containsExactlyInAnyOrder(3.19f, 4.21f, 6.78f);

        val searchEnsembleM2InvocationRequestWithSecondCost = EnsembleInvocationRequest
                .builder()
                .callerTrackingId("some_id")
                .ensembleName("search_ensemble_m2")
                .globalFeatures(ImmutableMap.of("location_mode", "DELIVERY", "order_type", "STANDARD"))
                .ensembleWeight("2_revenue_1cost")
                .features(Lists.newArrayList(
                        ImmutableMap.of("RESTAURANT_ID", "rid_1"),
                        ImmutableMap.of("RESTAURANT_ID", "rid_2"),
                        ImmutableMap.of("RESTAURANT_ID", "rid_3")
                ))
                .build();
        val m2EnsembleScoresWithSecondCost = ensembleService.invokeEnsemble(searchEnsembleM2InvocationRequestWithSecondCost);

        assertThat(m2EnsembleScoresWithSecondCost).isNotEmpty();
        assertThat(m2EnsembleScoresWithSecondCost).containsExactlyInAnyOrder(17.33f, 26.05f, 24.91f);
    }

    @Test
    void test_ensemble_of_ensemble() {
        val weightsName = "default";
        val model1 = ModelFactory.createSimpleFunctionModel("model1", "feature1");
        val model2 = ModelFactory.createSimpleFunctionModel("model2", "feature2");
        val model3 = ModelFactory.createSimpleFunctionModel("model3", "feature3");

        // Ensemble of model1 and model2
        val ensemble1and2 = EnsembleFactory.createSimpleEnsemble("ensemble1and2",
                List.of(model1.getModelName(), model2.getModelName()), weightsName);

        // Ensemble of model3 and the previous ensemble
        val ensembleGlobal = EnsembleFactory.createSimpleEnsemble("ensembleGlobalAlias",
                List.of(ensemble1and2.getEnsembleName(), model3.getModelName()), weightsName);

        // Add all models and ensembles
        modelService.createOrUpdateModel(model1);
        modelService.createOrUpdateModel(model2);
        modelService.createOrUpdateModel(model3);
        ensembleService.createOrUpdateEnsemble(ensemble1and2);
        ensembleService.createOrUpdateEnsemble(ensembleGlobal);

        // Check values
        assertThat(ensembleService.getEnsemble(ensemble1and2.getEnsembleName()).isDefined()).isTrue();
        assertThat(ensembleService.getEnsemble(ensembleGlobal.getEnsembleName()).isDefined()).isTrue();

        // Invoke global ensemble
        val invocationRequest = EnsembleInvocationRequest
                .builder()
                .callerTrackingId("some_id")
                .ensembleName("ensembleGlobalAlias") // Call by alias.
                .ensembleWeight(weightsName)
                .features(Lists.newArrayList(
                        ImmutableMap.of("feature1", 1, "feature2", 2, "feature3", 3),
                        ImmutableMap.of("feature1", 10, "feature2", 20, "feature3", 30),
                        ImmutableMap.of("feature1", 100, "feature2", 200, "feature3", 300)
                ))
                .build();
        val output = ensembleService.invokeEnsemble(invocationRequest);

        // Compare output is merging well all models
        val half = 0.5f;
        assertThat(output).containsExactly(
                half * (half * 1f + half * 2f) + half * 3f, //2.25
                half * (half * 10f + half * 20f) + half * 30f, //22.5
                half * (half * 100f + half * 200f) + half * 300f //225
        );

    }

    @Test
    void updateAndDeleteAlias_successfully() {
        val ensemble = createEnsemble();
        modelService.createOrUpdateModel(createModel());
        ensembleService.createOrUpdateEnsemble(ensemble);
        val ensembleName = ensemble.getEnsembleName();
        val aliasName = "test_alias";
        ensembleService.updateAlias(aliasName, ensembleName);

        Option<EntitiesAliasDTO> existingAlias = ensembleService.getAlias(aliasName);
        assertThat(existingAlias.isDefined()).isTrue();
        existingAlias.peek(alias -> assertThat(alias.getEntityId()).isEqualTo(ensembleName));

        ensembleService.deleteAlias(aliasName);
        Option<EntitiesAliasDTO> deletedAlias = ensembleService.getAlias(aliasName);
        assertThat(deletedAlias.isDefined()).isFalse();
    }

    @Test
    void updateAndDeleteAlias_ensembleDoesNotExist_exception() {
        val ensemble = createEnsemble();

        // Ensemble not created.
        val ensembleName = ensemble.getEnsembleName();
        val aliasName = "test_alias";

        assertThrows(RuntimeException.class, () -> {
            ensembleService.updateAlias(aliasName, ensembleName);
        });

        Option<EntitiesAliasDTO> existingAlias = ensembleService.getAlias(aliasName);
        assertThat(existingAlias.isDefined()).isFalse();
    }

    @Test
    public void getEnsembleByNameOrAlias_byEnsembleName_success() {
        EnsembleDTO ensemble = createEnsemble();
        modelService.createOrUpdateModel(createModel());
        ensembleService.createOrUpdateEnsemble(ensemble);

        val result = ensembleService.getEnsembleByNameOrAlias(ensemble.getEnsembleName());

        assertTrue(result.isDefined());
        assertEquals(result.get().getEnsembleName(), ensemble.getEnsembleName());
    }

    @Test
    public void getEnsembleByNameOrAlias_ensembleNotFound() {
        val result = ensembleService.getEnsembleByNameOrAlias("some_ensemble");

        assertFalse(result.isDefined());
    }

}

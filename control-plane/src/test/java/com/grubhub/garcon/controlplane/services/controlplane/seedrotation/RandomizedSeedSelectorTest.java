package com.grubhub.garcon.controlplane.services.controlplane.seedrotation;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import io.vavr.collection.List;
import org.junit.jupiter.api.Test;

import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createDTOFlow;
import static org.assertj.core.api.Assertions.assertThat;

class RandomizedSeedSelectorTest {

    @Test
    void select_shouldReturnExpectedWhenFlagIsTrue() {
        FlowDTO flow = createDTOFlow();
        flow.setRoutingGroupsSeedRotation(null);
        flow.setRoutingGroupsSeedRotationEnabled(true);
        List<FlowDTO> response = new RandomizedSeedSelector(List.of(flow)).select();

        assertThat(response).isNotEmpty();
        assertThat(response).hasSize(1);
    }

    @Test
    void select_shouldReturnEmptyWhenFlagIsTrueButListIsNotEmpty() {
        FlowDTO flow = createDTOFlow();
        flow.setRoutingGroupsSeedRotation(List.of("1"));
        flow.setRoutingGroupsSeedRotationEnabled(true);
        List<FlowDTO> response = new RandomizedSeedSelector(List.of(flow)).select();

        assertThat(response).isEmpty();
    }

    @Test
    void select_shouldIgnoreFlowsWithFalseFlag() {

        List<FlowDTO> response = new RandomizedSeedSelector(List.of(createDTOFlow())).select();

        assertThat(response).isEmpty();
    }

    @Test
    void select_shouldIgnoreFlowsWhenRotationSeedsListIsEmpty() {
        FlowDTO flow = createDTOFlow();
        flow.setRoutingGroupsSeedRotation(List.of());
        flow.setRoutingGroupsSeedRotationEnabled(false);
        List<FlowDTO> response = new RandomizedSeedSelector(List.of(createDTOFlow())).select();

        assertThat(response).isEmpty();
    }

    @Test
    void select_shouldIgnoreFlowsWhenRotationSeedsListIsNull() {
        FlowDTO flow = createDTOFlow();
        flow.setRoutingGroupsSeedRotation(null);
        flow.setRoutingGroupsSeedRotationEnabled(false);
        List<FlowDTO> response = new RandomizedSeedSelector(List.of(createDTOFlow())).select();

        assertThat(response).isEmpty();
    }
}


package com.grubhub.garcon.controlplane.services.ensembler;

import com.google.common.base.Optional;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.cacherole.Cacherole;
import com.grubhub.garcon.controlplane.guice.ControlTestModule;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.utils.ContainersInitializer;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureKeyDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureValueDTO;
import com.grubhub.garcon.ensembler.cassandra.dao.FeatureValueDao;
import com.grubhub.garcon.guice.PersistenceTestModule;
import com.grubhub.garcon.shared.CassandraTestCase;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.test.cassandra.EmbeddedCassandraExt;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.val;
import name.falgout.jeffrey.testing.junit.guice.GuiceExtension;
import name.falgout.jeffrey.testing.junit.guice.IncludeModule;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static com.grubhub.garcon.controlplane.guice.ControlTestModule.internalFeaturesCacheroleWithMemcached;
import static com.grubhub.garcon.controlplane.utils.APIConstants.MEMCACHED_PORT;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.DATA;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.initCasserole;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;

@ExtendWith({MockitoExtension.class, GuiceExtension.class})
@IncludeModule({PersistenceTestModule.class, ControlTestModule.class})
@CassandraTestCase
@Disabled
public class FeatureServiceWithCacheIT extends ContainersInitializer {

    @Inject
    @Named("featureValueServiceWithCache")
    private FeatureValueService featureValueServiceWithCache;
    @Inject
    @Named("featureValueService")
    private FeatureValueService featureValueServiceSpy;
    @Inject
    private FeatureValueDao featureValueDao;
    @Inject
    @Named("cacherole-ddmlConfiguration-with-memcached")
    private Cacherole cacherole;
    @Inject
    private ControlPlaneMapper controlPlaneMapper;

    @Inject
    @Named("casserole-v2-ddmlControlPlane")
    private static Casserole casserole;

    private FeatureKeyDTO firstFeatureKey;

    private FeatureKeyDTO secondFeatureKey;

    @RegisterExtension
    public static final EmbeddedCassandraExt EMBEDDED_CASSANDRA = new EmbeddedCassandraExt(DATA);

    @BeforeAll
    static void beforeClass() {
        casserole = initCasserole(EMBEDDED_CASSANDRA.getPort());
    }

    @BeforeEach
    void setUp() {
        cacherole = spy(getCacherole());
        featureValueServiceSpy = spy(featureValueServiceSpy);
        Mockito.reset(cacherole);
        firstFeatureKey = FeatureKeyDTO.builder()
                .featureKey("test key 1")
                .featureName("test name 1")
                .majorVersion(3)
                .minorVersion(1)
                .build();
        secondFeatureKey = FeatureKeyDTO.builder()
                .featureKey("test key 2")
                .featureName("test name 2")
                .majorVersion(4)
                .minorVersion(2)
                .build();
        cacherole.deleteBatch(firstFeatureKey.getCacheKey(), secondFeatureKey.getCacheKey());
    }

    @AfterEach
    void afterEach() {
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }

    @AfterAll
    static void afterClass() {
        casserole.shutdown();
    }

    @Test
    void getFeatureValue_should_hit_the_cache_for_second_call() {
        val featureValue = FeatureValueDTO.builder()
                .featureValue("test value")
                .featureKey("test key")
                .featureName("test name")
                .majorVersion(3)
                .minorVersion(1)
                .build();

        val featureKey = FeatureKeyDTO.builder()
                .featureKey(featureValue.getFeatureKey())
                .featureName(featureValue.getFeatureName())
                .majorVersion(featureValue.getMajorVersion())
                .minorVersion(featureValue.getMinorVersion())
                .build();

        featureValueDao.createOrUpdate(controlPlaneMapper.toModel(featureValue));

        val featureValueFromDb = featureValueServiceWithCache
                .getFeatureValue(featureKey)
                .join()
                .orElse(null);
        assertThat(featureValueFromDb).isNotNull();

        Mockito.verify(cacherole, never()).get(eq(featureKey.getCacheKey()), eq(FeatureValueDTO.class));

        Optional<FeatureValueDTO> featureValueFromCache = cacherole.get(featureKey.getCacheKey(), FeatureValueDTO.class);
        assertThat(featureValueFromCache.isPresent()).isTrue();
        featureValueFromCache.toJavaUtil()
                .ifPresent(aFeatureValue -> assertThat(aFeatureValue).isEqualTo(featureValueFromDb));

        val featureValueSecondCall = featureValueServiceWithCache
                .getFeatureValue(featureKey)
                .join()
                .orElse(null);
        assertThat(featureValueSecondCall).isNotNull();

        Mockito.verify(cacherole, times(1)).get(eq(featureKey.getCacheKey()), eq(FeatureValueDTO.class));
    }

    @Test
    void getMany_should_get_all_the_features_from_cache_for_second_invocation() {
        val firstFeatureValue = FeatureValueDTO.builder()
                .featureValue("test value 1")
                .featureKey("test key 1")
                .featureName("test name 1")
                .majorVersion(3)
                .minorVersion(1)
                .build();
        val secondFeatureValue = FeatureValueDTO.builder()
                .featureValue("test value 2")
                .featureKey("test key 2")
                .featureName("test name 2")
                .majorVersion(4)
                .minorVersion(2)
                .build();

        List.of(firstFeatureValue, secondFeatureValue)
                .map(controlPlaneMapper::toModel)
                .forEach(featureValueDao::createOrUpdate);

        Map<FeatureKeyDTO, FeatureValueDTO> featureValuesFromDatabase = featureValueServiceWithCache.getMany(List.of(firstFeatureKey, secondFeatureKey));
        assertThat(featureValuesFromDatabase.size()).isEqualTo(2);

        ArgumentCaptor<String> cacheKeyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<FeatureValueDTO> cacheValueCaptor = ArgumentCaptor.forClass(FeatureValueDTO.class);
        Mockito.verify(cacherole, times(2)).putAsync(cacheKeyCaptor.capture(), cacheValueCaptor.capture());

        assertThat(cacheKeyCaptor.getAllValues()).containsExactlyInAnyOrder(firstFeatureKey.getCacheKey(), secondFeatureKey.getCacheKey());

        Mockito.reset(featureValueServiceSpy);
        Map<FeatureKeyDTO, FeatureValueDTO> featureValuesFromCache = featureValueServiceWithCache.getMany(List.of(firstFeatureKey, secondFeatureKey));
        assertThat(featureValuesFromCache.values()).containsExactlyInAnyOrder(featureValuesFromDatabase.values().toJavaArray(FeatureValueDTO[]::new));
    }


    @Test
    void getMany_should_get_features_from_cache_and_from_database() {
        val firstFeatureValue = FeatureValueDTO.builder()
                .featureValue("test value 11")
                .featureKey("test key 11")
                .featureName("test name 11")
                .majorVersion(3)
                .minorVersion(1)
                .build();
        val secondFeatureValue = FeatureValueDTO.builder()
                .featureValue("test value 22")
                .featureKey("test key 22")
                .featureName("test name 22")
                .majorVersion(4)
                .minorVersion(2)
                .build();

        featureValueServiceWithCache.createOrUpdateFeatureValue(firstFeatureValue).join();
        featureValueDao.createOrUpdate(controlPlaneMapper.toModel(secondFeatureValue));

        val firstFeatureKey = FeatureKeyDTO.builder()
                .featureKey(firstFeatureValue.getFeatureKey())
                .featureName(firstFeatureValue.getFeatureName())
                .majorVersion(firstFeatureValue.getMajorVersion())
                .minorVersion(firstFeatureValue.getMinorVersion())
                .build();

        val secondFeatureKey = FeatureKeyDTO.builder()
                .featureKey(secondFeatureValue.getFeatureKey())
                .featureName(secondFeatureValue.getFeatureName())
                .majorVersion(secondFeatureValue.getMajorVersion())
                .minorVersion(secondFeatureValue.getMinorVersion())
                .build();

        Map<FeatureKeyDTO, FeatureValueDTO> featureValuesFromDatabase = featureValueServiceWithCache.getMany(List.of(firstFeatureKey, secondFeatureKey));
        assertThat(featureValuesFromDatabase.size()).isEqualTo(2);

        ArgumentCaptor<String> cacheKeyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<FeatureValueDTO> cacheValueCaptor = ArgumentCaptor.forClass(FeatureValueDTO.class);
        Mockito.verify(cacherole, times(1)).putAsync(cacheKeyCaptor.capture(), cacheValueCaptor.capture());

        assertThat(cacheKeyCaptor.getAllValues()).containsExactlyInAnyOrder(secondFeatureKey.getCacheKey());
        Mockito.verify(featureValueServiceSpy, times(1)).getMany(any());
    }

    @Test
    void createOrUpdateFeatureValue_should_store_features_into_cache() {
        val firstFeatureValue = FeatureValueDTO.builder()
                .featureValue("test value 1")
                .featureKey("test key 1")
                .featureName("test name 1")
                .majorVersion(3)
                .minorVersion(1)
                .build();
        featureValueServiceWithCache.createOrUpdateFeatureValue(firstFeatureValue).join();

        val featureValueFromCache = cacherole.get(firstFeatureValue.getCacheKey(), FeatureValueDTO.class);
        assertThat(featureValueFromCache.isPresent()).isTrue();
        featureValueFromCache.toJavaUtil()
                .ifPresent(featureValue -> assertThat(featureValue).isEqualTo(firstFeatureValue));
    }

    @Test
    void deleteFeatureValue_should_evict_cache() {
        val firstFeatureValue = FeatureValueDTO.builder()
                .featureValue("test value 1")
                .featureKey("test key 1")
                .featureName("test name 1")
                .majorVersion(3)
                .minorVersion(1)
                .build();
        featureValueServiceWithCache.createOrUpdateFeatureValue(firstFeatureValue).join();

        val featureKey = FeatureKeyDTO
                .builder()
                .featureName(firstFeatureValue.getFeatureName())
                .featureKey(firstFeatureValue.getFeatureKey())
                .majorVersion(firstFeatureValue.getMajorVersion())
                .minorVersion(firstFeatureValue.getMinorVersion())
                .build();

        featureValueServiceWithCache.deleteFeatureValue(featureKey);

        assertThat(cacherole.get(featureKey.getCacheKey(), FeatureValueDTO.class).isPresent()).isFalse();
    }

    @Test
    void deleteMany_should_evict_cache() {
        val firstFeatureValue = FeatureValueDTO.builder()
                .featureValue("test value 1")
                .featureKey("test key 1")
                .featureName("test name 1")
                .majorVersion(3)
                .minorVersion(1)
                .build();
        val secondFeatureValue = FeatureValueDTO.builder()
                .featureValue("test value 2")
                .featureKey("test key 2")
                .featureName("test name 2")
                .majorVersion(4)
                .minorVersion(2)
                .build();

        List.of(firstFeatureValue, secondFeatureValue)
                .map(controlPlaneMapper::toModel)
                .forEach(featureValueDao::createOrUpdate);


        val firstFeatureKey = FeatureKeyDTO.builder()
                .featureKey(firstFeatureValue.getFeatureKey())
                .featureName(firstFeatureValue.getFeatureName())
                .majorVersion(firstFeatureValue.getMajorVersion())
                .minorVersion(firstFeatureValue.getMinorVersion())
                .build();

        val secondFeatureKey = FeatureKeyDTO.builder()
                .featureKey(secondFeatureValue.getFeatureKey())
                .featureName(secondFeatureValue.getFeatureName())
                .majorVersion(secondFeatureValue.getMajorVersion())
                .minorVersion(secondFeatureValue.getMinorVersion())
                .build();

        featureValueServiceWithCache.deleteMany(List.of(firstFeatureKey, secondFeatureKey));

        List.of(firstFeatureKey, secondFeatureKey)
                .toStream()
                .map(featureKey -> cacherole.get(featureKey.getCacheKey(), FeatureValueDTO.class))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .forEach(featureKey -> assertThat(featureKey).isIn(List.of(firstFeatureKey, secondFeatureKey)));
    }

    @Test
    void getCachedFeatureKeys_should_return_non_empty() {

        val featureValues = List.ofAll(
                IntStream.range(0, 60).mapToObj(i -> FeatureValueDTO.builder()
                                .featureValue("test_value_" + i)
                                .featureKey("test_key_" + i)
                                .featureName("test_name_" + i)
                                .majorVersion(i + 2)
                                .minorVersion(i)
                                .build())
                        .collect(Collectors.toList()));

        featureValueServiceWithCache.createOrUpdateMany(featureValues.take(50));
        featureValueDao.createOrUpdateMany(featureValues.takeRight(10).map(controlPlaneMapper::toModel).asJava());

        var cachedFeatureKeys = ((FeatureValueServiceWithCache) featureValueServiceWithCache)
                .getCachedFeatureKeys(featureValues.map(FeatureValueDTO::toFeatureKey));
        assertThat(cachedFeatureKeys.isEmpty()).isFalse();
        assertThat(cachedFeatureKeys.size()).isEqualTo(50);
    }

    @Test
    void getCachedFeatureKeys_should_return_empty() {
        var cachedFeatureKeys = ((FeatureValueServiceWithCache) featureValueServiceWithCache).getCachedFeatureKeys(List.of(firstFeatureKey, secondFeatureKey));
        assertThat(cachedFeatureKeys.isEmpty()).isTrue();
    }

    private Cacherole getCacherole() {
        return internalFeaturesCacheroleWithMemcached(memcachedContainer.getMappedPort(MEMCACHED_PORT));
    }
}

package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByFlowSetDao;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByMarketDao;
import com.grubhub.garcon.controlplaneapi.models.BucketingMode;
import com.grubhub.garcon.guice.PersistenceTestModule;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByFlowSet;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.config.MealTimeByTimeOfDayConfig;
import com.grubhub.garcon.controlplane.dto.FlowResponseContext;
import com.grubhub.garcon.controlplane.dto.FlowWithGeoHashDTO;
import com.grubhub.garcon.controlplane.eventhub.ControlPlaneLogger;
import com.grubhub.garcon.controlplane.guice.ControlTestModule;
import com.grubhub.garcon.controlplane.mapper.VavrMapper;
import com.grubhub.garcon.controlplane.services.common.service.CachedConfigurationService;
import com.grubhub.garcon.controlplane.services.controlplane.FlowByGeoHashService;
import com.grubhub.garcon.controlplane.services.controlplane.FlowRoutingGroupsService;
import com.grubhub.garcon.controlplane.services.controlplane.MarketService;
import com.grubhub.garcon.controlplane.util.SessionAttributeProvider;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowRoutingGroupApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.service.controlplane.FlowService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.EnsembleService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.ensembler.cassandra.dao.EntityCollectionDao;
import com.grubhub.garcon.ensembler.cassandra.models.EntityCollection;
import com.grubhub.garcon.grpc.GrpcChannelFactory;
import com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelConfiguration;
import com.grubhub.garcon.modelinteraction.tensorflow.TfsClusterConfig;
import com.grubhub.garcon.shared.CassandraTestCase;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.test.cassandra.EmbeddedCassandraExt;
import com.grubhub.roux.uuid.UuidUtil;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Stream;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import name.falgout.jeffrey.testing.junit.guice.GuiceExtension;
import name.falgout.jeffrey.testing.junit.guice.IncludeModule;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.validation.Validation;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.google.common.collect.Sets.newHashSet;
import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.ROUTING_GROUPS_ENSEMBLE_NAME;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlow;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowForCriteria;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowWithEmptyLocationMarkets;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.MarketFactory.buildChicagoMarket;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.MarketFactory.buildGlobalMarket;
import static com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory.createModelWithName;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.DATA;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.initCasserole;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

@ExtendWith({MockitoExtension.class, GuiceExtension.class})
@IncludeModule({PersistenceTestModule.class, ControlTestModule.class})
@Slf4j
@CassandraTestCase
class FlowServiceImplTest {

    @Inject
    protected FlowConfig flowConfig;
    @Inject
    protected SessionAttributeProvider sessionAttributeProvider;
    @Inject
    protected JavaDateTimeHelper dateTimeHelper;
    @Inject
    ControlPlaneLogger controlPlaneLogger;
    @Mock
    GrpcChannelFactory grpcChannelFactory;
    @Inject
    CachedConfigurationService cachedConfigurationService;
    @Inject
    FlowByGeoHashService flowByGeoHashService;
    @Inject
    FlowRoutingGroupsService flowRoutingGroupsService;
    @Inject
    MarketService marketService;
    @Inject
    ModelService modelService;
    @Inject
    FlowService flowService;
    @Inject
    EnsembleService ensembleService;
    @Inject
    FlowByMarketDao flowByMarketDao;
    @Inject
    FlowByFlowSetDao flowByFlowSetDao;
    @Inject
    EntityCollectionDao entityCollectionDao;
    @Inject
    TensorFlowModelConfiguration tensorFlowModelConfiguration;
    @Inject
    MealTimeByTimeOfDayConfig mealTimeByTimeOfDayConfig;

    @RegisterExtension
    public static final EmbeddedCassandraExt EMBEDDED_CASSANDRA = new EmbeddedCassandraExt(DATA);
    protected static Casserole casserole;

    @BeforeAll
    static void beforeClass() {
        casserole = initCasserole(EMBEDDED_CASSANDRA.getPort());
    }

    @BeforeEach
    void setUp() {
        openMocks(this);
        reset(sessionAttributeProvider, flowConfig, grpcChannelFactory, controlPlaneLogger, flowConfig);
        lenient().when(grpcChannelFactory.fetchMultiEurekaChannels(any())).thenReturn(Collections.emptyMap());
        lenient().when(flowConfig.getFlowSetAsStream()).thenReturn(Stream.empty());
        lenient().when(flowConfig.getConfigurationCaching()).thenReturn(false);
        tensorFlowModelConfiguration.setTfsClusterConfigs(HashMap.of("search", new TfsClusterConfig()).toJavaMap());
        cachedConfigurationService.cleanAllCache();
    }

    @AfterEach
    void afterEach() {
        cachedConfigurationService.cleanAllCache();
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }

    @AfterAll
    static void afterClass() {
        casserole.shutdown();
    }

    @Test
    void flowApi_shouldCorrectlyValidate() {
        val validator = Validation.buildDefaultValidatorFactory().getValidator();
        val flow = createFlowForCriteria();
        assertThat(validator.validate(flow)).isEmpty();
    }

    @Test
    void createFlow_should_fail_when_flow_id_is_not_a_valid_UUID() {
        marketService.createOrUpdateMarket(buildChicagoMarket());

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());

        val flow = FlowApi.builder()
                .flowId("********")
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(ImmutableSet.of("DELIVERY", "CATERING"))
                .matchingMealtime(ImmutableSet.of("breakfast", "dinner"))
                .matchingQueryTypes(ImmutableSet.of("INTENTFUL"))
                .matchingApplications(ImmutableSet.of("umami", "iOS Native"))
                .matchingQueryTokens(ImmutableList.of("pizza", "pasta"))
                .locationMarkets(ImmutableSet.of("manhattan"))
                .matchingDinerTypes(ImmutableSet.of("dish"))
                .routingGroupsCriteria(getNewDefaultRoutingGroups())
                .priority(1)
                .build();

        assertThatThrownBy(() -> flowService.createOrUpdateFlow(flow))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("Invalid UUID string: ********");
    }

    @Test
    void createFlow_should_fail_when_routing_groups_are_not_valid() {
        marketService.createOrUpdateMarket(buildChicagoMarket());

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());

        val flow = FlowApi.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(ImmutableSet.of("DELIVERY", "CATERING"))
                .matchingMealtime(ImmutableSet.of("breakfast", "dinner"))
                .matchingQueryTypes(ImmutableSet.of("INTENTFUL"))
                .matchingApplications(ImmutableSet.of("umami", "iOS Native"))
                .matchingQueryTokens(ImmutableList.of("pizza", "pasta"))
                .locationMarkets(ImmutableSet.of("manhattan"))
                .matchingDinerTypes(ImmutableSet.of("dish"))
                .routingGroupsCriteria(new java.util.HashMap<>() {{
                    put("default", ImmutableList.of(
                            FlowRoutingGroupApi
                                    .builder()
                                    .groupName("group_50")
                                    .routingPercentage(0.4f)
                                    .ensembleName("search_m1_m2")
                                    .ensembleWeight("default")
                                    .build(),
                            FlowRoutingGroupApi
                                    .builder()
                                    .groupName("default_group")
                                    .routingPercentage(0.6f)
                                    .ensembleName("search_m1_m3")
                                    .ensembleWeight("default")
                                    .build()
                    ));
                }})
                .priority(1)
                .build();

        assertThatThrownBy(() -> flowService.createOrUpdateFlow(flow))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("For flow_name=SEARCH, ensemble_name=search_m1_m3 does not exist in database.");
    }

    @Test
    void createFlow_should_fail_when_ensembles_are_not_valid() {
        marketService.createOrUpdateMarket(buildChicagoMarket());

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());

        val flow = FlowApi.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(ImmutableSet.of("DELIVERY", "CATERING"))
                .matchingMealtime(ImmutableSet.of("breakfast", "dinner"))
                .matchingQueryTypes(ImmutableSet.of("INTENTFUL"))
                .matchingApplications(ImmutableSet.of("umami", "iOS Native"))
                .matchingQueryTokens(ImmutableList.of("pizza", "pasta"))
                .locationMarkets(ImmutableSet.of("manhattan"))
                .matchingDinerTypes(ImmutableSet.of("dish"))
                .routingGroupsCriteria(new java.util.HashMap<>() {{
                    put("default", ImmutableList.of(
                            FlowRoutingGroupApi
                                    .builder()
                                    .groupName("group_50")
                                    .routingPercentage(0.4f)
                                    .ensembleName("search_m1_m2")
                                    .ensembleWeight("default")
                                    .build(),
                            FlowRoutingGroupApi
                                    .builder()
                                    .groupName("default_group")
                                    .routingPercentage(0.6f)
                                    .ensembleName("search_m1_m2")
                                    .ensembleWeight("default2")
                                    .build()
                    ));
                }})
                .priority(1)
                .build();

        assertThatThrownBy(() -> flowService.createOrUpdateFlow(flow))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("For flow_name=SEARCH, ensemble_name=search_m1_m2 does not contain ensemble_weight=default2.");
    }

    @Test
    void createFlow_fail_should_when_location_markets_are_not_valid() {
        marketService.createOrUpdateMarket(buildChicagoMarket());

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());

        val flow = FlowApi.builder()
                .flowId(String.valueOf(UUID.randomUUID()))
                .flowSet("SEARCH")
                .flowName("SEARCH")
                .enabled(true)
                .matchingStrategy("LOCATION")
                .matchingOrderTypes(ImmutableSet.of("DELIVERY", "CATERING"))
                .matchingMealtime(ImmutableSet.of("breakfast", "dinner"))
                .matchingQueryTypes(ImmutableSet.of("INTENTFUL"))
                .matchingApplications(ImmutableSet.of("umami", "iOS Native"))
                .matchingQueryTokens(ImmutableList.of("pizza", "pasta"))
                .locationMarkets(ImmutableSet.of("manhattan"))
                .matchingDinerTypes(ImmutableSet.of("dish"))
                .routingGroupsCriteria(new java.util.HashMap<>() {{
                    put("default", Collections.emptyList());
                }})
                .priority(1)
                .build();

        assertThatThrownBy(() -> flowService.createOrUpdateFlow(flow))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("For flow name SEARCH location market manhattan is missing from database");
    }

    @Test
    void createFlow_should_apply_location_matching_strategy() {
        when(flowConfig.getPrecision()).thenReturn(5, 5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true, true);
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        flowService.createOrUpdateFlow(flow);

        val flowByGeoHash = flowByGeoHashService.select(flow.getFlowSet(), "dp3te");
        assertThat(flowByGeoHash.isDefined()).isTrue();
        val routingGroups = flowRoutingGroupsService.selectAllAsync(UuidUtil.decode(flow.getFlowId()))
                .getList();
        assertThat(routingGroups).isNotEmpty();
        assertThat(routingGroups.stream().map(FlowRoutingGroupV2::getGroupName).collect(Collectors.toList()))
                .containsExactlyInAnyOrder("default_group", "group_50");
    }

    @Test
    void createFlow_should_apply_default_matching_strategy() {
        marketService.createOrUpdateMarket(buildGlobalMarket());
        val flow = createFlowWithEmptyLocationMarkets();

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        flowService.createOrUpdateFlow(flow);

        val flowByGeoHash = flowByGeoHashService.select(flow.getFlowSet(), marketService.selectGlobalMarket().getMarketName());
        assertThat(flowByGeoHash.isDefined()).isTrue();
        val routingGroups = flowRoutingGroupsService.selectAllAsync(UuidUtil.decode(flow.getFlowId()))
                .getList();
        assertThat(routingGroups).isNotEmpty();
        assertThat(routingGroups.stream().map(FlowRoutingGroupV2::getGroupName).collect(Collectors.toList()))
                .containsExactlyInAnyOrder("default_group", "group_50");
    }

    @Test
    void createFlow_should_add_flow_to_flow_set_list() {
        val flow = createFlow();
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        flowService.createOrUpdateFlow(flow);

        val flowsByFlowSetList = flowByFlowSetDao.select(flow.getFlowSet());
        assertThat(flowsByFlowSetList.stream()
                .map(FlowByFlowSet::getFlowId)
                .filter(flowId -> flowId.equals(UuidUtil.decode(flow.getFlowId())))
                .toList()
                .size()).isEqualTo(1);
    }

    @Test
    void getFlow_should_return_empty_optional() {
        assertThat(flowService.getFlow(UUID.randomUUID())).isEmpty();
    }

    @Test
    void getFlow_should_return_non_empty_optional() {
        val flow = createFlow();
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        flowService.createOrUpdateFlow(flow);

        Optional<FlowApi> savedFlowAsOptional = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(savedFlowAsOptional.isPresent()).isTrue();
        savedFlowAsOptional.ifPresent(savedFlow -> {
            assertThat(savedFlow.getLocationMarkets()).containsExactlyInAnyOrderElementsOf(newHashSet("chicago"));
            assertThat(savedFlow.getMatchingDinerTypes()).containsExactlyInAnyOrderElementsOf(newHashSet("dish"));
            assertThat(savedFlow.getMatchingMealtime()).containsExactlyInAnyOrderElementsOf(newHashSet("breakfast", "dinner"));
            assertThat(savedFlow.getMatchingQueryTypes()).containsExactlyInAnyOrderElementsOf(newHashSet("INTENTFUL"));
            assertThat(savedFlow.getMatchingOrderTypes()).containsExactlyInAnyOrderElementsOf(newHashSet("DELIVERY", "CATERING"));
            assertThat(savedFlow.getMatchingApplications()).containsExactlyInAnyOrderElementsOf(newHashSet("umami", "iOS Native"));
        });
    }

    @Test
    void updateFlow_should_update_successfully() {
        val flow = createFlow();
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        flowService.createOrUpdateFlow(flow);

        Optional<FlowApi> savedFlowAsOptional = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(savedFlowAsOptional.isPresent()).isTrue();
        savedFlowAsOptional.ifPresent(savedFlow -> {
            val updatedFlow = createFlow();
            updatedFlow.setFlowId(flow.getFlowId());
            updatedFlow.setLocationMarkets(ImmutableSet.of("chicago", "new york"));
            updatedFlow.setMatchingDinerTypes(ImmutableSet.of("ribbon"));
            updatedFlow.setMatchingMealtime(ImmutableSet.of("midday"));
            updatedFlow.setMatchingQueryTypes(ImmutableSet.of("DEFAULT"));
            updatedFlow.setMatchingOrderTypes(ImmutableSet.of("PICKUP"));
            updatedFlow.setMatchingApplications(ImmutableSet.of("Android Native"));
            flowService.updateFlow(savedFlow, updatedFlow);
        });

        Optional<FlowApi> updatedFlowAsOptional = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(updatedFlowAsOptional.isPresent()).isTrue();
        updatedFlowAsOptional.ifPresent(updatedFlow -> {
            assertThat(updatedFlow.getLocationMarkets()).containsExactlyInAnyOrderElementsOf(ImmutableSet.of("chicago", "new york"));
            assertThat(updatedFlow.getMatchingDinerTypes()).containsExactlyInAnyOrderElementsOf(ImmutableSet.of("ribbon"));
            assertThat(updatedFlow.getMatchingMealtime()).containsExactlyInAnyOrderElementsOf(ImmutableSet.of("midday"));
            assertThat(updatedFlow.getMatchingQueryTypes()).containsExactlyInAnyOrderElementsOf(ImmutableSet.of("DEFAULT"));
            assertThat(updatedFlow.getMatchingOrderTypes()).containsExactlyInAnyOrderElementsOf(ImmutableSet.of("PICKUP"));
            assertThat(updatedFlow.getMatchingApplications()).containsExactlyInAnyOrderElementsOf(ImmutableSet.of("Android Native"));
        });
    }

    @Test
    void updateFlow_should_update_routing_groups() {
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();

        flowService.createOrUpdateFlow(flow);

        Optional<FlowApi> savedFlowAsOptional = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(savedFlowAsOptional.isPresent()).isTrue();
        savedFlowAsOptional.ifPresent(savedFlow -> {
            FlowApi updatedFlow = createFlow();
            updatedFlow.setFlowId(flow.getFlowId());
            updatedFlow.setLocationMarkets(ImmutableSet.of("chicago", "new york"));
            updatedFlow.setMatchingDinerTypes(ImmutableSet.of("ribbon"));
            updatedFlow.setMatchingMealtime(ImmutableSet.of("midday"));
            updatedFlow.setMatchingQueryTypes(ImmutableSet.of("DEFAULT"));
            updatedFlow.setMatchingOrderTypes(ImmutableSet.of("PICKUP"));
            updatedFlow.setMatchingApplications(ImmutableSet.of("Android Native"));
            updatedFlow.setRoutingGroupsCriteria(getNewDefaultRoutingGroups());
            flowService.updateFlow(savedFlow, updatedFlow);
        });

        Optional<FlowApi> updatedFlowAsOptional = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(updatedFlowAsOptional.isPresent()).isTrue();
        updatedFlowAsOptional.ifPresent(updatedFlow -> {
            assertThat(updatedFlow.getLocationMarkets()).containsExactlyInAnyOrderElementsOf(newHashSet("chicago", "new york"));
            assertThat(updatedFlow.getMatchingDinerTypes()).containsExactlyInAnyOrderElementsOf(newHashSet("ribbon"));
            assertThat(updatedFlow.getMatchingMealtime()).containsExactlyInAnyOrderElementsOf(newHashSet("midday"));
            assertThat(updatedFlow.getMatchingQueryTypes()).containsExactlyInAnyOrderElementsOf(newHashSet("DEFAULT"));
            assertThat(updatedFlow.getMatchingOrderTypes()).containsExactlyInAnyOrderElementsOf(newHashSet("PICKUP"));
            assertThat(updatedFlow.getMatchingApplications()).containsExactlyInAnyOrderElementsOf(newHashSet("Android Native"));
            assertThat(updatedFlow.getRoutingGroupsCriteria()).hasSize(1);
            assertThat(updatedFlow.getRoutingGroupsCriteria().get("default")).hasSize(1);
            assertThat(updatedFlow.getRoutingGroupsCriteria().get("default").get(0).getGroupName()).isEqualTo("new_default");
        });
    }

    @Test
    void updateFlow_should_update_flow_by_flow_set() {
        val flow = createFlow();
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        flowService.createOrUpdateFlow(flow);

        Optional<FlowApi> savedFlowAsOptional = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(savedFlowAsOptional.isPresent()).isTrue();
        savedFlowAsOptional.ifPresent(savedFlow -> {
            val updatedFlow = createFlow();
            updatedFlow.setFlowId(flow.getFlowId());
            updatedFlow.setFlowSet("TOPICS");
            flowService.updateFlow(savedFlow, updatedFlow);
        });

        assertThat(flowByFlowSetDao.select(flow.getFlowSet()).stream()
                .map(FlowByFlowSet::getFlowId)
                .collect(Collectors.toList()))
                .doesNotContain(UuidUtil.decode(flow.getFlowId()));
        assertThat(flowByFlowSetDao.select("TOPICS").stream()
                .map(FlowByFlowSet::getFlowId)
                .collect(Collectors.toList()))
                .contains(UuidUtil.decode(flow.getFlowId()));
    }

    @Test
    void deleteFlow_should_delete_if_exists() {
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();
        flowService.createOrUpdateFlow(flow);
        assertThat(flowService.getFlow(UuidUtil.decode(flow.getFlowId())).isPresent()).isTrue();

        flowService.deleteFlow(UuidUtil.decode(flow.getFlowId()));

        assertThat(flowService.getFlow(UuidUtil.decode(flow.getFlowId()))).isEmpty();
    }

    @Test
    void deleteFlow_should_remove_flow_set_if_flow_set_has_no_more_flows() {
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();
        flow.setFlowSet("TestFlowSet");
        flowService.createOrUpdateFlow(flow);
        assertThat(flowService.getFlow(UuidUtil.decode(flow.getFlowId())).isPresent()).isTrue();
        val flowSetEntityCollection = entityCollectionDao.select(EntityCollection.Constants.COLLECTION_FLOW_SET, flow.getFlowSet());
        assertThat(flowSetEntityCollection.isDefined()).isTrue();

        flowService.deleteFlow(UuidUtil.decode(flow.getFlowId()));

        val flowSetEntityCollectionAfterDelete = entityCollectionDao.select(EntityCollection.Constants.COLLECTION_FLOW_SET, flow.getFlowSet());
        assertThat(flowSetEntityCollectionAfterDelete.isEmpty()).isTrue();
    }

    @Test
    void getAllFlowSets_gets_all_flowSets() {
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();
        val flow2 = createFlow();
        flow.setFlowSet("TestFlowSet");
        flow2.setFlowSet("TestFlowSet2");
        flowService.createOrUpdateFlow(flow);
        flowService.createOrUpdateFlow(flow2);
        assertEquals(flowService.getAllFlowSets().size(), 2);
        assertEquals(flowService.getAllFlowSets().get(0).getEntityId(), "TestFlowSet");
        assertEquals(flowService.getAllFlowSets().get(1).getEntityId(), "TestFlowSet2");
    }

    @Test
    void getAllFlowDetailed_gets_all_flows() {
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();
        val flow2 = createFlow();
        flow.setFlowSet("TestFlowSet");
        flow2.setFlowSet("TestFlowSet2");
        flowService.createOrUpdateFlow(flow);
        flowService.createOrUpdateFlow(flow2);
        val result = flowService.getAllFlowsDetailed(null);
        assertEquals(result.size(), 2);
        assertTrue(result.stream().anyMatch(x -> x.getFlowSet().equals("TestFlowSet")));
        assertTrue(result.stream().anyMatch(x -> x.getFlowSet().equals("TestFlowSet2")));
    }

    @Test
    void getAllFlowDetailed_gets_specific_flowSets() {
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();
        val flow2 = createFlow();
        val flow3 = createFlow();
        flow.setFlowSet("TestFlowSet");
        flow2.setFlowSet("TestFlowSet2");
        flow3.setFlowSet("TestFlowSet3");
        flowService.createOrUpdateFlow(flow);
        flowService.createOrUpdateFlow(flow2);
        flowService.createOrUpdateFlow(flow3);
        val result = flowService.getAllFlowsDetailed("TestFlowSet,TestFlowSet3");
        assertEquals(result.size(), 2);
        assertTrue(result.stream().anyMatch(x -> x.getFlowSet().equals("TestFlowSet")));
        assertTrue(result.stream().anyMatch(x -> x.getFlowSet().equals("TestFlowSet3")));
        assertTrue(result.stream().noneMatch(x -> x.getFlowSet().equals("TestFlowSet2")));
    }

    @Test
    void deleteFlow_should_remove_only_entry_for_current_flow() {
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();
        val secondFlow = createFlow();
        flowService.createOrUpdateFlow(flow);
        flowService.createOrUpdateFlow(secondFlow);
        assertThat(flowService.getFlow(UuidUtil.decode(flow.getFlowId())).isPresent()).isTrue();

        flowService.deleteFlow(UuidUtil.decode(flow.getFlowId()));

        flowByMarketDao.getFlowsByMarketAsync(flow.getFlowSet(), "chicago").toListCompletableFuture()
                .thenAccept(flowByMarkets -> {
                    assertThat(flowByMarkets).isNotEmpty();
                    assertThat(flowByMarkets.size()).isEqualTo(1);
                    assertThat(flowByMarkets.get(0).getFlowId()).isEqualTo(UUID.fromString(secondFlow.getFlowId()));
                })
                .join();

        flowByFlowSetDao.selectAsync(flow.getFlowSet()).toListCompletableFuture()
                .thenAccept(flowsByFlowSet -> {
                    assertThat(flowsByFlowSet).isNotEmpty();
                    assertThat(flowsByFlowSet.size()).isEqualTo(1);
                    assertThat(flowsByFlowSet.get(0).getFlowId()).isEqualTo(UUID.fromString(secondFlow.getFlowId()));
                })
                .join();

        assertThat(flowService.getFlow(UuidUtil.decode(flow.getFlowId()))).isEmpty();
    }


    @Test
    void configureFlowsGlobal_shouldReturnExpected() {
        int randomSeed = 123;

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();
        flow.setRoutingGroupsSeed(999);
        flow.setRoutingGroupsSeedRotation(List.of("5", "1", "2", "3", "4").asJava());
        flowService.createOrUpdateFlow(flow);

        flowService.configureFlowsGlobal(randomSeed);

        val updatedFlow = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(updatedFlow.get().getRoutingGroupsSeed()).isEqualTo(3);
    }

    @Test
    void configureFlowsGlobal_shouldUpdateFlowsWithRandomSeeds() {
        int randomSeed = 123;

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();
        flow.setRoutingGroupsSeedRotationEnabled(true);
        flow.setRoutingGroupsSeed(999);
        flowService.createOrUpdateFlow(flow);

        flowService.configureFlowsGlobal(randomSeed);

        val updatedFlow = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(updatedFlow.get().getRoutingGroupsSeed()).isNotEqualTo(999);
    }

    @Test
    void configureFlowsGlobal_shouldPreserveRoutingGroups() {
        int randomSeed = 123;
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();
        flow.setRoutingGroupsSeed(999);
        flow.setRoutingGroupsSeedRotation(List.of("5", "1", "2", "3", "4").asJava());
        flowService.createOrUpdateFlow(flow);

        flowService.configureFlowsGlobal(randomSeed);

        val updatedFlow = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(updatedFlow.get().getRoutingGroupsCriteria().get("default").size()).isEqualTo(2);
    }

    @Test
    void configureFlowsGlobal_whenManyElements_shouldUpdateSeed() {
        int randomSeed = 123;

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());

        List<FlowApi> flowApis = Stream.range(0, 5)
                .map(String::valueOf)
                .map(index -> {
                    FlowApi flow = createFlow();
                    flow.setRoutingGroupsSeed(999 + Integer.parseInt(index));
                    flow.setRoutingGroupsSeedRotation(List.of(index, index + 1, index + 2, index + 3, index + 4).asJava());
                    return flow;
                })
                .map(flow -> {
                    flowService.createOrUpdateFlow(flow);
                    return flow;
                })
                .map(flow -> flowService.getFlow(UuidUtil.decode(flow.getFlowId())).get())
                .collect(List.collector());

        flowService.configureFlowsGlobal(randomSeed);

        val updatedFlow1 = flowService.getFlow(UuidUtil.decode(flowApis.get(0).getFlowId()));
        assertThat(updatedFlow1.get().getRoutingGroupsSeed()).isEqualTo(3);

        val updatedFlow2 = flowService.getFlow(UuidUtil.decode(flowApis.get(1).getFlowId()));
        assertThat(updatedFlow2.get().getRoutingGroupsSeed()).isEqualTo(13);

        val updatedFlow3 = flowService.getFlow(UuidUtil.decode(flowApis.get(2).getFlowId()));
        assertThat(updatedFlow3.get().getRoutingGroupsSeed()).isEqualTo(23);
    }

    @Test
    void configureFlowsGlobal_whenEmptyGroupsSeedsRotation_shouldPreserveExistingSeed() {
        int randomSeed = 123;

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());
        val flow = createFlow();
        flow.setRoutingGroupsSeed(999);
        flowService.createOrUpdateFlow(flow);

        flowService.configureFlowsGlobal(randomSeed);

        val updatedFlow = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(updatedFlow.get().getRoutingGroupsSeed()).isEqualTo(999);
    }

    @Test
    void configureFlowsGlobal_shouldOnlyChangeSeed() {
        int randomSeed = 123;

        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        marketService.createOrUpdateMarket(buildChicagoMarket());

        val flow = createFlow();
        flow.setRoutingGroupsSeedRotationEnabled(true);
        flow.setRoutingGroupsSeed(999);
        flowService.createOrUpdateFlow(flow);
        val currentFlow = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));

        flowService.configureFlowsGlobal(randomSeed);

        val updatedFlow = flowService.getFlow(UuidUtil.decode(flow.getFlowId()));

        assertTrue(updatedFlow.isPresent());
        assertEquals(123, updatedFlow.get().getRoutingGroupsSeed());
        assertThat(updatedFlow)
                .usingRecursiveComparison()
                .ignoringFields("value.routingGroupsSeed", "value.updatedTimestamp")
                .isEqualTo(currentFlow);
    }

    @Test
    void deleteFlow_should_delete_using_global_market_empty_location_markets() {
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        val flow = createFlow();
        flow.setLocationMarkets(Collections.emptySet());

        flowService.createOrUpdateFlow(flow);
        assertThat(flowService.getFlow(UuidUtil.decode(flow.getFlowId())).isPresent()).isTrue();

        flowService.deleteFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(flowService.getFlow(UuidUtil.decode(flow.getFlowId()))).isEmpty();
        assertThat(flowByMarketDao.getFlowsByMarkets(flow.getFlowSet(), java.util.List.of(MarketDTO.GLOBAL_MARKET.getMarketName()))).isEmpty();
    }

    @Test
    void deleteFlow_should_delete_using_global_market_null_location_markets() {
        modelService.createOrUpdateModel(createModelWithName("search_m1_m2"));
        ensembleService.createOrUpdateEnsemble(createEnsemble());
        val flow = createFlow();
        flow.setLocationMarkets(null);

        flowService.createOrUpdateFlow(flow);
        assertThat(flowService.getFlow(UuidUtil.decode(flow.getFlowId())).isPresent()).isTrue();

        flowService.deleteFlow(UuidUtil.decode(flow.getFlowId()));
        assertThat(flowService.getFlow(UuidUtil.decode(flow.getFlowId()))).isEmpty();
        assertThat(flowByMarketDao.getFlowsByMarkets(flow.getFlowSet(), java.util.List.of(MarketDTO.GLOBAL_MARKET.getMarketName()))).isEmpty();
    }

    @Test
    void deleteFlow_should_do_nothing_if_flow_does_not_exists() {
        val flowId = UUID.randomUUID();
        assertThat(flowService.getFlow(flowId)).isEmpty();

        flowService.deleteFlow(flowId);

        assertThat(flowService.getFlow(flowId)).isEmpty();
    }

    EnsembleDTO createEnsemble() {
        return EnsembleDTO.builder()
                .models(VavrMapper.toVavrSet(ImmutableSet.of(ROUTING_GROUPS_ENSEMBLE_NAME)))
                .versioningStrategy("DYNAMIC")
                .version("123.123")
                .status("ENABLED")
                .ensembleName("search_m1_m2")
                .ensembleWeights(HashMap.of("default", HashMap.of("restaurant_id", 0.7f)))
                .ensembleDescription("LTR model")
                .build();
    }

    @Test
    public void selectRoutingGroups_test() {
        FlowRoutingGroupDTO firstRoutingGroup = FlowRoutingGroupDTO.builder()
                .groupName("group1")
                .groupOrder(2)
                .build();
        FlowRoutingGroupDTO secondRoutingGroup = FlowRoutingGroupDTO.builder()
                .groupName("group2")
                .groupOrder(0)
                .build();
        FlowRoutingGroupDTO thirdRoutingGroup = FlowRoutingGroupDTO.builder()
                .groupName("group3")
                .groupOrder(1)
                .build();

        List<FlowRoutingGroupDTO> routingGroups = List.of(firstRoutingGroup, secondRoutingGroup, thirdRoutingGroup);
        HashMap<String, List<FlowRoutingGroupDTO>> criteria = HashMap.of("default", routingGroups);

        final FlowDTO flowDTO = FlowDTO.builder()
                .flowSet("TestFlowSet")
                .flowId("********-9998-0000-0000-********0001")
                .flowName("Test geohash flow")
                .routingGroupsBucketingMode(BucketingMode.GEOHASH)
                .routingGroupsCriteria(criteria)
                .build();

        final FlowWithGeoHashDTO flowWithGeoHashDTO = FlowWithGeoHashDTO.builder()
                .flow(flowDTO)
                .build();

        FlowResponseContext actualContext = ((FlowServiceImpl) flowService).selectRoutingGroups(flowWithGeoHashDTO);

        assertThat(actualContext).isNotNull();
        assertThat(actualContext.getFlowRoutingGroups().size()).isEqualTo(3);
        assertThat(actualContext.getFlowRoutingGroups().get(0).getGroupName()).isEqualTo("group2");
        assertThat(actualContext.getFlowRoutingGroups().get(1).getGroupName()).isEqualTo("group3");
        assertThat(actualContext.getFlowRoutingGroups().get(2).getGroupName()).isEqualTo("group1");
    }

    @NotNull
    private static Map<String, java.util.List<FlowRoutingGroupApi>> getNewDefaultRoutingGroups() {
        java.util.List<FlowRoutingGroupApi> routingGroupApis = Collections.singletonList(FlowRoutingGroupApi
                .builder()
                .groupName("new_default")
                .routingPercentage(1)
                .variation("exploreExploit")
                .ensembleName("search_m1_m2")
                .ensembleWeight("default")
                .routingGroupCriteria("default")
                .build());

        Map<String, java.util.List<FlowRoutingGroupApi>> defaultGroup = new java.util.HashMap<>();
        defaultGroup.put("default", routingGroupApis);
        return defaultGroup;
    }

}

package com.grubhub.garcon.controlplane.modelinteraction;

import com.grubhub.garcon.modelinteraction.DistanceFalloffModel;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;

public class DistanceFalloffModelTest {

    private static final double OFFICE_LAT = 40.753291;
    private static final double OFFICE_LNG = -73.985990;
    private static final double REF_LAT = 40.6660817;
    private static final double REF_LNG = -73.9779453;

    private static final double  restaurantThreshold = 0.5;
    private static final double  dinerDistanceThreshold = 3.0;
    private static final double trafficLevelMultiplier = 0.0;
    private static final double minWeight = 0.01;
    private static final double thresholdScalar = 0.001;
    private static final double dinerBias = 0.5;
    private static final double minBiasWeight = 0.0;
    private static final double minBias = 0.8;

    private DistanceFalloffModel model;

    @BeforeEach
    public void setUp() {
        model = new DistanceFalloffModel();
    }

    @Test
    public void arcDistance() {
        val dist = model.arcDistanceMeters(OFFICE_LAT, OFFICE_LNG, 40.956291, -73.985990);
        // Expecting 22572m
        assertThat(dist).isEqualTo(22572.0, within(1.0));
    }

    @Test
    public void arcDistanceZeroDistance() {
        val lat = 37.77665328;
        val lng = -122.40252686;
        val dist = model.arcDistanceMeters(lat, lng, lat, lng);
        // Expecting 0
        assertThat(dist).isEqualTo(0.0);
    }

    @Test
    public void falloffBaseline() {
        val decayRate = 0.8;
        val quality = 1.0;
        val qualityExponent = 0; // 0 turns off quality
        val distanceExponent = 1.0; // 1 turns off distance exponent
        val dist = doDistanceFalloff(decayRate, quality, qualityExponent, distanceExponent);
        assertThat(dist).isEqualTo(0.07027833658116368, within(0.001));
    }

    @Test
    public void falloffAggressive() {
        val decayRate = 0.6;
        val quality = 1.0;
        val qualityExponent = 0.25; // activates steep falloff
        val distanceExponent = 1.5;
        val dist = doDistanceFalloff(decayRate, quality, qualityExponent, distanceExponent);
        assertThat(dist).isEqualTo(0.01, within(0.001));
    }

    @Test
    public void falloffSoft() {
        val decayRate = 0.9;
        val quality = 1.0;
        val qualityExponent = 0.1;
        val distanceExponent = 0.5;
        val dist = doDistanceFalloff(decayRate, quality, qualityExponent, distanceExponent);
        assertThat(dist).isEqualTo(0.5342627357177892, within(0.001));
    }

    private double doDistanceFalloff(double decayRate, double quality, double qualityExponent, double distanceExponent) {
        return model.distanceFalloff(OFFICE_LAT, OFFICE_LNG, REF_LAT, REF_LNG, restaurantThreshold, dinerDistanceThreshold,
                decayRate, trafficLevelMultiplier, minWeight, thresholdScalar, dinerBias, minBiasWeight, minBias, quality,
                qualityExponent, distanceExponent);
    }

}

package com.grubhub.garcon.controlplane.domainstreams;

import com.grubhub.garcon.controlplane.metrics.MetricsProvider;
import com.grubhub.garcon.controlplane.services.controlplane.domainstreams.ConditionalModelInferenceSequenceItemStream;
import com.grubhub.garcon.controlplane.services.controlplane.factories.ModelInferenceSequenceItemStreamFactory;
import com.grubhub.garcon.controlplane.services.controlplane.sequential.SequentialExecutionProcessor;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.*;
import lombok.val;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

import java.util.*;
import java.util.function.Function;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ConditionalModelInferenceSequenceItemStreamTest {
    private final SequentialExecutionProcessor sequentialExecutionProcessor = mock(SequentialExecutionProcessor.class);
    private final MetricsProvider metricsProvider = mock(MetricsProvider.class);

    private final ArgumentCaptor<Map<String, Object>> contextCapture = ArgumentCaptor.forClass(Map.class);

    private final ModelInferenceSequenceItemStreamFactory modelInferenceSequenceItemStreamFactory =
            new ModelInferenceSequenceItemStreamFactory(sequentialExecutionProcessor, metricsProvider);

    @Test
    public void invokeModelInferenceMultiOutput_should_executeAllModels() {
        ModelInferenceSequenceRequest request = ModelInferenceSequenceRequest.builder()
                .callerTrackingId("callerTrackingId")
                .invocations(Arrays.asList(
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_a_v1.0")
                                .features(List.of(new HashMap<>() {{
                                    put("features_a_1", List.of(1, 2, 3));
                                }}))
                                .globalFeatures(new HashMap<>() {{
                                    put("global_a_1", 1);
                                }})
                                .preCondition(new ExecutionCondition("", null))
                                .build(),
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_b_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build(),
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_c_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build()
                ))
                .build();
        val target = modelInferenceSequenceItemStreamFactory.create(request);
        Assertions.assertThat(target.getClass()).isEqualTo(ConditionalModelInferenceSequenceItemStream.class);

        val tracker = new InvocationsTracker();
        when(sequentialExecutionProcessor.shouldExecute(any(), contextCapture.capture())).thenReturn(true);

        target.invokeModelInferenceMultiOutput(createDummyInvocationFunction(tracker));

        Assertions.assertThat(tracker.modelsInvoked).isNotEmpty();
        Assertions.assertThat(tracker.modelsInvoked.size()).isEqualTo(3);
        Assertions.assertThat(contextCapture.getValue()).isNotEmpty();
        Assertions.assertThat(contextCapture.getValue().size()).isEqualTo(9);
        Assertions.assertThat(contextCapture.getValue().get("model_a.EXECUTION.success")).isEqualTo(true);
        Assertions.assertThat(contextCapture.getValue().get("model_b.EXECUTION.success")).isEqualTo(true);
        Assertions.assertThat(contextCapture.getValue().get("model_c.EXECUTION.success")).isEqualTo(true);
    }


    @Test
    public void invokeModelInferenceMultiOutput_should_execute2OutOf3Models() {
        ModelInferenceSequenceRequest request = ModelInferenceSequenceRequest.builder()
                .callerTrackingId("callerTrackingId")
                .invocations(Arrays.asList(
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_a_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build(),
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_b_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build(),
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_c_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build()
                ))
                .build();
        val target = modelInferenceSequenceItemStreamFactory.create(request);
        Assertions.assertThat(target.getClass()).isEqualTo(ConditionalModelInferenceSequenceItemStream.class);

        val tracker = new InvocationsTracker();
        when(sequentialExecutionProcessor.shouldExecute(any(), contextCapture.capture())).thenReturn(true, true, false);

        target.invokeModelInferenceMultiOutput(createDummyInvocationFunction(tracker));

        Assertions.assertThat(tracker.modelsInvoked).isNotEmpty();
        Assertions.assertThat(tracker.modelsInvoked.size()).isEqualTo(2);
        Assertions.assertThat(contextCapture.getValue()).isNotEmpty();
        Assertions.assertThat(contextCapture.getValue().size()).isEqualTo(8);
        Assertions.assertThat(contextCapture.getValue().get("model_a.EXECUTION.success")).isEqualTo(true);
        Assertions.assertThat(contextCapture.getValue().get("model_b.EXECUTION.success")).isEqualTo(true);
        Assertions.assertThat(contextCapture.getValue().get("model_c.EXECUTION.success")).isEqualTo(false);

        Assertions.assertThat(contextCapture.getValue().get("model_a.EXECUTION.skipped")).isEqualTo(false);
        Assertions.assertThat(contextCapture.getValue().get("model_b.EXECUTION.skipped")).isEqualTo(false);
        Assertions.assertThat(contextCapture.getValue().get("model_c.EXECUTION.skipped")).isEqualTo(true);
    }

    @Test
    public void invokeModelInferenceMultiOutput_should_notExecuteModels() {
        ModelInferenceSequenceRequest request = ModelInferenceSequenceRequest.builder()
                .callerTrackingId("callerTrackingId")
                .invocations(Arrays.asList(
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_a_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build(),
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_b_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build(),
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_c_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build()
                ))
                .build();
        val target = modelInferenceSequenceItemStreamFactory.create(request);
        Assertions.assertThat(target.getClass()).isEqualTo(ConditionalModelInferenceSequenceItemStream.class);

        val tracker = new InvocationsTracker();
        when(sequentialExecutionProcessor.shouldExecute(any(), contextCapture.capture())).thenReturn(false, false, false);

        target.invokeModelInferenceMultiOutput(createDummyInvocationFunction(tracker));

        Assertions.assertThat(tracker.modelsInvoked).isEmpty();
        Assertions.assertThat(contextCapture.getValue()).isNotEmpty();
        Assertions.assertThat(contextCapture.getValue().size()).isEqualTo(6);
        Assertions.assertThat(contextCapture.getValue().get("model_a.EXECUTION.skipped")).isEqualTo(true);
        Assertions.assertThat(contextCapture.getValue().get("model_b.EXECUTION.skipped")).isEqualTo(true);
        Assertions.assertThat(contextCapture.getValue().get("model_c.EXECUTION.skipped")).isEqualTo(true);

        Assertions.assertThat(contextCapture.getValue().get("model_a.EXECUTION.success")).isEqualTo(false);
        Assertions.assertThat(contextCapture.getValue().get("model_b.EXECUTION.success")).isEqualTo(false);
        Assertions.assertThat(contextCapture.getValue().get("model_c.EXECUTION.success")).isEqualTo(false);
    }

    @Test
    public void invokeModelInferenceMultiOutput_should_addOutputsToTheContext() {
        ModelInferenceSequenceRequest request = ModelInferenceSequenceRequest.builder()
                .callerTrackingId("callerTrackingId")
                .invocations(Arrays.asList(
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_a_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build(),
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_b_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build(),
                        ModelInferenceSequenceItem.builder()
                                .modelName("model_c_v1.0")
                                .preCondition(new ExecutionCondition("", null))
                                .build()
                ))
                .build();
        val target = modelInferenceSequenceItemStreamFactory.create(request);
        Assertions.assertThat(target.getClass()).isEqualTo(ConditionalModelInferenceSequenceItemStream.class);

        val tracker = new InvocationsTracker();
        when(sequentialExecutionProcessor.shouldExecute(any(), contextCapture.capture())).thenReturn(true, true, false);

        target.invokeModelInferenceMultiOutput(createInvocationFunction(tracker, Arrays.asList(
                new HashMap<>() {{
                    put("output_a", io.vavr.collection.List.of(ModelInferenceOutputType.of(10), ModelInferenceOutputType.of(15)));
                }},
                new HashMap<>() {{
                    put("output_b", io.vavr.collection.List.of(ModelInferenceOutputType.of("string_1"), ModelInferenceOutputType.of("string_2")));
                }},
                new HashMap<>() {{
                    put("output_c", io.vavr.collection.List.of(ModelInferenceOutputType.of(10.), ModelInferenceOutputType.of(20.)));
                }}
        )));

        Assertions.assertThat(tracker.modelsInvoked).isNotEmpty();
        Assertions.assertThat(contextCapture.getValue()).isNotEmpty();
        Assertions.assertThat(contextCapture.getValue().size()).isEqualTo(8);
        Assertions.assertThat(contextCapture.getValue().get("model_a.EXECUTION.success")).isEqualTo(true);
        Assertions.assertThat(contextCapture.getValue().get("model_b.EXECUTION.success")).isEqualTo(true);
        Assertions.assertThat(contextCapture.getValue().get("model_c.EXECUTION.skipped")).isEqualTo(true);
        Assertions.assertThat(contextCapture.getValue().get("model_c.EXECUTION.success")).isEqualTo(false);
        Assertions.assertThat(contextCapture.getValue().containsKey("model_a.OUTPUTS.output_a")).isTrue();
        Assertions.assertThat(contextCapture.getValue().containsKey("model_b.OUTPUTS.output_b")).isTrue();
        Assertions.assertThat(contextCapture.getValue().containsKey("model_c.OUTPUTS.output_c")).isFalse();

        Assertions.assertThat(contextCapture.getValue().get("model_a.OUTPUTS.output_a")).isInstanceOf(List.class);
        Assertions.assertThat(contextCapture.getValue().get("model_b.OUTPUTS.output_b")).isInstanceOf(List.class);

        List<Long> o1 = (List<Long>) contextCapture.getValue().get("model_a.OUTPUTS.output_a");
        Assertions.assertThat(o1.size()).isEqualTo(2);
        Assertions.assertThat(o1.get(0)).isEqualTo(10);
        Assertions.assertThat(o1.get(1)).isEqualTo(15);

        List<String> o2 = (List<String>) contextCapture.getValue().get("model_b.OUTPUTS.output_b");
        Assertions.assertThat(o2.size()).isEqualTo(2);
        Assertions.assertThat(o2.get(0)).isEqualTo("string_1");
        Assertions.assertThat(o2.get(1)).isEqualTo("string_2");
    }


    // Returns one value in the response, so models executions should be successful
    private Function<ModelInferenceRequest, ModelInferenceOutput> createDummyInvocationFunction(
            InvocationsTracker tracker) {
        Map<String, io.vavr.collection.List<ModelInferenceOutputType>> response = new HashMap<>();
        // dummy result
        response.put("result", io.vavr.collection.List.of(ModelInferenceOutputType.of(10)));
        return (i) -> {
            ModelDTO model = ModelDTO.builder()
                    .modelName(i.getModelName())
                    .build();
            tracker.addInvocation(i.getModelName());
            return new ModelInferenceOutput(io.vavr.collection.HashMap.ofAll(response),
                    ModelInferenceInput.builder().model(model).build(),
                    false);
        };
    }

    // Returns the values mapped in the inputsWithResponses - mapped by ModelInferenceRequest hashCode
    private Function<ModelInferenceRequest, ModelInferenceOutput> createInvocationFunction(
            InvocationsTracker tracker,
            List<Map<String, io.vavr.collection.List<ModelInferenceOutputType>>> responses) {

        return (i) -> {
            ModelDTO model = ModelDTO.builder()
                    .modelName(i.getModelName())
                    .build();
            tracker.addInvocation(i.getModelName());
            return new ModelInferenceOutput(io.vavr.collection.HashMap.ofAll(responses.get(tracker.modelsInvoked.size() - 1)),
                    ModelInferenceInput.builder().model(model).build(),
                    false);
        };
    }

    private static class InvocationsTracker {
        private final List<String> modelsInvoked = new ArrayList<>();

        public void addInvocation(String modelName) {
            modelsInvoked.add(modelName);
        }
    }
}

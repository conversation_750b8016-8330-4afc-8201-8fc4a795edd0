package com.grubhub.garcon.controlplane.services.controlplane.sequential;

import com.grubhub.garcon.controlplane.services.controlplane.sequential.exceptions.ExpressionExecutionException;
import lombok.val;
import org.apache.commons.jexl3.JexlException;
import org.apache.commons.jexl3.JexlExpression;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

public class JexlExpressionExecutorImplTest {
    private final JexlExpressionExecutorImpl target = new JexlExpressionExecutorImpl();

    @Test
    public void evaluate_should_handleEmptyClause() {
        Assert.assertThrows(ExpressionExecutionException.class, () ->target.createExpression(""));
    }

    @Test
    public void evaluate_should_handleInvalidSyntax() {
        Assert.assertThrows(JexlException.class, () -> target.createExpression("(value.a > value.b"));
    }

    @Test
    public void evaluate_should_handleInvalidSyntax2() {
        Assert.assertThrows(JexlException.class, () -> target.createExpression("=_123 value.b.contains(\"something\")"));
    }

    @Test
    public void evaluate_should_handleInvalidContextParameter() {
        val context = new HashMap<String, Object>();

        context.put("=0-superinvalid", 2);
        context.put("value.b", "model_a");

        JexlExpression expression = target.createExpression("value.b");
        Assert.assertThrows(ExpressionExecutionException.class, () -> target.evaluateToBoolean(expression, context));
    }

    @Test
    public void evaluate_float_should_evaluate_correctly(){
        val context = new HashMap<String, Object>();
        context.put("value.a", 25f);
        context.put("value.b", 10f);

        JexlExpression expression = target.createExpression("value.a + value.b * 0.5");
        Float result = target.evaluateToFloat(expression, context);
        Assertions.assertThat(result).isEqualTo(30f);
    }

    @Test
    public void evaluate_float_should_return_expression_execution_exception_if_bad_result(){
        Assert.assertThrows(ExpressionExecutionException.class, () -> target.createExpression(""));
    }

    @Test
    public void evaluate_float_should_handleInvalidSyntax() {
        Assert.assertThrows(JexlException.class, () -> target.createExpression("(value.a"));
    }


    @ParameterizedTest
    @MethodSource("expressions")
    public void evaluate_should_correctlyEvaluate(Map<String, Object> context, String clause, boolean expectedResult) {
        JexlExpression expression = target.createExpression(clause);
        boolean result = target.evaluateToBoolean(expression, context);

        Assertions.assertThat(result).isEqualTo(expectedResult);
    }

    private static Stream<Arguments> expressions() {
        return Stream.of(
                Arguments.of(new HashMap<String, Object>() {{
                    put("namespace.field.field_a", 5);
                    put("namespace.field.field_b", 10);
                }}, "namespace.field.field_a < namespace.field.field_b", true),

                Arguments.of(new HashMap<String, Object>() {{
                    put("namespace.field.field_a", "value");
                    put("namespace.field.field_b", "value");
                }}, "namespace.field.field_a == namespace.field.field_b", true),

                Arguments.of(new HashMap<String, Object>() {{
                    put("namespace.field.field_a", 10);
                    put("namespace.field.field_b", 15);
                    put("namespace.field.field_c", "value");
                }}, "(namespace.field.field_a < namespace.field.field_b) && namespace.field.field_c == \"value\"", true),

                Arguments.of(new HashMap<String, Object>() {{
                    put("namespace.field.field_a", 10);
                    put("namespace.field.field_b", 15);
                    put("namespace.field.field_c", "value");
                    put("namespace.field.field_d", "value");
                }}, "(namespace.field.field_a < namespace.field.field_b && (namespace.field.field_c.equals(\"value\")))" +
                        " && namespace.field.field_d == \"value\"", true),

                Arguments.of(new HashMap<String, Object>() {{
                    put("namespace.field.list_field", Arrays.asList("value1", "value2"));
                    put("namespace.field.field_b", "value1");
                }}, "namespace.field.list_field.contains(namespace.field.field_b)", true)

        );
    }
}

package com.grubhub.garcon.controlplane.modelinteraction;

import com.grubhub.garcon.modelinteraction.LinearCombinationModel;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;

public class LinearCombinationModelTest {

    private static final float EPSILON = 0.001f;

    private LinearCombinationModel model;

    @BeforeEach
    public void setUp() {
        model = new LinearCombinationModel();
    }

    @Test
    public void simpleInput() {
        Map<String, Object> sample = HashMap.of(
          "a", 1, "weight_a", 10,
          "b", 2, "weight_b", 100,
          "c", 3, "weight_c", 1000
        );
        val output = model.computeForSample(sample);
        val expected = 1 * 10 + 2 * 100 + 3 * 1000;
        assertThat(output).isEqualTo(expected, within(EPSILON));
    }

    @Test
    public void multipleTypesInInput() {
        Map<String, Object> sample = HashMap.of(
                "a", 1, "weight_a", 10.0,
                "b", 2L, "weight_b", "100",
                "c", 3f, "weight_c", 1000D
        );
        val output = model.computeForSample(sample);
        val expected = 1 * 10 + 2 * 100 + 3 * 1000;
        assertThat(output).isEqualTo(expected, within(EPSILON));
    }

    @Test
    public void nullWeight() {
        Map<String, Object> sample = HashMap.of(
                "a", 1, "weight_a", 10,
                "b", 2, "weight_b", null,
                "c", 3, "weight_c", 1000
        );
        val output = model.computeForSample(sample);
        val expected = 1 * 10 + 2 * 0 + 3 * 1000;
        assertThat(output).isEqualTo(expected, within(EPSILON));
    }

    @Test
    public void missingWeight() {
        Map<String, Object> sample = HashMap.of(
                "a", 1, "weight_a", 10,
                "b", 2,
                "c", 3, "weight_c", 1000
        );
        val output = model.computeForSample(sample);
        val expected = 1 * 10 + 2 * 0 + 3 * 1000;
        assertThat(output).isEqualTo(expected, within(EPSILON));
    }

    @Test
    public void moreWeightsThanInputs() {
        Map<String, Object> sample = HashMap.of(
                "a", 1, "weight_a", 10,
                "weight_b", 100,
                "c", 3, "weight_c", 1000
        );
        val output = model.computeForSample(sample);
        val expected = 1 * 10 + 3 * 1000;
        assertThat(output).isEqualTo(expected, within(EPSILON));
    }


}

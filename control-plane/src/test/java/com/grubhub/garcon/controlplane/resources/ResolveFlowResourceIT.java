package com.grubhub.garcon.controlplane.resources;

import com.google.common.collect.Collections2;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.inject.Module;
import com.google.inject.*;
import com.google.inject.spi.Element;
import com.google.inject.spi.Elements;
import com.google.inject.spi.ScopeBinding;
import com.grubhub.garcon.guice.PersistenceTestModule;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.guice.ControlTestITModule;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketByGeohashDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.ResolveFlowRequest;
import com.grubhub.garcon.controlplaneapi.resource.controlplane.FlowResource;
import com.grubhub.garcon.controlplaneapi.resource.controlplane.MarketResource;
import com.grubhub.garcon.controlplaneapi.resource.ensembler.EnsembleResource;
import com.grubhub.garcon.controlplaneapi.resource.ensembler.ModelResource;
import com.grubhub.garcon.ddml.config.SvcConfig;
import com.grubhub.garcon.ddml.controlplane.Svc;
import com.grubhub.garcon.ddml.resources.ensembler.EnsembleResourceImpl;
import com.grubhub.garcon.ddml.resources.ensembler.ModelResourceImpl;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import com.grubhub.garcon.shared.CassandraTestCase;
import com.grubhub.roux.BaseServiceModuleBuilder;
import com.grubhub.roux.LazySingleton;
import com.grubhub.roux.OptionalRequestInfoProvider;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import com.grubhub.roux.api.responses.DeleteResponse;
import com.grubhub.roux.api.responses.GetResponse;
import com.grubhub.roux.api.responses.UpdateResponse;
import com.grubhub.roux.config.RuntimeConfig;
import com.grubhub.roux.core.RouxletSetup;
import com.grubhub.roux.core.Service;
import com.grubhub.roux.core.ServiceBuilder;
import com.grubhub.roux.rest.PublicApiRouxletSetup;
import com.grubhub.roux.test.PortHelper;
import com.grubhub.roux.test.cassandra.EmbeddedCassandraExt;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.val;
import name.falgout.jeffrey.testing.junit.guice.GuiceExtension;
import name.falgout.jeffrey.testing.junit.guice.IncludeModule;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.utility.DockerImageName;

import javax.ws.rs.core.MediaType;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.grubhub.garcon.controlplane.resources.APIITConstants.*;
import static com.grubhub.garcon.controlplane.resources.ITFactory.*;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.MarketFactory.buildNewYorkMarketWithGeohashes;
import static com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory.createModelWithName;
import static com.grubhub.garcon.controlplane.utils.APIConstants.MEMCACHED_PORT;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.DATA;
import static com.grubhub.roux.rest.PublicApiRouxletSetup.newPublicApiSetup;
import static java.util.Collections.emptyMap;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.TestInstance.Lifecycle.PER_CLASS;

@ExtendWith({MockitoExtension.class, GuiceExtension.class})
@TestInstance(PER_CLASS)
@IncludeModule({PersistenceTestModule.class, ControlTestITModule.class})
@CassandraTestCase
public class ResolveFlowResourceIT {
    @Container
    public static GenericContainer<?> memcachedContainer = new GenericContainer<>(DockerImageName.parse("memcached"))
            .withExposedPorts(MEMCACHED_PORT);

    private Service<SvcConfig, RuntimeConfig> service;
    private int publicPort;

    @RegisterExtension
    public static final EmbeddedCassandraExt EMBEDDED_CASSANDRA = new EmbeddedCassandraExt(DATA);

    @Inject
    private FlowConfig flowConfig;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @BeforeAll
    protected void beforeAll() throws Exception {
        memcachedContainer.start();
        val svcConfig = new SvcConfig();
        publicPort = PortHelper.freePort();
        svcConfig.getPublicApi().setPort(publicPort);
        service = ServiceBuilder.start(svcConfig, RuntimeConfig.class)
                .withSharedModules(emptyMap())
                .disableRuntimeConfigUpdates()
                .disableDiscovery()
                .disableTracing()
                .disableMetrics()
                .withRouxlets(Stream.of(withPublicApiSetup()).toArray(RouxletSetup[]::new))
                .build(Svc.class);
        service.runService();
    }

    public static Module subtractBinding(Module module, List<Key<?>> toSubtract) {
        List<Element> elements = Elements.getElements(module);

        return Elements.getModule(Collections2.filter(elements, input -> {
            if (input instanceof Binding) {
                return !toSubtract.contains(((Binding) input).getKey());
            } else if (input instanceof ScopeBinding && ((ScopeBinding) input).getScope().equals(Scopes.SINGLETON)) {
                return false;
            }

            return true;
        }));
    }

    private PublicApiRouxletSetup withPublicApiSetup() {
        return newPublicApiSetup().withModules(
                        BaseServiceModuleBuilder.start().add("http-resources")
                                .implementation(new AbstractModule() {
                                    @Override
                                    protected void configure() {
                                        install(subtractBinding(new PersistenceTestModule(), ImmutableList.of(Key.get(LazySingleton.class))));
                                        install(subtractBinding(new ControlTestITModule(), ImmutableList.of(
                                                Key.get(JavaDateTimeHelper.class),
                                                Key.get(OptionalRequestInfoProvider.class),
                                                Key.get(MeterRegistry.class),
                                                Key.get(LazySingleton.class)))
                                        );
                                        bind(FlowResource.class).to(FlowResourceImpl.class);
                                        bind(MarketResource.class).to(MarketResourceImpl.class);
                                        bind(ModelResource.class).to(ModelResourceImpl.class);
                                        bind(EnsembleResource.class).to(EnsembleResourceImpl.class);
                                    }
                                })
                                .noSimulation()
                                .build()
                )
                .withObjectMapper(ObjectMapperHelper.INSTANCE);
    }

    @AfterAll
    void cleanUp() {
        if (service != null) {
            service.stop();
        }
    }

    @Test
    public void newGeohashResolutionE2eTest() {

        createMarket();
        createModel();
        createEnsembleForModel();
        FlowApi flow = createNYCFlow();
        createFlow(flow);

        // Resolve flow after all the resources were created and the new geohash resolution flag is disabled
        val resolveFlowRequest = getResolveFlowRequest();
        resolveFlow(flow, resolveFlowRequest);

        // Enable the new geohash resolution flag
        toggleFlowByGeohashFlag();

        // Populate the market by geohashes table that will be used for resolving flows
        createMarketByGeohashes();

        // Resolving the flow again should return the same result
        resolveFlowAgain(flow, resolveFlowRequest);

        // Trigger the update flow request
        final FlowApi updatedFlow = updateFlow(flow);

        // Get the flow and make check that the updated operation succeeded
        getFlow(flow, updatedFlow);

        // After the flow update the resolve flow should resolve properly and return the updated flow
        resolveFlowAfterUpdate(flow);

        // Disable the new geohash resolution flag
        toggleFlowByGeohashFlag();

        // Delete the second flow
        deleteFlow(flow);

    }

    @Test
    public void orderGroupHappyFlow() {
        createMarket();
        createModel();
        createEnsembleForModel();
        FlowApi flow = createFlowWithMultipleRoutingGroups();
        createFlow(flow);

        GetResponse<LinkedHashMap> getFlowResponse = getClient()
                .resource(getFlowsPath(publicPort) + "/" + flow.getFlowId())
                .get(GetResponse.class);

        assertThat(getFlowResponse.getStatus()).isEqualTo(GetResponse.Status.SUCCESS);
        Map<String, List<LinkedHashMap>> routingGroupsCriteria = (Map<String, List<LinkedHashMap>>) getFlowResponse.get().get("routing_groups_criteria");
        assertThat(routingGroupsCriteria.get("default").stream().map(map -> map.get("group_order")).collect(Collectors.toList()))
                .contains(null, null, null, null);
    }

    @Test
    public void orderGroup_update_group_order_should_execute_successfully() {
        createMarket();
        createModel();
        createEnsembleForModel();
        FlowApi flow = createFlowWithMultipleRoutingGroups();
        createFlow(flow);

        flow.getRoutingGroupsCriteria().get("default").get(0).setGroupOrder(77);
        createFlow(flow);

        GetResponse<LinkedHashMap> getFlowResponse = getClient()
                .resource(getFlowsPath(publicPort) + "/" + flow.getFlowId())
                .get(GetResponse.class);

        assertThat(getFlowResponse.getStatus()).isEqualTo(GetResponse.Status.SUCCESS);
        Map<String, List<LinkedHashMap>> routingGroupsCriteria = (Map<String, List<LinkedHashMap>>) getFlowResponse.get().get("routing_groups_criteria");
        assertThat(routingGroupsCriteria.get("default").stream().map(map -> map.get("group_order")).collect(Collectors.toList()))
                .contains(null, null, null, null);
    }

    @Test
    public void newGeohashResolutionE2eTestWithDelete() {

        createMarket();
        createModel();
        createEnsembleForModel();
        FlowApi flow = createNYCFlow();
        createFlow(flow);
        FlowApi secondFlow = createNYCFlow().withPriority(2);
        createFlow(secondFlow);

        // Resolve flow after all the resources were created and the new geohash resolution flag is disabled
        val resolveFlowRequest = getResolveFlowRequest();
        resolveFlow(flow, resolveFlowRequest);

        // Enable the new geohash resolution flag
        toggleFlowByGeohashFlag();

        // Delete the second flow
        deleteFlow(secondFlow);

        // Populate the market by geohashes table that will be used for resolving flows
        createMarketByGeohashes();

        // Resolving the flow again should return the same result
        resolveFlowAgain(flow, resolveFlowRequest);

        // Trigger the update flow request
        final FlowApi updatedFlow = updateFlow(flow);

        // Get the flow and make check that the updated operation succeeded
        getFlow(flow, updatedFlow);

        // After the flow update the resolve flow should resolve properly and return the updated flow
        resolveFlowAfterUpdate(flow);

        getFlowByMarkets(flow);
        // Disable the new geohash resolution flag
        toggleFlowByGeohashFlag();

    }

    private void getFlowByMarkets(FlowApi flow) {
        GetResponse<ArrayList> getFlowResponse = getClient()
                .resource(getFlowsPath(publicPort) + "/by-markets" + "?flowSet=" + flow.getFlowSet() + "&marketName=" + "manhattan")
                .get(GetResponse.class);

        assertThat(getFlowResponse.getStatus()).isEqualTo(GetResponse.Status.SUCCESS);
        List<LinkedHashMap> linkedHashMap = getFlowResponse.get();
        assertThat(linkedHashMap.size()).isEqualTo(1);
        assertThat(linkedHashMap.get(0).get("flow_id")).isEqualTo(flow.getFlowId());
    }

    private void deleteFlow(FlowApi flow) {
        DeleteResponse deleteResponse = getClient()
                .resource(getFlowsPath(publicPort) + "/" + flow.getFlowId())
                .delete(DeleteResponse.class);

        assertThat(deleteResponse.getStatus()).isEqualTo(DeleteResponse.Status.SUCCESS);
    }

    private void resolveFlowAfterUpdate(FlowApi flow) {
        ResolveFlowRequest resolveFlowRequestSecond = ResolveFlowRequest.builder()
                .lat(40.77667)
                .lng(-73.97132)
                .flowSet("Search")
                .orderType("PICKUP")
                .applicationId("Android Native")
                .queryText("mozzarella")
                .build();

        GetResponse<LinkedHashMap> resolveFlowResponseWithGeohashEnabledSecondCall = resolveFlow(resolveFlowRequestSecond);

        assertThat(resolveFlowResponseWithGeohashEnabledSecondCall.getStatus()).isEqualTo(GetResponse.Status.SUCCESS);
        assertThat(resolveFlowResponseWithGeohashEnabledSecondCall.get().get("flow_id")).isEqualTo(flow.getFlowId());
    }

    private void getFlow(FlowApi flow, FlowApi updatedFlow) {
        GetResponse<LinkedHashMap> getFlowResponse = getClient()
                .resource(getFlowsPath(publicPort) + "/" + flow.getFlowId())
                .get(GetResponse.class);

        assertThat(getFlowResponse.getStatus()).isEqualTo(GetResponse.Status.SUCCESS);
        assertThat(getFlowResponse.get().get("flow_id")).isEqualTo(updatedFlow.getFlowId());
        assertThat(getFlowResponse.get().get("matching_applications")).isEqualTo(ImmutableList.of("Android Native"));
        assertNull(getFlowResponse.get().get("routing_groups"));
    }

    private FlowApi updateFlow(FlowApi flow) {
        val updatedFlow = flow.withFlowName("Search Updated")
                .withMatchingOrderTypes(ImmutableSet.of("DELIVERY", "PICKUP"))
                .withMatchingApplications(ImmutableSet.of("Android Native"))
                .withMatchingQueryTokens(ImmutableList.of("mozzarella"));
        UpdateResponse updateFlowResponse = getClient().resource(getFlowsPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .put(UpdateResponse.class, updatedFlow);

        assertThat(updateFlowResponse.getStatus()).isEqualTo(UpdateResponse.Status.SUCCESS);
        return updatedFlow;
    }

    private void createMarketByGeohashes() {
        List<MarketByGeohashDTO> marketByGeohashes = ImmutableList.of(
                MarketByGeohashDTO.builder()
                        .marketName("manhattan")
                        .geohash("dr5ru")
                        .geohashPrecision(5).build()
        );
        UpdateResponse marketByGeohashResponse = getClient().resource(getMarketsByGeohashesPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .put(UpdateResponse.class, marketByGeohashes);
        assertThat(marketByGeohashResponse.getStatus()).isEqualTo(UpdateResponse.Status.SUCCESS);
    }

    private void toggleFlowByGeohashFlag() {
        flowConfig.setNewGeohashesResolutionEnabled(!flowConfig.getNewGeohashesResolutionEnabled());
    }

    private GetResponse<LinkedHashMap> resolveFlow(ResolveFlowRequest resolveFlowRequest) {
        GetResponse<LinkedHashMap> resolveFlowResponse = getClient()
                .resource(getResolveFlowPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .post(GetResponse.class, resolveFlowRequest);

        assertThat(resolveFlowResponse.getStatus()).isEqualTo(GetResponse.Status.SUCCESS);

        return resolveFlowResponse;
    }

    private ResolveFlowRequest getResolveFlowRequest() {
        return ResolveFlowRequest.builder()
                .lat(40.77667)
                .lng(-73.97132)
                .flowSet("Search")
                .orderType("DELIVERY")
                .applicationId("umami")
                .queryText("pizza")
                .build();
    }

    private void createFlow(FlowApi flow) {
        UpdateResponse createFlowResponse = getClient()
                .resource(getFlowsPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .put(UpdateResponse.class, flow);

        assertThat(createFlowResponse.getStatus()).isEqualTo(UpdateResponse.Status.SUCCESS);
    }

    private void createEnsembleForModel() {
        UpdateResponse createEnsembleResponse = getClient()
                .resource(getEnsemblesPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .put(UpdateResponse.class, createEnsemble());

        assertThat(createEnsembleResponse.getStatus()).isEqualTo(UpdateResponse.Status.SUCCESS);
    }

    private void createModel() {
        UpdateResponse createModelResponse = getClient()
                .resource(getModelsPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .put(UpdateResponse.class, createModelWithName("search_m1_m2"));
        assertThat(createModelResponse.getStatus()).isEqualTo(UpdateResponse.Status.SUCCESS);
    }

    private void createMarket() {
        UpdateResponse createMarketResponse = getClient()
                .resource(getMarketsPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .put(UpdateResponse.class, buildNewYorkMarketWithGeohashes(Date.from(Instant.now())));
        assertThat(createMarketResponse.getStatus()).isEqualTo(UpdateResponse.Status.SUCCESS);
    }

    private void resolveFlow(FlowApi flow, ResolveFlowRequest resolveFlowRequest) {
        GetResponse<LinkedHashMap> resolveFlowResponse = getClient()
                .resource(getResolveFlowPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .post(GetResponse.class, resolveFlowRequest);

        assertThat(resolveFlowResponse.getStatus()).isEqualTo(GetResponse.Status.SUCCESS);
        assertThat(resolveFlowResponse.get().get("flow_id")).isEqualTo(flow.getFlowId());
    }

    private void resolveFlowAgain(FlowApi flow, ResolveFlowRequest resolveFlowRequest) {
        GetResponse<LinkedHashMap> resolveFlowResponseWithGeohashEnabled = getClient()
                .resource(getResolveFlowPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .post(GetResponse.class, resolveFlowRequest);

        assertThat(resolveFlowResponseWithGeohashEnabled.getStatus()).isEqualTo(GetResponse.Status.SUCCESS);
        assertThat(resolveFlowResponseWithGeohashEnabled.get().get("flow_id")).isEqualTo(flow.getFlowId());
    }
}

package com.grubhub.garcon.controlplane.services.controlplane.rules;

import com.grubhub.garcon.controlplane.cassandra.models.DinerType;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.services.controlplane.rules.diner.ExistingDinerRule;
import com.grubhub.garcon.controlplane.services.controlplane.rules.diner.NewDinerRule;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.ResolveFlowRequest;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
public class DinerRulesTest {

    @Mock
    private FlowConfig flowConfig;

    @BeforeEach
    public void setUp() {
        lenient().when(flowConfig.getTotalOrdersMax()).thenReturn(5);
    }

    @Test
    public void applicableTo_for_existingDinerRule_returns_existing_diner_when_threshold_exists_and_lower_than_orders() {
        ExistingDinerRule existingDinerRule = new ExistingDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(3);
        Integer dinerTypeOrdersThreshold = 1;

        val dinerTypeOp = existingDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isTrue();
        assertThat(dinerTypeOp.get()).isEqualTo(DinerType.EXISTING_DINER);
    }

    @Test
    public void applicableTo_for_existingDinerRule_returns_nothing_when_threshold_exists_and_greater_than_orders() {
        ExistingDinerRule existingDinerRule = new ExistingDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(10);
        Integer dinerTypeOrdersThreshold = 20;

        val dinerTypeOp = existingDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isFalse();
    }

    @Test
    public void applicableTo_for_existingDinerRule_returns_nothing_when_threshold_exists_and_is_equal_to_orders() {
        ExistingDinerRule existingDinerRule = new ExistingDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(4);
        Integer dinerTypeOrdersThreshold = 4;

        val dinerTypeOp = existingDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isFalse();
    }

    @Test
    public void applicableTo_for_existingDinerRule_returns_existing_diner_when_threshold_is_null_and_orders_greater_than_config() {
        ExistingDinerRule existingDinerRule = new ExistingDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(20);
        Integer dinerTypeOrdersThreshold = null;

        val dinerTypeOp = existingDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isTrue();
        assertThat(dinerTypeOp.get()).isEqualTo(DinerType.EXISTING_DINER);
    }

    @Test
    public void applicableTo_for_existingDinerRule_returns_nothing_when_threshold_is_null_and_orders_lower_than_config() {
        ExistingDinerRule existingDinerRule = new ExistingDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(1);
        Integer dinerTypeOrdersThreshold = null;

        val dinerTypeOp = existingDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isFalse();
    }

    @Test
    public void applicableTo_for_existingDinerRule_returns_nothing_when_threshold_is_null_and_orders_is_equal_to_config() {
        ExistingDinerRule existingDinerRule = new ExistingDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(5);
        Integer dinerTypeOrdersThreshold = null;

        val dinerTypeOp = existingDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isFalse();
    }

    @Test
    public void applicableTo_for_newDinerRule_returns_new_diner_when_threshold_exists_and_greater_than_orders() {
        NewDinerRule newDinerRule = new NewDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(50);
        Integer dinerTypeOrdersThreshold = 51;

        val dinerTypeOp = newDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isTrue();
        assertThat(dinerTypeOp.get()).isEqualTo(DinerType.NEW_DINER);
    }

    @Test
    public void applicableTo_for_newDinerRule_returns_nothing_when_threshold_exists_and_lower_than_orders() {
        NewDinerRule newDinerRule = new NewDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(4);
        Integer dinerTypeOrdersThreshold = 1;

        val dinerTypeOp = newDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isFalse();
    }

    @Test
    public void applicableTo_for_newDinerRule_returns_new_diner_when_threshold_exists_and_is_equal_to_orders() {
        NewDinerRule newDinerRule = new NewDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(99);
        Integer dinerTypeOrdersThreshold = 99;

        val dinerTypeOp = newDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isTrue();
        assertThat(dinerTypeOp.get()).isEqualTo(DinerType.NEW_DINER);
    }

    @Test
    public void applicableTo_for_newDinerRule_returns_new_diner_when_threshold_is_null_and_orders_lower_than_config() {
        NewDinerRule newDinerRule = new NewDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(0);
        Integer dinerTypeOrdersThreshold = null;

        val dinerTypeOp = newDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isTrue();
        assertThat(dinerTypeOp.get()).isEqualTo(DinerType.NEW_DINER);
    }

    @Test
    public void applicableTo_for_newDinerRule_returns_nothing_when_threshold_is_null_and_orders_greater_than_config() {
        NewDinerRule newDinerRule = new NewDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(75);
        Integer dinerTypeOrdersThreshold = null;

        val dinerTypeOp = newDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isFalse();
    }

    @Test
    public void applicableTo_for_newDinerRule_returns_new_diner_when_threshold_is_null_and_orders_is_equal_to_config() {
        NewDinerRule newDinerRule = new NewDinerRule(flowConfig);
        val resolveFlowRequest = createResolveFlowRequestWithTotalOrders(5);
        Integer dinerTypeOrdersThreshold = null;

        val dinerTypeOp = newDinerRule.applicableTo(resolveFlowRequest, dinerTypeOrdersThreshold);

        assertThat(dinerTypeOp.isPresent()).isTrue();
        assertThat(dinerTypeOp.get()).isEqualTo(DinerType.NEW_DINER);
    }

    private ResolveFlowRequest createResolveFlowRequestWithTotalOrders(int totalOrders) {
        return ResolveFlowRequest
                .builder()
                .callerTrackingId("test")
                .flowSet("Test")
                .applicationId("3")
                .dinerId("100")
                .totalOrders(totalOrders)
                .build();
    }
}

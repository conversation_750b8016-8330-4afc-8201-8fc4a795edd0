package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.google.common.collect.Lists;
import com.grubhub.garcon.controlplane.cassandra.dao.impl.MarketByGeohashDaoImpl;
import com.grubhub.garcon.controlplane.cassandra.dao.impl.MarketDaoImpl;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import com.grubhub.garcon.controlplane.cassandra.models.Market;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.eventhub.ControlPlaneLogger;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.controlplane.MarketService;
import com.grubhub.garcon.controlplane.util.SessionAttributeProvider;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketDTO;
import com.grubhub.garcon.ensembler.cassandra.dao.impl.EntityCollectionDaoImpl;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.test.cassandra.EmbeddedCassandraExt;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import lombok.val;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.Date;
import java.util.UUID;
import java.util.stream.Stream;

import static com.grubhub.garcon.DaoUtils.truncateMarketTable;
import static com.grubhub.garcon.controlplane.utils.InjectorProvider.INJECTOR;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.DATA;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.initCasserole;
import static java.util.stream.Collectors.toList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MarketServiceImplTest {

    private MarketService marketService;
    @Mock
    private FlowConfig flowConfig;
    @Mock
    private SessionAttributeProvider sessionAttributeProvider;

    @RegisterExtension
    public static final EmbeddedCassandraExt EMBEDDED_CASSANDRA = new EmbeddedCassandraExt(DATA);
    private static Casserole casserole;

    @BeforeAll
    static void beforeClass() {
        casserole = initCasserole(EMBEDDED_CASSANDRA.getPort());
    }

    @BeforeEach
    void setUp() {
        val marketDao = new MarketDaoImpl(casserole);
        val controlPlaneMapper = INJECTOR.getInstance(ControlPlaneMapper.class);
        val entityCollectionDao = new EntityCollectionDaoImpl(casserole);
        val marketByGeohashDao = new MarketByGeohashDaoImpl(casserole);
        marketService = new MarketServiceImpl(
                marketDao,
                flowConfig,
                mock(ControlPlaneLogger.class),
                controlPlaneMapper,
                entityCollectionDao,
                sessionAttributeProvider,
                marketByGeohashDao);
    }

    @AfterEach
    void afterEach() {
        truncateMarketTable(casserole);
    }

    @AfterAll
    static void afterClass() {
        casserole.shutdown();
    }

    @Test
    void createMarket_should_save_market_successfully() {
        when(flowConfig.getPrecision()).thenReturn(5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true);
        val updatedTimestamp = Date.from(Instant.now());
        val marketDTO = buildNewYorkMarket(updatedTimestamp);
        marketService.createOrUpdateMarket(marketDTO);
        val savedMarket = marketService.getMarket("manhattan");
        assertThat(savedMarket.isDefined()).isEqualTo(true);
        savedMarket.peek(actualMarket -> {
            assertThat(actualMarket.getGeohashes());
            assertThat(actualMarket.getGeohashes().size()).isEqualTo(5);
            assertThat(actualMarket.getRegionName()).isEqualTo("new york");
            assertThat(actualMarket.getZipcode()).isEqualTo("64232");
        });
    }

    @Test
    void updateMarket_should_use_existing_geohashes() {
        val updatedTimestamp = Date.from(Instant.now());
        val marketDTO = buildNewYorkMarketWithGeohashes(updatedTimestamp);
        marketService.createOrUpdateMarket(marketDTO);
        val savedMarket = marketService.getMarket("manhattan");
        assertThat(savedMarket.isDefined()).isEqualTo(true);
        savedMarket.peek(actualMarket -> {
            assertThat(actualMarket.getGeohashes());
            assertThat(actualMarket.getGeohashes().size()).isEqualTo(3);
            assertThat(actualMarket.getGeohashes()).containsExactly("dw3rt", "dw3ra", "dw3rb");
            assertThat(actualMarket.getRegionName()).isEqualTo("new york");
            assertThat(actualMarket.getZipcode()).isEqualTo("64232");
        });
    }

    @Test
    void updateMarket_should_override_existing_geohashes() {
        val updatedTimestamp = Date.from(Instant.now());
        val marketDTO = buildNewYorkMarketWithGeohashes(updatedTimestamp);
        marketService.createOrUpdateMarket(marketDTO);
        val savedFirstMarket = marketService.getMarket("manhattan");
        savedFirstMarket.peek(actualMarket -> {
            assertThat(actualMarket.getGeohashes().size()).isEqualTo(3);
            assertThat(actualMarket.getGeohashes()).containsExactly("dw3rt", "dw3ra", "dw3rb");
            assertThat(actualMarket.getRegionName()).isEqualTo("new york");
            assertThat(actualMarket.getZipcode()).isEqualTo("64232");
        });

        marketService.createOrUpdateMarket(marketDTO.withGeohashes(marketDTO.getGeohashes().add("dw3pt")));
        val savedMarket = marketService.getMarket("manhattan");

        assertThat(savedMarket.isDefined()).isEqualTo(true);
        savedMarket.peek(actualMarket -> {
            assertThat(actualMarket.getGeohashes().size()).isEqualTo(4);
            assertThat(actualMarket.getGeohashes()).containsExactlyInAnyOrder("dw3rt", "dw3ra", "dw3rb", "dw3pt");
            assertThat(actualMarket.getRegionName()).isEqualTo("new york");
            assertThat(actualMarket.getZipcode()).isEqualTo("64232");
        });

    }

    @Test
    void updateMarket_should_update_market_successfully() {
        when(flowConfig.getPrecision()).thenReturn(5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true);
        val updatedTimestamp = Date.from(Instant.now());
        val marketDTO = buildNewYorkMarket(updatedTimestamp);
        marketService.createOrUpdateMarket(marketDTO);
        val marketAsOptional = marketService.getMarket("manhattan");

        marketAsOptional.peek(existingMaket -> existingMaket.setRegionName("updated new york"));
        assertThat(marketAsOptional.isDefined()).isEqualTo(true);


        marketAsOptional.peek(marketService::updateMarket);

        val updatedMarket = marketService.getMarket("manhattan");
        assertThat(marketAsOptional.isDefined()).isEqualTo(true);

        updatedMarket.peek(actualMarket -> {
            assertThat(actualMarket.getGeohashes());
            assertThat(actualMarket.getGeohashes().size()).isEqualTo(5);
            assertThat(actualMarket.getRegionName()).isEqualTo("updated new york");
            assertThat(actualMarket.getZipcode()).isEqualTo("64232");
        });
    }

    @Test
    void getMarketsForFlow_should_return_list_of_markets() {
        val updatedTimestamp = Date.from(Instant.now());
        Lists.newArrayList(buildNewYorkMarket(updatedTimestamp), buildChicagoMarket(updatedTimestamp))
                .forEach(marketService::createOrUpdateMarket);

        val flow = mock(FlowDTO.class);
        when(flow.getLocationMarkets()).thenReturn(HashSet.of("chicago", "manhattan"));
        List<Market> markets = marketService.getMarketsForFlow(flow);
        assertThat(markets.toStream().map(Market::getMarketName).collect(toList())).containsOnly("chicago", "manhattan");
    }

    @Test
    void getFlowsByGeoHash_should_return_flow() {
        when(flowConfig.getPrecision()).thenReturn(5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true);
        val marketDTO = buildChicagoMarket(Date.from(Instant.now()));
        marketService.createOrUpdateMarket(marketDTO);

        val flow = mock(FlowDTO.class);
        when(flow.getLocationMarkets()).thenReturn(HashSet.of("chicago"));
        when(flow.getFlowId()).thenReturn(String.valueOf(UUID.randomUUID()));
        when(flow.getFlowSet()).thenReturn("SEARCH");
        when(flow.getMatchingDinerTypes()).thenReturn(HashSet.of("dish"));
        List<FlowByGeoHash> geoHashes = marketService.getFlowsByGeoHash(flow);
        assertThat(geoHashes.toStream().map(FlowByGeoHash::getGeohash).collect(toList()))
                .containsExactlyInAnyOrder(expectedGeoHashes());
        assertThat(geoHashes.size()).isEqualTo(18);
    }

    @Test
    void getFlowsByGeoHash_should_return_flow_for_chicago_market() {
        when(flowConfig.getPrecision()).thenReturn(5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true);
        val marketDTO = buildChicagoMarket(Date.from(Instant.now()));
        marketService.createOrUpdateMarket(marketDTO);

        val flow = mock(FlowDTO.class);
        when(flow.getLocationMarkets()).thenReturn(HashSet.of("chicago"));
        when(flow.getFlowId()).thenReturn(String.valueOf(UUID.randomUUID()));
        when(flow.getFlowSet()).thenReturn("SEARCH");
        when(flow.getMatchingDinerTypes()).thenReturn(HashSet.of("dish"));
        List<FlowByGeoHash> geoHashes = marketService.getFlowsByGeohashOrGlobal(flow);

        assertThat(geoHashes.toStream().map(FlowByGeoHash::getGeohash).collect(toList())).containsExactlyInAnyOrder(expectedGeoHashesWithGlobal());
        assertThat(geoHashes.size()).isEqualTo(19);
    }

    @Test
    void getFlowsByGeoHash_should_return_flow_for_global_market_location_markets_emtpy() {
        when(flowConfig.getPrecision()).thenReturn(5);
        when(flowConfig.getFlipCoordinates()).thenReturn(true);
        val marketDTO = buildChicagoMarket(Date.from(Instant.now()));
        marketService.createOrUpdateMarket(marketDTO);

        val flow = mock(FlowDTO.class);
        when(flow.getLocationMarkets()).thenReturn(HashSet.empty());
        when(flow.getFlowId()).thenReturn(String.valueOf(UUID.randomUUID()));
        when(flow.getFlowSet()).thenReturn("SEARCH");
        List<FlowByGeoHash> geoHashes = marketService.getFlowsByGeohashOrGlobal(flow);

        assertThat(geoHashes.size()).isEqualTo(1);
        assertThat(geoHashes.get(0).getMarketName()).isEqualTo("GLOBAL");
    }

    private String[] expectedGeoHashes() {
        return expectedGeohashesStream().toArray(String[]::new);
    }

    private Stream<String> expectedGeohashesStream() {
        return Stream.of(
                "dp3te",
                "dp3tg",
                "dp3ts",
                "dp3tu",
                "dp3tv",
                "dp3ty",
                "dp3w5",
                "dp3w6",
                "dp3w7",
                "dp3wd",
                "dp3we",
                "dp3wh",
                "dp3wj",
                "dp3wk",
                "dp3wm",
                "dp3wn",
                "dp3ws",
                "dp3wt"
        );
    }

    private String[] expectedGeoHashesWithGlobal() {
        return Stream.of(expectedGeohashesStream(), Stream.of("GLOBAL"))
                .flatMap(x -> x)
                .toArray(String[]::new);
    }

    private MarketDTO buildNewYorkMarket(Date updatedTimestamp) {
        return MarketDTO.builder()
                .city("New York")
                .marketName("manhattan")
                .regionName("new york")
                .zipcode("64232")
                .geoPolygon("{" +
                        "\"type\":\"Polygon\"," +
                        "\"coordinates\":" +
                        "   [" +
                        "       [" +
                        "           [-73.93799,40.87848]," +
                        "           [-74.040985,40.700684]," +
                        "           [-73.97026,40.69756]," +
                        "           [-73.9246,40.800816]," +
                        "           [-73.93799,40.87848]" +
                        "       ]" +
                        "   ]" +
                        "}")
                .updatedTimestamp(updatedTimestamp)
                .regionUuid(String.valueOf(UUID.randomUUID()))
                .build();
    }

    private MarketDTO buildNewYorkMarketWithGeohashes(Date updatedTimestamp) {
        return MarketDTO.builder()
                .city("New York")
                .marketName("manhattan")
                .regionName("new york")
                .zipcode("64232")
                .geoPolygon("{" +
                        "\"type\":\"Polygon\"," +
                        "\"coordinates\":" +
                        "   [" +
                        "       [" +
                        "           [-73.93799,40.87848]," +
                        "           [-74.040985,40.700684]," +
                        "           [-73.97026,40.69756]," +
                        "           [-73.9246,40.800816]," +
                        "           [-73.93799,40.87848]" +
                        "       ]" +
                        "   ]" +
                        "}")
                .geohashes(HashSet.of("dw3rt", "dw3ra", "dw3rb"))
                .updatedTimestamp(updatedTimestamp)
                .regionUuid(String.valueOf(UUID.randomUUID()))
                .build();
    }

    private MarketDTO buildChicagoMarket(Date updatedTimestamp) {
        return MarketDTO.builder()
                .city("Chicago")
                .marketName("chicago")
                .regionName("chicago")
                .zipcode("64232")
                .geoPolygon("{" +
                        "\"type\":\"Polygon\"," +
                        "\"coordinates\":" +
                        "   [" +
                        "       [" +
                        "           [-87.798615,41.95847]," +
                        "           [-87.757416,41.76312]," +
                        "           [-87.561035,41.77746]," +
                        "           [-87.64618,41.964596]," +
                        "           [-87.798615,41.95847]" +
                        "       ]" +
                        "   ]" +
                        "}"
                )
                .updatedTimestamp(updatedTimestamp)
                .regionUuid(String.valueOf(UUID.randomUUID()))
                .build();
    }

}

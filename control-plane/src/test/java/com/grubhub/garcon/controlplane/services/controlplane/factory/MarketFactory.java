package com.grubhub.garcon.controlplane.services.controlplane.factory;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketDTO;
import lombok.experimental.UtilityClass;

import java.time.Instant;
import java.util.Date;
import java.util.UUID;

@UtilityClass
public class MarketFactory {

    public static MarketDTO buildGlobalMarket() {
        return MarketDTO.builder()
                .city("no city")
                .marketName("global")
                .regionName("global")
                .zipcode("64232")
                .geoPolygon("{" +
                        "\"type\":\"Polygon\"," +
                        "\"coordinates\":" +
                        "   [" +
                        "       [" +
                        "           [-87.798615,41.95847]," +
                        "           [-87.757416,41.76312]," +
                        "           [-87.561035,41.77746]," +
                        "           [-87.64618,41.964596]," +
                        "           [-87.798615,41.95847]" +
                        "       ]" +
                        "   ]" +
                        "}"
                )
                .updatedTimestamp(Date.from(Instant.now()))
                .regionUuid(String.valueOf(UUID.randomUUID()))
                .build();
    }

    public static MarketDTO buildChicagoMarket() {
        return MarketDTO.builder()
                .city("Chicago")
                .marketName("chicago")
                .regionName("chicago")
                .zipcode("64232")
                .geoPolygon("{" +
                        "\"type\":\"Polygon\"," +
                        "\"coordinates\":" +
                        "   [" +
                        "       [" +
                        "           [-87.798615,41.95847]," +
                        "           [-87.757416,41.76312]," +
                        "           [-87.561035,41.77746]," +
                        "           [-87.64618,41.964596]," +
                        "           [-87.798615,41.95847]" +
                        "       ]" +
                        "   ]" +
                        "}"
                )
                .updatedTimestamp(Date.from(Instant.now()))
                .regionUuid(String.valueOf(UUID.randomUUID()))
                .build();
    }

    public static MarketDTO buildOverlapChicagoMarket() {
        return MarketDTO.builder()
                .city("Chicago")
                .marketName("overlap chicago")
                .regionName("overlap chicago")
                .zipcode("64232")
                .geoPolygon("{" +
                        "\"type\":\"Polygon\"," +
                        "\"coordinates\":" +
                        "   [" +
                        "       [" +
                        "            [-87.69218444824219,41.97174336327968],\n" +
                        "            [-87.68875122070311,41.92475971933975],\n" +
                        "            [-87.64137268066406,41.923737951221014],\n" +
                        "            [-87.65510559082031,41.97174336327968],\n" +
                        "            [-87.69218444824219, 41.97174336327968] " +
                        "       ]" +
                        "   ]" +
                        "}"
                )
                .updatedTimestamp(Date.from(Instant.now()))
                .regionUuid(String.valueOf(UUID.randomUUID()))
                .build();
    }

    public static MarketDTO buildNewYorkMarketWithGeohashes(Date updatedTimestamp) {
        return MarketDTO.builder()
                .city("New York")
                .marketName("manhattan")
                .regionName("new york")
                .zipcode("64232")
                .geoPolygon("{" +
                        "\"type\":\"Polygon\"," +
                        "\"coordinates\":" +
                        "   [" +
                        "       [" +
                        "           [-73.93799,40.87848]," +
                        "           [-74.040985,40.700684]," +
                        "           [-73.97026,40.69756]," +
                        "           [-73.9246,40.800816]," +
                        "           [-73.93799,40.87848]" +
                        "       ]" +
                        "   ]" +
                        "}")
                .updatedTimestamp(updatedTimestamp)
                .regionUuid(String.valueOf(UUID.randomUUID()))
                .build();
    }


}

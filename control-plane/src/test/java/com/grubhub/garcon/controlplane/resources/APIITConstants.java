package com.grubhub.garcon.controlplane.resources;

import lombok.experimental.UtilityClass;

@UtilityClass
public class APIITConstants {

    public static final String HOST = "http://localhost";
    public static final String MARKETS_PATH = "ddmlcontrolplane/markets";
    public static final String MODELS_PATH = "ddmlcontrolplane/models";
    public static final String ENSEMBLES_PATH = "ddmlcontrolplane/ensembles";
    public static final String FLOWS_PATH = "ddmlcontrolplane/flows";
    public static final String SEPARATOR = ":";
    public static final String MARKETS_BY_GEOHASHES = "ddmlcontrolplane/markets/marketsByGeohashes";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String RESOLVE_FLOW_PATH = "ddmlcontrolplane/flows/resolveFlow";


    public static String getBasePath(int publicPort) {
        return HOST + SEPARATOR + publicPort + "/";
    }

    public static String getMarketsPath(int publicPort) {
        return getBasePath(publicPort) + MARKETS_PATH;
    }

    public static String getModelsPath(int publicPort) {
        return getBasePath(publicPort) + MODELS_PATH;
    }

    public static String getEnsemblesPath(int publicPort) {
        return getBasePath(publicPort) + ENSEMBLES_PATH;
    }

    public static String getFlowsPath(int publicPort) {
        return getBasePath(publicPort) + FLOWS_PATH;
    }

    public static String getResolveFlowPath(int publicPort) {
        return getBasePath(publicPort) + RESOLVE_FLOW_PATH;
    }

    public static String getMarketsByGeohashesPath(int publicPort) {
        return getBasePath(publicPort) + MARKETS_BY_GEOHASHES;
    }
}

package com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup;

import com.google.common.hash.Hasher;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class CompositeRandomRoutingGroupStrategyTest {

    @Test
    public void appendToHasher_null_sameDigest(){
        Hasher digest = mock(Hasher.class);

        Hasher result = CompositeRandomRoutingGroupStrategy.appendToHasher(digest, null);

        assertThat(result).isEqualTo(digest);
        verifyNoInteractions(digest);
    }

    @Test
    public void appendToHasher_UUID_isHandledAsUUID(){
        UUID uuid = UUID.randomUUID();
        String value = uuid.toString();
        Hasher digest = mock(Hasher.class);

        when(digest.putLong(uuid.getLeastSignificantBits()))
                .thenReturn(digest);
        when(digest.putLong(uuid.getMostSignificantBits()))
                .thenReturn(digest);

        final Hasher result = CompositeRandomRoutingGroupStrategy.appendToHasher(digest, value);

        assertThat(result).isEqualTo(digest);

        verify(digest).putLong(uuid.getLeastSignificantBits());
        verify(digest).putLong(uuid.getMostSignificantBits());
    }

    @Test
    public void appendToHasher_String_isHandledAsString(){
        String value = "test";
        Hasher digest = mock(Hasher.class);

        when(digest.putUnencodedChars(value))
                .thenReturn(digest);

        final Hasher result = CompositeRandomRoutingGroupStrategy.appendToHasher(digest, value);

        assertThat(result).isEqualTo(digest);
        verify(digest).putUnencodedChars(value);
    }

}

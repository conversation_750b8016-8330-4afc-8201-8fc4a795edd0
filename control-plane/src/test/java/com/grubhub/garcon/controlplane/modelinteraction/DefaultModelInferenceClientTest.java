package com.grubhub.garcon.controlplane.modelinteraction;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import com.google.common.collect.ImmutableList;
import com.google.protobuf.ByteString;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelConfigUpdateResponse;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.ensembler.config.ModelGroupConfig;
import com.grubhub.garcon.grpc.DdmlTensorflowGrpcClient;
import com.grubhub.garcon.grpc.GrpcChannelFactory;
import com.grubhub.garcon.grpc.GrpcConfiguration;
import com.grubhub.garcon.grpc.GrpcResolveType;
import com.grubhub.garcon.modelinteraction.DefaultModelInferenceClient;
import com.grubhub.garcon.modelinteraction.ExternalModelInference;
import com.grubhub.garcon.modelinteraction.ModelInferenceClient;
import com.grubhub.garcon.modelinteraction.PredictionClientFactory;
import com.grubhub.garcon.modelinteraction.tensorflow.*;
import com.grubhub.garcon.modelinteraction.tfs.TfsModelInference;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.val;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;
import org.tensorflow.example.Example;
import org.tensorflow.example.Feature;
import org.tensorflow.example.Features;
import org.tensorflow.example.FloatList;
import org.tensorflow.framework.DataType;
import org.tensorflow.framework.TensorProto;
import org.tensorflow.framework.TensorShapeProto;
import tensorflow.serving.Predict.PredictResponse;
import tensorflow.serving.PredictionServiceGrpc;

import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.grubhub.garcon.controlplane.utils.InjectorProvider.INJECTOR;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.*;

class DefaultModelInferenceClientTest {
    private TensorFlowModelAdaptor tensorFlowModelAdaptorSpy;
    private TfsClusterInteraction tfsClusterInteractionSpy;
    private ModelInferenceClient client;
    private ListAppender<ILoggingEvent> appender;
    private Logger defaultModelInferenceClientLogger = (Logger) LoggerFactory.getLogger((DefaultModelInferenceClient.class));
    private ExternalModelInference externalModelInferenceSpy;

    private final PredictionClientFactory predictionClientFactory = INJECTOR.getInstance(PredictionClientFactory.class);

    private final GrpcChannelFactory grpcChannelFactory = INJECTOR.getInstance(GrpcChannelFactory.class);

    @Mock
    private ModelAwareClientProvider modelAwareClientProvider;
    @Mock
    private FlowConfig flowConfig;
    @Mock
    private ModelInferenceInput modelInferenceInput;
    @Mock
    private ModelService modelService;
    @Mock
    private DdmlTensorflowGrpcClient ddmlTensorflowGrpcClient;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        reset(predictionClientFactory);
        TensorFlowModelConfiguration tensorFlowModelConfiguration = new TensorFlowModelConfiguration();

        tensorFlowModelConfiguration.setGrpcResolveType(GrpcResolveType.STATIC.name());
        tensorFlowModelConfiguration.setModelPlatform("tensorflow");
        TfsClusterConfig tfsClusterConfig = new TfsClusterConfig();
        tensorFlowModelConfiguration.setTfsClusterConfigs(HashMap.of("search", tfsClusterConfig).toJavaMap());
        MeterRegistry meterRegistry = new SimpleMeterRegistry();


        when(flowConfig.getModelGroupConfigs()).thenReturn(HashMap.of("search", ModelGroupConfig.builder()
                .tfsClusterName("search")
                .internalFeaturesCacheroleName("internalFeatures")
                .build()).toJavaMap());
        GrpcConfiguration grpcConfig = new GrpcConfiguration();
        grpcConfig.setGrpcEnabledForDdmlTensorFlow(false);
        // Create mock tf adaptor
        TensorFlowModelAdaptor tensorFlowModelAdaptor = new TensorFlowModelAdaptor(
                predictionClientFactory,
                grpcChannelFactory,
                tensorFlowModelConfiguration,
                meterRegistry,
                flowConfig,
                modelService,
                grpcConfig
        );


        TfsClusterInteraction tfsClusterInteraction = new TfsClusterInteraction(
                "search",
                tfsClusterConfig,
                predictionClientFactory,
                grpcChannelFactory,
                tensorFlowModelConfiguration,
                meterRegistry,
                modelAwareClientProvider,
                modelService,
                flowConfig,
                grpcConfig
        );

        tfsClusterInteractionSpy = spy(tfsClusterInteraction);
        tensorFlowModelAdaptorSpy = Mockito.spy(tensorFlowModelAdaptor);
        doReturn(tfsClusterInteractionSpy).when(tensorFlowModelAdaptorSpy).getTfsCluster(any());
        ExternalModelInference externalModelInference = new TfsModelInference(tensorFlowModelAdaptorSpy, ddmlTensorflowGrpcClient);
        externalModelInferenceSpy = Mockito.spy(externalModelInference);

        // Avoid making an actual gRPC request
        PredictResponse.Builder fakePredictResponseBuilder = PredictResponse.newBuilder();
        doReturn(fakePredictResponseBuilder.build()).when(tfsClusterInteractionSpy).getPredictResponse(any(), any());

        ExecutorService batchInvokerExecutor = Mockito.spy(Executors.newFixedThreadPool(4));
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();  // Run immediately for test purposes
            return null;
        }).when(batchInvokerExecutor).execute(any(Runnable.class));

        client = new DefaultModelInferenceClient(tensorFlowModelAdaptorSpy,
                meterRegistry,
                tensorFlowModelConfiguration,
                ModelFactory.getJavaModels(),
                externalModelInferenceSpy,
                batchInvokerExecutor
        );

        when(tensorFlowModelAdaptorSpy.getTfsCluster(any())).thenReturn(tfsClusterInteractionSpy);
        PredictionServiceGrpc.PredictionServiceBlockingStub mockedClient = mock(PredictionServiceGrpc.PredictionServiceBlockingStub.class);
        when(tfsClusterInteractionSpy.getPredictionClient()).thenReturn(mockedClient);
    }

    @AfterEach
    void tearDown() {
        tensorFlowModelAdaptorSpy = null;
    }

    @Test
    void getTensorFlowDataTypeFromMappedInputValue() {
        assertThat(TensorFlowModelAdaptor.getTensorFlowDataTypeFromMappedInputValue(1.1))
                .isEqualTo(DataType.DT_DOUBLE);

        assertThat(TensorFlowModelAdaptor.getTensorFlowDataTypeFromMappedInputValue(1.0))
                .isEqualTo(DataType.DT_DOUBLE);

        assertThat(TensorFlowModelAdaptor.getTensorFlowDataTypeFromMappedInputValue(1.1f))
                .isEqualTo(DataType.DT_FLOAT);

        assertThat(TensorFlowModelAdaptor.getTensorFlowDataTypeFromMappedInputValue(1))
                .isEqualTo(DataType.DT_INT64);

        assertThat(TensorFlowModelAdaptor.getTensorFlowDataTypeFromMappedInputValue(ImmutableList.of(2L)))
                .isEqualTo(DataType.DT_INT64);

        assertThat(TensorFlowModelAdaptor.getTensorFlowDataTypeFromMappedInputValue(ImmutableList.of(1.0)))
                .isEqualTo(DataType.DT_DOUBLE);

        assertThat(TensorFlowModelAdaptor.getTensorFlowDataTypeFromMappedInputValue("foo"))
                .isEqualTo(DataType.DT_STRING);

        assertThat(TensorFlowModelAdaptor.getTensorFlowDataTypeFromMappedInputValue(ImmutableList.of("a", "b", "c")))
                .isEqualTo(DataType.DT_STRING);
    }

    @Test
    void convertFeaturesInputToExampleList() {
        List<Tuple2<List<Map<String, Object>>, Map<String, List<Example>>>> testCases = List.of(
                // Test case 1: Single value inputs
                Tuple.of(
                        List.of(
                                HashMap.of("f1", HashMap.of("x", 1.1)),
                                HashMap.of("f1", HashMap.of("x", 2.2)),
                                HashMap.of("f1", HashMap.of("x", 3.3))
                        ),
                        HashMap.of(
                                "f1",
                                List.of(
                                        Example.newBuilder()
                                                .setFeatures(
                                                        Features.newBuilder()
                                                                .putFeature(
                                                                        "x", Feature.newBuilder().setFloatList(
                                                                                FloatList.newBuilder()
                                                                                        .addValue(
                                                                                                1.1f
                                                                                        )
                                                                                        .build()
                                                                        ).build()
                                                                )
                                                                .build()
                                                ).build()
                                        , Example.newBuilder()
                                                .setFeatures(
                                                        Features.newBuilder()
                                                                .putFeature(
                                                                        "x", Feature.newBuilder().setFloatList(
                                                                                FloatList.newBuilder()
                                                                                        .addValue(
                                                                                                2.2f
                                                                                        )
                                                                                        .build()
                                                                        ).build()
                                                                )
                                                                .build()
                                                ).build()
                                        , Example.newBuilder()
                                                .setFeatures(
                                                        Features.newBuilder()
                                                                .putFeature(
                                                                        "x", Feature.newBuilder().setFloatList(
                                                                                FloatList.newBuilder()
                                                                                        .addValue(
                                                                                                3.3f
                                                                                        )
                                                                                        .build()
                                                                        ).build()
                                                                )
                                                                .build()
                                                ).build()
                                )
                        )
                ),
                Tuple.of(
                        List.of(
                                HashMap.of("f1", HashMap.of("x", 1.1, "y", 2.2)),
                                HashMap.of("f1", HashMap.of("x", 2.2, "y", 4.4)),
                                HashMap.of("f1", HashMap.of("x", 3.3, "y", 6.6))
                        ),
                        HashMap.of(
                                "f1",
                                List.of(
                                        Example.newBuilder()
                                                .setFeatures(
                                                        Features.newBuilder()
                                                                .putFeature(
                                                                        "x", Feature.newBuilder().setFloatList(
                                                                                FloatList.newBuilder()
                                                                                        .addValue(
                                                                                                1.1f
                                                                                        )
                                                                                        .build()
                                                                        ).build()
                                                                )
                                                                .putFeature(
                                                                        "y", Feature.newBuilder().setFloatList(
                                                                                FloatList.newBuilder()
                                                                                        .addValue(
                                                                                                2.2f
                                                                                        )
                                                                                        .build()
                                                                        ).build()
                                                                )
                                                                .build()
                                                ).build()
                                        , Example.newBuilder()
                                                .setFeatures(
                                                        Features.newBuilder()
                                                                .putFeature(
                                                                        "x", Feature.newBuilder().setFloatList(
                                                                                FloatList.newBuilder()
                                                                                        .addValue(
                                                                                                2.2f
                                                                                        )
                                                                                        .build()
                                                                        ).build()
                                                                )
                                                                .putFeature(
                                                                        "y", Feature.newBuilder().setFloatList(
                                                                                FloatList.newBuilder()
                                                                                        .addValue(
                                                                                                4.4f
                                                                                        )
                                                                                        .build()
                                                                        ).build()
                                                                )
                                                                .build()
                                                ).build()
                                        , Example.newBuilder()
                                                .setFeatures(
                                                        Features.newBuilder()
                                                                .putFeature(
                                                                        "x", Feature.newBuilder().setFloatList(
                                                                                FloatList.newBuilder()
                                                                                        .addValue(
                                                                                                3.3f
                                                                                        )
                                                                                        .build()
                                                                        ).build()
                                                                )
                                                                .putFeature(
                                                                        "y", Feature.newBuilder().setFloatList(
                                                                                FloatList.newBuilder()
                                                                                        .addValue(
                                                                                                6.6f
                                                                                        )
                                                                                        .build()
                                                                        ).build()
                                                                )
                                                                .build()
                                                ).build()
                                )
                        )
                )
        );

        testCases.forEach((t) -> {
            Map<String, List<Object>> groupedInputs = (
                    tensorFlowModelAdaptorSpy.convertFeaturesInputToExampleList(t._1)
            );

            assertThat(groupedInputs).isEqualTo(t._2);
        });
    }

    @Test
    void createTensorProtoFromFeaturesExamplesMap() {

        TensorShapeProto oneByThree = TensorShapeProto.newBuilder()
                .addDim(TensorShapeProto.Dim.newBuilder()
                        .setSize(1)
                        .build()
                )
                .addDim(TensorShapeProto.Dim.newBuilder()
                        .setSize(3)
                        .build()
                )
                .build();

        TensorShapeProto justThree = TensorShapeProto.newBuilder()
                .addDim(TensorShapeProto.Dim.newBuilder()
                        .setSize(3)
                        .build()
                )
                .build();

        List<Tuple2<List<Map<String, Object>>, Map<String, TensorProto>>> testCases = List.of(
                // Test case: Single value inputs
                Tuple.of(
                        List.of(
                                HashMap.of("x", HashMap.of("x", 1.1)),
                                HashMap.of("x", HashMap.of("x", 2.2)),
                                HashMap.of("x", HashMap.of("x", 3.3))
                        ),
                        HashMap.of(
                                "x",
                                TensorProto.newBuilder()
                                        .setDtype(DataType.DT_STRING)
                                        .setTensorShape(justThree)
                                        .addStringVal(
                                                Example.newBuilder()
                                                        .setFeatures(
                                                                Features.newBuilder()
                                                                        .putFeature(
                                                                                "x", Feature.newBuilder().setFloatList(
                                                                                        FloatList.newBuilder()
                                                                                                .addValue(
                                                                                                        1.1f
                                                                                                )
                                                                                                .build()
                                                                                ).build()
                                                                        )
                                                                        .build()
                                                        ).build().toByteString()
                                        )
                                        .addStringVal(
                                                Example.newBuilder()
                                                        .setFeatures(
                                                                Features.newBuilder()
                                                                        .putFeature(
                                                                                "x", Feature.newBuilder().setFloatList(
                                                                                        FloatList.newBuilder()
                                                                                                .addValue(
                                                                                                        2.2f
                                                                                                )
                                                                                                .build()
                                                                                ).build()
                                                                        )
                                                                        .build()
                                                        ).build().toByteString()
                                        )
                                        .addStringVal(
                                                Example.newBuilder()
                                                        .setFeatures(
                                                                Features.newBuilder()
                                                                        .putFeature(
                                                                                "x", Feature.newBuilder().setFloatList(
                                                                                        FloatList.newBuilder()
                                                                                                .addValue(
                                                                                                        3.3f
                                                                                                )
                                                                                                .build()
                                                                                ).build()
                                                                        )
                                                                        .build()
                                                        ).build().toByteString()
                                        )
                                        .build()
                        )
                )


//               Test case: Primitive value types
                , Tuple.of(
                        List.of(
                                HashMap.of("x", 1),
                                HashMap.of("x", 2),
                                HashMap.of("x", 3),
                                HashMap.of("y", 2),
                                HashMap.of("y", 4),
                                HashMap.of("y", 6)
                        ),
                        HashMap.of(
                                "x", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_INT64)
                                        .addAllInt64Val(List.of(1L, 2L, 3L).toJavaList())
                                        .setTensorShape(justThree).build(),
                                "y", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_INT64)
                                        .addAllInt64Val(List.of(2L, 4L, 6L).toJavaList())
                                        .setTensorShape(justThree).build()
                        )
                )

                // Test case: Arrays of primitive value types
                , Tuple.of(
                        List.of(
                                HashMap.of("x", List.of(1, 2, 3)),
                                HashMap.of("y", List.of(2, 4, 6))
                        ),
                        HashMap.of(
                                "x", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_INT64)
                                        .addAllInt64Val(List.of(1L, 2L, 3L).toJavaList())
                                        .setTensorShape(oneByThree).build(),
                                "y", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_INT64)
                                        .addAllInt64Val(List.of(2L, 4L, 6L).toJavaList())
                                        .setTensorShape(oneByThree).build()
                        )
                )

                // Test case: Primitive value types - conversion from double
                , Tuple.of(
                        List.of(
                                HashMap.of("x", 1.1),
                                HashMap.of("x", 2.1),
                                HashMap.of("x", 3.1),
                                HashMap.of("y", 2.1),
                                HashMap.of("y", 4.1),
                                HashMap.of("y", 6.1)
                        ),
                        HashMap.of(
                                "x", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_FLOAT)
                                        .addAllFloatVal(List.of(1.1f, 2.1f, 3.1f).toJavaList())
                                        .setTensorShape(justThree).build(),
                                "y", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_FLOAT)
                                        .addAllFloatVal(List.of(2.1f, 4.1f, 6.1f).toJavaList())
                                        .setTensorShape(justThree).build()
                        )
                )

                // Test case: Primitive value types - conversion from boolean
                , Tuple.of(
                        List.of(
                                HashMap.of("x", true),
                                HashMap.of("x", false),
                                HashMap.of("x", true),
                                HashMap.of("y", false),
                                HashMap.of("y", true),
                                HashMap.of("y", false)
                        ),
                        HashMap.of(
                                "x", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_INT64)
                                        .addAllInt64Val(List.of(1L, 0L, 1L).toJavaList())
                                        .setTensorShape(justThree).build(),
                                "y", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_INT64)
                                        .addAllInt64Val(List.of(0L, 1L, 0L).toJavaList())
                                        .setTensorShape(justThree).build()
                        )
                )

                // Test case: Primitive value types - conversion from string
                , Tuple.of(
                        List.of(
                                HashMap.of("x", "a"),
                                HashMap.of("x", "b"),
                                HashMap.of("x", "c"),
                                HashMap.of("y", "x"),
                                HashMap.of("y", "y"),
                                HashMap.of("y", "z")
                        ),
                        HashMap.of(
                                "x", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_STRING)
                                        .addAllStringVal(List.of("a", "b", "c").map(ByteString::copyFromUtf8).toJavaList())
                                        .setTensorShape(justThree).build(),
                                "y", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_STRING)
                                        .addAllStringVal(List.of("x", "y", "z").map(ByteString::copyFromUtf8).toJavaList())
                                        .setTensorShape(justThree).build()
                        )
                )

                // Test case: Primitive value types - conversion from list of strings
                , Tuple.of(
                        List.of(
                                HashMap.of("x", List.of("a", "b", "c")),
                                HashMap.of("y", List.of("x", "y", "z"))
                        ),
                        HashMap.of(
                                "x", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_STRING)
                                        .addAllStringVal(List.of("a", "b", "c").map(ByteString::copyFromUtf8).toJavaList())
                                        .setTensorShape(oneByThree).build(),
                                "y", TensorProto.newBuilder()
                                        .setDtype(DataType.DT_STRING)
                                        .addAllStringVal(List.of("x", "y", "z").map(ByteString::copyFromUtf8).toJavaList())
                                        .setTensorShape(oneByThree).build()
                        )
                )
        );

        testCases.forEach((t) -> {
            Map<String, List<Object>> groupedFeatures = tensorFlowModelAdaptorSpy.convertFeaturesInputToExampleList(t._1);

            java.util.Map<String, TensorProto> mapOfFeatureNamesTensorProtos = tensorFlowModelAdaptorSpy
                    .createTensorProtoFromFeaturesExamplesMap(groupedFeatures);

            assertThat(mapOfFeatureNamesTensorProtos).isEqualTo(t._2.toJavaMap());
        });
    }

    @Test
    void configureTensorFlow_should_update_TF_config_for_TF_models() {
        List<String> tensorFlowModelNames = List.of("m1", "m2", "m3");
        List<ModelDTO> tensorFlowModelsList = tensorFlowModelNames.map(ModelFactory::createModelWithName);

        List<String> nonTensorFlowModelNames = List.of("f1", "f2");

        List<ModelDTO> nonTensorFlowModels = nonTensorFlowModelNames
                .map(ModelFactory::createModelWithName)
                .peek(model -> model.setModelType("FUNCTION"));

        List<ModelDTO> combinedList = List.ofAll(nonTensorFlowModels).appendAll(tensorFlowModelsList);

        List<ModelConfigUpdateResponse> responses = tensorFlowModelsList
                .map(modelDTO -> ModelConfigUpdateResponse
                        .builder()
                        .message("Foo")
                        .code("200")
                        .build()
                );

        // Mock tensorflow update request

        doReturn("search").when(flowConfig).getTfsClusterNameForModel(any());
        doReturn(responses).when(tfsClusterInteractionSpy).updateModelConfigsAllHosts(any());
        doReturn(tfsClusterInteractionSpy).when(tensorFlowModelAdaptorSpy).getTfsClusterByName(eq("search"));
        assertThat(client.configureTensorFlow(combinedList)).isEqualTo(responses);
    }

    @Test
    void configureTensorFlow_should_exclude_any_model_with_a_null_serving_location() {
        val modelWithServingLocation = ModelFactory.createModelWithName("modelWithServingLocation");
        val modelWithNullServingLocation = ModelFactory.createModelWithName("modelWithoutServingLocation");
        modelWithNullServingLocation.setServingLocation(null);
        val modelList = List.of(modelWithServingLocation, modelWithNullServingLocation);
        modelList.forEach(modelService::createOrUpdateModel);

        doReturn("search").when(flowConfig).getTfsClusterNameForModel(any());

        appender = new ListAppender<>();
        appender.start();
        defaultModelInferenceClientLogger.addAppender(appender);

        client.configureTensorFlow(modelList);

        verify(tensorFlowModelAdaptorSpy, times(1)).getTfsClusterNameForModel(any(ModelDTO.class));

        val errorLog = appender.list.get(1);
        assertThat(errorLog.getLevel().levelStr).isEqualTo(Level.ERROR.toString());
        assertThat(errorLog.getFormattedMessage()).startsWith("Error creating ModelConfig from the model_name=modelWithoutServingLocation with error_message");

        defaultModelInferenceClientLogger.detachAppender(appender);
    }

    @Test
    void inferWithTensorFlow_should_throw_exception_when_tensorProto_outputs_map_is_empty() {
        val model = ModelFactory.createModel();
        model.setTfUseExamplesSerialization(true);
        when(modelInferenceInput.getProcessedFeatures()).thenReturn(List.empty());
        when(modelInferenceInput.getModel()).thenReturn(model);

        assertThatExceptionOfType(RuntimeException.class).isThrownBy(() -> client.inferWithTensorFlow(modelInferenceInput))
                .withMessageContaining(
                        String.format("TensorFlow output of model=%s is empty", "search_m1"));
    }

    @Test
    void inferWithTensorFlow_should_return_map_outputs_from_tensor() {
        val model = ModelFactory.createModel();
        model.setTfUseExamplesSerialization(true);
        when(modelInferenceInput.getProcessedFeatures()).thenReturn(List.empty());
        when(modelInferenceInput.getModel()).thenReturn(model);

        val predictResponse = mock(PredictResponse.class);
        doReturn(predictResponse).when(tfsClusterInteractionSpy).getPredictResponse(any(), any());
        java.util.Map<String, TensorProto> outputsProtoMap = new java.util.HashMap<>();
        float floatValue = 100.0f;

        // model has an output with name "output"
        outputsProtoMap.put("output", TensorProto.newBuilder()
                .addFloatVal(floatValue)
                .build());
        when(predictResponse.getOutputsMap()).thenReturn(outputsProtoMap);

        ModelInferenceResult inferenceResult = client.inferWithTensorFlow(modelInferenceInput);
        val tensorOutputMap = inferenceResult.getOutput();
        assertThat(tensorOutputMap.size()).isEqualTo(1);
        assertThat(tensorOutputMap.get("output").get().get(0).getFloat()).isEqualTo(floatValue);
    }

    @Test
    void inferWithTensorFlow_should_batchTensorflowInputs_persistOrderOfResults() {
        val model = ModelFactory.createModel();
        model.setTfUseExamplesSerialization(true);
        model.setInputBatchSize(5);
        model.setBatchingEnabled(true);
        val features = createFeatures();
        when(modelInferenceInput.getProcessedFeatures()).thenReturn(features);
        when(modelInferenceInput.getModel()).thenReturn(model);
        doAnswer(a -> {
            // we need to "tag" the output somehow - we'll use the batch sequence number for this
            val inputs = (ModelInferenceInput) a.getArguments()[0];
            java.util.List<ModelInferenceOutputType> outputs = inputs.getProcessedFeatures().map(m -> inputs.getBatchSequenceNumber().toString())
                    .map(Objects::hashCode)
                    .map(Float::valueOf)
                    .map(ModelInferenceOutputType::of)
                    .collect(Collectors.toList());

            return ModelInferenceResult.builder()
                    .batchSequenceNumber(inputs.getBatchSequenceNumber())
                    .output(HashMap.of("output", List.ofAll(outputs)))
                    .build();
        }).when(externalModelInferenceSpy).doInference(any(), any(), any(), any());

        ModelInferenceResult inferenceResult = client.inferWithTensorFlow(modelInferenceInput);
        val tensorOutputMap = inferenceResult.getOutput();

        // we expect that the result will be returned in the order of the batch
        assertThat(tensorOutputMap.size()).isEqualTo(1);
        assertThat(tensorOutputMap.get("output").get().get(0).getFloat()).isEqualTo(Float.valueOf("0".hashCode()));
        assertThat(tensorOutputMap.get("output").get().get(5).getFloat()).isEqualTo(Float.valueOf("1".hashCode()));
        assertThat(tensorOutputMap.get("output").get().get(10).getFloat()).isEqualTo(Float.valueOf("2".hashCode()));
        assertThat(tensorOutputMap.get("output").get().get(15).getFloat()).isEqualTo(Float.valueOf("3".hashCode()));
    }

    private List<Map<String, Object>> createFeatures() {
        List<Map<String, Object>> list = List.empty();
        for (int i = 0; i < 20; i++) {
            Map<String, Object> rows = HashMap.empty();
            rows = rows.put("RESTAURANT_ID", i);
            list = list.append(rows);
        }
        return list;
    }

    private java.util.Map<String, TensorProto> getTensorOutput(AtomicInteger incremental) {

        java.util.Map<String, TensorProto> outputsProtoMap = new java.util.HashMap<>();
        // model has an output with name "output"
        outputsProtoMap.put("output", TensorProto.newBuilder()
                .addAllFloatVal(List.of(
                        (float) incremental.incrementAndGet(),
                        (float) incremental.incrementAndGet(),
                        (float) incremental.incrementAndGet(),
                        (float) incremental.incrementAndGet(),
                        (float) incremental.incrementAndGet()
                ))
                .build());

        return outputsProtoMap;
    }

    @Test
    void inferWithTensorFlow_should_throw_exception_when_tensorProto_outputs_map_does_not_contain_model_outputNames() {
        val model = ModelFactory.createModel();
        model.setTfUseExamplesSerialization(true);
        when(modelInferenceInput.getProcessedFeatures()).thenReturn(List.empty());
        when(modelInferenceInput.getModel()).thenReturn(model);

        val predictResponse = mock(PredictResponse.class);
        doReturn(predictResponse).when(tfsClusterInteractionSpy).getPredictResponse(any(), any());
        java.util.Map<String, TensorProto> outputsProtoMap = new java.util.HashMap<>();
        float floatValue = 100.0f;

        // model has an output with name "output"
        outputsProtoMap.put("not_output", TensorProto.newBuilder()
                .addFloatVal(floatValue)
                .build());
        when(predictResponse.getOutputsMap()).thenReturn(outputsProtoMap);

        assertThatExceptionOfType(RuntimeException.class).isThrownBy(() -> client.inferWithTensorFlow(modelInferenceInput))
                .withMessageContaining(String.format("TensorFlow for output_name=%s of model=%s is null, available output names=%s.",
                        model.getModelOutputs().get(0).getOutputName(), model.getModelName(), outputsProtoMap.keySet()));
    }

}

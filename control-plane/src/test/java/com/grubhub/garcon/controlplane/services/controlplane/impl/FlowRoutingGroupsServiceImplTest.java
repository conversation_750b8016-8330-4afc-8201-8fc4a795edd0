package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowRoutingGroupCriteriaDao;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowRoutingGroupDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupCriteria;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.guice.ControlTestModule;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.controlplane.FlowRoutingGroupsService;
import com.grubhub.garcon.guice.PersistenceTestModule;
import com.grubhub.garcon.shared.CassandraTestCase;
import com.grubhub.roux.casserole.api.Casserole;
import com.grubhub.roux.test.cassandra.EmbeddedCassandraExt;
import com.grubhub.roux.uuid.UuidUtil;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import name.falgout.jeffrey.testing.junit.guice.GuiceExtension;
import name.falgout.jeffrey.testing.junit.guice.IncludeModule;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.grubhub.garcon.DaoUtils.truncateAllTables;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlow;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowWithMultipleRoutingGroups;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.FlowFactory.createFlowWithMultipleRoutingGroupsAndManyCriterias;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.DATA;
import static com.grubhub.garcon.shared.SharedEmbeddedCassandra.initCasserole;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.MockitoAnnotations.openMocks;

@ExtendWith({MockitoExtension.class, GuiceExtension.class})
@IncludeModule({PersistenceTestModule.class, ControlTestModule.class})
@Slf4j
@CassandraTestCase
class FlowRoutingGroupsServiceImplTest {

    @Inject
    private FlowRoutingGroupDao flowRoutingGroupDao;
    @Inject
    private FlowRoutingGroupCriteriaDao flowRoutingGroupCriteriaDao;
    @Inject
    private ControlPlaneMapper controlPlaneMapper;

    @Inject
    FlowRoutingGroupsService flowRoutingGroupsService;

    @RegisterExtension
    public static final EmbeddedCassandraExt EMBEDDED_CASSANDRA = new EmbeddedCassandraExt(DATA);
    private static Casserole casserole;

    @BeforeAll
    static void beforeClass() {
        casserole = initCasserole(EMBEDDED_CASSANDRA.getPort());
    }

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @AfterEach
    void afterEach() {
        truncateAllTables(casserole, EMBEDDED_CASSANDRA);
    }

    @AfterAll
    static void teardown() {
        casserole.shutdown();
    }

    @Test
    void insertRoutingGroups() {
        val flow = Option.of(createFlow())
                .map(controlPlaneMapper::toDTO)
                .get();

        flowRoutingGroupsService.insertRoutingGroups(flow);

        Optional<FlowRoutingGroupCriteria> flowRoutingGroupCriteria = flowRoutingGroupCriteriaDao.select(UuidUtil.decode(flow.getFlowId()), "default");

        assertThat(flowRoutingGroupCriteria.isPresent()).isTrue();
        assertThat(flowRoutingGroupCriteria.get().getRoutingGroupCriteria()).isEqualTo("default");

    }

    @Test
    void populateGroupOrderV2_should_execute_successfully_for_one_criteria() {
        val flow = Option.of(createFlowWithMultipleRoutingGroups())
                .map(controlPlaneMapper::toDTO)
                .get();

        flowRoutingGroupsService.insertRoutingGroups(flow);

        java.util.List<FlowRoutingGroupV2> flowRoutingGroupV2s = flowRoutingGroupsService.selectAllAsync(UuidUtil.decode(flow.getFlowId())).getList();

        assertThat(flowRoutingGroupV2s.stream().map(FlowRoutingGroupV2::getGroupOrder))
                .containsExactlyElementsOf(Stream.of(0, 1, 2, 3).collect(Collectors.toList()));

    }

    @Test
    void populateGroupOrderV2_should_execute_successfully_for_many_criteria() {
        val flow = Option.of(createFlowWithMultipleRoutingGroupsAndManyCriterias())
                .map(controlPlaneMapper::toDTO)
                .get();

        flowRoutingGroupsService.insertRoutingGroups(flow);

        java.util.List<FlowRoutingGroupV2> flowRoutingGroupV2s = flowRoutingGroupsService.selectAllAsync(UuidUtil.decode(flow.getFlowId())).getList();

        // here we have three criteria and the group order will be applied on each criteria
        // ex:
        //criteria 1 has two group orders:
        // groupOrder = 0 & groupOrder = 1
        // criteria two has one group order: groupOrder = 0
        // criteria three has one group order: groupOrder = 0
        // => 0, 1, 0, 0
        assertThat(flowRoutingGroupV2s.stream().map(FlowRoutingGroupV2::getGroupOrder))
                .containsExactlyInAnyOrder(0, 1, 0, 0);

    }


}

package com.grubhub.garcon.controlplane.resources;

import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.roux.api.responses.UpdateResponse;
import lombok.experimental.UtilityClass;

import javax.ws.rs.core.MediaType;
import java.time.Instant;
import java.util.Date;

import static com.grubhub.garcon.controlplane.resources.APIITConstants.CONTENT_TYPE;
import static com.grubhub.garcon.controlplane.resources.APIITConstants.getEnsemblesPath;
import static com.grubhub.garcon.controlplane.resources.APIITConstants.getFlowsPath;
import static com.grubhub.garcon.controlplane.resources.APIITConstants.getMarketsPath;
import static com.grubhub.garcon.controlplane.resources.APIITConstants.getModelsPath;
import static com.grubhub.garcon.controlplane.resources.ITFactory.createEnsemble;
import static com.grubhub.garcon.controlplane.resources.ITFactory.getClient;
import static com.grubhub.garcon.controlplane.services.controlplane.factory.MarketFactory.buildNewYorkMarketWithGeohashes;
import static com.grubhub.garcon.controlplane.services.ensembler.impl.ModelFactory.createModelWithName;
import static org.assertj.core.api.Assertions.assertThat;

@UtilityClass
public class ResourcesFactoryUtils {

    public static void createFlow(FlowApi flow, int publicPort) {
        UpdateResponse createFlowResponse = getClient().resource(getFlowsPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .put(UpdateResponse.class, flow);

        assertThat(createFlowResponse.getStatus()).isEqualTo(UpdateResponse.Status.SUCCESS);
    }

    public static void createEnsembleForModel(int publicPort) {
        UpdateResponse createEnsembleResponse = getClient()
                .resource(getEnsemblesPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .put(UpdateResponse.class, createEnsemble());

        assertThat(createEnsembleResponse.getStatus()).isEqualTo(UpdateResponse.Status.SUCCESS);
    }

    public static void createModel(int publicPort) {
        UpdateResponse createModelResponse = getClient().resource(getModelsPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .put(UpdateResponse.class, createModelWithName("search_m1_m2"));
        assertThat(createModelResponse.getStatus()).isEqualTo(UpdateResponse.Status.SUCCESS);
    }

    public static void createMarket(int publicPort) {
        UpdateResponse createMarketResponse = getClient()
                .resource(getMarketsPath(publicPort))
                .accept(MediaType.APPLICATION_JSON)
                .header(CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .put(UpdateResponse.class, buildNewYorkMarketWithGeohashes(Date.from(Instant.now())));
        assertThat(createMarketResponse.getStatus()).isEqualTo(UpdateResponse.Status.SUCCESS);
    }
}

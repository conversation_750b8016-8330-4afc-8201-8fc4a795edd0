package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.grubhub.garcon.controlplane.dto.EnrichedResolveFlowRequest;
import com.grubhub.garcon.controlplane.dto.FlowWithGeoHashDTO;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.api.Assertions;

import java.util.stream.Stream;

public class VersionMatcherTest {

    static class TestCase {
        String appVersion;
        String pattern;
        boolean expected;

        TestCase(String appVersion, String pattern, boolean expected) {
            this.appVersion = appVersion;
            this.pattern = pattern;
            this.expected = expected;
        }

        @Override
        public String toString() {
            return "appVersion=" + appVersion + ", pattern=" + pattern + ", expected=" + expected;
        }
    }

    static Stream<TestCase> versionMatchCases() {
        return Stream.of(
                new TestCase("1.0.0", "1.0.0", true),
                new TestCase("2.0.0", "1.0.0", false),
                new TestCase("3.1.0", "3.1.+", true),
                new TestCase("3.1.0", "3.2.+", false),
                new TestCase("3.1.8", "3.1.5+", true),
                new TestCase("3.2.8", "3.1.5+", true),
                new TestCase("3.2.8", "3.2+", true),
                new TestCase("3.2.8", "3+", true),
                new TestCase("3.asdf.rew+rqq.asdvke.djel", "3.1.5+", false),
                new TestCase("", "3.1.5+", false),
                new TestCase(null, "3.1.5+", false),
                new TestCase("2025.1.0", "2024.32+", true),
                new TestCase("2025.0", "2024.32+", true),
                new TestCase("9999.999.999", "+", true),
                new TestCase("4.2.0", "4.1.5+", true),
                new TestCase("2024.1", "2024+", true),
                new TestCase("2025.1", "2024.+", true),
                new TestCase("2025.1", "2025.2+", false),
                new TestCase("3.1", "3.1.5+", false),
                new TestCase("3.1", "+", true)
        );
    }

    @ParameterizedTest(name = "{index} => {0}")
    @MethodSource("versionMatchCases")
    public void versionCompare_paramTest(TestCase testCase) {
        EnrichedResolveFlowRequest request = EnrichedResolveFlowRequest.builder()
                .applicationVersion(testCase.appVersion)
                .build();

        FlowWithGeoHashDTO flowWithGeoHashDTO = FlowWithGeoHashDTO.builder()
                .resolveFlowRequest(request)
                .build();

        Assertions.assertEquals(
                testCase.expected,
                flowWithGeoHashDTO.matchingVersionId(testCase.pattern),
                testCase.toString()
        );
    }
}

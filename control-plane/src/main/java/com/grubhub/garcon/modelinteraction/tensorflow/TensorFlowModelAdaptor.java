package com.grubhub.garcon.modelinteraction.tensorflow;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.google.protobuf.ByteString;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.*;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.grpc.GrpcChannelFactory;
import com.grubhub.garcon.grpc.GrpcConfiguration;
import com.grubhub.garcon.modelinteraction.PredictionClientFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.vavr.Tuple;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Traversable;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.tensorflow.example.*;
import org.tensorflow.framework.DataType;
import org.tensorflow.framework.TensorProto;
import org.tensorflow.framework.TensorShapeProto;
import tensorflow.serving.Model;
import tensorflow.serving.ModelServerConfigOuterClass;

import java.util.Collection;
import java.util.HashMap;
import java.util.Set;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelAdaptor.Constants.FANOUT_INIT_DELAY_SECONDS;
import static com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelAdaptor.Constants.FANOUT_POLL_SECONDS;

@Slf4j
@Singleton
public class TensorFlowModelAdaptor {

    private static final ScheduledExecutorService EXECUTOR = new ScheduledThreadPoolExecutor(1);
    private final FlowConfig flowConfig;
    private final PredictionClientFactory predictionClientFactory;
    private final GrpcChannelFactory grpcChannelFactory;
    private final MeterRegistry meterRegistry;
    private final TensorFlowModelConfiguration tensorFlowModelConfiguration;
    private final java.util.Map<String, TfsClusterInteraction> tfsClusterInteractions;
    private final ModelService modelService;
    private final GrpcConfiguration grpcConfiguration;

    @Inject
    public TensorFlowModelAdaptor(
            PredictionClientFactory predictionClientFactory,
            GrpcChannelFactory grpcChannelFactory,
            TensorFlowModelConfiguration tensorFlowModelConfiguration,
            MeterRegistry meterRegistry,
            FlowConfig flowConfig,
            ModelService modelService,
            GrpcConfiguration grpcConfiguration
    ) {
        log.info("TFS cluster configs in TensorFlowModelAdaptor's constructor tfs_cluster_configs={}",
                tensorFlowModelConfiguration.getTfsClusterConfigs());
        this.flowConfig = flowConfig;
        this.grpcConfiguration = grpcConfiguration;
        this.predictionClientFactory = predictionClientFactory;
        this.grpcChannelFactory = grpcChannelFactory;
        this.meterRegistry = meterRegistry;
        this.tensorFlowModelConfiguration = tensorFlowModelConfiguration;
        this.modelService = modelService;
        this.tfsClusterInteractions = obtainClusterInteractions(tensorFlowModelConfiguration.getTfsClusterConfigs());


        if (shouldUseRouxManagedGrpcClient()) {
            log.info("Using Roux-managed gRPC clients; skipping manual TFS fanout polling.");
        } else {
            EXECUTOR.scheduleWithFixedDelay(
                    () ->
                            tfsClusterInteractions.values()
                                    .forEach(TfsClusterInteraction::refreshFanOutClients),
                    FANOUT_INIT_DELAY_SECONDS,
                    FANOUT_POLL_SECONDS,
                    TimeUnit.SECONDS
            );
        }
    }


    public static DataType getTensorFlowDataTypeFromMappedInputValue(Object mappedInputValue) {
        if (mappedInputValue != null) {
            if (mappedInputValue instanceof Float) {
                return DataType.DT_FLOAT;
            } else if (mappedInputValue instanceof Double) {
                return DataType.DT_DOUBLE;
            } else if (mappedInputValue instanceof Long || mappedInputValue instanceof Integer) {
                return DataType.DT_INT64;
            } else if (mappedInputValue instanceof String) {
                return DataType.DT_STRING;
            } else {
                if (mappedInputValue instanceof java.util.List) {
                    return getTensorFlowDataTypeFromMappedInputValue(((java.util.List<?>) mappedInputValue).get(0));
                } else {
                  log.info("Unable to resolve type: " + mappedInputValue + " type: " + mappedInputValue.getClass());
                }
            }
        }

        return null;
    }

    private java.util.Map<String, TfsClusterInteraction> obtainClusterInteractions(java.util.Map<String, TfsClusterConfig> tfsClusterConfigs) {
        val clusterInteractions = Option.of(tfsClusterConfigs)
                .map(java.util.Map::entrySet)
                .map(Set::stream)
                .map(tfsClusterConfigsStream -> tfsClusterConfigsStream
                        .collect(Collectors.toMap(java.util.Map.Entry::getKey, this::getTfsClusterInteraction)))
                .getOrElse(HashMap::new);
        log.info("Tfs cluster interactions obtained tfs_cluster_interactions={}", clusterInteractions);
        return clusterInteractions;
    }

    private TfsClusterInteraction getTfsClusterInteraction(java.util.Map.Entry<String, TfsClusterConfig> entry) {
        return getTfsClusterInteraction(entry.getKey(), entry.getValue());
    }

    private TfsClusterInteraction getTfsClusterInteraction(String tfsClusterName, TfsClusterConfig clusterConfig) {
        return new TfsClusterInteraction(
                tfsClusterName, clusterConfig,
                predictionClientFactory,
                grpcChannelFactory,
                tensorFlowModelConfiguration,
                meterRegistry,
                new ModelAwareClientProvider(meterRegistry),
                modelService,
                flowConfig,
                grpcConfiguration
        );
    }

    public ModelServerConfigOuterClass.ModelConfigList createModelConfigListFromTFModelConfigs(
            List<ModelServerConfigOuterClass.ModelConfig> tensorFlowModelConfigList
    ) {
        ModelServerConfigOuterClass.ModelConfigList.Builder configListBuilder = ModelServerConfigOuterClass.ModelConfigList.newBuilder();

        // Add configs
        tensorFlowModelConfigList.forEach(configListBuilder::addConfig);

        return configListBuilder.build();
    }

    public Feature featureFromValue(Float... values) {
        FloatList.Builder builder = FloatList.newBuilder();
        for (Float v : values) {
            builder.addValue(v);
        }
        return Feature.newBuilder().setFloatList(builder.build()).build();
    }

    public Feature featureFromValue(Long... values) {
        Int64List.Builder builder = Int64List.newBuilder();
        for (Long v : values) {
            builder.addValue(v);
        }
        return Feature.newBuilder().setInt64List(builder.build()).build();
    }

    public Feature featureFromValue(String... values) {
        BytesList.Builder builder = BytesList.newBuilder();
        for (String v : values) {
            builder.addValue(ByteString.copyFromUtf8(v));
        }
        return Feature.newBuilder().setBytesList(builder).build();
    }

    public Example buildExample(java.util.Map<String, ?> featureMapping) {
        Features.Builder featuresBuilder = Features.newBuilder();
        featureMapping.forEach((featureName, featureValue) -> {
            if (featureValue instanceof Float) {
                featuresBuilder.putFeature(featureName, featureFromValue((Float) featureValue));
            } else if (featureValue instanceof Double) {
                // Cast Doubles as floats
                featuresBuilder.putFeature(featureName, featureFromValue(((Double) featureValue).floatValue()));
            } else if (featureValue instanceof Long) {
                featuresBuilder.putFeature(featureName, featureFromValue((Long) featureValue));
            } else if (featureValue instanceof String) {
                featuresBuilder.putFeature(featureName, featureFromValue(((String) featureValue)));
            } else if (featureValue instanceof Integer) {
                featuresBuilder.putFeature(featureName, featureFromValue(((Integer) featureValue).longValue()));
            } else if (featureValue instanceof Boolean) {
                featuresBuilder.putFeature(featureName, featureFromValue(Boolean.TRUE.equals(featureValue) ? 1L : 0L));
            } else {
                throw new UnsupportedOperationException("Unsupported feature type from=" + featureValue);
            }
        });

        Features features = featuresBuilder.build();
        return Example.newBuilder().setFeatures(features).build();
    }

    void addNumericalValueToTensorProtoBuilder(TensorProto.Builder tpb, Number value) {
        try {
            if (value instanceof Float) {
                tpb.setDtype(DataType.DT_FLOAT);
                tpb.addFloatVal((Float) value);
            } else if (value instanceof Double) {
                tpb.setDtype(DataType.DT_FLOAT);
                tpb.addFloatVal(value.floatValue());
            } else if (value instanceof Integer) {
                tpb.setDtype(DataType.DT_INT64);
                tpb.addInt64Val(value.intValue());
            } else if (value instanceof Long) {
                tpb.setDtype(DataType.DT_INT64);
                tpb.addInt64Val(value.longValue());
            } else if (value instanceof Short) {
                tpb.setDtype(DataType.DT_INT64);
                tpb.addInt64Val(value.shortValue());
            } else {
                throw new RuntimeException("Unsupported numeric value for TFS, value=" + value);
            }
        } catch (Exception e) {
            log.warn("Unable to parse number={}, Using default=0.0f", value, e);
            tpb.setDtype(DataType.DT_FLOAT);
            tpb.addFloatVal(0.0f);
        }
    }

    void addBooleanValueToTensorProtoBuilder(TensorProto.Builder tpb, boolean value) {
        tpb.setDtype(DataType.DT_INT64);
        tpb.addInt64Val((value) ? 1L : 0L);
    }

    void addStringValueToTensorProtoBuilder(TensorProto.Builder tpb, String value) {
            tpb.setDtype(DataType.DT_STRING);
            tpb.addStringVal(ByteString.copyFromUtf8(value));
    }

    void addExampleToTensorProto(TensorProto.Builder tpb, Object value, java.util.List<Integer> dims, Integer depth) {
        if (value instanceof Number) {
            addNumericalValueToTensorProtoBuilder(tpb, (Number) value);
        } else if (value instanceof Boolean) {
            addBooleanValueToTensorProtoBuilder(tpb, (boolean) value);
        } else if (value instanceof String) {
            addStringValueToTensorProtoBuilder(tpb, (String) value);
        } else if (value instanceof Example) {
            tpb.setDtype(DataType.DT_STRING);
            tpb.addStringVal(((Example) value).toByteString());
        } else if (value instanceof Collection) {
            if (depth < TensorFlowModelAdaptor.Constants.MAX_TENSOR_DIMENSION) {
                if (depth < dims.size()) {
                    dims.add(((Collection<?>) value).size());
                }
                // Add nested dimension size
                ((Collection<?>) value).forEach(element -> addExampleToTensorProto(tpb, element, dims, depth + 1));
            }
        } else {
            throw new UnsupportedOperationException("Unhandled input type=" + value);
        }
    }

    public java.util.Map<String, TensorProto> createTensorProtoFromFeaturesExamplesMap(
            Map<String, List<Object>> groupedFeaturesAndInputs
    ) {
        java.util.Map<String, TensorProto> result = new java.util.HashMap<>();
        groupedFeaturesAndInputs.forEach((featureName, groupedInputExamples) -> {
            TensorProto.Builder tpb = TensorProto.newBuilder();

            TensorShapeProto.Builder shapeProto = TensorShapeProto.newBuilder();

            java.util.List<Integer> dims = List.of(groupedInputExamples.size()).toJavaList();

            groupedInputExamples.forEach((example -> addExampleToTensorProto(tpb, example, dims, 0)));

            // Adding dims
            dims.forEach(d -> shapeProto.addDim(
                    TensorShapeProto.Dim.newBuilder().setSize(d).build()
            ));

            tpb.setTensorShape(shapeProto.build());
            result.put(featureName, tpb.build());
        });

        return result;
    }

    @SuppressWarnings("unchecked")
    public Map<String, List<Object>> convertFeaturesInputToExampleList(
            List<Map<String, Object>> processedFeatures
    ) {
        Map<String, List<Object>> groupedFeatures = processedFeatures
                .map(i -> i.mapValues(List::of)) // Wrap in list
                .reduce((a, b) -> a.merge(b, List::appendAll));

        return groupedFeatures.map(
                (featureName, featureInputsJsonObjList) -> {
                    List<Object> accumulator = featureInputsJsonObjList
                            .map((o) -> {
                                if (o instanceof String
                                        || o instanceof Number
                                        || o instanceof Collection
                                        || o instanceof Boolean
                                ) {
                                    return o;
                                // Handling vavr types
                                } else if (o instanceof List) {
                                    return ((List<?>) o).toJavaList();
                                } else if (o instanceof Map) {
                                    return buildExample(((Map<String, ?>) o).toJavaMap());
                                // Handling alternative Example type
                                } else if (o instanceof java.util.Map) {
                                    return buildExample((java.util.Map<String, ?>) o);
                                } else {
                                    throw new UnsupportedOperationException("Unable to parse TFS input value type=" + o);
                                }
                            });
                    return Tuple.of(featureName, accumulator);
                }
        );
    }

    public Map<String, TensorProto> convertFeaturesToSerializedExamples(String inputName, List<Map<String, Object>> processedFeatures) {
        java.util.List<ByteString> serializedExamples = processedFeatures.map(features -> {
            val featuresBuilder = Features.newBuilder();
            features.forEach((key, value) -> featuresBuilder.putFeature(key, toTensorFlowExampleFeature(key, value)));
            return Example.newBuilder().setFeatures(featuresBuilder.build()).build().toByteString();
        }).toJavaList();
        TensorProto inputsProto = TensorProto.newBuilder()
                .addAllStringVal(serializedExamples)
                .setDtype(DataType.DT_STRING)
                .setTensorShape(TensorShapeProto.newBuilder()
                        .addDim(TensorShapeProto.Dim.newBuilder().setSize(processedFeatures.size()).build())
                        .build())
                .build();
        return io.vavr.collection.HashMap.of(inputName, inputsProto);
    }

    private Feature toTensorFlowExampleFeature(String key, Object o) {
        log.debug("parsing tfs example feature. key={}, value={}", key, o);

        val f = Feature.newBuilder();
        if (o == null) {
            return f.build();
        }
        if (o instanceof Long) {
            addListAsTensorFlowExampleFeature(f, List.of((long) o));
        } else if (o instanceof Integer) {
            addListAsTensorFlowExampleFeature(f, List.of((int) o));
        } else if (o instanceof Short) {
            addListAsTensorFlowExampleFeature(f, List.of((short) o));
        }  else if (o instanceof Byte) {
            addListAsTensorFlowExampleFeature(f, List.of((byte) o));
        } else if (o instanceof Float) {
            addListAsTensorFlowExampleFeature(f, List.of((float) o));
        } else if (o instanceof Double) {
            addListAsTensorFlowExampleFeature(f, List.of((double) o));
        } else if (o instanceof String) {
            addListAsTensorFlowExampleFeature(f, List.of((String) o));
        } else if (o instanceof java.util.Collection) {
            addListAsTensorFlowExampleFeature(f, List.ofAll((java.util.Collection<?>) o));
        } else if (o instanceof List) {
            addListAsTensorFlowExampleFeature(f, (List<?>) o);
        } else if (o instanceof Traversable) {
            addListAsTensorFlowExampleFeature(f, List.ofAll((Traversable<?>) o));
        } else {
            log.warn("Invalid TFS feature type for key={}, value={}", key, o);
        }

        Feature feature = f.build();
        log.debug("parsed tfs feature={}", feature);
        return feature;
    }

    private void addListAsTensorFlowExampleFeature(Feature.Builder f, List<?> list) {
        if (list.nonEmpty()) {
            Object first = list.get(0);

            // Convert to Int64List
            if (first instanceof Long || first instanceof Integer || first instanceof Short || first instanceof Byte) {
                val builder = Int64List.newBuilder();
                list.forEach(i -> builder.addValue(((Number) i).longValue()));
                f.setInt64List(builder);

            // Convert to FloatList
            } else if (first instanceof Float) {
                val builder = FloatList.newBuilder();
                list.forEach(i -> builder.addValue(((Float) i)));
                f.setFloatList(builder);
            } else if (first instanceof Double) {
                val builder = FloatList.newBuilder();
                list.forEach(i -> builder.addValue(((Double) i).floatValue()));
                f.setFloatList(builder);

            // Convert to BytesList
            } else if (first instanceof String) {
                val builder = BytesList.newBuilder();
                list.forEach(i -> builder.addValue(ByteString.copyFromUtf8((String) i)));
                f.setBytesList(builder);
            }
        }
    }

    public List<ModelInferenceOutputType> extractOutputFromTensor(TensorProto tensor, ModelOutputType outputType, List<Integer> outputShape) {
        switch (outputType) {
            case FLOAT:
                return ModelInferenceOutputType.ofFloatList(tensor.getFloatValList());
            case LONG:
                return ModelInferenceOutputType.ofLongList(tensor.getInt64ValList());
            case STRING:
                return ModelInferenceOutputType.ofStringList(List.ofAll(tensor.getStringValList()).map(ByteString::toStringUtf8));
            case FLOAT_ARRAY:
                return ModelInferenceOutputType.ofFloatArrayList(outputTensorToArrayOfItems(tensor.getFloatValList(), outputShape));
            case LONG_ARRAY:
                return ModelInferenceOutputType.ofLongArrayList(outputTensorToArrayOfItems(tensor.getInt64ValList(), outputShape));
            case STRING_ARRAY:
                return ModelInferenceOutputType.ofStringArrayList(outputTensorToArrayOfItems(
                        tensor.getStringValList().stream().map(ByteString::toStringUtf8).collect(Collectors.toList()),
                        outputShape));
            default:
                throw new RuntimeException("Invalid TF tensor output_type=" + outputType.name());
        }
    }

    private <T> List<List<T>> outputTensorToArrayOfItems(java.util.List<T> items, List<Integer> outputShape) {
        int groupSize = outputShape != null && outputShape.nonEmpty() ? Math.max(outputShape.get(0), 1) : 1;
        return List.ofAll(items).grouped(groupSize).toList();
    }

    public Model.ModelSpec createModelSpec(ModelDTO model) {
        return getTfsCluster(model).createModelSpec(model);
    }

    public TensorFlowModelMetadataDTO getTensorFlowMetadata(ModelDTO model) {
        return getTfsCluster(model).getTensorFlowMetadata(model);
    }

    public ModelStatus getTensorFlowStatus(ModelDTO model) {
        return getTfsCluster(model).getTensorFlowStatus(model);
    }

    public TfsClusterInteraction getTfsCluster(ModelDTO modelDTO) {
        if (!flowConfig.getModelGroupConfigs().containsKey(modelDTO.getModelGroup())) {
            throw new RuntimeException(String.format("There is no model group %s for model %s found " +
                    "on tfs clusters %s", modelDTO.getModelGroup(), modelDTO.getModelName(), flowConfig.getModelGroupConfigs().keySet()));
        }
        val modelGroupConfig = flowConfig.getModelGroupConfigs().get(modelDTO.getModelGroup());
        log.debug("Getting TFS cluster for model={} within group={}", modelDTO.getModelName(), modelDTO.getModelGroup());
        val tfsClusterInteraction = Option.of(tfsClusterInteractions.get(modelGroupConfig.getTfsClusterName()))
                .getOrElseThrow(() -> new RuntimeException(
                        String.format("There is no cluster %s for model %s within group %s found " +
                                        "on the following tfs clusters %s", modelGroupConfig.getTfsClusterName(),
                                modelDTO.getModelName(),
                                modelDTO.getModelGroup(),
                                tfsClusterInteractions.values().stream().map(TfsClusterInteraction::getTfsClusterName).collect(Collectors.toList())))
                );
        log.debug("Found tfs_cluster={} for model={} within group={}", tfsClusterInteraction.getTfsClusterName(),
                modelDTO.getModelName(), modelDTO.getModelGroup());
        return tfsClusterInteraction;
    }

    public TfsClusterInteraction getTfsClusterByName(String tfsClusterName) {
        return tfsClusterInteractions.get(tfsClusterName);
    }

    public String getTfsClusterNameForModel(ModelDTO modelDTO) {
        return flowConfig.getTfsClusterNameForModel(modelDTO);
    }

    public static class Constants {
        public static final Integer MAX_TENSOR_DIMENSION = 5;
        public static final long FANOUT_INIT_DELAY_SECONDS = 15;
        public static final long FANOUT_POLL_SECONDS = 30;
    }

    public boolean shouldUseRouxManagedGrpcClient() {
        return ControlPlaneUtils.isTrue(grpcConfiguration.isGrpcEnabledForDdmlTensorFlow());
    }
}

package com.grubhub.garcon.modelinteraction.vectorsimilaritymodel;

import io.vavr.collection.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CosineSimilarityCalculator {

    public static float cosineSimilarity(final List<Float> u, final List<Float> v) {
        if (u.size() != v.size()) {
            final String message = String.format("The query and target vectors have different length, it's not possible to calculate similarity. " +
                    "Input length=%s, Target length=%s", u.size(), v.size());
            throw new RuntimeException(message);
        }

        if (isZeroVec(u) || isZeroVec(v)) {
            log.error("One of the vectors to is zero (in all the components). Unable to perform cosine similarity, returning minimum value.");
            return -1;
        }

        return dot(u, v) / (norm(u) * norm(v));
    }

    public static float cosineToAngularNormalization(float similarity) {
        // When abs(similarity) > 1 => Math.acos(similarity) = NaN.
        // This can happen when there is precision loss, such as 1.00001. Otherwise, this should not be necessary.
        if (similarity > 1) {
            similarity = 1;
        } else if (similarity < -1) {
            similarity = -1;
        }

        return (float) (1 - Math.acos(similarity) / Math.PI);
    }

    private static float norm(final List<Float> u) {
        float n = 0;
        for (float x : u) {
            n += x * x;
        }
        return (float) Math.sqrt(n);
    }

    private static float dot(final List<Float> u, final List<Float> v) {
        double d = 0;
        for (int i = 0; i < u.size(); i++) {
            d += u.get(i) * v.get(i);
        }
        return (float) d;
    }

    public static boolean isZeroVec(List<Float> v) {
        for (float value : v) {
            if (value != 0) {
                return false;
            }
        }
        return true;
    }

}

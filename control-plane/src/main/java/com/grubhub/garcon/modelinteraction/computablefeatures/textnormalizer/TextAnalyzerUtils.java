package com.grubhub.garcon.modelinteraction.computablefeatures.textnormalizer;

import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.tokenattributes.CharTermAttribute;

import java.io.IOException;
import java.io.StringReader;
import java.util.Optional;
import java.util.StringJoiner;

@Slf4j
public class TextAnalyzerUtils {

    /**
     * Use to analyze and tokenize text with the given {@link Analyzer}, will recompose the text string after
     * analysis by joining the tokens with a {@link StringJoiner} on " "
     */
    public static Optional<String> analyzeAndJoin(String text, Analyzer analyzer) {
        return analyzeAndJoin(text, analyzer, " ");
    }

    /**
     * Use to analyze and tokenize text with the given {@link Analyzer}, will recompose the text string after
     * analysis by joining the tokens with a given {@link StringJoiner}
     */
    public static Optional<String> analyzeAndJoin(String text, Analyzer analyzer, CharSequence delimiter) {
        StringJoiner sj = new StringJoiner(delimiter);
        TokenStream ts = analyzer.tokenStream("", new StringReader(text));

        try {
            ts.reset();
            while (ts.incrementToken()) {
                sj.add(ts.getAttribute(CharTermAttribute.class).toString());
            }
            ts.close();
            return Optional.of(sj.toString());
        } catch (IOException e) {
            log.warn("Search Analysis failed on analyzing text [{}] due to IOException", text, e);
            return Optional.empty();
        }
    }

}

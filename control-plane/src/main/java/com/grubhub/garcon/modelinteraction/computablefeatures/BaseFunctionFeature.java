package com.grubhub.garcon.modelinteraction.computablefeatures;

import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class BaseFunctionFeature {

    @Getter
    private final FunctionFeatureType functionType;

    public BaseFunctionFeature(FunctionFeatureType functionType) {
        this.functionType = functionType;
    }

    public abstract Map<String, Object> compute(Map<String, Object> inputs);

    protected int getInteger(Map<String, Object> inputs, String inputKey) {
        return ControlPlaneUtils.parseInteger(getObject(inputs.get(inputKey), inputKey), 0);
    }

    protected double getDouble(Map<String, Object> inputs, String inputKey) {
        return ControlPlaneUtils.parseDouble(getObject(inputs.get(inputKey), inputKey), 0);
    }

    protected boolean getBoolean(Map<String, Object> inputs, String inputKey) {
        return ControlPlaneUtils.parseBoolean(getObject(inputs.get(inputKey), inputKey), false);
    }

    protected String getString(Map<String, Object> inputs, String inputKey) {
        return String.valueOf(getObject(inputs.get(inputKey), inputKey));
    }

    protected Map<String, Object> createOutput(String key, Object value) {
        return HashMap.of(key, value);
    }

    protected Map<String, Object> createOutput(String key1, Object value1, String key2, Object value2) {
        return HashMap.of(key1, value1, key2, value2);
    }

    private Object getObject(Object valueObj, String inputKey) {
        Option<?> valueOption = (Option<?>) valueObj;
        return valueOption.getOrElseThrow(() -> new RuntimeException(
                String.format("Invalid input key=%s for function=%s",
                        inputKey, functionType)));
    }
}

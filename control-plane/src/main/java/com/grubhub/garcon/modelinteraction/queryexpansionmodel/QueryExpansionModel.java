package com.grubhub.garcon.modelinteraction.queryexpansionmodel;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.modelinteraction.JavaModel;
import com.grubhub.garcon.modelinteraction.ModelWithDefaultOutput;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;


/**
 * Responsible for invoking the query expansion process.
 * It uses the BedrockRuntimeClientApi to get query expansions based on the input features.
 * The input features are expected to contain the query text and the model name.
 * The output is a list of query expansions.
 */

@Slf4j
@Singleton
public class QueryExpansionModel implements JavaModel, ModelWithDefaultOutput {

    public static final String QUERY_TEXT = "query_text";
    public static final String BEDROCK_MODEL_NAME = "model_name";
    public static final String DELIMITER = ",";
    public static final String QUERY_EXPANSION_OUTPUT_NAME = "query_expansion";

    private final QueryExpansionModelConfig queryExpansionModelConfig;
    private final BedrockConfiguration bedrockConfiguration;
    private final LLMClient queryExpansionClient;
    private final ModelPrompt modelPrompt;

    @Inject
    public QueryExpansionModel(QueryExpansionModelConfig queryExpansionModelConfig,
                               BedrockConfiguration bedrockConfiguration,
                               LLMClient queryExpansionClient,
                               ModelPrompt modelPrompt) {
        this.queryExpansionModelConfig = queryExpansionModelConfig;
        this.bedrockConfiguration = bedrockConfiguration;
        this.queryExpansionClient = queryExpansionClient;
        this.modelPrompt = modelPrompt;
    }

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {

        if (input.getProcessedFeatures() == null || input.getProcessedFeatures().isEmpty()) {
            throw new RuntimeException("Processed feature list is null or empty when calling QueryExpansionModel.");
        }

        final Map<String, Object> firstEntry = input.getProcessedFeatures().head();
        log.debug("First Entry (Features Map)={}", firstEntry);

        String query = firstEntry.getOrElse(QUERY_TEXT, "").toString();
        String modelName = bedrockConfiguration.getBedrockModelConfig().getModelName();

        if (StringUtils.isEmpty(query) || StringUtils.isEmpty(modelName)) {
            log.error("No query or model name given, {}", firstEntry);
            return defaultOutput(input);
        }

        final String promptWithQuery = modelPrompt.resolvePromptWithQuery(query);
        final String promptJson = modelPrompt.resolvePromptToJson(promptWithQuery);
        final String modelResponse = queryExpansionClient.invoke(modelName, promptJson);
        log.debug("QueryExpansionClient response={}", modelResponse);

        final List<String> queryExpansions = splitAndTrim(modelResponse);

        List<ModelInferenceOutputType> response = ModelInferenceOutputType.ofStringList(queryExpansions);
        Map<String, List<ModelInferenceOutputType>> output = HashMap.of(QUERY_EXPANSION_OUTPUT_NAME, response);

        return ModelInferenceResult.builder()
                .output(output)
                .build();
    }

    @Override
    public Map<String, List<ModelInferenceOutputType>> generateDefaultOutput(ModelInferenceInput input) {
        return HashMap.of(QUERY_EXPANSION_OUTPUT_NAME, ModelInferenceOutputType.ofStringList(List.empty()));
    }

    private List<String> splitAndTrim(String input) {
        if (StringUtils.isBlank(input)) {
            return List.of();
        }

        return List.of(input.split(DELIMITER))
                .filter(StringUtils::isNotBlank)
                .map(String::trim);
    }

}


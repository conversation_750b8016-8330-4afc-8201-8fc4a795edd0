package com.grubhub.garcon.modelinteraction.utils;

import com.grubhub.garcon.controlplane.services.common.utils.InternalFieldsUtils;
import com.grubhub.garcon.modelinteraction.RandomDistributionModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;

@Slf4j
public class SeedGeneratorUtils {
    public static String computeSeed(Map<String, Object> features, String defaultSeedMode, String additionalSeed) {
        String seed = "";

        String seedMode = String.valueOf(features.getOrDefault(RandomDistributionModel.SEED_MODE, defaultSeedMode));

        String lookupKeys = features.getOrDefault(InternalFieldsUtils.getInternalLookupKeysField(0), "").toString();

        if (RandomDistributionModel.TRACKING_ID.equals(seedMode)) {
            seed = seed + Optional.ofNullable(features.getOrDefault(RandomDistributionModel.TRACKING_ID, null))
                    .orElseThrow(() -> new IllegalArgumentException("tracking_id can't be null when using TRACKING_ID mode"));

        } else if (RandomDistributionModel.DINER_ID.equals(seedMode)) {
            seed = seed + Optional.ofNullable(features.getOrDefault(RandomDistributionModel.DINER_ID, null))
                    .orElseThrow(() -> new IllegalArgumentException("diner_id can't be null when using DINER_ID mode"));
        } else {
            log.debug("No seed_mode provided.");
        }

        if (StringUtils.isNotEmpty(lookupKeys)) {
            seed = seed + "-" + lookupKeys;
        }

        if (StringUtils.isNotEmpty(additionalSeed)) {
            seed = seed + "-" + additionalSeed;
        }

        if (log.isDebugEnabled()) {
            log.debug("Using seedMode={}, lookup keys={}, seed value={}.", seedMode, lookupKeys, seed);
        }

        return seed;
    }
}

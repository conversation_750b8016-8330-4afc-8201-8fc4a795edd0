package com.grubhub.garcon.modelinteraction.computablefeatures;

import ch.hsr.geohash.GeoHash;
import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import io.vavr.collection.Map;
import lombok.val;

/**
 * This function calculates the latitude and longitude coordinates based on the given geohash.
 * Inputs: map with key: geohash.
 * Outputs: map with keys: lat, lng.
 */
public class GeohashToCoordinatesFunction extends BaseFunctionFeature {

    public static final String GEOHASH = "geohash";
    public static final String LATITUDE = "lat";
    public static final String LONGITUDE = "lng";

    public GeohashToCoordinatesFunction() {
        super(FunctionFeatureType.GEOHASH_TO_COORDINATES);
    }

    public Map<String, Object> compute(Map<String, Object> inputs) {
        val geohash = GeoHash.fromGeohashString(getString(inputs, GEOHASH));
        val latitude = geohash.getPoint().getLatitude();
        val longitude = geohash.getPoint().getLongitude();
        return createOutput(LATITUDE, latitude, LONGITUDE, longitude);
    }
}

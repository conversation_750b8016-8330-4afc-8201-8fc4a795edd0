package com.grubhub.garcon.modelinteraction.distribution;

import com.grubhub.garcon.modelinteraction.RandomDistributionModel;
import com.grubhub.garcon.modelinteraction.utils.SeedGeneratorUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class RandomDistributionFactory {
    private static final String HASH_CONSTANT = "VERY_CONSTANT_STRING";

    @SuppressWarnings("unchecked")
    public BaseDistribution createRandomDistribution(DistributionType type,
                                                            DistributionResultType resultType,
                                                            Map<String, Object> features) {
        if (type == DistributionType.BETA) {
            log.debug("Running BETA distribution, with {} mode.", resultType);

            List<String> targetValues = (List<String>) convertObjectToList(features.getOrDefault(RandomDistributionModel.INTERNAL_TARGET_VALUES_KEY, null));
            List<Number> alphaParams = (List<Number>) convertObjectToList(features.getOrDefault(RandomDistributionModel.INTERNAL_ALPHA_PARAMS_KEY, null));
            List<Number> betaParams = (List<Number>) convertObjectToList(features.getOrDefault(RandomDistributionModel.INTERNAL_BETA_PARAMS_KEY, null));

            if (!isValidInput(targetValues, alphaParams, betaParams, resultType)) {
                return new BetaDistribution(Collections.emptyList());
            }


            List<BetaDistribution.BetaInput> betaInputs = new ArrayList<>();
            for (int index = 0; index < alphaParams.size(); index++) {
                if (DistributionResultType.RANDOM_SAMPLING.equals(resultType)) {
                    final String individualSeed = targetValues.get(index);
                    val seed = SeedGeneratorUtils.computeSeed(features, RandomDistributionModel.DEFAULT_SEED_MODE,
                            individualSeed);

                    betaInputs.add(new BetaDistribution.BetaInput(
                            targetValues.get(index),
                            seed,
                            alphaParams.get(index).doubleValue(),
                            betaParams.get(index).doubleValue()));
                } else if (DistributionResultType.RANKING.equals(resultType)) {
                    val seed = SeedGeneratorUtils.computeSeed(features, RandomDistributionModel.DEFAULT_SEED_MODE,
                            HASH_CONSTANT);

                    betaInputs.add(new BetaDistribution.BetaInput(
                            alphaParams.get(index).doubleValue(),
                            betaParams.get(index).doubleValue(),
                            seed));
                }
            }
            log.debug("Beta inputs for model {}", betaInputs);
            return new BetaDistribution(betaInputs);
        }
        log.error("Distribution type {} not implemented yet", type.toString());
        throw new IllegalStateException("Distribution type not implemented.");
    }

    private static boolean isValidInput(List<String> targetValues, List<Number> alphaParams, List<Number> betaParams,
                                        DistributionResultType mode) {
        if (CollectionUtils.isEmpty(alphaParams) || alphaParams.size() != betaParams.size()) {
            log.debug("Invalid arguments provided - alpha values=({}) and beta=({})", alphaParams, betaParams);
            return false;
        }

        if (DistributionResultType.RANDOM_SAMPLING.equals(mode)) {
            if (CollectionUtils.isEmpty(targetValues)) {
                log.debug("Invalid arguments provided - target values=({}) for mode={}", targetValues, mode);
                return false;
            }

            if (targetValues.size() != alphaParams.size()) {
                log.debug("Invalid arguments provided - target values size={} expected={}", targetValues.size(),
                        alphaParams.size());
                return false;
            }
        }
        return true;
    }

    public static List<?> convertObjectToList(Object obj) {
        if (Objects.isNull(obj)) {
            return new ArrayList<>();
        }
        List<?> list = new ArrayList<>();
        if (obj.getClass().isArray()) {
            list = Arrays.asList((Object[]) obj);
        } else if (obj instanceof Collection) {
            list = new ArrayList<>((Collection<?>) obj);
        }
        return list;
    }
}

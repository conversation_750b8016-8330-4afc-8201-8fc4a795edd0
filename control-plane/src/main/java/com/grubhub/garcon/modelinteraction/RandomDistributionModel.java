package com.grubhub.garcon.modelinteraction;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.modelinteraction.distribution.BaseDistribution;
import com.grubhub.garcon.modelinteraction.distribution.DistributionResult;
import com.grubhub.garcon.modelinteraction.distribution.DistributionResultType;
import com.grubhub.garcon.modelinteraction.distribution.DistributionType;
import com.grubhub.garcon.modelinteraction.distribution.RandomDistributionFactory;
import io.vavr.collection.HashMap;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static io.vavr.API.Some;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class RandomDistributionModel implements JavaModel, ModelWithDefaultOutput {
    public static final String DINER_ID = "diner_id";
    public static final String TRACKING_ID = "tracking_id";
    public static final String DISTRIBUTION = "config_distribution";
    public static final String MODE = "mode";
    public static final String TOP_N = "config_top_n";
    public static final String SEED_MODE = "seed_mode";
    public static final String DEFAULT_SEED_MODE = "tracking_id";
    public static final String INTERNAL_TARGET_VALUES_KEY = "image_urls";
    public static final String INTERNAL_ALPHA_PARAMS_KEY = "alpha_params";
    public static final String INTERNAL_BETA_PARAMS_KEY = "beta_params";
    public static final String OUTPUT_TARGET_URLS = "image_urls";
    public static final String OUTPUT_SCORES = "scores";
    public static final String DEFAULT_DISTRIBUTION = "beta";
    public static final String DEFAULT_MODE = "random_sampling";

    public static final Integer DEFAULT_TOP_N = 2;

    private final RandomDistributionFactory randomDistributionFactory;

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        val rows = input.getProcessedFeatures();
        val firstFeatureRow = rows.head();

        if (log.isDebugEnabled()) {
            log.debug("All entries for random distribution model (configuration)={}", rows);
        }

        // globals
        String distribution = String.valueOf(firstFeatureRow.getOrElse(DISTRIBUTION, DEFAULT_DISTRIBUTION)).trim().toLowerCase();
        DistributionResultType distributionMode = DistributionResultType.valueOf(
                String.valueOf(firstFeatureRow.getOrElse(MODE, DEFAULT_MODE)).trim().toUpperCase());

        val distributionType = parseDistributionType(distribution);

        if (distributionType.isEmpty()) {
            log.error("Invalid distribution type provided {}", distribution);
            return defaultOutput(input);
        }
        List<List<DistributionResult>> results = Collections.emptyList();

        // this model requires an internal feature that holds the alpha/beta/target params
        // the internal feature MUST HAVE featureOrder = 0
        boolean requiredInternalFeatureIsMissing = input.getModel().getModelFeatures().toJavaStream()
                .filter(f -> f.getFeatureOrder() != null && f.getFeatureOrder() == 0)
                .findAny()
                .isEmpty();

        if (requiredInternalFeatureIsMissing) {
            log.error("Invalid model definition. Missing feature holding alpha/beta params, with order 0.");
            return defaultOutput(input);
        }

        try {

            results = rows
                    .map(row -> mapToRandomDistribution(row.toJavaMap(), distributionType.get(), distributionMode))
                    .map(BaseDistribution::runDistribution)
                    .map(result -> trimResults(result, getTopN(firstFeatureRow)))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), "Failed to create distribution outputs");
        }

        // These implementations need to be separated completely because now they are too coupled.
        HashMap<String, io.vavr.collection.List<ModelInferenceOutputType>> output;
        switch (distributionMode) {
            case RANDOM_SAMPLING:
                output = createResponseForRandomSampling(results);
                break;
            case RANKING:
                output = createResponseForRanking(results);
                break;
            default:
                log.error("Invalid distribution mode provided {}", distributionMode);
                return defaultOutput(input);
        }
        return ModelInferenceResult.builder()
                .output(output)
                .build();
    }

    @Override
    public HashMap<String, io.vavr.collection.List<ModelInferenceOutputType>> generateDefaultOutput(ModelInferenceInput input) {
        return io.vavr.collection.HashMap.of(
                OUTPUT_TARGET_URLS, ModelInferenceOutputType.ofStringArrayList(io.vavr.collection.List.empty()),
                OUTPUT_SCORES, ModelInferenceOutputType.ofDoubleArrayList(io.vavr.collection.List.empty())
        );
    }

    private HashMap<String, io.vavr.collection.List<ModelInferenceOutputType>> createResponseForRanking(List<List<DistributionResult>> results) {
        val flattenScores = io.vavr.collection.List.ofAll(results
                .stream()
                .map(this::mapToScores)).flatMap(i -> i).toList(); // Flatten to a single list.

        log.debug("Scores size is {}", flattenScores.size());

        return HashMap.of(
                OUTPUT_TARGET_URLS,
                ModelInferenceOutputType.ofStringArrayList(io.vavr.collection.List.empty()),
                OUTPUT_SCORES,
                ModelInferenceOutputType.ofFloatList(flattenScores)
        );
    }

    private HashMap<String, io.vavr.collection.List<ModelInferenceOutputType>> createResponseForRandomSampling(List<List<DistributionResult>> results) {
        val targets = io.vavr.collection.List.ofAll(results
                .stream().map(this::mapToTargetValues));

        val scores = io.vavr.collection.List.ofAll(results
                .stream().map(this::mapToScores));

        log.debug("Target size is {}", targets.size());
        log.debug("Scores size is {}", scores.size());

        return HashMap.of(
                OUTPUT_TARGET_URLS,
                ModelInferenceOutputType.ofStringArrayList(targets),
                OUTPUT_SCORES,
                ModelInferenceOutputType.ofFloatArrayList(scores)
        );
    }

    private int getTopN(io.vavr.collection.Map<String, Object> firstSample) {
        DistributionResultType distributionMode = DistributionResultType.valueOf(
                String.valueOf(firstSample.getOrElse(MODE, DEFAULT_MODE)).trim().toUpperCase());

        if (DistributionResultType.RANDOM_SAMPLING.equals(distributionMode)) {
            return ControlPlaneUtils.parseInteger(String.valueOf(firstSample.getOrElse(RandomDistributionModel.TOP_N,
                    RandomDistributionModel.DEFAULT_TOP_N)), RandomDistributionModel.DEFAULT_TOP_N);
        }

        return 1;
    }

    private List<DistributionResult> trimResults(List<DistributionResult> intermediaryResults, int topN) {
        if (intermediaryResults.isEmpty()) {
            log.debug("Trimming cannot be done - no results.");
            return intermediaryResults;
        }

        log.debug("Trimming from {} to {}", intermediaryResults.size(), topN);
        return intermediaryResults.subList(0, Math.min(topN, intermediaryResults.size()));
    }

    private BaseDistribution mapToRandomDistribution(Map<String, Object> features,
                                                     DistributionType distributionType,
                                                     DistributionResultType distributionResultType) {
        return randomDistributionFactory.createRandomDistribution(distributionType, distributionResultType, features);
    }


    private Option<DistributionType> parseDistributionType(String distribution) {
        if (StringUtils.isEmpty(distribution)) {
            return Option.none();
        }
        try {
            return Some(DistributionType.valueOf(distribution.toUpperCase()));
        } catch (IllegalArgumentException e) {
            log.warn("Invalid distribution type {}", distribution.toUpperCase());
            return Option.none();
        }
    }

    private io.vavr.collection.List<String> mapToTargetValues(List<DistributionResult> distributionResults) {
        final List<String> targetValues = distributionResults.stream()
                .map(DistributionResult::getValue)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return io.vavr.collection.List.ofAll(targetValues);
    }

    private io.vavr.collection.List<Float> mapToScores(List<DistributionResult> distributionResults) {
        final List<Float> scores = distributionResults.stream()
                .map(DistributionResult::getDistribution)
                .collect(Collectors.toList());

        return io.vavr.collection.List.ofAll(scores);
    }

}

package com.grubhub.garcon.modelinteraction;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.modelinteraction.precomputedranker.PrecomputedRankerModel;
import com.grubhub.garcon.modelinteraction.queryexpansionmodel.QueryExpansionModel;
import com.grubhub.garcon.modelinteraction.sagemaker.SageMakerModel;
import com.grubhub.garcon.modelinteraction.searchembeddings.SearchEmbeddingsModel;
import com.grubhub.garcon.modelinteraction.vectorsimilaritymodel.VectorSimilarityModel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Getter
public class JavaModels {

    private final DistanceFalloffModel distanceFalloffModel;
    private final LinearCombinationModel linearCombinationModel;
    private final FeaturesFetchModel featuresFetchModel;
    private final RandomSortModel randomSortModel;
    private final SearchEmbeddingsModel searchEmbeddingsModel;
    private final TextSimilarityModel textSimilarityModel;
    private final VectorSimilarityModel vectorSimilarityModel;
    private final RandomSampleModel randomSampleModel;
    private final RandomDistributionModel randomDistributionModel;
    private final RankingPostProcessingModel rankingPostProcessingModel;
    private final ScoreAggregationModel scoreAggregationModel;
    private final RuleMatchingModel ruleMatchingModel;
    private final QueryExpansionModel queryExpansionModel;
    private final PrecomputedRankerModel precomputedRankerModel;
    private final SageMakerModel sageMakerModel;

}

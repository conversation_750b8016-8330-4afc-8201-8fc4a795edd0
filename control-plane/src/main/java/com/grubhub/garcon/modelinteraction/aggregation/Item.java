package com.grubhub.garcon.modelinteraction.aggregation;

import io.vavr.collection.List;
import lombok.AllArgsConstructor;
import lombok.Value;
import lombok.With;

@Value
@AllArgsConstructor
@With
public class Item {
    public static final Item EMPTY_ITEM = new Item(0, List.empty(), List.empty());
    String id;
    Double score;
    Double weight;

    public Item(int index, List<String> ids, List<Double> scores) {
        this.id = (ids == null || ids.isEmpty()) ? "-1" : ids.get(index);
        this.score = index < scores.size() ? scores.get(index) : 0d;
        this.weight = 1d;
    }

    public double scoreTimesWeight() {
        return score * weight;
    }
}

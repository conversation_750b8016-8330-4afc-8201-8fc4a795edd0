package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.grpc.AppKey;
import com.grubhub.garcon.grpc.GrpcChannelFactory;
import com.grubhub.garcon.grpc.GrpcResolveType;
import com.grubhub.garcon.modelinteraction.tensorflow.TfsClusterConfig;
import io.grpc.ManagedChannel;
import tensorflow.serving.PredictionServiceGrpc;

import com.google.inject.Singleton;

@Singleton
public class PredictionClientFactory {
    public PredictionServiceGrpc.PredictionServiceBlockingStub createBlockingClient(
            GrpcResolveType grpcResolveType,
            TfsClusterConfig tfsClusterConfig,
            GrpcChannelFactory grpcChannelFactory
    ) {
        if (grpcResolveType == GrpcResolveType.STATIC) {
            // Add static channel
            ManagedChannel channel = grpcChannelFactory.fetchStaticChannel(
                    tfsClusterConfig.getTfServingStaticAddresses(),
                    tfsClusterConfig.getTfServingPort()
            );
            return PredictionServiceGrpc.newBlockingStub(channel);
        } else if (grpcResolveType == GrpcResolveType.EUREKA) {
            ManagedChannel channel = grpcChannelFactory.fetchEurekaChannel(
                    AppKey.of(
                        tfsClusterConfig.getTfServingAppName(),
                        tfsClusterConfig.getTfServingVersion(),
                        tfsClusterConfig.getTfServingPort()
                    )
            );
            return PredictionServiceGrpc.newBlockingStub(channel);
        } else {
            throw new RuntimeException("Unable to create eureka gRPC channel.  Unussported resolver type: " + grpcResolveType);
        }
    }
}

package com.grubhub.garcon.modelinteraction.computablefeatures;

import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import io.vavr.collection.Map;
import lombok.val;

/**
 * This function returns the first non-empty and non-null value between str1, str2, and default, in that order.
 * Inputs: map with key: str1, str2, default.
 * Outputs: map with key: str.
 */
public class StringCoalesceFunction extends BaseFunctionFeature {

    public static final String INPUT_STRING_ONE = "str1";
    public static final String INPUT_STRING_TWO = "str2";
    public static final String INPUT_STRING_DEFAULT = "default";
    public static final String OUTPUT_STRING = "str";

    public StringCoalesceFunction() {
        super(FunctionFeatureType.STR_COALESCE);
    }

    public Map<String, Object> compute(Map<String, Object> inputs) {
        if (isStringNotEmpty(getString(inputs, INPUT_STRING_ONE))) {
            return createOutput(OUTPUT_STRING, getString(inputs, INPUT_STRING_ONE));
        } else if (isStringNotEmpty(getString(inputs, INPUT_STRING_TWO))) {
            return createOutput(OUTPUT_STRING, getString(inputs, INPUT_STRING_TWO));
        }
        String outputStr = getString(inputs, INPUT_STRING_DEFAULT);
        val finalOutputStr = outputStr.equals("null") ? "" : outputStr;
        return createOutput(OUTPUT_STRING, finalOutputStr);
    }

    private boolean isStringNotEmpty(String input) {
        return !input.isEmpty() && !input.equals("null");
    }
}

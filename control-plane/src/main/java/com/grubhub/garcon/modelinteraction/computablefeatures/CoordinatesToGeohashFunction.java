package com.grubhub.garcon.modelinteraction.computablefeatures;


import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import io.vavr.collection.Map;
import lombok.val;

import static ch.hsr.geohash.GeoHash.withCharacterPrecision;

/**
 * This function converts latitude and longitude coordinates to a geohash with the given precision.
 * Inputs: map with keys: lat, lng, precision.
 * Outputs: map with key: geohash.
 */
public class CoordinatesToGeohashFunction extends BaseFunctionFeature {

    public static final String LATITUDE = "lat";
    public static final String LONGITUDE = "lng";
    public static final String PRECISION = "precision";
    public static final String GEOHASH = "geohash";

    public CoordinatesToGeohashFunction() {
        super(FunctionFeatureType.COORDINATES_TO_GEOHASH);
    }

    public Map<String, Object> compute(Map<String, Object> inputs) {
        val latitude = getDouble(inputs, LATITUDE);
        val longitude = getDouble(inputs, LONGITUDE);
        val precision = getInteger(inputs, PRECISION);
        val geohashString = withCharacterPrecision(latitude, longitude, precision).toBase32();
        return createOutput(GEOHASH, geohashString);
    }
}

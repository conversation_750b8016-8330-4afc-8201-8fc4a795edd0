package com.grubhub.garcon.modelinteraction.computablefeatures.stringconcat;

import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import com.grubhub.garcon.modelinteraction.computablefeatures.BaseFunctionFeature;

public abstract class StringConcatenateBaseFunction extends BaseFunctionFeature {

    public static final String INPUT_STRING_ONE = "str1";
    public static final String INPUT_STRING_TWO = "str2";
    public static final String INPUT_STRING_THREE = "str3";
    public static final String INPUT_STRING_FOUR = "str4";
    public static final String INPUT_SEPARATOR = "sep";
    public static final String OUTPUT_STRING = "str";

    public StringConcatenateBaseFunction(FunctionFeatureType functionFeatureType) {
        super(functionFeatureType);
    }

    public String concatenate(int numberOfInputs, String separator, String... inputs) {
        StringBuilder outputStr = new StringBuilder();
        for (int inputNum = 0; inputNum < numberOfInputs - 1; inputNum++) {
            outputStr.append(convertToEmptyIfNull(inputs[inputNum]));
            outputStr.append(separator);
        }
        return outputStr.append(convertToEmptyIfNull(inputs[numberOfInputs - 1])).toString();
    }

    private String convertToEmptyIfNull(String input) {
        return input.equals("null") ? "" : input;
    }
}

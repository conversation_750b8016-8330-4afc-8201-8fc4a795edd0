package com.grubhub.garcon.modelinteraction.queryexpansionmodel;

import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Objects;

@Slf4j
public class ResourceModelPromptLoader implements ModelPromptLoader{

    private final String promptPath;

    @Inject
    public ResourceModelPromptLoader(String promptPath) {
        this.promptPath = promptPath;
    }

    @Override
    public ModelPrompt loadPrompt() {
        try {

            ClassLoader classLoader = QueryExpansionModel.class.getClassLoader();
            File file = new File(Objects.requireNonNull(classLoader.getResource(promptPath)).getFile());
            String promptTemplate = Files.readString(Paths.get(file.toURI()));
            log.debug("Prompt={}", promptTemplate);
            return new ModelPrompt(promptTemplate);

        } catch (Exception e) {
            String logMessage = String.format(
                    "Failed loading prompt with Resource Loader at promptPath=%s",
                    promptPath);
            log.error(logMessage, e);
            throw new RuntimeException(logMessage, e);
        }
    }
}

package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelConfigUpdateResponse;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.TensorFlowModelMetadataDTO;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import io.vavr.collection.List;

public interface ModelInferenceClient {
    ModelInferenceResult infer(ModelType modelType, ModelInferenceInput modelInferenceInput);

    ModelStatus getTensorFlowStatus(ModelDTO model);

    TensorFlowModelMetadataDTO getTensorFlowMetadata(ModelDTO model);

    ModelInferenceResult inferWithTensorFlow(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithNativeFunction(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferQueryExpansions(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithSort(ModelInferenceInput modelInferenceInput, boolean ascending, float multiplier);

    ModelInferenceResult inferWithRandomSort(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithDistanceFalloff(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithLinearCombination(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithFeaturesFetch(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithSearchEmbedding(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithTextSimilarity(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithScoreAggregation(ModelInferenceInput modelInferenceInput);
    ModelInferenceResult inferVectorCosineSimilarity(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithRandomSample(ModelInferenceInput modelInferenceInput);
    ModelInferenceResult inferWithRandomDistribution(ModelInferenceInput modelInferenceInput);
    ModelInferenceResult inferWithRankingPostProcessing(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithRuleMatching(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithPrecomputedRanker(ModelInferenceInput modelInferenceInput);

    ModelInferenceResult inferWithSageMaker(ModelInferenceInput modelInferenceInput);

    ModelStatus getSearchEmbeddingStatus(ModelDTO model);

    void configureSearchEmbeddingModels(List<ModelDTO> models);

    List<ModelConfigUpdateResponse> configureTensorFlow(List<ModelDTO> models);
}

package com.grubhub.garcon.modelinteraction.precomputedranker;

import com.google.inject.Singleton;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import lombok.extern.slf4j.Slf4j;

import java.util.AbstractMap;
import java.util.Comparator;
import java.util.Map;
import java.util.PriorityQueue;

import static com.grubhub.garcon.modelinteraction.precomputedranker.PrecomputedRankerModel.*;

/**
 * Model that preprocesses scores, depending on the provided mode.
 * Required inputs:
 * - mode: the mode determine the type of processing to be done
 * <p>
 * Optional inputs:
 * - top_n: the number of top scores to return
 * <p>
 */
@Slf4j
@Singleton
public class TopNRanker {

    protected ModelInferenceResult processScores(ModelInferenceInput input,
                                                  io.vavr.collection.List<io.vavr.collection.Map<String, Object>> rows, int topN) {

        PriorityQueue<Map.Entry<Integer, Double>> topNScores = new PriorityQueue<>(topN, Comparator.comparingDouble(Map.Entry::getValue));
        float[] scoreArray = new float[rows.length()];

        for (int i = 0; i < rows.length(); i++) {
            io.vavr.collection.Map<String, Object> row = rows.get(i);
            Double score = (Double) row.get(SCORE).getOrElse(0.0);
            if (topNScores.size() < topN) {
                topNScores.offer(new AbstractMap.SimpleEntry<>(i, score));
                scoreArray[i] = score.floatValue();
            } else if (score > topNScores.peek().getValue()) {
                // Swap the lowest score in topNScores, with the current score if it's higher
                Map.Entry<Integer, Double> poll = topNScores.poll();
                scoreArray[poll.getKey()] = 0.0f;
                topNScores.offer(new AbstractMap.SimpleEntry<>(i, score));
                scoreArray[i] = score.floatValue();
            }
        }
        return ModelInferenceResult.from(input, io.vavr.collection.List.ofAll(scoreArray));
    }
}

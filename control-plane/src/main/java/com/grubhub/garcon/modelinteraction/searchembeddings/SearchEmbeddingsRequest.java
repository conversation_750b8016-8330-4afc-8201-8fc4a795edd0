package com.grubhub.garcon.modelinteraction.searchembeddings;

import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.Normalization;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.QueryFilters;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import io.vavr.Predicates;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Collections.emptyList;

@Data
@Slf4j
public class SearchEmbeddingsRequest {
    private static final int MAX_K = 5_000;
    private static final String K = "k";
    private static final String SEARCH_K = "search_k";
    private static final String QUERY_VECTOR = "query_vector";
    private static final String INDEX_NAME_LIST = "index_name_list";
    static final String SE_ENTITY_MAPPING_ENABLED = "se_entity_mapping_enabled";

    private List<Float> queryVector;

    @Getter(AccessLevel.PRIVATE)
    private List<String> indexesName;
    private String modelName;
    private Integer k;
    private Integer searchK;
    private QueryFilters queryFilters;
    private Normalization normalization;

    public SearchEmbeddingsRequest(Map<String, Object> inputFeatures, String modelName) {
        if (inputFeatures.containsKey(K)) {
            this.k = inputFeatures.get(K)
                    .filter(kVal -> kVal instanceof Integer)
                    .map(Integer.class::cast)
                    .filter(this::isInRange)
                    .getOrElseThrow(() -> new RuntimeException(
                                    String.format("K is not present in the input_features=%s for model, model_name=%s", inputFeatures, modelName)
                            )
                    );
        }

        if (inputFeatures.containsKey(SEARCH_K)) {
            this.searchK = inputFeatures.get(SEARCH_K)
                    .filter(kVal -> kVal instanceof Integer)
                    .map(Integer.class::cast)
                    .getOrNull();
        }

        if (inputFeatures.containsKey(QUERY_VECTOR)) {
            this.queryVector = inputFeatures.get(QUERY_VECTOR)
                    .map(this::parseQueryVectorValues)
                    .filter(Predicates.not(List::isEmpty))
                    .getOrElseThrow(() -> new RuntimeException(
                            String.format("Query vector is not present in the input_features=%s for model, model_name=%s", inputFeatures, modelName)));
        }

        if (inputFeatures.containsKey(INDEX_NAME_LIST)) {
            this.indexesName = inputFeatures.get(INDEX_NAME_LIST)
                    .map(ControlPlaneUtils::toJavaListString)
                    .filter(Predicates.not(List::isEmpty))
                    .getOrElseThrow(() -> new RuntimeException(
                            String.format("Indexes are not present in the input_features=%s for model_name=%s", inputFeatures, modelName)));
        }

        this.modelName = modelName;
        this.normalization = new NormalizationProvider(inputFeatures, modelName).get();
        this.queryFilters = new QueryFiltersProvider(inputFeatures, modelName).get();
    }

    private static List<Float> queryVectorValuesToList(io.vavr.collection.List<Float> vectorValues) {
        return Option.of(vectorValues).getOrElse(io.vavr.collection.List.empty()).toJavaList();
    }

    private List<Float> parseQueryVectorValues(Object queryValue) {
        return Try.of(() -> ControlPlaneUtils.toJavaListFloat(queryValue))
                .onFailure(ex -> log.error("Failed to parse query_values={} for model, model_name={}", queryValue, modelName, ex))
                .recover(ex -> tryToGetValuesFromArray(queryValue))
                .get();
    }

    private List<Float> tryToGetValuesFromArray(Object queryValue) {
        return Try.of(() -> ((String[]) queryValue))
                .map(Arrays::toString)
                .map(ControlPlaneUtils::toJavaListFloat)
                .onFailure(e -> log.error("Failed to parse query_values={} for model, model_name={}", queryValue, modelName, e))
                .recover(e -> emptyList())
                .get();
    }

    private boolean isInRange(Integer kVal) {
        return kVal >= 1 && kVal <= MAX_K;
    }

    public List<String> getUniqueIndexNames(Function<String, String> addPrefix) {
        return indexesName.stream().map(indexName -> addPrefix.apply(modelName) + indexName)
                .collect(Collectors.toList());

    }
}

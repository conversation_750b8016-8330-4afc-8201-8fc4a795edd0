package com.grubhub.garcon.modelinteraction.computablefeatures;

import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import io.vavr.collection.Map;
import lombok.val;

/**
 * This function modifies the given string to only have lowercase letters. Any non uppercase characters are not modified.
 * Inputs: map with key: str.
 * Outputs: map with key: str.
 */
public class StringToLowerCaseFunction extends BaseFunctionFeature {

    public static final String INPUT_STRING = "str";
    public static final String OUTPUT_STRING = "str";

    public StringToLowerCaseFunction() {
        super(FunctionFeatureType.STR_LOWER);
    }

    public Map<String, Object> compute(Map<String, Object> inputs) {
        String outputStr = "";
        if (inputs.get(INPUT_STRING).get() != null) {
            val inputStr = getString(inputs, INPUT_STRING);
            outputStr = inputStr.trim().toLowerCase();
        }
        return createOutput(OUTPUT_STRING, outputStr);
    }
}

package com.grubhub.garcon.modelinteraction.tfs;

import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.*;
import com.grubhub.garcon.ddml.util.LogUtil;
import com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelAdaptor;
import io.vavr.Tuple;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.tensorflow.framework.TensorProto;
import tensorflow.serving.Model;
import tensorflow.serving.Predict;
import com.grubhub.garcon.grpc.DdmlTensorflowGrpcClient;

import javax.inject.Inject;

import static com.grubhub.garcon.controlplane.metrics.Spans.TRACING_HELPER;

@Slf4j
@AllArgsConstructor(onConstructor_ = {@Inject})
public class TfsModelInference implements com.grubhub.garcon.modelinteraction.ExternalModelInference {
    private TensorFlowModelAdaptor tensorFlowModelAdaptor;
    private static final String DEFAULT_TF_INPUT_NAME = "inputs";
    private final DdmlTensorflowGrpcClient ddmlTensorflowGrpcClient;
    private static final String TENSOR_FLOW_SERVING_SERIALIZATION = "tensorFlowServingSerialization";

    @Override
    public ModelInferenceResult doInference(ModelInferenceInput modelInferenceInput,
                                            Model.ModelSpec modelSpec,
                                            String modelName,
                                            ModelDTO modelDto) {
        val inferWithTensorFlow = TRACING_HELPER.startSpanManual(TENSOR_FLOW_SERVING_SERIALIZATION, getTagsForSpan(modelInferenceInput));
        log.debug("Batching log. Calling infer with tensor flow with input={}", modelInferenceInput);

        if (tensorFlowModelAdaptor.getTfsCluster(modelInferenceInput.getModel()).getPredictionClient() == null) {
            throw new RuntimeException(String.format("No TensorFlow predictClient was found, cannot invoke model=%s", modelName));
        }

        log.debug("Model spec for infer with tensor flow, modelSpec={}", modelSpec);
        java.util.Map<String, TensorProto> inputFeaturesMap;
        val useExamplesSerialization = ControlPlaneUtils.isTrue(modelInferenceInput.getModel().getTfUseExamplesSerialization());
        if (useExamplesSerialization) {
            // Row-format: send inputs as a serialized list of Example, one item per sample with all its features
            val inputName = StringUtils.defaultIfEmpty(modelInferenceInput.getModel().getTfExamplesSerializationName(), DEFAULT_TF_INPUT_NAME);
            log.debug("using serialization. before converting to proto. processedFeatures={}", modelInferenceInput.getProcessedFeatures());
            inputFeaturesMap = tensorFlowModelAdaptor.convertFeaturesToSerializedExamples(inputName, modelInferenceInput.getProcessedFeatures()).toJavaMap();
        } else {
            // Column-format: send one tensor per feature, each tensor with values from all the samples
            Map<String, List<Object>> groupedFeatures = tensorFlowModelAdaptor.convertFeaturesInputToExampleList(
                    modelInferenceInput.getProcessedFeatures()
            );
            log.debug("without serialization. before converting to proto. processedFeatures={}", LogUtil.logGroupedFeatures(groupedFeatures));
            inputFeaturesMap = tensorFlowModelAdaptor.createTensorProtoFromFeaturesExamplesMap(groupedFeatures);
        }
        log.debug("Input features for invoking with tensor flow, inputFeaturesMap={}", inputFeaturesMap);

        inferWithTensorFlow.finish();

        Predict.PredictResponse predictResponse;

        boolean useNewClient = tensorFlowModelAdaptor.shouldUseRouxManagedGrpcClient();

        //this will be removed after further grpc integration
        if (useNewClient) {
            log.debug("Using new DdmlTensorflowGrpcClient for model: {}", modelName);
            // The DdmlTensorflowGrpcClient is expected to handle its own metrics, retries, spans internally
            predictResponse = ddmlTensorflowGrpcClient.getPredictResponse(modelSpec, inputFeaturesMap);
        } else {
            log.debug("Using existing TfsClusterInteraction client for model: {}", modelName);
            val tfsCluster = tensorFlowModelAdaptor.getTfsCluster(modelInferenceInput.getModel());
            // This tfsCluster.getPredictResponse() is the original one, which internally handles its metrics/spans/retries
            predictResponse = tfsCluster.getPredictResponse(modelSpec, inputFeaturesMap);
        }
        java.util.Map<String, TensorProto> outputsProtoMap = predictResponse.getOutputsMap();
        log.debug("outputsProtoMap={}", outputsProtoMap);

        if (outputsProtoMap.isEmpty()) {
            throw new RuntimeException(String.format("TensorFlow output of model=%s is empty, modelSpec=%s, inputs=%s, processedFeatures=%s",
                    modelName, modelSpec, inputFeaturesMap, LogUtil.logFeatures(modelInferenceInput.getProcessedFeatures())));
        } else {
            Map<String, List<ModelInferenceOutputType>> output = HashMap.ofEntries(modelDto.getModelOutputs().map(out -> Tuple.of(
                            out.getOutputName(),
                            tensorFlowModelAdaptor.extractOutputFromTensor(
                                    getTensorFromOutputsMap(outputsProtoMap, out, modelName),
                                    out.getOutputTypeEnum(),
                                    out.getOutputShape())
                    )
            ));
            return ModelInferenceResult.builder()
                    .output(output)
                    .batchSequenceNumber(modelInferenceInput.getBatchSequenceNumber())
                    .build();
        }
    }

    private TensorProto getTensorFromOutputsMap(java.util.Map<String, TensorProto> outputsProtoMap, ModelOutputDTO modelOutputDTO, String modelName) {
        val tensorProto = outputsProtoMap.get(modelOutputDTO.getOutputName());
        if (tensorProto == null) {
            throw new RuntimeException(String.format("TensorFlow for output_name=%s of model=%s is null, available output names=%s.",
                    modelOutputDTO.getOutputName(), modelName, outputsProtoMap.keySet()));
        }
        return tensorProto;
    }

    private java.util.Map<String, String> getTagsForSpan(ModelInferenceInput modelInferenceInput) {
        return HashMap.of(
                "model_name", modelInferenceInput.getModel().getModelName(),
                "model_version", modelInferenceInput.getModel().getVersion(),
                "features_size", String.valueOf(modelInferenceInput.getProcessedFeatures().size()),
                "batch_sequence_number", String.valueOf(modelInferenceInput.getBatchSequenceNumber())
        ).toJavaMap();
    }
}

package com.grubhub.garcon.modelinteraction.computablefeatures;

import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

import static io.vavr.collection.HashMap.collector;

@Slf4j
public class StringReplaceFunction extends BaseFunctionFeature {
    private static final String STR_KEY = "str";
    private static final String DEFAULT_VALUE_KEY = "default";
    private static final String MAPPING = "mapping";
    private static final String REGEX = "=";
    private static final String COMMA_REGEX = "\\,";

    public StringReplaceFunction() {
        super(FunctionFeatureType.STR_MAP);
    }

    @Override
    public Map<String, Object> compute(Map<String, Object> inputs) {
        val strValue = getString(inputs, STR_KEY);
        val outputValue = processMapping(inputs).get(strValue)
                .getOrElse(getString(inputs, DEFAULT_VALUE_KEY));
        return createOutput(STR_KEY, outputValue);
    }

    private Map<String, String> processMapping(Map<String, Object> inputs) {
        val availableMapping = getString(inputs, MAPPING);
        return Option.of(availableMapping)
                .map(this::buildMapOfMappings)
                .getOrElse(HashMap.empty());
    }

    private HashMap<String, String> buildMapOfMappings(String mapping) {
        return Pattern.compile(COMMA_REGEX)
                .splitAsStream(mapping)
                .collect(
                        collector(
                                this::extractKey,
                                this::extractValue
                        )
                );
    }

    private String extractValue(String rawPair) {
        return getAtIndex(rawPair, 1);
    }

    private String extractKey(String rawPair) {
        return getAtIndex(rawPair, 0);
    }

    private String getAtIndex(String rawPair, int index) {
        return Option.of(rawPair.split(REGEX)[index]).getOrElse(StringUtils.EMPTY).trim();
    }
}

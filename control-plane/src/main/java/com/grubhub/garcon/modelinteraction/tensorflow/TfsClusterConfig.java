package com.grubhub.garcon.modelinteraction.tensorflow;

import com.grubhub.garcon.grpc.AppKey;
import lombok.Data;

@Data
public class TfsClusterConfig {
    private String tfServingAppName = "DDML-TENSORFLOW";
    private String tfServingVersion = "2.3.0-3.gh1";
    private Integer tfServingPort = 8500;
    private Integer predictRetries = 2;
    private Long metadataTimeoutSeconds = 30L;
    private Long predictTimeoutMs = 2000L;
    private String tfServingStaticAddresses = "localhost";

    public AppKey toAppKey() {
        return AppKey.of(tfServingAppName, tfServingVersion, tfServingPort);
    }
}

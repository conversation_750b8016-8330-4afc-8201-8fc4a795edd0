package com.grubhub.garcon.modelinteraction.distribution;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.math3.random.JDKRandomGenerator;
import org.apache.commons.math3.random.RandomGenerator;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Getter
public class BetaDistribution implements BaseDistribution {
    private final List<BetaInput> betaInputs;

    @Override
    public List<DistributionResult> runDistribution() {
        if (Objects.isNull(betaInputs) || betaInputs.isEmpty()) {
            log.debug("Empty beta inputs provided");
            return Collections.emptyList();
        }

        List<DistributionResult> results = betaInputs.stream()
                .map(i -> new ImmutablePair<>(i, runRandomDistribution(i)))
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder())) // Higher values first, we want to get the highest.
                .map(result -> new DistributionResult(result.right, result.left.value))
                .collect(Collectors.toList());

        log.debug("Beta distribution results: {}", results);
        return results;
    }

    private Float runRandomDistribution(BetaInput i) {
        RandomGenerator randomGenerator = createRandomGenerator(i.getHash());
        double betaDistResult = new org.apache.commons.math3.distribution.BetaDistribution(randomGenerator,
                i.getAlpha(), i.getBeta()).sample();

        return Double.valueOf(betaDistResult).floatValue();
    }

    private RandomGenerator createRandomGenerator(String seed) {
        if (StringUtils.isEmpty(seed)) {
            log.debug("Hash not provided with beta inputs, setting to default empty string.");
        }

        int hashCodeSeed = seed.hashCode();

        log.debug("Creating Random generator seed hashcode={} from={}.", hashCodeSeed, seed);
        final RandomGenerator randomGenerator = new JDKRandomGenerator();
        randomGenerator.setSeed(hashCodeSeed);
        return randomGenerator;
    }

    @Getter
    public static class BetaInput {
        private String value;
        private final String hash;
        private final Double alpha;
        private final Double beta;

        public BetaInput(Double alpha, Double beta, String hash) {
            this.alpha = alpha;
            this.beta = beta;
            this.hash = hash;
        }

        public BetaInput(String value, String hash, Double alpha, Double beta) {
            this.value = value;
            this.hash = hash;
            this.alpha = alpha;
            this.beta = beta;
        }

        @Override
        public String toString() {
            return String.format("BetaInput(alpha={%s}, beta={%s}, value={%s}, hash={%s})", alpha, beta, value, hash);
        }

        @Override
        public int hashCode() {
            int hashCode = 12;

            hashCode += value == null ? 0 : value.hashCode();
            hashCode += hash == null ? 0 : hash.hashCode();
            hashCode += alpha == null ? 0 : alpha.hashCode();
            hashCode += beta == null ? 0 : beta.hashCode();

            return hashCode;
        }

        @Override
        public boolean equals(Object obj) {
            if (Objects.isNull(obj)) {
                return false;
            }

            if (!(obj instanceof BetaInput)) {
                return false;
            }

            BetaInput other = (BetaInput) obj;

            return Objects.equals(this.alpha, other.alpha) && Objects.equals(this.beta, other.beta)
                    && Objects.equals(this.value, other.value) && Objects.equals(this.hash, other.hash);
        }
    }
}

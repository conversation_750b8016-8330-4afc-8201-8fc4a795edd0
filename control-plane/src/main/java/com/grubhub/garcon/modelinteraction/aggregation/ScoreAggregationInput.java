package com.grubhub.garcon.modelinteraction.aggregation;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;

public class ScoreAggregationInput {

    private final ScoreAggregationConfig scoreAggregationConfig;
    private final ModelInferenceInput modelInferenceInput;

    private ScoreAggregationInput(ModelInferenceInput modelInferenceInput) {
        this.modelInferenceInput = modelInferenceInput;
        this.scoreAggregationConfig = new ScoreAggregationConfig(modelInferenceInput);
    }

    public static ScoreAggregationInput of(ModelInferenceInput modelInferenceInput) {
        return new ScoreAggregationInput(modelInferenceInput);
    }

    public ScoreItemProcessor buildItemById() {
        return new ScoreItemProcessor(modelInferenceInput, scoreAggregationConfig);
    }


}

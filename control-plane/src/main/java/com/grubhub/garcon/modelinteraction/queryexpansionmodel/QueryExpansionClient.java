package com.grubhub.garcon.modelinteraction.queryexpansionmodel;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.bedrock.BedrockRuntimeClientProvider;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.json.JSONPointer;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeClient;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelRequest;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelResponse;

@Slf4j
public class QueryExpansionClient implements LLMClient {
    public static final String PROMPT_PLACEHOLDER = "\"{{prompt}}\"";
    private final BedrockConfiguration bedrockConfiguration;
    private final BedrockRuntimeClientProvider bedrockRuntimeClientProvider;

    @Inject
    public QueryExpansionClient(BedrockConfiguration bedrockConfiguration, BedrockRuntimeClientProvider bedrockRuntimeClientProvider) {
        this.bedrockConfiguration = bedrockConfiguration;
        this.bedrockRuntimeClientProvider = bedrockRuntimeClientProvider;
    }

    public String invoke(String modelName, String prompt) {

        String modelPayloadTemplate = bedrockConfiguration.getBedrockModelConfig().getNativeRequestTemplate();
        String modelPayload = modelPayloadTemplate.replace(PROMPT_PLACEHOLDER, prompt);
        log.info("ModelPayload={}, modelName={}", modelPayload, modelName);

        InvokeModelRequest request = InvokeModelRequest.builder()
                .body(SdkBytes.fromUtf8String(modelPayload))
                .modelId(modelName)
                .build();


        try {
            BedrockRuntimeClient client = bedrockRuntimeClientProvider.get();

            InvokeModelResponse response = client.invokeModel(request);
            log.debug("Bedrock API response={}", response);

            JSONObject responseBody = new JSONObject(response.body().asUtf8String());
            String text = new JSONPointer("/content/0/text").queryFrom(responseBody).toString();
            log.debug("Query expansions returned text={}, responseBody={}", text, responseBody);

            return text;

        } catch (SdkClientException e) {
            log.error("Can't invoke modelName={}", modelName, e);
            throw new RuntimeException(e);
        }

    }
}


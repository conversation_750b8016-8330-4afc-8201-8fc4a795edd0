package com.grubhub.garcon.modelinteraction.rankingpostprocessing;

import com.google.common.collect.ImmutableList;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;

import static com.grubhub.garcon.modelinteraction.RankingPostProcessingModel.*;
import static java.util.Collections.emptyList;


@Slf4j
public class RankingPostProcessingFactory {

    public static RankingPostProcessing create(PostProcessingStrategy strategy, Map<String, Object> firstFeatureRow) {
        String randomSeed = extractRandomSeed(firstFeatureRow);
        int maxRankingSlots = extractMaxRankingSlots(firstFeatureRow);

        switch (strategy) {
            case RAND_PAIR:
                return new RandomPairRankingPostProcessing(randomSeed);
            case RAND_INSERT:
                List<String> targetEntityIds = extractTargetEntityIds(firstFeatureRow);
                validateEntityIds(targetEntityIds);
                return new RandomInsertRankingPostProcessing(randomSeed, targetEntityIds, maxRankingSlots);
            default:
                throw new IllegalArgumentException("Invalid strategy provided: " + strategy);
        }
    }

    private static String extractRandomSeed(Map<String, Object> firstFeatureRow) {
        String randomSeed = String.valueOf(firstFeatureRow.getOrDefault(RANDOM_SEED_GLOBAL_FIELD, "")).trim();
        if (StringUtils.isEmpty(randomSeed)) {
            throw new IllegalArgumentException("Missing random_seed field.");
        }
        return randomSeed;
    }

    private static int extractMaxRankingSlots(Map<String, Object> firstFeatureRow) {
        return (Integer) firstFeatureRow.getOrDefault(MAX_RANKING_SLOTS, 0);
    }

    private static List<String> extractTargetEntityIds(Map<String, Object> firstFeatureRow) {
        Object targetEntityIdsRaw = firstFeatureRow.getOrDefault(PIN_ENTITIES_ID_GLOBAL_FIELD, emptyList());
        java.util.List<String> targetEntityIds = ControlPlaneUtils.toJavaListString(targetEntityIdsRaw);

        if (targetEntityIds.isEmpty()) {
            // For backward compatibility
            return Optional.ofNullable(String.valueOf(firstFeatureRow.getOrDefault(PIN_ENTITY_ID_GLOBAL_FIELD, "")))
                    .filter(StringUtils::isNotBlank)
                    .map(ImmutableList::of)
                    .orElse(ImmutableList.of());
        }
        return targetEntityIds;
    }

    private static void validateEntityIds(java.util.List<String> entityIds) {
        if (entityIds.isEmpty()) {
            log.error("Target entity id was not provided for random insert strategy.");
            throw new IllegalArgumentException("Missing target entity for random insert strategy");
        }
    }
}

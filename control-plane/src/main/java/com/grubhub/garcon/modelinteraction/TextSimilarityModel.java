package com.grubhub.garcon.modelinteraction;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.modelinteraction.computablefeatures.textnormalizer.ControlPlaneTextNormalizer;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.apache.commons.text.similarity.SimilarityScore;

import java.util.function.Function;
import java.util.regex.Pattern;

/**
 * Model that computes a similarity score between a given input string and a list of attributes
 * It returns the score of each input string, where 0 is poor similarity and 1 is high similarity.
 * Current implementation only supports Edit-distance.
 * Required inputs:
 * - input_text
 * - config_target_keys: comma separated list of features you want to use for text comparison. Each value will be treated as a list of strings.
 * <p>
 * Optional inputs:
 * - config_cut_off: return score 0 for rows with a score lower than this threshold. The cut-off is always applied to range 0-1
 * - config_multiplier: multiply the final score by the given scalar (after cut-off)
 * - config_normalize_input: true/false to normalize input text with ControlPlaneTextNormalizer
 * - config_normalize_target: true/false to normalize target text with ControlPlaneTextNormalizer
 * - config_remove_chars: comma separated list of char/string to remove from both input and target string
 * - config_remove_blanks: true/false to remove blanks (every white-space) before normalization.
 *
 * <p>
 * Example:
 * - Global features:
 * - input_text: "burger king"
 * - config_target_keys: restaurant_name,cuisines
 * - Row features:
 * - Row1: restaurant_name=["burger joint"], cuisines=["american", "italian"]
 * - Row2: restaurant_name=["shake shack"], cuisines=["american", "dessert"]
 * <p>
 * This model will combine restaurant_name and cuisines into a single list (for each row) and compare against input_text
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class TextSimilarityModel implements JavaModel, ModelWithDefaultOutput {

    public static final String INPUT_TEXT = "input_text";
    public static final String TARGET_KEYS = "config_target_keys";
    public static final String CUT_OFF_THRESHOLD = "config_cut_off";
    public static final String SCORE_MULTIPLIER = "config_multiplier";
    public static final String ALGORITHM = "config_algorithm";
    public static final String NORMALIZE_INPUT = "config_normalize_input";
    public static final String NORMALIZE_TARGET = "config_normalize_target";
    public static final String REMOVE_CHARS = "config_remove_chars";
    public static final String REMOVE_BLANKS = "config_remove_blanks";

    public static final double DEFAULT_CUT_OFF_THRESHOLD = 0.9;
    public static final double DEFAULT_SCORE_MULTIPLIER = 1.0;
    public static final String DEFAULT_ALGORITHM = "edit_dist";

    public static final Pattern TARGET_KEY_SEP = Pattern.compile(",");

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        // Get model config
        val firstSample = input.getProcessedFeatures().head();
        log.debug("First entry (configuration)={}", firstSample);

        String inputText = String.valueOf(firstSample.getOrElse(INPUT_TEXT, "")).trim().toLowerCase();
        if (StringUtils.isEmpty(inputText)) {
            return defaultOutput(input);
        }
        val targetKeys = List.of(TARGET_KEY_SEP.split(String.valueOf(firstSample.getOrElse(TARGET_KEYS, "")).trim()));
        if (targetKeys.isEmpty()) {
            throw new RuntimeException("Missing targetKeys");
        }
        val algorithm = String.valueOf(firstSample.getOrElse(ALGORITHM, DEFAULT_ALGORITHM)).trim().toLowerCase();
        if (!DEFAULT_ALGORITHM.equals(algorithm)) {
            throw new RuntimeException(String.format("Not supported text similarity algo=%s", algorithm));
        }
        val cutOff = ControlPlaneUtils.parseDouble(firstSample.getOrElse(CUT_OFF_THRESHOLD, DEFAULT_CUT_OFF_THRESHOLD), DEFAULT_CUT_OFF_THRESHOLD);
        val multiplier = ControlPlaneUtils.parseDouble(firstSample.getOrElse(SCORE_MULTIPLIER, DEFAULT_SCORE_MULTIPLIER), DEFAULT_SCORE_MULTIPLIER);
        val normalizeInput = ControlPlaneUtils.parseBoolean(firstSample.getOrElse(NORMALIZE_INPUT, false), false);
        val normalizeTarget = ControlPlaneUtils.parseBoolean(firstSample.getOrElse(NORMALIZE_TARGET, false), false);
        val removeChars = List.of(TARGET_KEY_SEP.split(String.valueOf(firstSample.getOrElse(REMOVE_CHARS, ""))));
        val removeBlanks = ControlPlaneUtils.parseBoolean(firstSample.getOrElse(REMOVE_BLANKS, false), false);

        // Normalize input (if required)
        inputText = normalizeText(inputText, normalizeInput, removeChars, removeBlanks);

        log.debug("Input text after normalization={}", inputText);
        if (StringUtils.isEmpty(inputText)) {
            return defaultOutput(input);
        }

        // Fetch target values for each row
        val targetValues = input.getProcessedFeatures()
                .map(inputs -> extractTargetValues(inputs, targetKeys, normalizeTarget, removeChars, removeBlanks));
        log.debug("Target values from stored features={}", targetValues);

        // Compute similarity score for each row
        val finalInputText = inputText;
        SimilarityScore<Integer> algo = new LevenshteinDistance(finalInputText.length());

        val scores = targetValues.map(values -> computeBestSimilarityScore(finalInputText, values, algo));
        log.debug("Scores before multiplier and cut-off={}", scores);

        val scoresWithCutOff = scores.map(score -> score >= cutOff ? (float) (score * multiplier) : 0.0f);
        log.debug("Scores after multiplier and cut-off={}", scoresWithCutOff);
        return ModelInferenceResult.from(input, scoresWithCutOff);
    }

    @Override
    public Map<String, List<ModelInferenceOutputType>> generateDefaultOutput(ModelInferenceInput input) {
        List<Float> output = input.getProcessedFeatures().map(i -> 0F);
        return ModelInferenceOutput.buildResponseMap(input, output);
    }

    private double computeBestSimilarityScore(String inputText, List<String> targetValues, SimilarityScore<Integer> algo) {
        if (targetValues.isEmpty()) {
            return 0.0;
        }
        return targetValues.map(targetValue -> computeSimilarityScore(inputText, targetValue, algo))
                .max().getOrElse(0.0);
    }

    private double computeSimilarityScore(String inputText, String targetValue, SimilarityScore<Integer> algo) {
        // Compute edit-distance (number of character modifications for one string to become the other one)
        double len = inputText.length();
        double editDistance = algo.apply(inputText, targetValue);

        // If -1 it means distance is greater than inputText length
        editDistance = editDistance < 0 ? len : editDistance;

        // Normalize to 0-1 based on inputText length
        return Math.max(len - editDistance, 0) / len;
    }

    private List<String> extractTargetValues(Map<String, Object> inputs,
                                             List<String> targetKeys,
                                             boolean normalizeTarget,
                                             List<String> removeCharacters,
                                             boolean removeBlanks) {
        return targetKeys.map(key -> {
            val valueObj = inputs.getOrElse(key, "");
            val list = valueObj instanceof String ? List.of(String.valueOf(valueObj)) : ControlPlaneUtils.toVavrListString(valueObj);
            return list.map(s -> s.trim().toLowerCase())
                    .map(s -> normalizeText(s, normalizeTarget, removeCharacters, removeBlanks))
                    .filter(s -> !s.isEmpty());
        }).flatMap(Function.identity());
    }

    private String normalizeText(String text,
                                 boolean normalize,
                                 List<String> removeCharacters,
                                 boolean removeBlanks) {
        if (removeCharacters.nonEmpty()) {
            for (String removeChar : removeCharacters) {
                text = text.replace(removeChar, "");
            }
        }

        if (normalize) {
            text = new ControlPlaneTextNormalizer(text).tokenize().orElse("");
        }

        if (removeBlanks) {
            text = text.replaceAll("\\s", "");
        }

        return text;
    }


}

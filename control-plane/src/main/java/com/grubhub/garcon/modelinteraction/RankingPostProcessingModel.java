package com.grubhub.garcon.modelinteraction;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.modelinteraction.rankingpostprocessing.PostProcessingStrategy;
import com.grubhub.garcon.modelinteraction.rankingpostprocessing.RankingPostProcessingFactory;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.ObjectUtils;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class RankingPostProcessingModel implements JavaModel, ModelWithDefaultOutput {
    public static final String STRATEGY_GLOBAL_FIELD = "strategy";
    public static final String RANDOM_SEED_GLOBAL_FIELD = "random_seed";
    public static final String PIN_ENTITY_ID_GLOBAL_FIELD = "pin_entity_id";
    public static final String PIN_ENTITIES_ID_GLOBAL_FIELD = "pin_entities_ids";
    public static final String ENTITY_ID_FEATURE = "entity_id";
    public static final String OUTPUT_ENTITIES = "entities";
    public static final String MAX_RANKING_SLOTS = "max_ranking_slots";

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        val rows = input.getProcessedFeatures();
        val firstFeatureRow = rows.head().toJavaMap();

        if (log.isDebugEnabled()) {
            log.debug("All entries for ranking post processing (configuration)={}", rows);
        }

        if (ObjectUtils.isEmpty(firstFeatureRow.get(STRATEGY_GLOBAL_FIELD))) {
            log.error("Please provide a strategy. Missing key {}", STRATEGY_GLOBAL_FIELD);
            return defaultOutput(input);
        }

        val strategyString = String.valueOf(firstFeatureRow.getOrDefault(STRATEGY_GLOBAL_FIELD, "")).trim();
        PostProcessingStrategy strategy;
        try {
            strategy = PostProcessingStrategy.valueOf(strategyString.toUpperCase());
        } catch (Exception e) {
            log.error("Invalid strategy provided {}", strategyString);
            return defaultOutput(input);
        }

        try {
            java.util.List<String> results = RankingPostProcessingFactory.create(strategy, firstFeatureRow)
                    .process(rows
                            .map(row -> (String) row.get(ENTITY_ID_FEATURE)
                                    .getOrElseThrow(() -> new IllegalArgumentException("Missing entity ID")))
                            .toJavaList());

            val vavrResults = io.vavr.collection.List.ofAll(results);
            HashMap<String, io.vavr.collection.List<ModelInferenceOutputType>> output = HashMap.of(
                    OUTPUT_ENTITIES,
                    ModelInferenceOutputType.ofStringArrayList(io.vavr.collection.List.of(vavrResults)));

            return ModelInferenceResult.builder()
                    .output(output)
                    .build();
        } catch (Exception e) {
            log.error("Failed to create or execute the processor", e);
            return defaultOutput(input);
        }

    }

    @Override
    public Map<String, List<ModelInferenceOutputType>> generateDefaultOutput(ModelInferenceInput input) {
        return io.vavr.collection.HashMap.of(
                "entities", ModelInferenceOutputType.ofStringArrayList(io.vavr.collection.List.empty())
        );
    }
}

package com.grubhub.garcon.modelinteraction.rankingpostprocessing;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.random.JDKRandomGenerator;
import org.apache.commons.math3.random.RandomGenerator;

import java.util.Collections;
import java.util.List;

@Slf4j
public class RandomPairRankingPostProcessing implements RankingPostProcessing {
    private final String seed;
    private final RandomGenerator randomGenerator;

    public RandomPairRankingPostProcessing(String seed) {
        this.seed = seed;
        this.randomGenerator = createRandomGenerator();
    }

    private RandomGenerator createRandomGenerator() {
        final RandomGenerator randomGenerator = new JDKRandomGenerator();
        randomGenerator.setSeed(seed.hashCode());
        return randomGenerator;
    }

    @Override
    public List<String> process(List<String> entities) {
        if (entities.isEmpty()) {
            log.warn("Empty entities list provided. Returning one element result.");
            return entities;
        }

        if (entities.size() == 1) {
            log.warn("List size is 1, cannot perform pair swap.");
            return entities;
        }

        int firstPos = randomGenerator.nextInt(entities.size() - 1);
        int secondPos = firstPos + 1;

        Collections.swap(entities, firstPos, secondPos);
        return entities;
    }
}

package com.grubhub.garcon.modelinteraction.adapter;

import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.services.ensembler.FeatureValueService;
import com.grubhub.garcon.controlplane.services.ensembler.impl.featureproviders.InternalFeaturesWithoutSkipProvider;
import com.grubhub.garcon.controlplane.services.ensembler.rules.FeatureStoreInternalRule;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.IndexesStatusDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.ensembler.dto.EnrichedModelInferenceRequest;
import com.grubhub.garcon.modelinteraction.searchembeddings.SearchEmbeddingsRequest;
import com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelConfiguration;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.IndexFileInfo;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.IndexInfo;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.IndexesStatus;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.KNearestNeighbors;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.Neighbor;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.resources.SearchEmbeddingsRpcApi;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import io.micrometer.core.instrument.MeterRegistry;
import io.vavr.Value;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static com.grubhub.garcon.controlplane.util.ControlPlaneUtils.parseBooleanFlag;
import static com.grubhub.garcon.controlplane.util.ControlPlaneUtils.toSingleton;
import static java.util.stream.Collectors.toList;

@Singleton
@Slf4j
public class SearchEmbeddingsAdapter {

    private static final String ENTITIES = "entities";
    private static final String SCORES = "scores";
    private static final String MOCKED_INDICES = "mocked_indices";
    private static final String SE_ENTITY_MAPPING_ENABLED = "se_entity_mapping_enabled";
    private static final String ANNOY_SUFFIX = ".annoy";
    private static final String METADATA_SUFFIX = "_metadata.dat";

    private final SearchEmbeddingsRpcApi searchEmbeddingsRpcApi;
    private final FeatureValueService featureValueService;
    private final FlowConfig flowConfig;
    private final MeterRegistry meterRegistry;
    private final TensorFlowModelConfiguration tensorFlowModelConfiguration;
    private final JavaDateTimeHelper dateTimeHelper;

    @Inject
    public SearchEmbeddingsAdapter(@Named("SEARCH-EMBEDDINGS-rpc-client") SearchEmbeddingsRpcApi searchEmbeddingsRpcApi,
                                   @Named("featureValueServiceWithCache") FeatureValueService featureValueService,
                                   FlowConfig flowConfig,
                                   MeterRegistry meterRegistry,
                                   TensorFlowModelConfiguration tensorFlowModelConfiguration,
                                   JavaDateTimeHelper dateTimeHelper) {
        this.searchEmbeddingsRpcApi = searchEmbeddingsRpcApi;
        this.featureValueService = featureValueService;
        this.flowConfig = flowConfig;
        this.meterRegistry = meterRegistry;
        this.tensorFlowModelConfiguration = tensorFlowModelConfiguration;
        this.dateTimeHelper = dateTimeHelper;
    }

    public void activateIndexes(List<ModelDTO> models) {
        try {
            log.info("Activating indexes for models={}", models.map(ModelDTO::getModelName).toList());
            val indexInfos = models.flatMap(this::makeIndexInfoList);
            log.info("Sending indexes for activation, indexes={}",
                    indexInfos.map(IndexInfo::getIndexName).toList());
            searchEmbeddingsRpcApi.activateIndexes(true, indexInfos.asJava());
        } catch (Exception e) {
            val errorMessage = String.format("Something wrong occurred when trying to activate the indexes for models=%s",
                    models.map(ModelDTO::getModelName).toList());
            throw new RuntimeException(errorMessage, e);
        }
    }

    private List<IndexInfo> makeIndexInfoList(ModelDTO model) {
        return Option.of(model)
                .filter(ModelDTO::isModelTypeSearchEmbeddings)
                .map(this::getIndexInfos)
                .getOrElse(List::empty);
    }

    private List<IndexInfo> getIndexInfos(ModelDTO aModel) {
        return aModel.getSearchEmbeddingsIndexNames()
                .map(indexName ->
                        new IndexInfo(
                                aModel.getModelName() + "_" + indexName,
                                makeS3PathForSearchEmbeddings(aModel, indexName),
                                getSearchEmbeddingsVectorDimension(aModel),
                                new IndexFileInfo(0L, dateTimeHelper.instant().toEpochMilli()),
                                aModel.getModelName(),
                                makeMetadataPathForSearchEmbeddings(aModel, indexName)
                        )
                );
    }

    private Integer getSearchEmbeddingsVectorDimension(ModelDTO aModel) {
        if (aModel.getSearchEmbeddingsVectorDimension() == null || aModel.getSearchEmbeddingsVectorDimension() < 1) {
            throw new IllegalArgumentException("Search embeddings vector dimension should be non null and greater than 1");
        }
        return aModel.getSearchEmbeddingsVectorDimension();
    }

    private String makeS3PathForSearchEmbeddings(ModelDTO model, String indexName) {
        return model.getServingLocation() + "/" + model.getModelMinorVersion() + "/" + indexName + ANNOY_SUFFIX;
    }

    private String makeMetadataPathForSearchEmbeddings(ModelDTO model, String indexName) {
        return model.getServingLocation() + "/" + model.getModelMinorVersion() + "/" + indexName + METADATA_SUFFIX;
    }

    public Map<String, List<ModelInferenceOutputType>> getNearestNeighbors(ModelInferenceInput modelInferenceInput) {
        try {
            val neighbors = fetchNearestNeighborsInternally(modelInferenceInput).toList();
            if (modelInferenceInput.isTestInvocation() && hasMockedIndices(neighbors)) {
                Map<String, List<ModelInferenceOutputType>> outputMap = makeMockedNeighborsMap(neighbors);
                log.error("Search Embeddings test model invocation returned mockedIndices={}, indicating an unavailable index. modelName={}",
                        outputMap.get(MOCKED_INDICES), modelInferenceInput.getModelName());
                return outputMap;
            }
            if (isSeEntityMappingEnabled(modelInferenceInput)) {
                return makeNeighboursMapWithoutInternalLookUp(neighbors, modelInferenceInput.getModelName());
            }
            val modelFeature = getFeatureWithSkippedFetching(modelInferenceInput);
            val internalFeatures = fetchInternalFeatures(modelInferenceInput, modelFeature, neighbors);
            return makeNeighboursMap(neighbors, internalFeatures, modelInferenceInput.getModelName());
        } catch (Exception e) {
            String errorMessage = String.format("Error getting the nearest neighbors for model_name=%s",
                    modelInferenceInput.getModelName());
            if (StringUtils.isNotBlank(e.getMessage())) {
                errorMessage = errorMessage + " - " + e.getMessage();
            }
            throw new RuntimeException(errorMessage, e);
        }
    }

    private Boolean isSeEntityMappingEnabled(ModelInferenceInput modelInferenceInput) {
        return modelInferenceInput.getProcessedFeatures()
                .toStream()
                .find(features -> features.containsKey(SE_ENTITY_MAPPING_ENABLED))
                .flatMap(features -> features.get(SE_ENTITY_MAPPING_ENABLED))
                .map(flag -> parseBooleanFlag(flag, String.format("Failed to parse the %s flag for search embeddings", SE_ENTITY_MAPPING_ENABLED)))
                .getOrElse(false);
    }

    private Map<String, List<ModelInferenceOutputType>> makeNeighboursMap(
            List<List<Neighbor>> neighbors, List<Map<String, Object>> internalFeatures, String modelName) {
        val neighborsMap = HashMap.of(
                ENTITIES, getEntitiesValues(neighbors, createLookUpMap(internalFeatures)),
                SCORES, getScoresValues(neighbors)
        );
        checkEntitiesAndScores(neighborsMap, modelName);
        return neighborsMap;
    }

    private Map<String, List<ModelInferenceOutputType>> makeNeighboursMapWithoutInternalLookUp(
            List<List<Neighbor>> neighbors, String modelName) {
        val neighborsMap = HashMap.of(
                ENTITIES, getEntitiesValues(neighbors),
                SCORES, getScoresValues(neighbors)
        );
        checkEntitiesAndScores(neighborsMap, modelName);
        return neighborsMap;
    }

    private Map<String, List<ModelInferenceOutputType>> makeMockedNeighborsMap(List<List<Neighbor>> neighbors) {
        return HashMap.of(
                ENTITIES, getEntitiesValues(neighbors),
                SCORES, getScoresValues(neighbors),
                MOCKED_INDICES, getMockedIndices(neighbors)
        );
    }


    private void checkEntitiesAndScores(HashMap<String, List<ModelInferenceOutputType>> neighborsMap, String modelName) {
        int numElements = neighborsMap.get(ENTITIES).get().get(0).getNumberOfElements();
        neighborsMap.forEach((outputsKey, listOfOutputTypes) -> listOfOutputTypes.forEach(outputType -> {
            if (outputType.getNumberOfElements() == 0) {
                log.debug("For model_name={}, empty {} list found in neighbors_map={}",
                        modelName, outputsKey, neighborsMap);
            }
            if (outputType.getNumberOfElements() != numElements) {
                throw new RuntimeException(
                        String.format("For model_name=%s, numbers of %s are not consistent in neighbors_map=%s",
                                modelName, outputsKey, neighborsMap));
            }
        }));
    }

    List<ModelInferenceOutputType> getEntitiesValues(List<List<Neighbor>> neighbors, Map<String, String> lookUpMap) {
        return ModelInferenceOutputType.ofStringArrayList(neighbors
                .map(internalNeighbors -> internalNeighbors
                        .map(this::createComposedKey)
                        .filter(lookUpMap::containsKey)
                        .map(lookUpMap::get)
                        .map(Option::get)
                )
        );
    }

    List<ModelInferenceOutputType> getEntitiesValues(List<List<Neighbor>> neighbors) {
        return ModelInferenceOutputType.ofStringArrayList(neighbors
                .map(internalNeighbors -> internalNeighbors.map(Neighbor::getEntityId))
        );
    }

    List<ModelInferenceOutputType> getScoresValues(List<List<Neighbor>> neighbors) {
        return ModelInferenceOutputType.ofFloatArrayList(getScores(neighbors));
    }

    List<ModelInferenceOutputType> getMockedIndices(List<List<Neighbor>> neighbors) {
        return ModelInferenceOutputType.ofStringArrayList(neighbors
                .map(internalNeighbors -> internalNeighbors.map(Neighbor::getIndexName)
                        .filter(indexName -> indexName.endsWith("_MOCK"))
                        .distinct()
                ));
    }

    private String createComposedKey(Neighbor neighbor) {
        return neighbor.getIndexName() + "|" + neighbor.getEntityId();
    }

    private Map<String, String> createLookUpMap(List<Map<String, Object>> internalFeatures) {
        return internalFeatures
                .toStream()
                .toMap(this::createComposedKey, this::getDomainEntityId);
    }

    private List<List<Float>> getScores(List<List<Neighbor>> neighbors) {
        return neighbors.map(n -> List.ofAll(n).map(Neighbor::getSimilarity));
    }

    private String getDomainEntityId(Map<String, Object> featureAsMap) {
        return featureAsMap.get("domain_entity_id")
                .map(String::valueOf)
                .getOrElseThrow(() -> new RuntimeException(String.format("Couldn't get the domain_entity_id key from features=[%s]",
                        getKeysAsString(featureAsMap))));
    }

    private String createComposedKey(Map<String, Object> featureAsMap) {
        val indexName = featureAsMap.get("index_name")
                .map(String::valueOf)
                .getOrElseThrow(() -> new RuntimeException(String.format("Couldn't get the index_name key from features=[%s]",
                        getKeysAsString(featureAsMap))));

        val entityId = featureAsMap.get("entity_id")
                .map(String::valueOf)
                .getOrElseThrow(() -> new RuntimeException(String.format("Couldn't get the entity_id key from features=[%s]",
                        getKeysAsString(featureAsMap))));

        return indexName + "|" + entityId;
    }

    private String getKeysAsString(Map<String, Object> featureAsMap) {
        return Option.of(featureAsMap)
                .getOrElse(HashMap::empty)
                .keySet()
                .toStream()
                .reduce(String::concat)
                .trim();
    }

    private List<Map<String, Object>> fetchInternalFeatures(ModelInferenceInput modelInferenceInput, FeatureDTO feature, List<List<Neighbor>> neighbors) {
        val modelInferenceRequest = createModelInferenceRequest(modelInferenceInput, neighbors);

        val enrichedModelInferenceRequest = createEnrichedModelInferenceRequest(
                modelInferenceInput,
                modelInferenceRequest,
                feature
        );

        return FeatureStoreInternalRule.createFeatureStoreInternalRule(
                featureValueService,
                enrichedModelInferenceRequest,
                getProcessedFeaturesAsVavr(neighbors),
                meterRegistry
        ).processInternalFeatures(new InternalFeaturesWithoutSkipProvider(enrichedModelInferenceRequest));
    }

    private EnrichedModelInferenceRequest createEnrichedModelInferenceRequest(
            ModelInferenceInput modelInferenceInput,
            ModelInferenceRequest modelInferenceRequest,
            FeatureDTO feature) {

        return EnrichedModelInferenceRequest.from(modelInferenceRequest, mutateModelFeatures(modelInferenceInput, feature), flowConfig,
                modelInferenceInput.getInvocationType());
    }

    private ModelDTO mutateModelFeatures(ModelInferenceInput modelInferenceInput, FeatureDTO feature) {
        return modelInferenceInput.getModel().withModelFeatures(List.of(feature));
    }

    private ModelInferenceRequest createModelInferenceRequest(ModelInferenceInput modelInferenceInput, List<List<Neighbor>> neighbors) {
        return ModelInferenceRequest
                .builder()
                .modelName(modelInferenceInput.getModelName())
                .features(getProcessedFeatures(neighbors))
                .build();
    }


    private java.util.List<java.util.Map<String, Object>> getProcessedFeatures(List<List<Neighbor>> neighbors) {
        return neighbors
                .flatMap(List::toStream)
                .map(this::transformNeighborIntoMap)
                .distinct()
                .collect(toList());
    }

    private List<Map<String, Object>> getProcessedFeaturesAsVavr(List<List<Neighbor>> neighbors) {
        return neighbors
                .flatMap(List::toStream)
                .map(this::transformNeighborIntoMapAsVavr)
                .distinct()
                .toList();
    }

    private java.util.Map<String, Object> transformNeighborIntoMap(Neighbor neighbor) {
        return ImmutableMap.of("entity_id", neighbor.getEntityId(), "index_name", neighbor.getIndexName());
    }

    private Map<String, Object> transformNeighborIntoMapAsVavr(Neighbor neighbor) {
        return HashMap.of("entity_id", neighbor.getEntityId(), "index_name", neighbor.getIndexName());
    }

    private FeatureDTO getFeatureWithSkippedFetching(ModelInferenceInput modelInferenceInput) {
        return modelInferenceInput.getModel()
                .getModelFeatures()
                .filter(FeatureDTO::hasSkippedFetching)
                .collect(toSingleton(createException(modelInferenceInput)));
    }

    private Stream<List<Neighbor>> fetchNearestNeighborsInternally(ModelInferenceInput modelInferenceInput) {
        val kNearestNeighbors = modelInferenceInput.getProcessedFeatures()
                .toStream()
                .map(features -> buildSearchEmbeddingsRequest(features, modelInferenceInput.getModelName()))
                .map(this::callSearchEmbeddings)
                .toList();

        return kNearestNeighbors.toStream()
                .map(KNearestNeighbors::getNearestNeighbors)
                .map(items -> List.ofAll(items.stream().map(n -> withPlainIndexName(modelInferenceInput.getModelName(), n))));
    }

    private KNearestNeighbors callSearchEmbeddings(SearchEmbeddingsRequest input) {
        return searchEmbeddingsRpcApi.getNearestNeighborsMultipleIndex(
                input.getQueryVector(),
                input.getK(),
                input.getSearchK(),
                input.getUniqueIndexNames(this::prefixWithModelName),
                input.getNormalization(),
                input.getQueryFilters()
        );
    }

    private String prefixWithModelName(String modelName) {
        return modelName + "_";
    }

    private String getUniqueIndexName(String modelName, String index) {
        return prefixWithModelName(modelName) + index;
    }

    private Neighbor withPlainIndexName(String modelName, Neighbor n) {
        return new Neighbor(n.getEntityId(), n.getSimilarity(), getPlainIndexName(modelName, n.getIndexName()));
    }

    private String getPlainIndexName(String modelName, String uniqueIndexName) {
        return uniqueIndexName.replaceFirst(prefixWithModelName(modelName), "");
    }

    private SearchEmbeddingsRequest buildSearchEmbeddingsRequest(Map<String, Object> feature, String modelName) {
        return feature.transform(features -> new SearchEmbeddingsRequest(features, modelName));
    }

    private IllegalStateException createException(ModelInferenceInput modelInferenceInput) {
        return new IllegalStateException(
                String.format("Found less/more number of skipped fetching features for model model_name=%s",
                        modelInferenceInput.getModelName()
                )
        );
    }

    public ModelStatus indexesStatus(ModelDTO model) {
        val indexesStatus = searchEmbeddingsRpcApi.getIndexesStatus(buildIndexNames(model));
        return getOverallStatus(indexesStatus);
    }

    private java.util.List<String> buildIndexNames(ModelDTO model) {
        return model.getSearchEmbeddingsIndexNames()
                .map(indexNames -> getUniqueIndexName(model.getModelName(), indexNames))
                .asJava();
    }

    public List<IndexesStatusDTO> indexesStatusAsList(ModelDTO model) {
        val indexesStatuses = searchEmbeddingsRpcApi.getIndexesStatus(buildIndexNames(model))
                .stream()
                .map(this::createIndexesStatus)
                .collect(toList());

        return List.ofAll(indexesStatuses);
    }

    private IndexesStatusDTO createIndexesStatus(IndexesStatus indexesStatus) {
        return IndexesStatusDTO
                .builder()
                .activeInstanceList(indexesStatus.getActiveInstanceList())
                .activeProportion(indexesStatus.getActiveProportion())
                .activeInstancesCount(indexesStatus.getActiveInstancesCount())
                .indexName(indexesStatus.getIndexName())
                .totalInstancesCount(indexesStatus.getTotalInstancesCount())
                .build();
    }

    private ModelStatus getOverallStatus(java.util.List<IndexesStatus> indexesStatus) {
        double overallStatus = computeOverallStatus(indexesStatus);
        return overallStatus == 1d
                ? ModelStatus.AVAILABLE
                : ModelStatus.UNAVAILABLE;
    }

    private double computeOverallStatus(java.util.List<IndexesStatus> indexesStatus) {
        double sum = indexesStatus
                .stream()
                .filter(this::isActivePortionGraterThanThreshold)
                .map(IndexesStatus::getActiveProportion)
                .filter(Objects::nonNull)
                .mapToDouble(Float::doubleValue)
                .sum();

        return sum / indexesStatus.size();
    }

    private boolean isActivePortionGraterThanThreshold(IndexesStatus indexesStatus) {
        return indexesStatus.getActiveProportion() >= tensorFlowModelConfiguration.getSearchEmbeddingsInstancesActiveRatio();
    }

    /**
     * For test invocations, check for mocked neighbors in the invocation response.
     * If a model index is not available, the returned neighbors will include the suffix "_MOCK" in the index name.
     * When this occurs, the model should not be promoted
     * See <a href="https://jira.grubhub.com/browse/SEAR-7773">SEAR-7773</a> for details.
     * @param neighbors Neighbors returned from Search Embeddings invocation
     */
    private boolean hasMockedIndices(List<List<Neighbor>> neighbors) {
        return neighbors.toStream().flatMap(Value::toStream)
                .filter(neighbor -> neighbor.getIndexName().endsWith("_MOCK"))
                .size() > 0;
    }
}

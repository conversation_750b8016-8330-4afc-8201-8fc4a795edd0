package com.grubhub.garcon.modelinteraction;

import com.google.inject.Singleton;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.ddml.util.LogUtil;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import io.vavr.collection.Map;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

/**
 * Distance falloff model.
 * Based on notebook https://github.com/GrubhubProd/search_jupyternotebooks/blob/master/notebooks/DefaultSortPlayground.ipynb
 */
@Slf4j
@Singleton
public class DistanceFalloffModel implements JavaModel {

    private static final float DEGREES_TO_RADIANS = (float) Math.PI / 180.0f;
    private static final float RADIUS_KM = 6371f;
    private static final float RADIUS_M = RADIUS_KM * 1000f;
    private static final float METERS_TO_MILES = 0.000621185f;

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        log.debug("Processed features={} ", LogUtil.logFeatures(input.getProcessedFeatures()));
        val output = input.getProcessedFeatures().map(this::computeForSample);
        log.debug("Output={} ", output);
        return ModelInferenceResult.from(input, output);
    }

    public float computeForSample(Map<String, Object> sample) {
        // Extract all inputs
        log.debug("DistanceFalloff_inputs={}", ControlPlaneUtils.toString(sample));
        val dinerLat = getDouble("diner_latitude", 0, sample);
        val dinerLng = getDouble("diner_longitude", 0, sample);
        val restaurantLat = getDouble("restaurant_latitude", 0, sample);
        val restaurantLng = getDouble("restaurant_longitude", 0, sample);
        val restaurantThreshold = getDouble("onion_powder", 0, sample);
        val dinerDistanceThreshold = getDouble("distance_threshold", 0, sample);
        val decayRate = getDouble("decay_rate", 0, sample);
        val trafficLevelMultiplier = getDouble("traffic_level_multiplier", 0, sample);
        val minWeight = getDouble("min_weight", 0, sample);
        val thresholdScalar = getDouble("threshold_scalar", 0, sample);
        val dinerBias = getDouble("diner_bias", 0, sample);
        val minBiasWeight = getDouble("min_bias_weight", 0, sample);
        val minBias = getDouble("min_bias", 0, sample);
        val quality = getDouble("quality", 0, sample);
        val qualityExponent = getDouble("quality_exponent", 0, sample);
        val distanceExponent = getDouble("distance_exponent", 0, sample);

        val falloff = this.distanceFalloff(dinerLat, dinerLng, restaurantLat, restaurantLng, restaurantThreshold,
                dinerDistanceThreshold, decayRate, trafficLevelMultiplier, minWeight, thresholdScalar, dinerBias,
                minBiasWeight, minBias, quality, qualityExponent, distanceExponent);
        log.debug("DistanceFalloff_inputs={}, output={}", ControlPlaneUtils.toString(sample), falloff);
        return (float) falloff;
    }

    public double distanceFalloff(double dinerLat, double dinerLng, double restaurantLat, double restaurantLng,
                                  double restaurantThreshold, double dinerDistanceThreshold, double decayRate,
                                  double trafficLevelMultiplier, double minWeight, double thresholdScalar,
                                  double dinerBias, double minBiasWeight, double minBias, double quality,
                                  double qualityExponent, double distanceExponent) {
        // 0.9 rt  + 0.1 rt  + 0.7 * 0 dt + 0.3 * 0 rt
        val falloffThreshold = Math.min(dinerDistanceThreshold, restaurantThreshold) * minBias * minBiasWeight
                + Math.max(dinerDistanceThreshold, restaurantThreshold) * (1.0 - minBias) * minBiasWeight
                + dinerDistanceThreshold * dinerBias * (1.0 - minBiasWeight)
                + restaurantThreshold * (1.0 - dinerBias) * (1.0 - minBiasWeight);

        val distanceMeters = arcDistanceMeters(restaurantLat, restaurantLng, dinerLat, dinerLng);
        val distanceMiles = distanceMeters * METERS_TO_MILES;

        val falloffDistance = distanceMiles - falloffThreshold * thresholdScalar;
        val falloffRate = decayRate - trafficLevelMultiplier * 0.05;

        double falloff;
        if (falloffDistance <= 0.0) {
            falloff = 1.0;
        } else {
            // Exponential decay function: x^(y^2 /z^2)
            val weight = Math.pow(quality, qualityExponent) *
                    Math.pow(Math.pow(
                            falloffRate,
                            Math.pow(falloffDistance, 2) / Math.pow(falloffThreshold, 2)
                    ), distanceExponent);
            falloff = Math.max(minWeight, weight);
        }

        log.debug("DistanceFalloff_function dinerLat={}, dinerLng={}, restaurantLat={}, restaurantLng={}, restaurantThreshold={}, " +
                        "dinerDistanceThreshold={}, decayRate={}, trafficLevelMultiplier={}, minWeight={}, thresholdScalar={}, " +
                        "dinerBias={}, minBiasWeight={}, minBias={}, quality={}, qualityExponent={}, distanceExponent={}, " +
                        "distanceMeters={}, distanceMiles={}, falloffDistance={}, falloffRate={}, falloff={}",
                dinerLat, dinerLng, restaurantLat, restaurantLng, restaurantThreshold, dinerDistanceThreshold, decayRate,
                trafficLevelMultiplier, minWeight, thresholdScalar, dinerBias, minBiasWeight, minBias, quality, qualityExponent,
                distanceExponent, distanceMeters, distanceMiles, falloffDistance, falloffRate, falloff);
        return falloff;
    }

    public double arcDistanceMeters(double lat1, double long1, double lat2, double long2) {
        // Convert latitude and longitude to spherical coordinates in radians.

        // phi = 90 - latitude
        val phi1 = (90.0f - lat1) * DEGREES_TO_RADIANS;
        val phi2 = (90.0f - lat2) * DEGREES_TO_RADIANS;

        // theta = longitude
        val theta1 = long1 * DEGREES_TO_RADIANS;
        val theta2 = long2 * DEGREES_TO_RADIANS;

        /*
            Compute spherical distance from spherical coordinates.
            For two locations in spherical coordinates: (1, theta, phi) and (1, theta, phi)
            cosine( arc length ) = sin phi sin phi' cos(theta-theta') + cos phi cos phi'
            distance = rho * arc length
         */

        val cos = (Math.sin(phi1) * Math.sin(phi2) * Math.cos(theta1 - theta2) + Math.cos(phi1) * Math.cos(phi2));
        if (cos > 1.0) {
            return 0;
        }
        val arc = Math.acos(cos) * RADIUS_M;

        log.debug("ArcDistance: lat1={}, long1={}, lat2={}, long2={}, cost={}, arc={}", lat1, long1, lat2, long2, cos, arc);
        return arc;
    }

    private double getDouble(String key, double def, Map<String, Object> map) {
        return ControlPlaneUtils.parseDouble(map.getOrElse(key, def), def);
    }


}

package com.grubhub.garcon.modelinteraction.searchembeddings;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.modelinteraction.JavaModel;
import com.grubhub.garcon.modelinteraction.adapter.SearchEmbeddingsAdapter;
import io.vavr.collection.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class SearchEmbeddingsModel implements JavaModel {

    private final SearchEmbeddingsAdapter searchEmbeddingsAdapter;

    @Inject
    public SearchEmbeddingsModel(SearchEmbeddingsAdapter searchEmbeddingsAdapter) {
        this.searchEmbeddingsAdapter = searchEmbeddingsAdapter;
    }

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput modelInferenceInput) {
        return ModelInferenceResult.builder()
                .output(searchEmbeddingsAdapter.getNearestNeighbors(modelInferenceInput))
                .build();
    }

    public void activateIndexes(List<ModelDTO> models) {
        searchEmbeddingsAdapter.activateIndexes(models);
    }

    public ModelStatus indexesStatus(ModelDTO model) {
        return searchEmbeddingsAdapter.indexesStatus(model);
    }

}


package com.grubhub.garcon.modelinteraction.tensorflow;

import com.grubhub.garcon.grpc.EurekaHostAndPort;
import io.grpc.Channel;
import io.vavr.control.Option;
import lombok.Value;
import tensorflow.serving.PredictionServiceGrpc;

@Value
public class PredictionServiceStubWithHost {
    PredictionServiceGrpc.PredictionServiceBlockingStub predictionServiceBlockingStub;
    EurekaHostAndPort eurekaHostAndPort; //todo: remove this field after to grpc managed by roux.


    public PredictionServiceStubWithHost(PredictionServiceGrpc.PredictionServiceBlockingStub predictionServiceBlockingStub) {
        this.predictionServiceBlockingStub = predictionServiceBlockingStub;
        this.eurekaHostAndPort = null;
    }

    public PredictionServiceStubWithHost(PredictionServiceGrpc.PredictionServiceBlockingStub predictionServiceBlockingStub,
                                         EurekaHostAndPort eurekaHostAndPort) {
        this.predictionServiceBlockingStub = predictionServiceBlockingStub;
        this.eurekaHostAndPort = eurekaHostAndPort;
    }

    public Option<EurekaHostAndPort> getEurekaHostAndPort() {
        return Option.of(eurekaHostAndPort);
    }

    public String getHostOrUnknown() {
        return this.getEurekaHostAndPort().map(EurekaHostAndPort::getHost).getOrElse("unknown");
    }

    public Channel getChannel() {
        return predictionServiceBlockingStub.getChannel();
    }
}

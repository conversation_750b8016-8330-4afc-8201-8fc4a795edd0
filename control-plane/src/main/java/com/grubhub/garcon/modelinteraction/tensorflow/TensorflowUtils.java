package com.grubhub.garcon.modelinteraction.tensorflow;

import lombok.experimental.UtilityClass;
import tensorflow.serving.GetModelStatus;

import java.util.Objects;

import static tensorflow.serving.GetModelStatus.ModelVersionStatus.State.AVAILABLE;

@UtilityClass
public class TensorflowUtils {
    public static GetModelStatus.GetModelStatusResponse returnStatusOrUnknown(GetModelStatus.GetModelStatusResponse statusResponse) {
        if (Objects.isNull(statusResponse)) {
            return GetModelStatus.GetModelStatusResponse.newBuilder()
                    .addModelVersionStatus(GetModelStatus.ModelVersionStatus.newBuilder()
                            .setState(GetModelStatus.ModelVersionStatus.State.UNKNOWN))
                    .build();
        }
        return statusResponse;
    }

    public static int queryStatuses(GetModelStatus.GetModelStatusResponse status) {
        return status
                .getModelVersionStatusList()
                .stream()
                .map(GetModelStatus.ModelVersionStatus::getState)
                .allMatch(AVAILABLE::equals) ? 1 : 0;
    }
}

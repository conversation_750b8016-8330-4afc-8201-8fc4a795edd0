package com.grubhub.garcon.modelinteraction.tensorflow;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.inject.Inject;
import com.google.protobuf.Int64Value;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.metrics.Spans;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelConfigUpdateResponse;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.TensorFlowModelMetadataDTO;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.grpc.EurekaHostAndPort;
import com.grubhub.garcon.grpc.GrpcChannelFactory;
import com.grubhub.garcon.grpc.GrpcConfiguration;
import com.grubhub.garcon.grpc.GrpcResolveType;
import com.grubhub.garcon.modelinteraction.PredictionClientFactory;
import io.grpc.InternalChannelz;
import io.grpc.InternalInstrumented;
import io.grpc.ManagedChannel;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.collection.List;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.tensorflow.framework.TensorProto;
import tensorflow.serving.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus.ENABLED;
import static com.grubhub.garcon.controlplanerpc.model.ModelType.TENSOR_FLOW;
import static java.util.stream.Collectors.toList;

@Slf4j
@Getter
@ToString(of = {"tfsClusterName", "tfsClusterConfig"})
public class TfsClusterInteraction {
    private static final String SIGNATURE_DEF = "signature_def";
    private static final String TENSOR_FLOW_SERVING_PREDICT = "tensorFlowServingPredict";
    private static final String PREDICTION_FAILURES = "predictionFailures";

    private static final String THS_HOST = "tfsHost";
    private static final String SERVING_APP_NAME = "servingAppName";
    private static final String SERVING_VERSION = "servingVersion";
    private static final String SERVING_PORT = "servingPort";
    private static final String TFS_DISCOVERED_NODE = "tfsDiscoveredNode";
    private static final String TFS_CLUSTER = "tfsCluster";

    private final String tfsClusterName;
    private final TfsClusterConfig tfsClusterConfig;
    private final GrpcChannelFactory grpcChannelFactory;
    private final Retryer<Predict.PredictResponse> predictRetryer;
    private final TensorFlowModelConfiguration tensorFlowModelConfiguration;
    private final java.util.Map<EurekaHostAndPort, ModelServiceGrpc.ModelServiceFutureStub> fanOutClients = new HashMap<>();
    private final MeterRegistry meterRegistry;
    private final ModelAwareClientProvider modelAwareClientProvider;
    private final ModelService modelService;
    private final ReadWriteLock rwLock;
    private final FlowConfig flowConfig;
    private final GrpcConfiguration grpcConfiguration;


    @Getter
    private final PredictionServiceGrpc.PredictionServiceBlockingStub predictionClient;

    @Inject
    public TfsClusterInteraction(
            String tfsClusterName,
            TfsClusterConfig tfsClusterConfig,
            PredictionClientFactory predictionClientFactory,
            GrpcChannelFactory grpcChannelFactory,
            TensorFlowModelConfiguration tensorFlowModelConfiguration,
            MeterRegistry meterRegistry,
            ModelAwareClientProvider modelAwareClientProvider,
            ModelService modelService,
            FlowConfig flowConfig,
            GrpcConfiguration grpcConfiguration
    ) {
        this.tfsClusterName = tfsClusterName;
        this.tfsClusterConfig = tfsClusterConfig;
        this.grpcChannelFactory = grpcChannelFactory;
        this.tensorFlowModelConfiguration = tensorFlowModelConfiguration;
        this.modelService = modelService;
        this.flowConfig = flowConfig;
        this.grpcConfiguration = grpcConfiguration;

        GrpcResolveType grpcResolveType = tensorFlowModelConfiguration.getGrpcResolveType();

        this.predictionClient =  predictionClientFactory.createBlockingClient(
                grpcResolveType,
                tfsClusterConfig,
                grpcChannelFactory
        );

        this.predictRetryer = RetryerBuilder.<Predict.PredictResponse>newBuilder()
                .retryIfException()
                .withWaitStrategy(WaitStrategies.exponentialWait())
                .withStopStrategy(
                        failedAttempt ->
                                failedAttempt.getAttemptNumber() >= tfsClusterConfig.getPredictRetries() + 1)
                .build();
        this.meterRegistry = meterRegistry;
        this.modelAwareClientProvider = modelAwareClientProvider;

        this.rwLock = new ReentrantReadWriteLock();
        if (!ControlPlaneUtils.isTrue(grpcConfiguration.isGrpcEnabledForDdmlTensorFlow())) {
            refreshFanOutClients();
        }
    }

    void refreshFanOutClients() {
        val channels = newChannels();
        java.util.Map<EurekaHostAndPort, ModelServiceGrpc.ModelServiceFutureStub> newClients = new HashMap<>();
        channels.forEach((node, channel) -> newClients.put(node, ModelServiceGrpc.newFutureStub(channel)));

        log.debug("Updated TFS fanOutClients for model service, clients={}, tfsCluster={}", newClients, tfsClusterName);

        updateFanoutClientsAtomically(newClients);
        modelAwareClientProvider.refreshPredictionClients(channels);
        refreshAllStatuses();
        logClientsIfNeeded(channels, newClients);
    }

    private Map<EurekaHostAndPort, ManagedChannel> newChannels() {
        return Option.of(tensorFlowModelConfiguration)
                .filter(TensorFlowModelConfiguration::grpcResolveTypeIsEqualToEureka)
                .map(ignore -> tfsClusterConfig.toAppKey())
                .map(grpcChannelFactory::fetchMultiEurekaChannels)
                .peek(this::addMetricsForChannelKey)
                .getOrElse(this::fallbackToStaticResolveType);
    }

    private void logClientsIfNeeded(
            Map<EurekaHostAndPort, ManagedChannel> channels,
            Map<EurekaHostAndPort, ModelServiceGrpc.ModelServiceFutureStub> newClients) {

        if (log.isDebugEnabled()) {
            log.debug("Updated TFS fanOutClients for model service, targets={}, clients_size={}, tfsCluster={}",
                    getTargets(channels),
                    newClients.size(),
                   tfsClusterName
            );
        }
    }

    private void updateFanoutClientsAtomically(Map<EurekaHostAndPort, ModelServiceGrpc.ModelServiceFutureStub> newClients) {
        rwLock.writeLock().lock();
        try {
            fanOutClients.clear();
            fanOutClients.putAll(newClients);
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    private java.util.List<String> getTargets(Map<EurekaHostAndPort, ManagedChannel> channels) {
        java.util.List<String> targets = channels.values().stream()
                .filter(managedChannel -> managedChannel instanceof InternalInstrumented)
                .map(channel -> {
                    ListenableFuture<InternalChannelz.ChannelStats> future =
                            ((InternalInstrumented<InternalChannelz.ChannelStats>) channel).getStats();
                    //todo: update to have roux handle
                    InternalChannelz.ChannelStats channelStats = Futures.getUnchecked(future);
                    return channelStats.target;
                })
                .filter(Objects::nonNull)
                .collect(toList());

        return targets;
    }

    private Map<EurekaHostAndPort, ManagedChannel> fallbackToStaticResolveType() {
        return Collections.singletonMap(
                EurekaHostAndPort.builder().host("GLOBAL").port(8501).build(),
                grpcChannelFactory.fetchStaticChannel(tfsClusterConfig.getTfServingStaticAddresses(), tfsClusterConfig.getTfServingPort())
        );
    }

    public void refreshAllStatuses() {
        try {
            if (tensorFlowModelConfiguration.getRefreshModelAwareTargeting()) {
                val models = getTensorFlowModelsForCurrentClusterWithStatusIn(ENABLED);
                log.debug("Refreshing={} models={} for tfs cluster tfs_cluster={}", models.size(), models.map(ModelDTO::getModelName), tfsClusterName);
                models.forEach(this::refreshStatus);
            }
        } catch (Exception e) {
            log.error("Exception when refreshing tensor flow statuses", e);
        }
    }

    private void refreshStatus(ModelDTO model) {
        try {
            val request = createModelStatusRequest(model);
            val response = Futures.successfulAsList(getStatusRequests(request));

            val statusMap = Futures.getUnchecked(response).stream()
                    .filter(Objects::nonNull)
                    .peek(modelStatusResponse -> log.debug("TF status response model_status_response={}, model_name={}",
                            modelStatusResponse,
                            model.getModelName())
                    ).collect(Collectors.toMap(
                            entry -> entry._1,
                            entry -> entry._2
                    ));

            val availableHosts = statusMap.entrySet().stream()
                    .filter(hostAndStatus -> {
                        GetModelStatus.GetModelStatusResponse statusOrUnknown = TensorflowUtils.returnStatusOrUnknown(hostAndStatus.getValue());
                        return TensorflowUtils.queryStatuses(statusOrUnknown) >= 1;
                    })
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            log.debug("Found={} out of={} available hosts for model={}, list={}", availableHosts.size(), statusMap.size(),
                    model.getModelName(), availableHosts);

            modelAwareClientProvider.refreshModelStatus(model, availableHosts);

        } catch (Exception e) {
            log.warn("Failed to refresh model={}", model.getModelName(), e);
        }
    }

    private void addMetricsForChannelKey(Map<EurekaHostAndPort, ManagedChannel> keys) {
        keys.keySet().forEach(key ->
            meterRegistry.counter(Metrics.name(getClass(), TFS_DISCOVERED_NODE),
                            Tags.of(THS_HOST, key.getHost(),
                                    SERVING_APP_NAME, tfsClusterConfig.getTfServingAppName(),
                                    SERVING_VERSION, tfsClusterConfig.getTfServingVersion(),
                                    SERVING_PORT, String.valueOf(tfsClusterConfig.getTfServingPort())
                            )
                    )
                    .increment()
        );
    }
    private void addMetricForModelStatus(ModelDTO model, double successRatio) {
        meterRegistry.counter(Metrics.name(getClass(), "tfsModelStatus"),
                        Tags.of("model_name", model.getModelName(),
                                "model_version", model.getVersion(),
                                "success_ratio", String.valueOf(successRatio))
                )
                .increment();
    }

    private int getFanOutClientsSize() {
        return fanOutClients.size();
    }

    private ModelStatus convertToModelStatus(double successRatio, ModelDTO model,
                                             java.util.Map<EurekaHostAndPort, GetModelStatus.GetModelStatusResponse> statusMap) {
        if (successRatio <= .5) {
            val statusListStr = StringUtils.joinWith("|", statusMap.values().stream().map(TensorflowUtils::returnStatusOrUnknown).toArray());
            log.error("Wrong TFS status for model_name={}, success_ratio={}, nodes_count={}, responses_count={}, status_list={}, tfsCluster={}",
                    model.getModelName(), successRatio, getFanOutClientsSize(), statusMap.size(), statusListStr, tfsClusterName);
            return ModelStatus.UNAVAILABLE;
        }
        return ModelStatus.AVAILABLE;
    }

    public Predict.PredictResponse getPredictResponse(Model.ModelSpec modelSpec, java.util.Map<String, TensorProto> tensorInputMap) {
        log.debug("before predict. tensorInputMap={}", tensorInputMap);
        Predict.PredictRequest.Builder builder = Predict.PredictRequest.newBuilder();
        builder.setModelSpec(modelSpec);
        builder.putAllInputs(tensorInputMap);

        Predict.PredictRequest request = builder.build();

        log.debug("predict request={}", request);

        PredictionServiceStubWithHost clientWithHost = determinePredictionClient(modelSpec.getName());

        log.debug("Batching log. Host={}.", clientWithHost.getHostOrUnknown());

        String timerName = Metrics.name(getClass(), "predict");
        Timer.Sample timer = Timer.start(meterRegistry);
        try {
            val result = predictRetryer.call(() ->
                    Spans.span(TENSOR_FLOW_SERVING_PREDICT,
                            io.vavr.collection.HashMap.of(
                                            "model_name", modelSpec.getName(),
                                            "model_version", String.valueOf(modelSpec.getVersion()),
                                            "tfsHost", clientWithHost.getHostOrUnknown(),
                                            "tfsCluster", tfsClusterName
                                    )
                                    .toJavaMap(),
                            () -> clientWithHost.getPredictionServiceBlockingStub()
                                    .withDeadlineAfter(tfsClusterConfig.getPredictTimeoutMs(), TimeUnit.MILLISECONDS)
                                    .predict(request)
                    ));
            timer.stop(meterRegistry.timer(timerName, 
                    Tags.of("model_name", modelSpec.getName(), 
                            "status", "success",
                            "tfsHost", clientWithHost.getHostOrUnknown(),
                            "tfsCluster", tfsClusterName
                    )
            ));
            return result;
        } catch (Exception e) {
            timer.stop(meterRegistry.timer(timerName, 
                    Tags.of("model_name", modelSpec.getName(), 
                            "status", "fail", 
                            "tfsHost", clientWithHost.getHostOrUnknown(),
                            "tfsCluster", tfsClusterName
                    )
            ));

            meterRegistry.counter(
                            Metrics.name(getClass(), PREDICTION_FAILURES),
                            Tags.of(
                                    "model_name", modelSpec.getName(),
                                    "tfsCluster", tfsClusterConfig.getTfServingAppName() + tfsClusterConfig.getTfServingVersion()
                            )
                    )
                    .increment();
            throw new RuntimeException(
                    String.format("Error invoking TensorFlow model=%s, version=%d, retries=%s, input_key_size=%d, input_keys=%s, tfsCluster=%s",
                            modelSpec.getName(),
                            modelSpec.getVersion().getValue(),
                            tfsClusterConfig.getPredictRetries(),
                            tensorInputMap.size(),
                            tensorInputMap.keySet(),
                            tfsClusterName),
                    e);
        }

    }

    private PredictionServiceStubWithHost determinePredictionClient(String modelName) {

        if (tensorFlowModelConfiguration.getUseModelAwareTargeting()) {
            return Option.of(modelAwareClientProvider.nextClient(modelName))
                    .filter(Objects::nonNull)
                    .getOrElse(new PredictionServiceStubWithHost(predictionClient));
        }

        return new PredictionServiceStubWithHost(predictionClient);
    }


    private GetModelMetadata.GetModelMetadataRequest createModelMetadataRequest(ModelDTO model) {
        val modelSpecBuilder = createModelSpec(model);
        return GetModelMetadata.GetModelMetadataRequest.newBuilder()
                .setModelSpec(modelSpecBuilder)
                .addMetadataField(SIGNATURE_DEF)
                .build();
    }


    public Int64Value tryToParseModelVersionAsInt(String modelVersion) {
        return Try
                .of(() -> Int64Value.of(Long.parseLong(modelVersion)))
                .getOrElseThrow(exception -> new RuntimeException("Unsupported TensorFlow model_version=" + modelVersion, exception));
    }

    private Option<Int64Value> getModelVersion(ModelDTO model) {
        return Stream.of(model)
                .map(ModelDTO::getVersion)
                .flatMap(this::splitModelVersion)
                .takeRight(1)
                .toOption()
                .map(this::tryToParseModelVersionAsInt);
    }

    private Stream<String> splitModelVersion(String version) {
        return Option.of(version)
                .map(aVersion -> aVersion.split("\\."))
                .filter(tokens -> tokens.length > 0)
                .map(Stream::of)
                .getOrElse(Stream::empty);
    }

    public Model.ModelSpec createModelSpec(ModelDTO model) {
        return getModelVersion(model)
                .map(version -> Model.ModelSpec.newBuilder().setName(model.getModelName()).setVersion(version).build())
                .getOrElseThrow(() -> new RuntimeException(String.format("Model spec couldn't be created for model=%s", model.getModelName())));
    }

    public GetModelStatus.GetModelStatusRequest createModelStatusRequest(ModelDTO model) {
        val modelSpecBuilder = createModelSpec(model);
        return GetModelStatus.GetModelStatusRequest.newBuilder()
                .setModelSpec(modelSpecBuilder)
                .build();
    }

    public ModelStatus getTensorFlowStatus(ModelDTO model) {
        try {
            val request = createModelStatusRequest(model);
            log.debug("Getting TF status with request={}", request);
            val response = Futures.successfulAsList(getStatusRequests(request));
            log.debug("TF status response={}", response);
            val statusMap = Futures.getUnchecked(response)
                    .stream()
                    .peek(modelStatusResponse ->
                        log.debug("TF status response model_status_response={}, model_name={}, tfsCluster={}",
                            modelStatusResponse,
                            model.getModelName(),
                            tfsClusterName
                            )
                    )
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            entry -> entry._1,
                            entry -> entry._2
                    ));
            log.debug("TF status map={}", statusMap);
            double successRatio = statusMap.values().stream()
                    .map(TensorflowUtils::returnStatusOrUnknown)
                    .mapToInt(TensorflowUtils::queryStatuses)
                    .average()
                    .orElse(0.0);
            log.debug("TFS model status success_ratio={}", successRatio);
            addMetricForModelStatus(model, successRatio);
            return convertToModelStatus(successRatio, model, statusMap);

        } catch (Exception e) {
            log.error("Error getting TensorFlow serving status for model={}, tfsCluster={}", model.getModelName(), tfsClusterName, e);
            return ModelStatus.UNAVAILABLE;
        }
    }

    public TensorFlowModelMetadataDTO getTensorFlowMetadata(ModelDTO model) {
        val request = createModelMetadataRequest(model);

        val modelSpec = predictionClient
                .withDeadlineAfter(tfsClusterConfig.getMetadataTimeoutSeconds(), TimeUnit.SECONDS)
                .getModelMetadata(request)
                .getModelSpec();

        return TensorFlowModelMetadataDTO.builder()
                .modelName(modelSpec.getName())
                .modelVersion(modelSpec.getVersion().getValue())
                .build();

    }

    public java.util.List<ListenableFuture<Tuple2<EurekaHostAndPort, GetModelStatus.GetModelStatusResponse>>> getStatusRequests(
            GetModelStatus.GetModelStatusRequest request) {

        try {
            rwLock.readLock().lock();
            log.debug("GetModelStatusRequest fanout clients size={}, request={}, tfsCluster={}", fanOutClients.size(), request, tfsClusterName);
            return fanOutClients.entrySet()
                    .stream()
                    .map(client -> Futures.transform(client.getValue().getModelStatus(request), status -> Tuple.of(client.getKey(), status),
                            MoreExecutors.directExecutor()))
                    .collect(Collectors.toList());
        } finally {
            rwLock.readLock().unlock();
        }


    }

    private ModelServerConfigOuterClass.ModelServerConfig getConfig(ModelServerConfigOuterClass.ModelConfigList configList) {
        return ModelServerConfigOuterClass.ModelServerConfig.newBuilder()
                .setModelConfigList(configList)
                .build();
    }

    public List<ModelConfigUpdateResponse> updateModelConfigsAllHosts(
            ModelServerConfigOuterClass.ModelConfigList configList
    ) {
        try {
            rwLock.readLock().lock();
            val request = ModelManagement
                    .ReloadConfigRequest.newBuilder()
                    .setConfig(getConfig(configList))
                    .build();

            val futures = fanOutClients.values().stream()
                    .map(client -> client.handleReloadConfigRequest(request)) //todo inject this in new interface
                    .collect(toList());

            return List.ofAll(Futures.getUnchecked(Futures.successfulAsList(futures))
                    .stream()
                    .filter(Objects::nonNull)
                    .map(this::toConfigUpdateResponse)
                    .collect(toList()));
        } finally {
            rwLock.readLock().unlock();
        }
    }

    private ModelConfigUpdateResponse toConfigUpdateResponse(ModelManagement.ReloadConfigResponse response) {
        return ModelConfigUpdateResponse.builder()
                .code(response.getStatus().getErrorCode().toString())
                .message(response.getStatus().getErrorMessage())
                .build();
    }

    public ModelServerConfigOuterClass.ModelConfig createModelConfigFromModelDTO(ModelDTO modelDTO) {
        return ModelServerConfigOuterClass.ModelConfig.newBuilder()
                .setBasePath(modelDTO.getServingLocation())
                .setModelVersionPolicy(FileSystemStoragePathSource.FileSystemStoragePathSourceConfig.ServableVersionPolicy.newBuilder()
                        .setSpecific(FileSystemStoragePathSource.FileSystemStoragePathSourceConfig.ServableVersionPolicy.Specific.newBuilder()
                                .addVersions(modelDTO.getModelMinorVersion())
                                .build())
                        .build())
                .setName(modelDTO.getModelName())
                .setModelPlatform(tensorFlowModelConfiguration.getModelPlatform())
                .build();
    }

    private io.vavr.collection.List<ModelDTO> getTensorFlowModelsForCurrentClusterWithStatusIn(ModelStatus... statuses) {
        return Stream.of(statuses)
                .map(ModelStatus::getName)
                .map(modelService::getAllModelsWithStatus)
                .flatMap(io.vavr.collection.List::toStream)
                .filter(this::filterByCurrentClusterOrFallbackToAll)
                .filter(model -> TENSOR_FLOW.isEqualTo(model.getModelType()))
                .collect(io.vavr.collection.List.collector());
    }

    private Boolean filterByCurrentClusterOrFallbackToAll(ModelDTO modelDTO) {
            if (flowConfig == null) {
                return false;
            }
            final String tfsClusterNameFromModel = flowConfig.getTfsClusterNameForModel(modelDTO);
            return tfsClusterNameFromModel != null && tfsClusterNameFromModel.equals(tfsClusterName);
    }
}

package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import tensorflow.serving.Model;

public interface ExternalModelInference {
    ModelInferenceResult doInference(ModelInferenceInput modelInferenceInput,
                                     Model.ModelSpec modelSpec,
                                     String modelName,
                                     ModelDTO modelDto);
}

package com.grubhub.garcon.modelinteraction.vectorsimilaritymodel;

import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import io.vavr.collection.Map;
import lombok.Data;

@Data
public class CosineSimilarityModelConfig {

    public static final float COSINE_SIMILARITY_MIN_VALUE = -1f;
    public static final float COSINE_TO_ANGULAR_NORMALIZED_MIN_VALUE = 0f;

    private static final double CUTOFF_DEFAULT_VALUE_WITHOUT_NORMALIZATION = -1;
    private static final double CUTOFF_DEFAULT_VALUE_WITH_NORMALIZATION = 0;

    private static final double MULTIPLIER_DEFAULT_VALUE = 1;

    private String[] targetKeys;
    private double multiplier;
    private double cutOff;
    private boolean normalizationEnabled;

    public CosineSimilarityModelConfig(Map<String, Object> processedFeature) {
        this.targetKeys = defineTargetKeys(processedFeature);
        this.multiplier = defineMultiplier(processedFeature);
        this.normalizationEnabled = defineNormalizationEnabled(processedFeature);
        this.cutOff = defineCutOff(processedFeature);
    }

    private String[] defineTargetKeys(Map<String, Object> processedFeature) {
        return String.valueOf(processedFeature.getOrElse("config_target_keys", "")).split(",");
    }

    private Double defineMultiplier(Map<String, Object> processedFeature) {
        return ControlPlaneUtils.parseDouble(processedFeature.getOrElse("config_multiplier", MULTIPLIER_DEFAULT_VALUE), MULTIPLIER_DEFAULT_VALUE);
    }

    private boolean defineNormalizationEnabled(Map<String, Object> processedFeature) {
        return ControlPlaneUtils.parseBoolean(processedFeature.getOrElse("normalization_enabled", true), true);
    }

    private Double defineCutOff(Map<String, Object> processedFeature) {
        if (defineNormalizationEnabled(processedFeature)) {
            final Object cutOffObject = processedFeature.getOrElse("config_cut_off", CUTOFF_DEFAULT_VALUE_WITH_NORMALIZATION);
            return ControlPlaneUtils.parseDouble(cutOffObject, CUTOFF_DEFAULT_VALUE_WITH_NORMALIZATION);
        }

        final Object cutOffObject = processedFeature.getOrElse("config_cut_off", CUTOFF_DEFAULT_VALUE_WITHOUT_NORMALIZATION);
        return ControlPlaneUtils.parseDouble(cutOffObject, CUTOFF_DEFAULT_VALUE_WITHOUT_NORMALIZATION);
    }

    public float getSimilarityMinValue() {
        if (normalizationEnabled) {
            return COSINE_TO_ANGULAR_NORMALIZED_MIN_VALUE;
        }

        return COSINE_SIMILARITY_MIN_VALUE;
    }

}

package com.grubhub.garcon.modelinteraction;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.ddml.random.RandomUtil;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Random;

/**
 * Model sort inputs with random order.
 * It returns a list of scores following the original sequence of elements.
 * Scores are returned in descending order (largest goes to index 0)
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class RandomSortModel implements JavaModel {

    public static final String CONFIG_FIXED_POSITION = "config_fixed_position";
    public static final int DEFAULT_FIXED_POSITION = -1;

    private final JavaDateTimeHelper dateTimeHelper;

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        // Get random seed
        val seed = getSeed(input.getProcessedFeatures().head());
        val random = new Random(seed);

        // Shuffle rows
        val rows = input.getProcessedFeatures();
        val indices = getIndices(rows.size());
        Collections.shuffle(indices, random);

        // Respect fixed positions (if any)
        applyFixedPositions(rows, indices);

        // Return scores for original input
        List<Float> output = generateOutput(indices);
        return ModelInferenceResult.from(input, output);
    }

    private List<Float> generateOutput(java.util.List<Integer> indices) {
        float[] output = new float[indices.size()];
        for (int i = 0; i < indices.size(); i++) {
            output[indices.get(i)] = indices.size() - i;
        }
        return List.ofAll(output);
    }

    private void applyFixedPositions(List<Map<String, Object>> rows, java.util.List<Integer> indices) {
        val fixedPositions = rows.zipWithIndex().map(t -> FixedPosition.from(t._1, t._2))
                .filter(FixedPosition::nonEmpty);
        if (fixedPositions.nonEmpty()) {
            for (FixedPosition pos : fixedPositions) {
                int currentIdx = indices.indexOf(pos.originalIdx);
                swap(currentIdx, pos.requiredIdx, indices);
            }
        }
    }

    private long getSeed(Map<String, Object> config) {
        // Get config
        val seedMode = RandomUtil.getSeedMode(config);

        long customSeedValue = RandomUtil.getCustomSeedValue(config, seedMode);

        long seed = RandomUtil.getSeed(seedMode, customSeedValue, dateTimeHelper);

        log.debug("RandomSort with seed={}, seedMode={}, config={}", seed, seedMode, config);
        return seed;
    }

    private java.util.List<Integer> getIndices(int size) {
        java.util.List<Integer> indices = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            indices.add(i);
        }
        return indices;
    }

    private void swap(int i, int j, java.util.List<Integer> list) {
        if (i != j) {
            val tmp = list.get(j);
            list.set(j, list.get(i));
            list.set(i, tmp);
        }
    }

    private ZonedDateTime getCurrentDateTime() {
        return dateTimeHelper.atZone(ZoneOffset.UTC);
    }


    private static class FixedPosition {

        public static FixedPosition from(Map<String, Object> row, int idx) {
            return new FixedPosition(
                    idx,
                    ControlPlaneUtils.parseDouble(row.getOrElse(CONFIG_FIXED_POSITION, DEFAULT_FIXED_POSITION), DEFAULT_FIXED_POSITION).intValue()
            );
        }

        public FixedPosition(int originalIdx, int requiredIdx) {
            this.originalIdx = originalIdx;
            this.requiredIdx = requiredIdx;
        }

        public final int originalIdx;
        public final int requiredIdx;

        public boolean nonEmpty() {
            return requiredIdx >= 0;
        }

        public Tuple2<Integer, Integer> toTuple() {
            return Tuple.of(this.originalIdx, this.requiredIdx);
        }

        @Override
        public String toString() {
            return "FixedPosition{" +
                    "originalIdx=" + originalIdx +
                    ", requiredIdx=" + requiredIdx +
                    '}';
        }
    }
}

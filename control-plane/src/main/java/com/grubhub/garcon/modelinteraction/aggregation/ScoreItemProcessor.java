package com.grubhub.garcon.modelinteraction.aggregation;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import io.vavr.collection.List;
import lombok.val;

import static com.grubhub.garcon.modelinteraction.ScoreAggregationModel.CONFIG_AGG_FUNCTION;
import static com.grubhub.garcon.modelinteraction.ScoreAggregationModel.TOP_N_ELEMENTS_FOR_AVG;

public class ScoreItemProcessor {

    private static final String DEFAULT_VALUE = Operation.MAX.name();

    private final ModelInferenceInput modelInferenceInput;
    private final ScoreAggregationConfig scoreAggregationConfig;

    public ScoreItemProcessor(ModelInferenceInput modelInferenceInput, ScoreAggregationConfig scoreAggregationConfig) {
        this.modelInferenceInput = modelInferenceInput;
        this.scoreAggregationConfig = scoreAggregationConfig;
    }

    public ScoreAggregator processItems() {
        val processedItems = getProcessedItems();
        val operation =  Operation.valueOf(String.valueOf(modelInferenceInput.getProcessedFeatures().head().getOrElse(CONFIG_AGG_FUNCTION, DEFAULT_VALUE)));
        val topNElements = Integer.valueOf(String.valueOf(modelInferenceInput.getProcessedFeatures().head().getOrElse(TOP_N_ELEMENTS_FOR_AVG, 0)));

        return new ScoreAggregator(processedItems, operation, topNElements);
    }

    private List<List<Item>> getProcessedItems() {
        return modelInferenceInput.getProcessedFeatures()
                .map(processedFeatures -> ScoreAggregationData.of(scoreAggregationConfig, processedFeatures))
                .map(ScoreAggregationData::extractItemsFromFeatures);
    }

}

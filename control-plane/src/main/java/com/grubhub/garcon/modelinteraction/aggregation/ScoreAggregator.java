package com.grubhub.garcon.modelinteraction.aggregation;

import io.vavr.collection.List;

import java.util.function.Function;

/**
 * ScoreAggregator is a class that manages how the scores are aggregated for the SCORE_AGGREGATION model.
 * The method getAggregationFunction decides which function is being applied. The function has to have the following features:
 * - It will return score 0 (zero) when the list doesn't have elements. This is useful to maintain the order and number of the elements
 * - It's performed in a map-reduce fashion. First, some transformation will be done to each of the elements and then it will return a single value.
 */
public class ScoreAggregator {
    private final List<List<Item>> items;
    private final Operation operation;
    private Integer topNElements;

    public ScoreAggregator(List<List<Item>> items,
                           Operation operation,
                           Integer topNElements) {
        this.items = items;
        this.operation = operation;
        this.topNElements = topNElements;
    }

    public List<Float> aggregate() {
        return items
                .map(getAggregationFunction())
                .map(Number::floatValue)
                .collect(List.collector());
    }

    private Function<List<Item>, Double> getAggregationFunction() {
        switch (operation) {
            case MAX:
                return this::maxAggregation;
            case MIN:
                return this::minAggregation;
            case AVG:
                return this::avgAggregation;
            case WEIGHTED_AVG:
                return this::weightedAvgAggregation;
            case TOP_N_AVG:
                return this::topNAvgAggregation;
            default:
                throw new RuntimeException(String.format("Unknown operation type operation_type=%s", operation));
        }
    }

    private Double maxAggregation(List<Item> itemList) {
        return itemList.map(Item::getScore).max().getOrElse(0.0);
    }

    private Double minAggregation(List<Item> itemList) {
        return itemList.map(Item::getScore).min().getOrElse(0.0);
    }

    private Double avgAggregation(List<Item> itemList) {
        return itemList.map(Item::getScore).average().getOrElse(0.0);
    }

    private Double weightedAvgAggregation(List<Item> itemList) {
        return itemList.map(Item::scoreTimesWeight).sum().doubleValue();
    }

    private Double topNAvgAggregation(List<Item> itemList) {
        if (topNElements == 0) {
            // Behave like a normal avg.
            topNElements = itemList.size();
        }

        return itemList.sortBy(Item::getScore).reverse()
                .take(topNElements)
                .map(Item::getScore)
                .average().getOrElse(0.0);
    }

}

package com.grubhub.garcon.modelinteraction.searchembeddings;

import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.QueryFilters;
import com.grubhub.garcon.controlplane.util.InputFeaturesUtils;
import io.vavr.collection.Map;
import lombok.Data;

import static com.grubhub.garcon.modelinteraction.searchembeddings.SearchEmbeddingsRequest.SE_ENTITY_MAPPING_ENABLED;
import static com.grubhub.garcon.controlplane.util.ControlPlaneUtils.parseBooleanFlag;

@Data
public class QueryFiltersProvider {
    private static final String SE_FILTERING_ENABLED = "se_filtering_enabled";
    private static final String DINER_GEOHASH_4 = "diner_geohash_4";
    private static final String DINER_GEOHASH_5 = "diner_geohash_5";
    private static final String DINER_GEOHASH_6 = "diner_geohash_6";
    private static final String DINER_LATITUDE = "diner_latitude";
    private static final String DINER_LONGITUDE = "diner_longitude";

    private boolean seFilteringEnabled;
    private String dinerGeohash4;
    private String dinerGeohash5;
    private String dinerGeohash6;
    private float dinerLatitude;
    private float dinerLongitude;
    private boolean seEntityMappingEnabled;

    public QueryFiltersProvider(Map<String, Object> inputFeatures, String modelName) {
        if (inputFeatures.containsKey(SE_FILTERING_ENABLED)) {
            this.seFilteringEnabled = inputFeatures.get(SE_FILTERING_ENABLED)
                    .map(flag -> parseBooleanFlag(flag, "Cannot read seFilteringEnabled=%s"))
                    .getOrElseThrow(() -> new RuntimeException(
                                    String.format(
                                            "Search embeddings flag is not present in the input_features=%s for model, model_name=%s",
                                            inputFeatures,
                                            modelName
                                    )
                            )
                    );
        }

        if (inputFeatures.containsKey(DINER_GEOHASH_4)) {
            this.dinerGeohash4 = inputFeatures.get(DINER_GEOHASH_4)
                    .map(String.class::cast)
                    .getOrElseThrow(() -> new RuntimeException(
                                    String.format("Diner geohash 4 is not present in the input_features=%s for model, model_name=%s", inputFeatures, modelName)
                            )
                    );
        }

        if (inputFeatures.containsKey(DINER_GEOHASH_5)) {
            this.dinerGeohash5 = inputFeatures.get(DINER_GEOHASH_5)
                    .map(String.class::cast)
                    .getOrElseThrow(() -> new RuntimeException(
                                    String.format("Diner geohash 5 is not present in the input_features=%s for model, model_name=%s", inputFeatures, modelName)
                            )
                    );
        }

        if (inputFeatures.containsKey(DINER_GEOHASH_6)) {
            this.dinerGeohash6 = inputFeatures.get(DINER_GEOHASH_6)
                    .map(String.class::cast)
                    .getOrElseThrow(() -> new RuntimeException(
                                    String.format("Diner geohash 6 is not present in the input_features=%s for model, model_name=%s", inputFeatures, modelName)
                            )
                    );
        }


        if (inputFeatures.containsKey(DINER_LATITUDE)) {
            this.dinerLatitude = inputFeatures.get(DINER_LATITUDE)
                    .map(lat -> InputFeaturesUtils.readFloatValueOrException(inputFeatures, modelName, lat, 0f, DINER_LATITUDE))
                    .getOrElseThrow(() -> new RuntimeException(
                                    String.format("Diner latitude is not present in the input_features=%s for model, model_name=%s", inputFeatures, modelName)
                            )
                    );
        }

        if (inputFeatures.containsKey(DINER_LONGITUDE)) {
            this.dinerLongitude = inputFeatures.get(DINER_LONGITUDE)
                    .map(lng -> InputFeaturesUtils.readFloatValueOrException(inputFeatures, modelName, lng, 0f, DINER_LONGITUDE))
                    .getOrElseThrow(() -> new RuntimeException(
                                    String.format("Diner longitude is not present in the input_features=%s for model, model_name=%s", inputFeatures, modelName)
                            )
                    );
        }

        if (inputFeatures.containsKey(SE_ENTITY_MAPPING_ENABLED)) {
            this.seEntityMappingEnabled = inputFeatures.get(SE_ENTITY_MAPPING_ENABLED)
                    .map(flag -> parseBooleanFlag(flag, "Cannot read seMappingEntityEnabled=%s"))
                    .getOrElseThrow(() -> new RuntimeException(
                                    String.format(
                                            "Search embeddings flag se_entity_mapping_enabled is not present in the input_features=%s for model, model_name=%s",
                                            inputFeatures,
                                            modelName
                                    )
                            )
                    );
        }
    }

    public QueryFilters get() {
        return QueryFilters.builder()
                .enabled(seFilteringEnabled)
                .lat(dinerLatitude)
                .lng(dinerLongitude)
                .geohash4(dinerGeohash4)
                .geohash5(dinerGeohash5)
                .geohash6(dinerGeohash6)
                .mappingEnabled(seEntityMappingEnabled)
                .build();

    }
}

package com.grubhub.garcon.modelinteraction;

import ch.qos.logback.classic.Logger;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import org.slf4j.LoggerFactory;

public interface ModelWithDefaultOutput {

    Map<String, List<ModelInferenceOutputType>> generateDefaultOutput(ModelInferenceInput input);

    default ModelInferenceResult defaultOutput(ModelInferenceInput input) {
        Map<String, List<ModelInferenceOutputType>> output = generateDefaultOutput(input);
        Logger logger = (Logger) LoggerFactory.getLogger(getClass());
        logger.debug("Using default model output. input={}, output={}", input, output);
        return ModelInferenceResult.builder()
                .output(output)
                .isDefaultOutput(true)
                .build();
    }
}


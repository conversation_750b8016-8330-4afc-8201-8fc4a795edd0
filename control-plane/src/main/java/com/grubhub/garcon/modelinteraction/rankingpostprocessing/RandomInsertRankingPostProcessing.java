package com.grubhub.garcon.modelinteraction.rankingpostprocessing;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.random.JDKRandomGenerator;
import org.apache.commons.math3.random.RandomGenerator;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;


@Slf4j
public class RandomInsertRankingPostProcessing implements RankingPostProcessing {
    private final String seed;
    private final List<String> targetEntityIds;
    private final RandomGenerator randomGenerator;
    private final int maxRankingSlots;

    public RandomInsertRankingPostProcessing(String seed, List<String> targetEntityId, int maxRankingSlots) {
        this.seed = seed;
        this.targetEntityIds = targetEntityId;
        this.randomGenerator = createRandomGenerator();
        this.maxRankingSlots = maxRankingSlots;

    }

    private RandomGenerator createRandomGenerator() {
        final RandomGenerator randomGenerator = new JDKRandomGenerator();
        randomGenerator.setSeed(seed.hashCode());
        return randomGenerator;
    }

    @Override
    public List<String> process(List<String> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("Empty entities list provided. Returning one element result.");
            return Optional.ofNullable(targetEntityIds).orElseGet(Collections::emptyList);
        }

        // Check if targetEntityId already exists in the list
        if (!checkThatAllExists(entities)) {
            log.warn("One or more elements from the target entity ids are missing from the entities provided. Doing nothing.");
            return entities;
        }

        return pinEntities(entities);

    }

    private boolean checkThatAllExists(List<String> entities) {
        return new HashSet<>(entities).containsAll(targetEntityIds);
    }

    private List<String> pinEntities(List<String> entities) {
        List<String> result = new ArrayList<>(entities);

        result.removeAll(targetEntityIds);

        int maxPossibleRankingPos = Math.min(entities.size(), maxRankingSlots);
        int adjustedMaxRankingPos = Math.max(0, maxPossibleRankingPos - targetEntityIds.size()) <= 0 ? 1 : maxPossibleRankingPos - targetEntityIds.size();

        targetEntityIds.forEach(pinEntityId -> {
            int randomPosition = randomGenerator.nextInt(adjustedMaxRankingPos);
            log.debug("Pinning entity {} to position {}", pinEntityId, randomPosition);
            result.add(randomPosition, pinEntityId);
        });

        return result;
    }
}

package com.grubhub.garcon.modelinteraction;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.function.BiFunction;

/**
 * When updating model types here, also update ModelType.java
 */
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum ModelInferenceType {

    TENSOR_FLOW(ModelInferenceClient::inferWithTensorFlow),
    FUNCTION(ModelInferenceClient::inferWithNativeFunction),
    QUERY_EXPANSION_LLM(ModelInferenceClient::inferQueryExpansions),
    SORT_ASC((client, input) -> client.inferWithSort(input, true, Constants.SORT_MULTIPLIER)),
    SORT_DESC((client, input) -> client.inferWithSort(input, false, Constants.SORT_MULTIPLIER)),
    DISTANCE_FALLOFF(ModelInferenceClient::inferWithDistanceFalloff),
    LINEAR_COMBINATION(ModelInferenceClient::inferWithLinearCombination),
    FEATURES_FETCH(ModelInferenceClient::inferWithFeaturesFetch),
    RANDOM_SORT(ModelInferenceClient::inferWithRandomSort),
    SEARCH_EMBEDDINGS(ModelInferenceClient::inferWithSearchEmbedding),
    TEXT_SIMILARITY(ModelInferenceClient::inferWithTextSimilarity),
    VECTOR_COSINE_SIMILARITY(ModelInferenceClient::inferVectorCosineSimilarity),
    RANDOM_SAMPLE(ModelInferenceClient::inferWithRandomSample),
    RANDOM_DISTRIBUTION(ModelInferenceClient::inferWithRandomDistribution),
    RANKING_POST_PROCESSING(ModelInferenceClient::inferWithRankingPostProcessing),
    SCORE_AGGREGATION(ModelInferenceClient::inferWithScoreAggregation),
    RULE_MATCHING(ModelInferenceClient::inferWithRuleMatching),
    PRECOMPUTED_RANKER(ModelInferenceClient::inferWithPrecomputedRanker),
    SAGE_MAKER(ModelInferenceClient::inferWithSageMaker);

    private final BiFunction<ModelInferenceClient, ModelInferenceInput, ModelInferenceResult> modelInferenceAlgorithm;

    private static class Constants {
        public static final int SORT_MULTIPLIER = 10;
    }

    public boolean isEqualTo(String modelType) {
        return ModelInferenceType.valueOf(modelType) == this;
    }

    public boolean isEqualTo(ModelDTO model) {
        return ModelInferenceType.valueOf(model.getModelType()) == this;
    }

    public static ModelInferenceType valueOf(ModelDTO model) {
        return valueOf(model.getModelType());
    }

    public ModelType toModelType() {
        return ModelType.valueOf(this.name());
    }

    public static ModelInferenceType valueOf(ModelType modelType) {
        return ModelInferenceType.valueOf(modelType.name());
    }
}

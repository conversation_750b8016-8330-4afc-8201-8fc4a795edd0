package com.grubhub.garcon.modelinteraction.tensorflow;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.inject.Singleton;
import com.grubhub.garcon.grpc.GrpcResolveType;
import io.vavr.control.Option;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import static io.vavr.Predicates.not;
//import static com.grubhub.garcon.search.restaurants.models.SearchRestaurantConstants.UNBIASED_DISTANCE_SORT_ID;

/**
 * POJO to hold all model configs that can be configured via FIG.
 *
 * Interpreting of this config should be handled through ModelConfigManager
 */
@Singleton
@Data
@NoArgsConstructor
public class TensorFlowModelConfiguration {
    private String bucket = "/opt/grubhub/apps/garcon/conf/ddmltensorflow";
    private String tfPrefixPath = "modelserving";
    private String modelPlatform = "tensorflow";

    private String grpcResolveType = "EUREKA";
    private Boolean validateServingLocationBucket = false;
    private String servingLocationBucket = "vnwre0qnbk1qy9zw-ddml-models";

    private Integer maxDynamicModelsPerVersioning;
    private Integer maxDynamicEnsemblesPerVersioning;

    // for STATIC resolving (primarily for local testing)
    // format is <address1,address2...>, ex: 127.0.0.1,localhost

    private Boolean checkForStaleEnsembles = false;
    private Boolean useModelAwareTargeting = false;
    private Boolean refreshModelAwareTargeting = false;
    private Boolean dynamicModelsAliasSwapEnabled = true;
    private Map<String, TfsClusterConfig> tfsClusterConfigs;
    private Float searchEmbeddingsInstancesActiveRatio = 0.9f;
    private Integer configureModelsInvocationsNumber = 0;
    private String modelRegions = "";
    private Boolean externalInferenceFeatureBatchEnabled = true;
    private Integer externalInferenceFeatureBatchSize = 200;

    public GrpcResolveType getGrpcResolveType() {
        return GrpcResolveType.valueOf(grpcResolveType);
    }

    public boolean grpcResolveTypeIsEqualToEureka() {
        return GrpcResolveType.valueOf(grpcResolveType) == GrpcResolveType.EUREKA;
    }

    public Stream<String> getModelRegionsAsStream() {
        return Option.of(modelRegions)
                .filter(not(String::isEmpty))
                .map(Pattern.compile("\\,")::splitAsStream)
                .getOrElse(Stream.empty());
    }

    @JsonIgnore
    public boolean modelTestInvocationEnabled() {
        return Option.of(configureModelsInvocationsNumber)
                .map(num -> num != 0)
                .getOrElse(false);
    }
}

package com.grubhub.garcon.modelinteraction.precomputedranker;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.modelinteraction.JavaModel;
import com.grubhub.garcon.modelinteraction.ModelWithDefaultOutput;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Model that preprocesses scores, depending on the provided mode.
 * Required inputs:
 * - mode: the mode determine the type of processing to be done
 * <p>
 * Optional inputs:
 * - top_n: the number of top scores to return
 * <p>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class PrecomputedRankerModel implements JavaModel, ModelWithDefaultOutput {

    public static final String SCORE = "score";
    public static final String MODE = "mode";
    public static final String TOP_N = "top_n";
    public static final int DEFAULT_TOP_N = 3;

    private final TopNRanker topNRanker;

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        List<Map<String, Object>> rows = input.getProcessedFeatures();
        if (rows.isEmpty()) {
            log.warn("Processed features list is empty for PrecomputedRankerModel, returning default output.");
            return defaultOutput(input);
        }

        Map<String, Object> firstFeatureRow = rows.head();

        if (log.isDebugEnabled()) {
            log.debug("All entries for precomputed ranking (configuration)={}", rows);
        }

        String precomputedModeString = String.valueOf(firstFeatureRow.getOrElse(MODE, PrecomputedMode.DEFAULT.name())).trim();
        PrecomputedMode precomputedMode = PrecomputedMode.valueOf(precomputedModeString);

        try {
            if (precomputedMode == PrecomputedMode.SCORE_TOP_N) {
                String topNString = String.valueOf(firstFeatureRow.getOrElse(TOP_N, DEFAULT_TOP_N)).trim();
                int topN = ControlPlaneUtils.parseInteger(topNString, DEFAULT_TOP_N);
                return topNRanker.processScores(input, rows, topN);
            }

            return returnScores(input, rows);
        } catch (Exception e) {
            log.error("Failed to create or execute the processor", e);
            return defaultOutput(input);
        }
    }

    private ModelInferenceResult returnScores(ModelInferenceInput input, List<Map<String, Object>> rows) {
        List<Float> results = rows.map(row -> (Double) row.get(SCORE).getOrElse(0.0))
                .map(Double::floatValue);
        return ModelInferenceResult.from(input, results);
    }

    @Override
    public Map<String, List<ModelInferenceOutputType>> generateDefaultOutput(ModelInferenceInput input) {
        List<Float> output = input.getProcessedFeatures().map(i -> 0F);
        return ModelInferenceOutput.buildResponseMap(input, output);
    }

}

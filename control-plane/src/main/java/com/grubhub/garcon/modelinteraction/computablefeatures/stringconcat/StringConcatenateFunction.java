package com.grubhub.garcon.modelinteraction.computablefeatures.stringconcat;

import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import io.vavr.collection.Map;
import lombok.val;

/**
 * This function concatenates the two input strings with the separator between them.
 * Inputs: map with key: str1, str2, sep.
 * Outputs: map with key: str.
 */
public class StringConcatenateFunction extends StringConcatenateBaseFunction {

    public StringConcatenateFunction() {
        super(FunctionFeatureType.STR_CONCAT);
    }

    public Map<String, Object> compute(Map<String, Object> inputs) {
        val outputStr = concatenate(
                2,
                getString(inputs, INPUT_SEPARATOR),
                getString(inputs, INPUT_STRING_ONE),
                getString(inputs, INPUT_STRING_TWO)
        );

        return createOutput(OUTPUT_STRING, outputStr);
    }
}

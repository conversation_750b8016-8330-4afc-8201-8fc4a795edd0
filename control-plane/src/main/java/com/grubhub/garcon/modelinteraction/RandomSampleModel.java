package com.grubhub.garcon.modelinteraction;

import com.google.inject.Inject;

import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.ddml.random.RandomPicker;
import com.grubhub.garcon.ddml.random.RandomUtil;
import com.grubhub.garcon.ddml.random.SamplingMode;
import com.grubhub.garcon.ddml.random.SeedMode;

import io.vavr.collection.HashMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import io.vavr.collection.List;
import io.vavr.collection.Map;

/**
 * This model accepts a list of lists and returns a list
 * with one or more entries at random per list.
 */
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class RandomSampleModel implements JavaModel {

    public static final String CONFIG_TOTAL_SAMPLE = "config_total_sample";
    public static final String CONFIG_SAMPLING_MODE = "config_sampling_mode";
    public static final String INPUT_LIST = "input_list";
    public static final String INPUT_PROBABILITY = "input_prob";
    private final RandomPicker randomPicker;

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        List<ModelInferenceOutputType> outputList = ModelInferenceOutputType.ofStringArrayList(process(input));
        String outputName = input.getModel().getModelOutputs().head().getOutputName();
        Map<String, List<ModelInferenceOutputType>> output = HashMap.of(outputName, outputList);
        return ModelInferenceResult.builder()
                .output(output)
                .build();
    }

    private List<List<String>> process(ModelInferenceInput input) {

        val rows = input.getProcessedFeatures();
        val firstSample = rows.head();

        //Get global config values, injected in each feature.
        val samplingMode = getSamplingMode(firstSample);
        int sampleTotal = getSampleTotal(firstSample);
        val seedMode = RandomUtil.getSeedMode(firstSample);
        long customSeedValue = RandomUtil.getCustomSeedValue(firstSample, seedMode);

       if (samplingMode == SamplingMode.NON_UNIFORM && sampleTotal == 1) {
            return rows.map(row -> generateWeightedOutput(row, seedMode, customSeedValue));
        }

        return rows.map(row -> generateRandomOutput(row, sampleTotal, seedMode, customSeedValue));
    }

    private List<String> generateRandomOutput(Map<String, Object> input, int sampleTotal, SeedMode seedMode, long customSeedValue) {
        List<String> inputList = getInputList(input);

        if (inputList.isEmpty()) {
            return inputList;
        }

        val randomSample = randomPicker.pick(inputList.toJavaList(), sampleTotal, seedMode, customSeedValue);
        return ControlPlaneUtils.toVavrListString(randomSample);
    }

    private List<String> generateWeightedOutput(Map<String, Object> input, SeedMode seedMode, long customSeedValue) {
        List<String> inputList = getInputList(input);

        if (inputList.isEmpty()) {
            return inputList;
        }

        List<String> output = getWeightedSelection(inputList, getWeightsList(input));

        if (output.isEmpty()) {
            output = generateRandomOutput(input, 1, seedMode, customSeedValue);
        }
        return output;
    }

    private List<String> getWeightedSelection(List<String> inputList, List<Float> weightsList) {
        val randomSample = randomPicker.weightedPick(inputList.toJavaList(), weightsList.toJavaList());
        return ControlPlaneUtils.toVavrListString(randomSample);
    }

    private List<String> getInputList(Map<String, Object> input) {
        Object inputListObj = input.get(INPUT_LIST).getOrElse(List.empty());
        return ControlPlaneUtils.toVavrListString(inputListObj);
    }

    private List<Float> getWeightsList(Map<String, Object> input) {
        Object weightsListObj = input.get(INPUT_PROBABILITY).getOrElse(List.empty());
        return ControlPlaneUtils.toVavrListFloat(weightsListObj);
    }

    private Integer getSampleTotal(Map<String, Object> config) {
        return ControlPlaneUtils.parseInteger(config.getOrElse(CONFIG_TOTAL_SAMPLE, 1), 1);
    }

    public SamplingMode getSamplingMode(Map<String, Object> config) {
        return SamplingMode.valueOf(String.valueOf(config.getOrElse(CONFIG_SAMPLING_MODE, SamplingMode.UNIFORM.name())));
    }
}

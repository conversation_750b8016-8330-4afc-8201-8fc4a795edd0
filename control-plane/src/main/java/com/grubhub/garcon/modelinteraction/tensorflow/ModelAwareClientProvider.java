package com.grubhub.garcon.modelinteraction.tensorflow;

import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.grpc.EurekaHostAndPort;
import io.grpc.ManagedChannel;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.vavr.Tuple2;
import io.vavr.control.Option;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import tensorflow.serving.PredictionServiceGrpc;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Slf4j
@Getter
public class ModelAwareClientProvider {
    private static final String TFS_CLIENT_MODEL_STATUS = "tfsClientModelStatus";
    private final Map<String, List<EurekaHostAndPort>> modelHosts;
    private final Map<EurekaHostAndPort, PredictionServiceGrpc.PredictionServiceBlockingStub> predictionClients;
    private final AtomicInteger index; // So we can start at 0 on nextClient();
    private final MeterRegistry meterRegistry;
    private final ReadWriteLock rwLock;

    public ModelAwareClientProvider(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.modelHosts = new ConcurrentHashMap<>();
        this.predictionClients = new HashMap<>();
        this.index = new AtomicInteger(-1);
        this.rwLock = new ReentrantReadWriteLock();
    }

    public void refreshModelStatus(ModelDTO model, java.util.List<EurekaHostAndPort> availableHosts) {
        try {
            log.debug("Refreshing model={} with addrs={}", model.getModelName(), availableHosts);
            modelHosts.put(model.getModelName(), availableHosts);
            addMetricForTfsHost(model, availableHosts);

        } catch (Exception e) {
            log.warn("Failed to refresh model={}", model.getModelName(), e);
        }
    }

    private void addMetricForTfsHost(ModelDTO model, List<EurekaHostAndPort> availableHosts) {
        Option.of(availableHosts).getOrElse(ArrayList::new).forEach(availableHost ->
            Option.of(availableHost)
                    .filter(host -> model.getModelName() != null)
                    .filter(host -> model.getVersion() != null)
                    .map(EurekaHostAndPort::getHost)
                    .filter(Objects::nonNull)
                    .peek(host ->
                        meterRegistry.counter(Metrics.name(getClass(), TFS_CLIENT_MODEL_STATUS),
                                Tags.of("model_name", model.getModelName(),
                                        "model_version", model.getVersion(),
                                        "tfs_host", host
                                        )
                        )
                        .increment()
                    )
        );
    }

    public void refreshPredictionClients(Map<EurekaHostAndPort, ManagedChannel> channels) {
        try {
            val newClients = getNewClients(channels);
            rwLock.writeLock().lock();
            predictionClients.clear();
            predictionClients.putAll(newClients);
            log.debug("Refreshed={} prediction clients={}", predictionClients.size(), predictionClients.keySet());
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    private Map<EurekaHostAndPort, PredictionServiceGrpc.PredictionServiceBlockingStub> getNewClients(Map<EurekaHostAndPort, ManagedChannel> channels) {
        return io.vavr.collection.HashMap.ofAll(channels)
                .map((node, channel) -> new Tuple2<>(node, PredictionServiceGrpc.newBlockingStub(channel)))
                .toJavaMap();
    }

    public PredictionServiceStubWithHost nextClient(String modelName) {
        try {
            rwLock.readLock().lock();
            val hostsWithModel = modelHosts.getOrDefault(modelName, Collections.emptyList());

            if (hostsWithModel.isEmpty()) {
                meterRegistry.counter("model_aware_discovery_no_hosts", Tags.of("model_name", modelName)).increment();
                log.warn("Model aware discovery failed to resolve a host for model={} because no hosts are available! Fallback to default client.", modelName);
                return null;
            }

            int i = index.incrementAndGet();
            if (i >= predictionClients.size()) {
                int old = i;
                i %= predictionClients.size();
                index.compareAndSet(old, i);
            }

            if (i < hostsWithModel.size()) {
                PredictionServiceGrpc.PredictionServiceBlockingStub predictionService = predictionClients.get(hostsWithModel.get(i));
                if (predictionService != null) {
                    return new PredictionServiceStubWithHost(predictionService, hostsWithModel.get(i));
                }
            }

            // Fallback to a random client
            val hostsCopy = new ArrayList<>(hostsWithModel);
            Collections.shuffle(hostsCopy);
            for (EurekaHostAndPort host : hostsCopy) {
                PredictionServiceGrpc.PredictionServiceBlockingStub predictionService = predictionClients.get(host);
                if (predictionService != null) {
                    return new PredictionServiceStubWithHost(predictionService, host);
                }
            }

            log.error("Model aware discovery failed to resolve a host with model={} in AVAILABLE state. Fallback to default mechanism.", modelName);
            return null;
        } finally {
            rwLock.readLock().unlock();
        }
    }

}

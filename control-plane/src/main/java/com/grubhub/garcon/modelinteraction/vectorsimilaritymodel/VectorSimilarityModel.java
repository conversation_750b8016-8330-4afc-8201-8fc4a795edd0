package com.grubhub.garcon.modelinteraction.vectorsimilaritymodel;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.modelinteraction.JavaModel;
import com.grubhub.garcon.modelinteraction.ModelWithDefaultOutput;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

import static com.grubhub.garcon.modelinteraction.vectorsimilaritymodel.CosineSimilarityCalculator.cosineSimilarity;
import static com.grubhub.garcon.modelinteraction.vectorsimilaritymodel.CosineSimilarityCalculator.cosineToAngularNormalization;
import static com.grubhub.garcon.modelinteraction.vectorsimilaritymodel.CosineSimilarityModelConfig.COSINE_SIMILARITY_MIN_VALUE;

/**
 * Return a set of similarities given:
 * - Input vector: Query vector generated from the user's input.
 * - Feature vectors: Set of vectors representing entities (such as menu items) along with their IDs and a parent entity (such as restaurant).
 * <p>
 * The output of the model is a set of maximum similarities between the query vector and each of the feature vectors.
 * Example in VectorSimilarityModelTest.buildProcessedFeatures.
 * <p>
 * The model will return a similarity assigned to each restaurant. That similarity value corresponds to the maximum similarity between the input vector
 * and any of its menu item vectors. This can be used to rank a restaurant given a particular menu item that is very similar to the user's query.
 * <p>
 * Configuration options:
 * - targetKeys: comma separated keys of each of the feature vectors. Such as: menu_item_1,menu_item_2,menu_item_3.
 * - multiplier: similarity multiplier. final_value = similarity * multiplier.
 * - cutOff: if the similarity (after normalization) is lower than this value, the value returned is the minimum similarity possible.
 * - normalizationEnabled: true if cosine -> angular.
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class VectorSimilarityModel implements JavaModel, ModelWithDefaultOutput {

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        if (log.isDebugEnabled()) {
            // Logging line by line to avoid huge log lines.
            input.getProcessedFeatures().peek(feature -> log.debug("Processed feature={}", feature));
        }

        if (input.getProcessedFeatures() == null || input.getProcessedFeatures().isEmpty()) {
            throw new RuntimeException("Processed feature list is null or empty when calling VectorSimilarityModel.");
        }

        // The configuration properties can be grabbed using the first element in the list.
        final Map<String, Object> firstEntry = input.getProcessedFeatures().head();
        final List<Float> inputVector = getVectorFromRow(firstEntry, "input_vec");
        if (inputVector == null || inputVector.isEmpty()) {
            return defaultOutput(input);
        }
        final CosineSimilarityModelConfig config = new CosineSimilarityModelConfig(firstEntry);

        // Returns a cosine similarity score for each input row.
        List<Float> output = input.getProcessedFeatures()
                .map(row -> getMaxRowSimilarity(inputVector, row, config))
                .map(similarity -> config.isNormalizationEnabled() ? cosineToAngularNormalization(similarity) : similarity)
                .map(similarity -> applyCutOffAndMultiplier(config, similarity));
        return ModelInferenceResult.from(input, output);
    }

    @Override
    public Map<String, List<ModelInferenceOutputType>> generateDefaultOutput(ModelInferenceInput input) {
        List<Float> output = input.getProcessedFeatures().map(i -> 0F);
        return ModelInferenceOutput.buildResponseMap(input, output);
    }

    private Float getMaxRowSimilarity(List<Float> inputVector, Map<String, Object> row, CosineSimilarityModelConfig config) {
        return Arrays.stream(config.getTargetKeys())
                .map(key -> getVectorFromRow(row, key))
                .filter(targetVector -> targetVector != null && !targetVector.isEmpty())
                .map(targetVector -> cosineSimilarity(inputVector, targetVector))
                .max(Float::compare)
                .orElse(COSINE_SIMILARITY_MIN_VALUE);
    }

    private List<Float> getVectorFromRow(Map<String, Object> row, String key) {
        final Object targetVector = row.getOrElse(key, null);
        if (targetVector == null) {
            return List.empty();
        }

        return ControlPlaneUtils.toVavrListFloat(targetVector);
    }

    private float applyCutOffAndMultiplier(CosineSimilarityModelConfig config, Float similarity) {
        return similarity >= config.getCutOff() ? (float) (similarity * config.getMultiplier()) : config.getSimilarityMinValue();
    }

}

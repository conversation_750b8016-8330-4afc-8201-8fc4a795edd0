package com.grubhub.garcon.modelinteraction.retrier;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Random;
import java.util.function.Predicate;
import java.util.function.Supplier;

@Slf4j
@RequiredArgsConstructor
public class ExponentialBackoffStrategy {

    private static final long MAX_BACKOFF = 15_000;
    private static final long DEFAULT_MAX_RETRIES = 10;
    private static final int UPPER_BOUND = 1000;
    private static Random random = new Random();


    public static <T> T retry(Supplier<T> function, Predicate<T> condition) throws RuntimeException {
        int attempts = 0;
        T result = function.get();
        while (attempts < DEFAULT_MAX_RETRIES && !condition.test(result)) {
            try {
                Thread.sleep(getWaitTimeExp(attempts));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Something wrong occur when retrying the request!");
            }
            attempts++;
            result = function.get();

        }
        return result;
    }

    public static long getWaitTimeExp(int attempts) {
        final double pow = Math.pow(2, attempts);
        final int rand = random.nextInt(UPPER_BOUND);
        return (long) Math.min(pow + rand, MAX_BACKOFF);
    }

}

package com.grubhub.garcon.modelinteraction.computablefeatures.textnormalizer;

import com.google.common.collect.ImmutableSet;
import com.grubhub.search.analysis.AnalyzerUtils;
import com.grubhub.search.analysis.EnglishMinimalSingularAnalyzer;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.CharArraySet;
import org.apache.lucene.analysis.LowerCaseFilter;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.Tokenizer;
import org.apache.lucene.analysis.core.StopFilter;
import org.apache.lucene.analysis.core.WhitespaceTokenizer;
import org.apache.lucene.analysis.en.EnglishMinimalStemFilter;
import org.apache.lucene.analysis.en.EnglishPossessiveFilter;
import org.apache.lucene.analysis.en.PorterStemFilter;
import org.apache.lucene.analysis.miscellaneous.SetKeywordMarkerFilter;
import org.apache.lucene.analysis.pattern.PatternReplaceFilter;

import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;

@RequiredArgsConstructor
public class ControlPlaneTextNormalizer extends Analyzer {
    private static final Pattern REPLACE_PATTERN = Pattern.compile("[^\\w\\s]");
    private static final Set<String> STOP_WORDS = ImmutableSet.of(
            "delivery", "deliver", "food", "pickup", "takeout", "carryout", "coupon", "coupons",
            "&", "a", "an", "and", "are", "as", "at", "be", "but", "by", "for", "if", "in",
            "into", "is", "it", "no", "not", "of", "on", "or", "such", "that", "the", "their",
            "then", "there", "these", "they", "this", "to", "was", "will", "with");

    private static final CharArraySet STOP_WORDS_CHAR_ARRAY = new CharArraySet(STOP_WORDS, true);
    private static final Set<String> keywords = ImmutableSet.of("fried", "fries", "ice", "one", "ave");

    private final String inputText;

    // Match with words that end with a vowel followed by 'y'.
    private static final String Y_PROBLEM = "(.*[aeiou]y\\s*.*)*";

    // If a word ends in 'i' and is preceded by a vowel, replace 'i' to 'y'.
    private static final String REPLACE_I = "(?<=[aeiou])(i)(?=\\s|$)";

    // Replace all 'fry' words
    private static final String REPLACE_FRY = "(?<=\\s|^)(fry)(?=\\s|$)";

    private final EnglishMinimalSingularAnalyzer minimalSingularAnalyzer = new EnglishMinimalSingularAnalyzer();

    @Override
    protected TokenStreamComponents createComponents(String fieldName) {
        Tokenizer tokenizer = new WhitespaceTokenizer();
        TokenStream tokenStream = buildPartialTokenStream(tokenizer);
        tokenStream = new PorterStemFilter(tokenStream);

        return new TokenStreamComponents(tokenizer, tokenStream);
    }

    public Optional<String> tokenize() {
        Optional<String> analyzedText;
        try {
            analyzedText = TextAnalyzerUtils.analyzeAndJoin(this.inputText, this);
        } catch (Exception e) {
            return Optional.empty();
        }

        return analyzedText.map(s -> fixYProblem(s, this.inputText))
                //Popular query text we normalize accurately, since lucene class does not.
                .map(s -> s.replaceAll(REPLACE_FRY, "fri"))
                .map(s -> s.trim().replaceAll("\\s+", " "));
    }

    /* The Lucene Porter Stemmer changes the ending y to i, when it is preceded by a vowel.
       Check for its existence in the original text then replace those instances.
    */
    private String fixYProblem(String analyzedText, String original) {
        Optional<String> origin = AnalyzerUtils.analyzeAndJoin(original.trim().toLowerCase(), minimalSingularAnalyzer);

        if (origin.isPresent() && origin.get().matches(Y_PROBLEM)) {
            return analyzedText.replaceAll(REPLACE_I, "y");
        }

        return analyzedText;
    }

    private TokenStream buildPartialTokenStream(Tokenizer tokenizer) {
        TokenStream tokenStream = new LowerCaseFilter(tokenizer);
        tokenStream = new PatternReplaceFilter(tokenStream, REPLACE_PATTERN, StringUtils.EMPTY, true);
        tokenStream = new SetKeywordMarkerFilter(tokenStream, new CharArraySet(keywords, true));
        tokenStream = new StopFilter(tokenStream, STOP_WORDS_CHAR_ARRAY);
        tokenStream = new EnglishPossessiveFilter(tokenStream);
        tokenStream = new EnglishMinimalStemFilter(tokenStream);

        return tokenStream;
    }
}

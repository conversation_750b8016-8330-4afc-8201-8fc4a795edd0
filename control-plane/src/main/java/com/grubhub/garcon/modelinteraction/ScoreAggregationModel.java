package com.grubhub.garcon.modelinteraction;


import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.modelinteraction.aggregation.ScoreAggregationInput;
import io.vavr.collection.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Model that performs an aggregation function on each row.
 * It returns a single score for each given row, which is the output of the aggregation function.
 *
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class ScoreAggregationModel implements JavaModel {

    public static final String CONFIG_AGG_FUNCTION = "config_agg_function";
    public static final String CONFIG_ITEM_IDS = "config_item_ids";
    public static final String CONFIG_ITEM_SCORES = "config_item_scores";
    public static final String ITEMS = "items";
    public static final String ITEM_WEIGHTS = "item_weights";
    public static final String TOP_N_ELEMENTS_FOR_AVG = "top_n_elements_for_avg";

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        log.debug("Score aggregation model input={}", input);

        List<Float> output = ScoreAggregationInput.of(input)
                .buildItemById()
                .processItems()
                .aggregate();
        return ModelInferenceResult.from(input, output);
    }
}

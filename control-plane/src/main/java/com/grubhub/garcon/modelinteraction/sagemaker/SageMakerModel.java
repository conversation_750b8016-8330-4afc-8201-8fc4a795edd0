package com.grubhub.garcon.modelinteraction.sagemaker;

import com.google.inject.Singleton;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.modelinteraction.JavaModel;
import io.vavr.collection.List;
import lombok.AllArgsConstructor;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointRequest;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

import javax.inject.Inject;
import java.nio.charset.StandardCharsets;

@Singleton
@AllArgsConstructor(onConstructor = @__(@Inject))
public class SageMakerModel implements JavaModel {

    private final SageMakerRuntimeClient sageMakerClient;

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {

        String endpointName = "your-endpoint-name";
        String contentType = "application/json"; // or the content type your model expects

        InvokeEndpointRequest request = InvokeEndpointRequest.builder()
                .endpointName(endpointName)
                .contentType(contentType)
                .body(SdkBytes.fromString(
                        input.toString(),     // TODO(more)
                        StandardCharsets.UTF_8))
                .build();

        InvokeEndpointResponse response = sageMakerClient.invokeEndpoint(request);

        SdkBytes body = response.body();
        List<Float> results = List.of(1.0f, 1.5f, 3.0f);        // TODO(bogus)

        return ModelInferenceResult.from(input, results);

    }
}

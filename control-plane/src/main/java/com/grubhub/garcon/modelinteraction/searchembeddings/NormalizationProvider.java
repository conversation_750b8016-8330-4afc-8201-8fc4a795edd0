package com.grubhub.garcon.modelinteraction.searchembeddings;

import com.grubhub.garcon.searchembeddings.rpc.resources.api.models.Normalization;
import com.grubhub.garcon.controlplane.util.InputFeaturesUtils;
import io.vavr.collection.Map;

public class NormalizationProvider {

    private static final String SE_NORMALIZATION_METHOD = "se_normalization_method";
    private static final String SE_MIN_SCORE_THRESHOLD = "se_min_score_threshold";

    private String normalizationMethod;
    private Float minScoreThreshold;

    public NormalizationProvider(Map<String, Object> inputFeatures, String modelName) {
        if (inputFeatures.containsKey(SE_NORMALIZATION_METHOD)) {
            this.normalizationMethod = inputFeatures.get(SE_NORMALIZATION_METHOD)
                    .map(String.class::cast)
                    .getOrElseThrow(() -> new RuntimeException(String.format("Unable to read %s from input_features=%s for model, model_name=%s",
                            SE_NORMALIZATION_METHOD, inputFeatures, modelName)));
        }

        if (inputFeatures.containsKey(SE_MIN_SCORE_THRESHOLD)) {
            this.minScoreThreshold = inputFeatures.get(SE_MIN_SCORE_THRESHOLD)
                    .map(t -> InputFeaturesUtils.readFloatValueOrException(inputFeatures, modelName, t, Float.NEGATIVE_INFINITY, SE_MIN_SCORE_THRESHOLD))
                    .get();
        }
    }

    public Normalization get() {
        return new Normalization(normalizationMethod, minScoreThreshold);
    }
}

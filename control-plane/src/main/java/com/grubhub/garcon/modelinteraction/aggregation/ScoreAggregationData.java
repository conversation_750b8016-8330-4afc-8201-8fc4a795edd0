package com.grubhub.garcon.modelinteraction.aggregation;

import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import io.vavr.collection.List;
import io.vavr.collection.Map;

import static com.grubhub.garcon.controlplane.util.ControlPlaneUtils.getVavrList;
import static com.grubhub.garcon.modelinteraction.ScoreAggregationModel.ITEMS;
import static com.grubhub.garcon.modelinteraction.ScoreAggregationModel.ITEM_WEIGHTS;
import static com.grubhub.garcon.modelinteraction.aggregation.Item.EMPTY_ITEM;

public class ScoreAggregationData {
    private final List<String> rawItems;
    private final List<Double> weights;
    private final Map<String, Object> row;
    private final Map<String, Item> itemById;

    private ScoreAggregationData(ScoreAggregationConfig scoreAggregationConfig, Map<String, Object> elem) {
        this.row = elem;
        this.rawItems = getItems();
        this.weights = getItemWeights();
        this.itemById = scoreAggregationConfig.getItemById();
    }

    public static ScoreAggregationData of(ScoreAggregationConfig scoreAggregationConfig, Map<String, Object> row) {
        return new ScoreAggregationData(scoreAggregationConfig, row);
    }

    private List<String> getItems() {
        return getVavrList(row.get(ITEMS).getOrElse(List.empty()));
    }

    private List<Double> getItemWeights() {
        return getVavrList(row.get(ITEM_WEIGHTS).getOrElse(List.empty()));
    }


    public List<Item> extractItemsFromFeatures() {
        return rawItems
                .toStream()
                .zipWithIndex()
                .map(tuple -> getItemOrDefault(tuple._1(), tuple._2()))
                .toList();
    }

    private Item getItemOrDefault(String item, int index) {
        return itemById.get(item)
                .map(existingItem -> existingItem.withWeight(ControlPlaneUtils.parseDouble(weights.get(index), 1d)))
                .getOrElse(EMPTY_ITEM);
    }

}

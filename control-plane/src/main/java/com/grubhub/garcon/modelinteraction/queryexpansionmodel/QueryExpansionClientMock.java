package com.grubhub.garcon.modelinteraction.queryexpansionmodel;

import static com.grubhub.garcon.modelinteraction.queryexpansionmodel.QueryExpansionModel.DELIMITER;

public class QueryExpansionClientMock implements LLMClient {

    public String invoke(String modelName, String prompt) {
      String testPrompt = prompt.replace("\"", "").trim();

      return String.join(DELIMITER, new String[] {
        "vegan " + testPrompt,
        "gluten-free " + testPrompt,
        testPrompt + " combos",
        "healthy " + testPrompt,
        "low-calorie " + testPrompt
});
    }
}

package com.grubhub.garcon.modelinteraction;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.metrics.Spans;
import com.grubhub.garcon.controlplane.services.controlplane.sequential.ExpressionExecutor;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.ddml.util.LogUtil;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.jexl3.JexlExpression;

import java.util.function.Function;

/**
 * Model that evaluates a set of rules against the model features
 * The rules are specified in the "rules" feature as a JSON object map where the key is the rule name and the value is the JEXL expression to be evaluated.
 * The model features name with their values will be set as context for the JEXL expression thus a feature name used in the JEXL expression will be replaced
 * with its value when evaluating.
 */
@Slf4j
@Singleton
public class RuleMatchingModel implements JavaModel {

    public static final String RULES_FEATURE = "rules";
    private static final String JEXL_EXPRESSION_CREATION = "jexlExpressionCreation";
    private static final String JEXL_EXPRESSION_EVALUATION = "jexlExpressionEvaluation";
    private static final String TAG_MODEL_NAME = "model_name";
    private static final String TAG_MODEL_VERSION = "model_version";

    private static final ObjectMapper objectMapper = ObjectMapperHelper.INSTANCE;

    private final ExpressionExecutor jexlExpressionExecutor;
    private final Timer jexlExpressionCreationTimer;
    private final Timer jexlExpressionEvaluationTimer;


    @Inject
    public RuleMatchingModel(ExpressionExecutor jexlExpressionExecutor,
                             MeterRegistry meterRegistry) {
        this.jexlExpressionExecutor = jexlExpressionExecutor;
        this.jexlExpressionCreationTimer = meterRegistry.timer(Metrics.name(getClass(), JEXL_EXPRESSION_CREATION),
                Tags.of(TAG_MODEL_NAME, TAG_MODEL_VERSION));
        this.jexlExpressionEvaluationTimer = meterRegistry.timer(Metrics.name(getClass(), JEXL_EXPRESSION_EVALUATION),
                Tags.of(TAG_MODEL_NAME, TAG_MODEL_VERSION));
    }

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput modelInferenceInput) {
        log.debug("Processing modelInferenceInput={}", LogUtil.logFeatures(modelInferenceInput.getProcessedFeatures()));

        // precompute the jexl rule expressions
        Map<String, JexlExpression> ruleNameToJexlExpressionMap = buildJexlRulesMap(modelInferenceInput.getProcessedFeatures().head(), modelInferenceInput);

        // for every feature row, we calculate a map from output name to its result
        List<Map<String, ModelInferenceOutputType>> outputPerFeatureRow = modelInferenceInput.getProcessedFeatures()
                .map(featureRow -> computeOutputForFeatureRow(featureRow, ruleNameToJexlExpressionMap, modelInferenceInput));

        // adapt to what ModelInferenceResult needs
        // we need to return a map from output name to a list of results
        // so we need to transform the list of map to a map of list
        Map<String, List<ModelInferenceOutputType>> output = convertForOutput(outputPerFeatureRow);

        return ModelInferenceResult.builder()
                .output(output)
                .build();
    }

    /**
     * Takes as input a list of map from output name to output value and converts them to a map from output name (rule name) to a list of results
     * Each element in the list represents a feature's row output.
     * A feature's output is a map because there are multiple rules with their mapped results.
     * <p>
     * Ex.: [(<RULE_1, true>, <RULE_2, false>, <RULE_3, true>), (<RULE_1, false>, <RULE_2, false>, <RULE_3, true>)] will be converted to
     * <RULE_1, [true, false]>, <RULE_2, [false, false]>, <RULE_3, [true, true]>
     *
     * @param outputPerFeatureRowList
     * @return map from output name (rule name) to a list of results
     */
    private Map<String, List<ModelInferenceOutputType>> convertForOutput(List<Map<String, ModelInferenceOutputType>> outputPerFeatureRowList) {
        // start with an empty map for accumulating
        return outputPerFeatureRowList.foldLeft(HashMap.empty(), (accumulatingMap, mapFromList) ->
                // for each map in the list, add the key-value pair to the accumulating map
                mapFromList.foldLeft(accumulatingMap, (innerAccumulatingMap, keyValueMapEntry) -> {
                    // key is the rule name, value is the result
                    String key = keyValueMapEntry.toEntry().getKey();
                    ModelInferenceOutputType value = keyValueMapEntry.toEntry().getValue();
                    // get the list of results for the rule
                    List<ModelInferenceOutputType> updatedList = innerAccumulatingMap.get(key)
                            .map(list -> list.append(value)) // if the list exists, append the new value
                            .getOrElse(List.of(value)); // if the list does not exist, create a new list with the value
                    return innerAccumulatingMap.put(key, updatedList); // update the map with the new list
                })
        );
    }

    /**
     * Creates a map from output name to rule result. The output name is the same as the rule name
     *
     * @param featureRow
     * @param ruleNameToJexlExpressionMap
     * @param modelInferenceInput
     * @return
     */
    private Map<String, ModelInferenceOutputType> computeOutputForFeatureRow(Map<String, Object> featureRow,
                                                                             Map<String, JexlExpression> ruleNameToJexlExpressionMap,
                                                                             ModelInferenceInput modelInferenceInput) {
        // map of rule name to evaluation result
        Map<String, ModelInferenceOutputType> ruleNameToResultMap = evaluateRules(featureRow, ruleNameToJexlExpressionMap, modelInferenceInput);

        // map each model's output name to the result of a rule with the same name
        return modelInferenceInput.getModel().getModelOutputs()
                .map(modelOut -> Tuple.of(modelOut.getOutputName(), ruleNameToResultMap.getOrElse(modelOut.getOutputName(), null)))
                .toMap(Function.identity());
    }

    /**
     * Evaluates the JEXL rules
     *
     * @param featureRow
     * @param ruleNameToJexlExpressionMap
     * @param modelInferenceInput
     * @return
     */
    private Map<String, ModelInferenceOutputType> evaluateRules(Map<String, Object> featureRow,
                                                                Map<String, JexlExpression> ruleNameToJexlExpressionMap,
                                                                ModelInferenceInput modelInferenceInput) {
        val jexlContextMap = buildJexlContextMap(featureRow);

        return Spans.span(
                JEXL_EXPRESSION_EVALUATION,
                getTagsForSpan(modelInferenceInput),
                () -> jexlExpressionEvaluationTimer.record(() -> ruleNameToJexlExpressionMap.mapValues(jexlExpression -> {
                            try {
                                boolean ruleResult = jexlExpressionExecutor.evaluateToBoolean(jexlExpression, jexlContextMap);
                                return ModelInferenceOutputType.of(String.valueOf(ruleResult));
                            } catch (Exception e) {
                                String logMessage = String.format("Failed evaluating JEXL rule expression=%s, errorMessage=%s featuresRow=%s",
                                        jexlExpression, e.getMessage(), featureRow);
                                log.error(logMessage, e);
                                throw new RuntimeException(logMessage, e);
                            }
                        })
                )
        );
    }

    /**
     * Reads the rules json map, deserialize it and create a map from rule name to jexl expression
     *
     * @param featureRow
     * @param modelInferenceInput
     * @return map from rule name to jexl expression
     */
    private Map<String, JexlExpression> buildJexlRulesMap(Map<String, Object> featureRow, ModelInferenceInput modelInferenceInput) {
        if (!featureRow.containsKey(RULES_FEATURE)) {
            String logMessage = String.format("Missing rules Feature featureRow=%s", featureRow);
            log.error(logMessage);
            throw new RuntimeException(logMessage);
        }

        val rulesFeatureValueString = String.valueOf(featureRow.getOrElse(RULES_FEATURE, "")).strip();
        if (rulesFeatureValueString.isBlank()) {
            String logMessage = String.format("Value of rules feature is invalid rulesFeatureValue=%s featureRow=%s", rulesFeatureValueString, featureRow);
            log.error(logMessage);
            throw new RuntimeException(logMessage);
        }

        Map<String, String> ruleNameToDefinitionMap;
        try {
            ruleNameToDefinitionMap = objectMapper.readValue(rulesFeatureValueString, new TypeReference<>() {
            });
        } catch (Exception e) {
            String logMessage = String.format("Failed deserializing JSON rules, errorMessage=%s featuresRow=%s", e.getMessage(), featureRow);
            log.error(logMessage, e);
            throw new RuntimeException(logMessage, e);
        }

        return Spans.span(
                JEXL_EXPRESSION_CREATION,
                getTagsForSpan(modelInferenceInput),
                () -> jexlExpressionCreationTimer.record(() -> {
                    try {
                        return ruleNameToDefinitionMap.mapValues(jexlExpressionExecutor::createExpression);
                    } catch (Exception e) {
                        String logMessage = String.format("Failed creating jexl rule expressions, errorMessage=%s featuresRow=%s", e.getMessage(), featureRow);
                        log.error(logMessage, e);
                        throw new RuntimeException(logMessage, e);
                    }
                })
        );
    }

    /**
     * Creates a context map with all the features names and their values.
     * It skips for the "rules" feature
     *
     * @param featureRow
     * @return
     */
    private java.util.Map<String, Object> buildJexlContextMap(Map<String, Object> featureRow) {
        java.util.Map<String, Object> contextMap = new java.util.HashMap<>();

        featureRow.toStream()
                .map(Tuple2::toEntry)
                .filter(entry -> !RULES_FEATURE.equalsIgnoreCase(String.valueOf(entry.getValue())))
                .forEach(entry -> contextMap.put(entry.getKey(), entry.getValue()));

        return contextMap;
    }

    private java.util.Map<String, String> getTagsForSpan(ModelInferenceInput modelInferenceInput) {
        return HashMap.of(
                        TAG_MODEL_NAME, modelInferenceInput.getModel().getModelName(),
                        TAG_MODEL_VERSION, modelInferenceInput.getModel().getVersion()
                )
                .toJavaMap();
    }
}

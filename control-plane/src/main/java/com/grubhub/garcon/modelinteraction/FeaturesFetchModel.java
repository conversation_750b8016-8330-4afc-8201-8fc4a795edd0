package com.grubhub.garcon.modelinteraction;

import com.google.inject.Singleton;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputType;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import io.vavr.Tuple;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Traversable;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.function.Function;

/**
 * Model that only retrieves the requested inputs as part of its output.
 * It doesn't perform any calculations.
 */
@Slf4j
@Singleton
public class FeaturesFetchModel implements JavaModel {

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        Map<String, List<ModelInferenceOutputType>> output = input.getModel().getModelOutputs()
                .map(modelOut -> Tuple.of(modelOut.getOutputName(), fetchFeatureForSamples(input.getProcessedFeatures(), modelOut)))
                .toMap(Function.identity());
        return ModelInferenceResult.builder()
                .output(output)
                .build();
    }

    private List<ModelInferenceOutputType> fetchFeatureForSamples(List<Map<String, Object>> samples, ModelOutputDTO modelOut) {
        return samples.map(features -> extractFeature(modelOut, features.get(modelOut.getOutputName())));
    }

    private ModelInferenceOutputType extractFeature(ModelOutputDTO modelOut, Option<Object> featureOp) {
        val outputType = modelOut.getOutputTypeEnum();
        ModelInferenceOutputType outValue = null;
        if (outputType == ModelOutputType.FLOAT) {
            outValue = ModelInferenceOutputType.of(extractFloat(featureOp));
        } else if (outputType == ModelOutputType.LONG) {
            outValue = ModelInferenceOutputType.of(extractLong(featureOp));
        } else if (outputType == ModelOutputType.STRING) {
            outValue = ModelInferenceOutputType.of(extractString(featureOp));
        } else if (outputType.isArrayType()) {
            outValue = featureOp.map(f -> extractArrayFeature(outputType, f)).getOrElse(ModelInferenceOutputType.emptyFloatArray());
        } else {
            log.warn("Invalid feature type, expected_output_type={}, value={}", outputType, featureOp);
        }
        return outValue;
    }

    private ModelInferenceOutputType extractArrayFeature(ModelOutputType outputType, Object o) {
        if (o == null) {
            return ModelInferenceOutputType.emptyFloatArray();
        }
        List<?> list = List.empty();
        if (o instanceof java.util.Collection) {
            list = List.ofAll((java.util.Collection<?>) o);
        } else if (o instanceof List) {
            list = (List<?>) o;
        } else if (o instanceof Traversable) {
            list = List.ofAll((Traversable<?>) o);
        } else if (o instanceof String[]) {
            list = List.of((String[]) o);
        } else if (o instanceof Float[]) {
            list = List.of((Float[]) o);
        } else if (o instanceof Long[]) {
            list = List.of((Long[]) o);
        } else {
            log.warn("Invalid feature array type, expected_output_type={}, value={}", outputType, o);
        }

        ModelInferenceOutputType outValue = null;
        if (outputType == ModelOutputType.FLOAT_ARRAY) {
            outValue = ModelInferenceOutputType.ofFloatArray(list.map(i -> extractFloat(Option.of(i))));
        } else if (outputType == ModelOutputType.LONG_ARRAY) {
            outValue = ModelInferenceOutputType.ofLongArray(list.map(i -> extractLong(Option.of(i))));
        } else if (outputType == ModelOutputType.STRING_ARRAY) {
            outValue = ModelInferenceOutputType.ofStringArray(list.map(i -> extractString(Option.of(i))));
        } else {
            log.warn("Unsupported array_type={}, object={}", outputType, o);
        }
        return outValue;
    }

    private float extractFloat(Option<Object> o) {
        return o.map(v -> Double.valueOf(ControlPlaneUtils.parseDouble(v, 0)).floatValue()).getOrElse(0f);
    }

    private long extractLong(Option<Object> o) {
        return o.map(v -> Double.valueOf(ControlPlaneUtils.parseDouble(v, 0)).longValue()).getOrElse(0L);
    }

    private String extractString(Option<Object> o) {
        return o.map(String::valueOf).getOrElse("");
    }

}

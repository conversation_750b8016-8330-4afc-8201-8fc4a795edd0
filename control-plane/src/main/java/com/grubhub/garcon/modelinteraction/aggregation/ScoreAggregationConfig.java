package com.grubhub.garcon.modelinteraction.aggregation;

import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.function.Function;

import static com.grubhub.garcon.controlplane.util.ControlPlaneUtils.getVavrList;
import static com.grubhub.garcon.controlplane.util.ControlPlaneUtils.toVavrList;
import static com.grubhub.garcon.modelinteraction.ScoreAggregationModel.CONFIG_ITEM_IDS;
import static com.grubhub.garcon.modelinteraction.ScoreAggregationModel.CONFIG_ITEM_SCORES;

@RequiredArgsConstructor
@Slf4j
public class ScoreAggregationConfig {

    private final List<String> configItemIds;
    private final List<Double> configItemScores;

    public ScoreAggregationConfig(ModelInferenceInput modelInferenceInput) {
        val firstSample = modelInferenceInput.getProcessedFeatures().head();

        this.configItemIds = getVavrList(firstSample.getOrElse(CONFIG_ITEM_IDS, ""));

        this.configItemScores = ControlPlaneUtils.parseDoubleList(String.valueOf(toVavrList(firstSample.getOrElse(CONFIG_ITEM_SCORES, "")).toJavaList()));
    }

    public Map<String, Item> getItemById() {
        return Stream.range(0, getMinValue())
                .map(index -> new Item(index, this.configItemIds, this.configItemScores))
                .toMap(Item::getId, Function.identity());
    }

    private int getMinValue() {
        return Math.min(configItemIds.size(), configItemScores.size());
    }

}

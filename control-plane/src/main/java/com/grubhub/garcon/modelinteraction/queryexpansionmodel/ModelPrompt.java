package com.grubhub.garcon.modelinteraction.queryexpansionmodel;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * Holds the prompt template for the query expansion model. Resolves the prompt with the query text.
 */

@Slf4j
@Data
public class ModelPrompt {

    private String promptTemplate;

    public ModelPrompt(String promptTemplate) {
        this.promptTemplate = promptTemplate;
    }

    public String resolvePromptWithQuery(String query) {
        return promptTemplate.replace("{{query}}", query);
    }

    public String resolvePromptToJson(String prompt) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(prompt);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}

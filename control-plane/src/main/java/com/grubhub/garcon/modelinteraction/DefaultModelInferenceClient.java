package com.grubhub.garcon.modelinteraction;

import com.google.common.base.Throwables;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.s3.S3Utils;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelConfigUpdateResponse;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.*;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import com.grubhub.garcon.ddml.util.LogUtil;
import com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelAdaptor;
import com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelConfiguration;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.vavr.Tuple2;
import io.vavr.collection.HashMap;
import io.vavr.collection.LinkedHashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import tensorflow.serving.Model;
import tensorflow.serving.ModelServerConfigOuterClass;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public class DefaultModelInferenceClient implements ModelInferenceClient {
    @Setter
    private TensorFlowModelAdaptor tensorFlowModelAdaptor;

    private final MeterRegistry meterRegistry;
    private final TensorFlowModelConfiguration tensorFlowModelConfiguration;
    private final JavaModels javaModels;
    private final ExternalModelInference externalModelInference;
    private final ExecutorService batchInvokerExecutor;

    @Inject
    public DefaultModelInferenceClient(TensorFlowModelAdaptor tensorFlowModelAdaptor,
                                       MeterRegistry meterRegistry,
                                       TensorFlowModelConfiguration tensorFlowModelConfiguration,
                                       JavaModels javaModels,
                                       ExternalModelInference externalModelInference,
                                       @Named("batchInvokerExecutor") ExecutorService batchInvokerExecutor) {
        this.tensorFlowModelAdaptor = tensorFlowModelAdaptor;
        this.meterRegistry = meterRegistry;
        this.tensorFlowModelConfiguration = tensorFlowModelConfiguration;
        this.javaModels = javaModels;
        this.externalModelInference = externalModelInference;
        this.batchInvokerExecutor = batchInvokerExecutor;
    }

    @Override
    public ModelInferenceResult infer(ModelType modelType, ModelInferenceInput modelInferenceInput) {
        return ModelInferenceType.valueOf(modelType).getModelInferenceAlgorithm().apply(this, modelInferenceInput);
    }

    @Override
    public ModelStatus getTensorFlowStatus(ModelDTO model) {
        return tensorFlowModelAdaptor.getTensorFlowStatus(model);
    }

    @Override
    public TensorFlowModelMetadataDTO getTensorFlowMetadata(ModelDTO model) {
        return tensorFlowModelAdaptor.getTensorFlowMetadata(model);
    }

    @Override
    public ModelInferenceResult inferWithTensorFlow(ModelInferenceInput modelInferenceInput) {
        ModelDTO modelDto = modelInferenceInput.getModel();
        val modelName = modelDto.getModelName();

        if (tensorFlowModelAdaptor.getTfsCluster(modelInferenceInput.getModel()).getPredictionClient() == null) {
            throw new RuntimeException(String.format("No TensorFlow predictClient was found, cannot invoke model=%s", modelName));
        }

        Model.ModelSpec modelSpec = tensorFlowModelAdaptor.createModelSpec(modelDto);
        log.debug("Model spec for inferWithTensorFlow, modelSpec={}", modelSpec);

        // Invoking a single inference if either the general or model-level batching are disabled.
        if (!tensorFlowModelConfiguration.getExternalInferenceFeatureBatchEnabled() || !Boolean.TRUE.equals(modelDto.getBatchingEnabled())) {
            log.debug("Batching log. Batching is not enabled. general_config={}, model_level_config={}",
                    tensorFlowModelConfiguration.getExternalInferenceFeatureBatchEnabled(),
                    modelDto.getBatchingEnabled());

            return inferenceTimed(
                    modelInferenceInput,
                    0,
                    false,
                    () -> externalModelInference.doInference(modelInferenceInput, modelSpec, modelName, modelDto)
            );
        }

        // Batched execution
        try {
            final Integer batchSize = calculateBatchSize(modelInferenceInput);
            log.debug("Batching log. Batching features for model={}, batch_size={}", modelName, batchSize);

            return inferenceTimed(modelInferenceInput,
                    batchSize,
                    true,
                    () -> executeInBatches(modelInferenceInput, modelName, modelSpec, batchSize, modelDto)
            );
        } catch (Exception e) {
            log.debug("Batching log. Failed to execute batched model inference for model_name={}", modelName, e);
            throw new RuntimeException(e);
        }

    }

    private ModelInferenceResult executeInBatches(ModelInferenceInput modelInferenceInput,
                                                  String modelName,
                                                  Model.ModelSpec modelSpec,
                                                  Integer batchSize,
                                                  ModelDTO modelDto) {
        try {
            java.util.List<ModelInferenceInput> batchedInputs = createBatches(modelInferenceInput, batchSize);
            log.debug("Batching log. Created batches of TFS inference operations. model={}, batch_size={}, number_of_batches={}",
                    modelName, batchSize, batchedInputs.size());

            CompletableFuture<ModelInferenceResult>[] futures = batchedInputs.stream()
                    .map(input -> CompletableFuture.supplyAsync(() ->
                            externalModelInference.doInference(input, modelSpec, modelName, modelDto), batchInvokerExecutor))
                    .toArray(CompletableFuture[]::new);

            Map<String, List<ModelInferenceOutputType>> flattenedResults = Arrays.stream(futures)
                    .map(CompletableFuture::join)
                    .sorted(Comparator.comparingInt(ModelInferenceResult::getBatchSequenceNumber).reversed())
                    .map(ModelInferenceResult::getOutput)
                    .reduce((a, b) -> b.merge(a, List::appendAll))
                    .map(LinkedHashMap::ofEntries)
                    .orElse(LinkedHashMap.empty());

            return ModelInferenceResult.builder()
                    .isDefaultOutput(futures[0].get().isDefaultOutput())
                    .output(flattenedResults)
                    .build();
        } catch (Exception e) {
            log.error("Batching log. Exception executing batched invocation for model_name={}", modelName, e);
            throw new RuntimeException(e);
        }
    }

    private Integer calculateBatchSize(ModelInferenceInput modelInferenceInput) {
        Integer modelBatchSize = modelInferenceInput.getModel().getInputBatchSize();
        Integer generalBatchSize = tensorFlowModelConfiguration.getExternalInferenceFeatureBatchSize();

        // For protection, the general batch size is the maximum allowed.
        if (modelBatchSize != null && modelBatchSize > 0 && modelBatchSize < generalBatchSize) {
            return modelBatchSize;
        }
        return generalBatchSize;
    }

    private ModelInferenceResult inferenceTimed(ModelInferenceInput modelInferenceInput,
                                                Integer batchSize,
                                                Boolean batched,
                                                Supplier<ModelInferenceResult> inferenceMethod) {
        Tags tags = Tags.of("model_name", modelInferenceInput.getModel().getModelName(),
                "model_version", modelInferenceInput.getModel().getVersion(),
                "model_feature_batch_size", String.valueOf(batchSize),
                "is_batched", String.valueOf(batched)
        );
        Timer inferenceTimer = meterRegistry.timer(Metrics.name(super.getClass(), "doInference"), tags);

        return inferenceTimer.record(inferenceMethod);
    }

    @Override
    public ModelInferenceResult inferWithSageMaker(ModelInferenceInput modelInferenceInput) {
        return javaModels.getSageMakerModel().invoke(modelInferenceInput);
    }

    @Override
    public ModelInferenceResult inferWithNativeFunction(ModelInferenceInput modelInferenceInput) {
        log.debug("Processed features={} ", LogUtil.logFeatures(modelInferenceInput.getProcessedFeatures()));
        val output = modelInferenceInput.getProcessedFeatures()
                .map(feature -> feature.values().toJavaStream().mapToDouble(obj -> ControlPlaneUtils.parseDouble(obj, 0.0)).sum())
                .map(Number::floatValue);
        log.debug("Return output={} when calling infer with native function with input={}", output, modelInferenceInput);
        return ModelInferenceResult.from(modelInferenceInput, output);
    }

    @Override
    public ModelInferenceResult inferQueryExpansions(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getQueryExpansionModel().invoke(modelInferenceInput);
    }

    @Override
    public ModelInferenceResult inferWithSort(ModelInferenceInput modelInferenceInput, boolean ascending, float multiplier) {
        log.debug("Processed features={} ", LogUtil.logFeatures(modelInferenceInput.getProcessedFeatures()));
        val output = modelInferenceInput.getProcessedFeatures().zipWithIndex()
                .map(i -> i._2 * multiplier)
                .sortBy(i -> ascending ? i : -i);
        return ModelInferenceResult.from(modelInferenceInput, output);
    }

    @Override
    public ModelInferenceResult inferWithRandomSort(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getRandomSortModel().invoke(modelInferenceInput);
    }

    @Override
    public ModelInferenceResult inferWithDistanceFalloff(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getDistanceFalloffModel().invoke(modelInferenceInput);
    }

    @Override
    public ModelInferenceResult inferWithLinearCombination(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getLinearCombinationModel().invoke(modelInferenceInput);
    }

    @Override
    public ModelInferenceResult inferWithFeaturesFetch(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getFeaturesFetchModel().invoke(modelInferenceInput);
    }

    @Override
    public ModelInferenceResult inferWithSearchEmbedding(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getSearchEmbeddingsModel().invoke(modelInferenceInput);
    }

    @Override
    public ModelInferenceResult inferWithTextSimilarity(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getTextSimilarityModel().invoke(modelInferenceInput);
    }

    @Override
    public ModelInferenceResult inferWithScoreAggregation(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getScoreAggregationModel().invoke(modelInferenceInput);
    }

    @Override
    public ModelInferenceResult inferVectorCosineSimilarity(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getVectorSimilarityModel().invoke(modelInferenceInput);
    }

    public ModelInferenceResult inferWithRandomSample(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getRandomSampleModel().invoke(modelInferenceInput);
    }

    public ModelInferenceResult inferWithRandomDistribution(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getRandomDistributionModel().invoke(modelInferenceInput);
    }

    public ModelInferenceResult inferWithRankingPostProcessing(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getRankingPostProcessingModel().invoke(modelInferenceInput);
    }

    public ModelInferenceResult inferWithRuleMatching(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getRuleMatchingModel().invoke(modelInferenceInput);
    }

    public ModelInferenceResult inferWithPrecomputedRanker(ModelInferenceInput modelInferenceInput) {
        return this.javaModels.getPrecomputedRankerModel().invoke(modelInferenceInput);
    }

    @Override
    public ModelStatus getSearchEmbeddingStatus(ModelDTO model) {
        log.debug("Getting search embeddings status for model_name={}", model.getModelName());
        val status = this.javaModels.getSearchEmbeddingsModel().indexesStatus(model);
        log.debug("Finish getting search embeddings status for model_name={} with status={}", model.getModelName(), status);
        return status;
    }

    @Override
    public void configureSearchEmbeddingModels(List<ModelDTO> models) {
        javaModels.getSearchEmbeddingsModel().activateIndexes(models);
    }

    @Override
    public List<ModelConfigUpdateResponse> configureTensorFlow(List<ModelDTO> models) {
        Timer.Sample configureTimer = Timer.start(meterRegistry);
        String timerName = Metrics.name(super.getClass(), "configureModels");
        if (models.isEmpty()) {
            configureTimer.stop(meterRegistry.timer(timerName, Tags.of("status", "empty")));
            log.debug("Skipping TensorFlow configuration because input models list is empty");
            return List.empty();
        }
        log.info("Configuring number of TensorFlow models={}, models={} ", models.size(), geModelNames(models));

        // 1. Get configuration data for each model by tfs cluster
        java.util.Map<ModelDTO, ModelServerConfigOuterClass.ModelConfig> modelsWithConfigMap = new java.util.HashMap<>();
        Map<String, List<ModelServerConfigOuterClass.ModelConfig>> tensorFlowModelsToConfigure = HashMap.ofAll(models
                .filter(model -> {
                    val modelConfigOption = createModelConfigFromModel(model);
                    if (modelConfigOption.isDefined()) {
                        modelsWithConfigMap.put(model, modelConfigOption.get());
                        return true;
                    }
                    return false;
                })
                .collect(Collectors.groupingBy(tensorFlowModelAdaptor::getTfsClusterNameForModel,
                        Collectors.mapping(modelsWithConfigMap::get,
                                List.collector())))
        );

        log.info("New TensorFlow Serving configuration=" + tensorFlowModelsToConfigure);

        Map<String, ModelServerConfigOuterClass.ModelConfigList> tensorFlowModelConfigList = tensorFlowModelsToConfigure
                .bimap(Function.identity(), tensorFlowModelAdaptor::createModelConfigListFromTFModelConfigs);

        // 3. Make request
        try {
            List<ModelConfigUpdateResponse> responses = tensorFlowModelConfigList
                    .map(entry -> new Tuple2<>(tensorFlowModelAdaptor.getTfsClusterByName(entry._1), entry._2))//dont need getTfsClusterByName?
                    .flatMap(tuple -> {
                        List<ModelConfigUpdateResponse> partialResponses = tuple._1.updateModelConfigsAllHosts(tuple._2);
                        log.info("TFConfigUpdate_responses={}, tfsClusterName={}-{}", partialResponses,
                                tuple._1.getTfsClusterConfig().getTfServingAppName(),
                                tuple._1.getTfsClusterConfig().getTfServingVersion()
                        );
                        return partialResponses;
                    })
                    .toList();

            configureTimer.stop(meterRegistry.timer(timerName, Tags.of("status", "success")));
            return responses;
        } catch (Exception exception) {
            configureTimer.stop(meterRegistry.timer(timerName, Tags.of("status", "fail")));
            throw new RuntimeException("Error updating TFS_models_configs=" + tensorFlowModelsToConfigure, exception);
        }
    }

    private Option<ModelServerConfigOuterClass.ModelConfig> createModelConfigFromModel(ModelDTO model) {
        if (checkServingLocationBucket(model)) {
            try {
                return Option.of(tensorFlowModelAdaptor.getTfsCluster(model).createModelConfigFromModelDTO(model));
            } catch (Exception e) {
                meterRegistry.counter(Metrics.name(getClass(), "createModelConfig"),
                                Tags.of("model_name", model.getModelName()))
                        .increment();
                log.error("Error creating ModelConfig from the model_name={} with error_message={}", model.getModelName(), Throwables.getStackTraceAsString(e));
            }
        }
        return Option.none();
    }

    private boolean checkServingLocationBucket(ModelDTO model) {
        if (tensorFlowModelConfiguration.getValidateServingLocationBucket()) {
            if (!bucketNamesAreEqual(model)) {
                logErrorForDifferentServingLocations(model);
            }
            return bucketNamesAreEqual(model);
        }
        return true;
    }

    private boolean bucketNamesAreEqual(ModelDTO model) {
        return tensorFlowModelConfiguration.getServingLocationBucket().equals(getBucketFromServingLocation(model));
    }

    private String getBucketFromServingLocation(ModelDTO model) {
        return S3Utils.S3Path.from(model.getServingLocation()).getBucket();
    }

    private void logErrorForDifferentServingLocations(ModelDTO model) {
        log.error(String.format(
                        "Serving locations for model with name=%s are different" +
                                " serving_location_model=%s, serving_location_bucket_config=%s",
                        model.getModelName(),
                        model.getServingLocation(),
                        tensorFlowModelConfiguration.getServingLocationBucket()
                )
        );
    }

    private String geModelNames(List<ModelDTO> models) {
        return models
                .toStream()
                .map(ModelDTO::getModelName)
                .intersperse(",")
                .reduce(String::concat)
                .trim();
    }

    /***
     * Divides  com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput#processedFeatures into
     * multiple self-contained ModelInferenceInput objects.
     * <p>
     * Returns null if com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO#featureBatchSize is null.
     *
     * @param modelInferenceInput model input containing processed features
     * @return divided list of processed features wrapped in ModelInferenceInput.
     */
    public static java.util.List<ModelInferenceInput> createBatches(ModelInferenceInput modelInferenceInput, Integer batchSize) {
        if (batchSize == null || batchSize <= 0) {
            log.error("Batching log. Batch size is invalid, returning a list with all the values. batch_size={}", batchSize);
            return Collections.singletonList(modelInferenceInput);
        }

        int totalSize = modelInferenceInput.getProcessedFeatures().size();
        int numBatches = (totalSize + batchSize - 1) / batchSize;

        final java.util.List<ModelInferenceInput> batches = IntStream.range(0, numBatches)
                .mapToObj(i -> ModelInferenceInput.builder()
                        .model(modelInferenceInput.getModel())
                        .batchSequenceNumber(i)
                        .processedFeatures(modelInferenceInput.getProcessedFeatures().subSequence(i * batchSize,
                                Math.min(totalSize, (i + 1) * batchSize)))
                        .invocationType(modelInferenceInput.getInvocationType())
                        .build())
                .collect(Collectors.toList());

        logFeatureValues(batches);
        return batches;
    }

    private static void logFeatureValues(java.util.List<ModelInferenceInput> batches) {
        batches.forEach(batch ->
                log.debug("Batching log. Batch number={}, Processed features size={}", batch.getBatchSequenceNumber(), batch.getProcessedFeatures().size())
        );
    }
}

package com.grubhub.garcon.modelinteraction.computablefeatures.textnormalizer;

import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import com.grubhub.garcon.modelinteraction.computablefeatures.BaseFunctionFeature;
import io.vavr.collection.Map;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

@Slf4j
public class TextNormalizerFunction extends BaseFunctionFeature {

    public static final String INPUT_STRING = "str";
    public static final String OUTPUT_STRING = "str";

    public TextNormalizerFunction() {
        super(FunctionFeatureType.TEXT_NORM_PORTER_STEMMER);
    }

    public Map<String, Object> compute(Map<String, Object> inputs) {
        val inputStr = getString(inputs, INPUT_STRING);
        val outputStr = new ControlPlaneTextNormalizer(inputStr).tokenize()
                .orElseGet(() -> {
                    log.warn("Input text={} was not normalized", inputStr);
                    return inputStr;
                });
        return createOutput(OUTPUT_STRING, outputStr);
    }
}

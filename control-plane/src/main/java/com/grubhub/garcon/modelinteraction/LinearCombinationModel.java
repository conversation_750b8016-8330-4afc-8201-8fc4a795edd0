package com.grubhub.garcon.modelinteraction;

import com.google.inject.Singleton;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.ddml.util.LogUtil;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import io.vavr.Tuple2;
import io.vavr.collection.Map;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

/**
 * Computes a linear combination of inputs and their corresponding weights.
 * Example: inputs: (a, b, c), weights: (w1, w2, w3), output: w1 * a + w2 * b + w3 * c
 */
@Slf4j
@Singleton
public class LinearCombinationModel implements JavaModel {

    private static final String WEIGHT_PREFIX = "weight_";
    private static final float DEF_VALUE = 0;

    @Override
    public ModelInferenceResult invoke(ModelInferenceInput input) {
        log.debug("Processed features={} ", LogUtil.logFeatures(input.getProcessedFeatures()));
        val output = input.getProcessedFeatures().map(this::computeForSample);
        log.debug("Output={} ", output);
        return ModelInferenceResult.from(input, output);
    }

    public float computeForSample(Map<String, Object> sample) {
        // Split params into weights and inputs (weights start with "weight_")
        val split = sample.partition(i -> i._1.startsWith(WEIGHT_PREFIX));
        val weights = split._1;
        val inputs = split._2;

        // Perform linear combination
        float output = 0;
        for (Tuple2<String, Object> entry : inputs) {
            // Parse input value
            val input = ControlPlaneUtils.parseDouble(entry._2, DEF_VALUE);

            // Find corresponding weight
            val inputName = entry._1;
            val weight = ControlPlaneUtils.parseDouble(weights.getOrElse(WEIGHT_PREFIX + inputName, DEF_VALUE), DEF_VALUE);

            output += weight * input;
        }
        return output;
    }
}

package com.grubhub.garcon.modelinteraction.computablefeatures.stringconcat;

import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import io.vavr.collection.Map;
import lombok.val;

/**
 * This function concatenates the four input strings with the separator between them.
 * Inputs: map with key: str1, str2, str3, str4, sep.
 * Outputs: map with key: str.
 */
public class StringConcatenateFourFunction extends StringConcatenateBaseFunction {

    public StringConcatenateFourFunction() {
        super(FunctionFeatureType.STR_CONCAT4);
    }

    public Map<String, Object> compute(Map<String, Object> inputs) {
        val outputStr = concatenate(
                4,
                getString(inputs, INPUT_SEPARATOR),
                getString(inputs, INPUT_STRING_ONE),
                getString(inputs, INPUT_STRING_TWO),
                getString(inputs, INPUT_STRING_THREE),
                getString(inputs, INPUT_STRING_FOUR)
        );

        return createOutput(OUTPUT_STRING, outputStr);
    }
}

package com.grubhub.garcon.ddml.controlplane;

import com.grubhub.garcon.controlplane.guice.AwsModule;
import com.grubhub.garcon.controlplane.guice.CoreModule;
import com.grubhub.garcon.controlplanerpc.ControlPlaneServiceRpc;
import com.grubhub.garcon.ddml.config.RuntimeSvcConfig;
import com.grubhub.garcon.ddml.config.RuntimeSvcConfigUpdater;
import com.grubhub.garcon.ddml.config.SvcConfig;
import com.grubhub.garcon.ddml.guice.ApiModule;
import com.grubhub.garcon.ddml.guice.PersistenceModule;
import com.grubhub.garcon.ddml.guice.RuntimeConfigModule;
import com.grubhub.garcon.ddml.guice.ServiceModule;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import com.grubhub.garcon.grpc.FlowRoutingGroupSeederHeartbeat;
import com.grubhub.garcon.grpc.GdpActionsPollingHeartbeat;
import com.grubhub.garcon.grpc.GlobalTFConfiguratorHeartbeat;
import com.grubhub.garcon.grpc.RegionalTFConfiguratorHeartbeat;
import com.grubhub.garcon.searchembeddings.rpc.resources.api.resources.SearchEmbeddingsRpcApi;
import com.grubhub.roux.BaseServiceModule;
import com.grubhub.roux.BaseServiceModuleBuilder;
import com.grubhub.roux.cacherole.runtime.CacheroleRuntimeUpdater;
import com.grubhub.roux.casserole.leader.LeadersRegistry;
import com.grubhub.roux.casserole.leader.RuntimeLeaderStateUpdater;
import com.grubhub.roux.config.DefaultConfigLoader;
import com.grubhub.roux.core.RouxletSetup;
import com.grubhub.roux.core.Service;
import com.grubhub.roux.core.ServiceBuilder;
import com.grubhub.roux.rest.PublicApiRouxletSetup;
import com.grubhub.roux.rpc.client.RpcClientRouxletSetup;
import com.grubhub.roux.rpc.server.RpcServerRouxletSetup;
import lombok.experimental.UtilityClass;
import com.grubhub.roux.grpc.client.GrpcClientRouxletSetup;
import lombok.val;
import tensorflow.serving.PredictionServiceGrpc;

import java.util.Map;
import java.util.stream.Stream;

import static com.grubhub.roux.cacherole.CacheroleRouxletSetup.newCacheroleSetup;
import static com.grubhub.roux.casserole.CasseroleRouxletSetup.newCasseroleSetup;
import static com.grubhub.roux.casserole.leader.LeaderRouxletSetup.newLeaderElectedSetup;
import static com.grubhub.roux.eventhub.EventHubRouxletSetup.newEventHubSetup;
import static com.grubhub.roux.rest.PublicApiRouxletSetup.newPublicApiSetup;
import static com.grubhub.roux.threading.ThreadPoolRouxletSetup.newThreadPoolSetup;

@UtilityClass
class DdmlControlPlaneServiceBuilder {

    private static final String TENSOR_FLOW_MODELS_CONFIGURATOR = "tensorFlowModelsConfigurator";
    private static final String TENSOR_FLOW_GLOBAL_MODELS_CONFIGURATOR = "tensorFlowGlobalModelsConfigurator";
    private static final String DDML_CONTROL_PLANE = "ddmlControlPlane";
    private static final String TENSOR_FLOW = "tensorFlow";
    private static final String TENSOR_FLOW_GLOBAL = "tensorFlowGlobal";
    private static final String FLOW_ROUTING_GROUP_GLOBAL = "flowRoutingGroupsSeedRotation";

    private static Map<String, BaseServiceModule> getSharedModules() {
        return BaseServiceModuleBuilder.start()
                .add("persistence")
                .implementation(new PersistenceModule())
                .simulation(binder -> {})
                .add("service-module")
                .implementation(new ServiceModule())
                .simulation(binder -> {})
                .add("runtimeConfig")
                .implementation(new RuntimeConfigModule())
                .simulation(new RuntimeConfigModule())
                .add("code")
                .implementation(new CoreModule())
                .simulation(binder -> {})
                .add("aws")
                .implementation(new AwsModule())
                .simulation(new AwsModule())
                .build();
    }

    Service build(SvcConfig config) {
        val serviceBuilder = ServiceBuilder
                .start(config, RuntimeSvcConfig.class)
                .withStartUpLoader(DefaultConfigLoader.class);
        return finalBuild(serviceBuilder, getSharedModules());
    }

    private PublicApiRouxletSetup withPublicApiSetup() {
        return newPublicApiSetup().withModules(
                BaseServiceModuleBuilder.start()
                        .add("public-api")
                        .implementation(new ApiModule())
                        .simulation(binder -> {})
                        .build()
        )
                .withObjectMapper(ObjectMapperHelper.INSTANCE);
    }

    private static RpcServerRouxletSetup withRpcServerSetup() {
        return RpcServerRouxletSetup.newRpcServerSetup().withInterfaces(ControlPlaneServiceRpc.class);
    }

    private static RpcClientRouxletSetup withRpcClientSetup() {
        return RpcClientRouxletSetup.newRpcClientSetup()
                .withServiceInterface("SEARCH-EMBEDDINGS", SearchEmbeddingsRpcApi.class);
    }

    private static GrpcClientRouxletSetup withGrpcClientSetup() {
        return GrpcClientRouxletSetup.<SvcConfig>newSetup()
                           .withServiceInterface("tensorFlow-prediction", PredictionServiceGrpc.class);

    }

    private Service finalBuild(ServiceBuilder serviceBuilder, Map<String, BaseServiceModule> sharedModules) {
        return serviceBuilder
                .withRouxlets(rouxletSetups())
                .withSharedModules(sharedModules)
                .withRuntimeListener(RuntimeLeaderStateUpdater.class)
                .withRuntimeListener(RuntimeSvcConfigUpdater.build())
                .withRuntimeListener(CacheroleRuntimeUpdater.build())
                .build(Svc.class);
    }

    private RouxletSetup[] rouxletSetups() {
        return Stream.of(
                withPublicApiSetup(),
                newCasseroleSetup(),
                newCacheroleSetup(),
                newThreadPoolSetup(),
                newEventHubSetup(ObjectMapperHelper.INSTANCE),
                withRpcServerSetup(),
                withRpcClientSetup(),
                withGrpcClientSetup(),
                newLeaderElectedSetup().withRegistry(new LeadersRegistry()
                        .add(TENSOR_FLOW_MODELS_CONFIGURATOR, DDML_CONTROL_PLANE, TENSOR_FLOW, RegionalTFConfiguratorHeartbeat.class)
                        .add(TENSOR_FLOW_GLOBAL_MODELS_CONFIGURATOR, DDML_CONTROL_PLANE, TENSOR_FLOW_GLOBAL, GlobalTFConfiguratorHeartbeat.class)
                        .add("gdpActionsPolling", DDML_CONTROL_PLANE, "gdpActionsPolling", GdpActionsPollingHeartbeat.class)
                        .add("flowRoutingGroupsSeedRotation", DDML_CONTROL_PLANE, FLOW_ROUTING_GROUP_GLOBAL, FlowRoutingGroupSeederHeartbeat.class)
                )
        ).toArray(RouxletSetup[]::new);
    }
}

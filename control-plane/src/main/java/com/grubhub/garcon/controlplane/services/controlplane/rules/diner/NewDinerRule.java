package com.grubhub.garcon.controlplane.services.controlplane.rules.diner;

import com.grubhub.garcon.controlplane.cassandra.models.DinerType;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.ResolveFlowRequest;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

import static com.grubhub.garcon.controlplane.cassandra.models.DinerType.NEW_DINER;

@RequiredArgsConstructor
public class NewDinerRule implements DinerRule {

    private final FlowConfig flowConfig;
    public static DinerRule match(FlowConfig flowConfig) {
        return new NewDinerRule(flowConfig);
    }

    @Override
    public Optional<DinerType> applicableTo(ResolveFlowRequest resolveFlowRequest, Integer dinerTypeOrdersThreshold) {
        return Optional.of(resolveFlowRequest)
                .map(ResolveFlowRequest::getTotalOrders)
                .filter(totalOrders -> totalOrders <= getTotalOrdersMax(dinerTypeOrdersThreshold, flowConfig.getTotalOrdersMax()))
                .map(any -> NEW_DINER);
    }
}

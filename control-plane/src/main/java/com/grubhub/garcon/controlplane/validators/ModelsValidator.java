package com.grubhub.garcon.controlplane.validators;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.service.ensembler.AliasService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.EnsembleService;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Stream;
import lombok.RequiredArgsConstructor;
import lombok.val;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class ModelsValidator {

    private final EnsembleService ensembleService;
    private final AliasService aliasService;

    public EnsembleDTO validateModels(EnsembleDTO ensemble) {
        val invalidModels = checkEnsemble(ensemble, HashMap.empty()).get(Boolean.FALSE).getOrElse(List::empty);
        if (invalidModels.nonEmpty()) {
            throw new RuntimeException(
                    String.format("For ensemble %s the following models/ensembles are not found in database %s",
                        ensemble.getEnsembleName(),
                        invalidModels
                    )
            );
        }
        return ensemble;
    }

    private Map<Boolean, List<String>> checkEnsemble(EnsembleDTO ensemble, Map<Boolean, List<String>> accumulator) {
        val models = ensembleService.selectModelsWithNamesIn(ensemble.getModels());
        val aliasesForModelsOfEnsemble = aliasService.getAliasesAndModelsForEnsemble(ensemble);
        val modelsWithAliases = ensembleService.selectModelsWithNamesIn(aliasesForModelsOfEnsemble.values().toSet());

        for (String modelName : ensemble.getModels()) {
            if (containsModelName(modelName, Stream.concat(models, modelsWithAliases), aliasesForModelsOfEnsemble)) {
                accumulator = accumulator.computeIfAbsent(Boolean.TRUE, key -> List.of(modelName))._2();
            } else {
                val retrievedEnsemble = ensembleService.getEnsemble(modelName);
                //workaround for fixing tests that are failing with Stack Overflow Error
                return retrievedEnsemble.isDefined()
                        ? checkEnsemble(retrievedEnsemble.get(), accumulator)
                        : accumulator.computeIfAbsent(Boolean.FALSE, key -> List.of(modelName))._2();
            }
        }
        return accumulator;
    }

    private boolean containsModelName(String modelOrAlias,
                                      Stream<ModelDTO> models,
                                      Map<String, String> aliasesForModelsOfEnsemble) {

        return models
                .map(ModelDTO::getModelName)
                .peek(System.out::println)
                .find(modelName -> modelName.equals(modelOrAlias)
                        || aliasesForModelsOfEnsemble.get(modelOrAlias).map(modelName::equals).getOrElse(false))
                .isDefined();
    }
}

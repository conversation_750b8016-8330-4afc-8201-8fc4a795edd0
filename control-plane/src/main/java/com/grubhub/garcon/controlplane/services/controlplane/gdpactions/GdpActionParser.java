package com.grubhub.garcon.controlplane.services.controlplane.gdpactions;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionDTO;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;

public class GdpActionParser {

    private static final ObjectMapper OBJECT_MAPPER = ObjectMapperHelper.INSTANCE;

    public static <T> T loadActionContent(GdpActionDTO action, Class<T> valueType) {
        try {
            return OBJECT_MAPPER.readValue(action.getActionContent(), valueType);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Error loading json action content from actionName=%s, type=%s, s3Path=%s, content=%s",
                    action.getActionName(), action.getActionType(), action.getFilePath(), action.getActionContent()), e);
        }
    }

}

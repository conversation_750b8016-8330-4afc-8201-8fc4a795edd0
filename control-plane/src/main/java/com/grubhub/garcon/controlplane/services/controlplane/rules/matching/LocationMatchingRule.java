package com.grubhub.garcon.controlplane.services.controlplane.rules.matching;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplane.services.controlplane.rules.actions.MatchingAction;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;

import java.util.Objects;
import java.util.Optional;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class LocationMatchingRule implements MatchingRule, MatchingAction {

    private final Runnable action;

    public static LocationMatchingRule match(Runnable action) {
        return new LocationMatchingRule(action);
    }

    @Override
    public Optional<MatchingAction> applicableTo(FlowDTO flow) {
        return Optional.of(flow)
                .filter(MatchingRule::isMatchingStrategyEqualToLocation)
                .filter(this::marketsNonNullOrEmpty)
                .map(any -> this);
    }

    private boolean marketsNonNullOrEmpty(FlowDTO flow) {
        return Objects.nonNull(flow.getLocationMarkets()) && !flow.getLocationMarkets().isEmpty();
    }

    @Override
    public void apply() {
        action.run();
    }
}

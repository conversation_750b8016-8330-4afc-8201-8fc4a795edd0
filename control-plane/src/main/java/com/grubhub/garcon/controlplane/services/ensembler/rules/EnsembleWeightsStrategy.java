package com.grubhub.garcon.controlplane.services.ensembler.rules;

import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EnsembleInvocationRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.vavr.Tuple2;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import lombok.val;

import java.util.concurrent.ConcurrentMap;

import static com.grubhub.garcon.controlplane.metrics.Spans.convertTagsMapToList;
import static com.grubhub.garcon.controlplane.metrics.Spans.span;

public class EnsembleWeightsStrategy implements EnsembleStrategy {
    private final EnsembleDTO ensemble;
    private final EnsembleInvocationRequest ensembleInvocationRequest;
    private final Timer combineResultWeightsTimer;
    private static final String COMBINE_WEIGHTS = "applyStrategyWeightsCombineResults";
    public static final String WEIGHT = "weight";
    public static final String WEIGHTS = "weights";

    public EnsembleWeightsStrategy(final EnsembleDTO ensemble,
                                   final EnsembleInvocationRequest ensembleInvocationRequest,
                                   final MeterRegistry meterRegistry) {
        this.ensemble = ensemble;
        this.ensembleInvocationRequest = ensembleInvocationRequest;
        this.combineResultWeightsTimer = meterRegistry.timer(Metrics.name(getClass(), COMBINE_WEIGHTS), convertTagsMapToList(getTagsForSpan()));
    }

    @Override
    public List<Float> applyStrategy(ConcurrentMap<String, ModelInferenceOutput> modelResults) {
        return span(COMBINE_WEIGHTS, getTagsForSpan(), () -> combineResultWeightsTimer.record(() -> this.applyStrategyInternal(modelResults)));
    }

    private List<Float> applyStrategyInternal(ConcurrentMap<String, ModelInferenceOutput> modelResults) {

        Map<String, Float> strategyFields = getEnsembleWeightsForInput(ensembleInvocationRequest, ensemble);

        val output = HashMap.ofAll(modelResults).toStream()
                .map(singleModelResult -> new Tuple2<>(singleModelResult._1, singleModelResult._2.getFloatResponse()))
                .map(ensembleWeightEntity -> strategyFields.get(ensembleWeightEntity._1)
                        .map(ensembleWeight -> new Tuple2<>(ensembleWeight, ensembleWeightEntity._2))
                        .getOrElseThrow(() -> new RuntimeException(String.format("Ensemble weight=%s was not found for ensemble=%s",
                                ensembleWeightEntity._1, ensembleInvocationRequest.getEnsembleName())))
                )
                .map(tuple -> tuple._2.toStream().map(score -> score * tuple._1).collect(List.collector()))
                .collect(List.collector());

        return computeLinearCombination(output);
    }

    private List<Float> computeLinearCombination(List<List<Float>> output) {
        return Option.of(output)
                .filter(list -> !list.isEmpty())
                .map(this::sameIndexSumOnInnerLists)
                .getOrElse(List::empty);
    }

    private List<Float> sameIndexSumOnInnerLists(List<List<Float>> list) {
        return Stream.range(0, list.get(0).size())
                .map(index -> list.toStream().map(innerList -> innerList.get(index)).sum())
                .map(Number::floatValue)
                .collect(List.collector());
    }

    public static Map<String, Float> getEnsembleWeightsForInput(EnsembleInvocationRequest ensembleInvocationRequest, EnsembleDTO ensemble) {
        try {
            return ensemble.getEnsembleWeights(ensembleInvocationRequest.getEnsembleWeight());
        } catch (Exception e) {
            throw new RuntimeException(String.format("Internal ensemble=%s doesn't have weight=%s, request_ensemble=%s",
                    ensemble.getEnsembleName(),
                    ensembleInvocationRequest.getEnsembleWeight(),
                    ensembleInvocationRequest.getEnsembleName())
            );
        }
    }

    private java.util.Map<String, String> getTagsForSpan() {
        return new java.util.HashMap<>() {{
            put("ensemble_name", ensembleInvocationRequest.getEnsembleName());
            put("ensemble_version", ensemble.getVersion());
            put("ensemble_weight", ensembleInvocationRequest.getEnsembleWeight());
        }};
    }
}

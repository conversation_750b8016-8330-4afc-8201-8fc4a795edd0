package com.grubhub.garcon.controlplane.services.ensembler.rules;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.modelinteraction.computablefeatures.BaseFunctionFeature;
import io.vavr.Tuple2;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.*;

@With
@Getter
@AllArgsConstructor
public class FunctionFeatureWithSamples {
    private FeatureDTO featureDTO;
    private BaseFunctionFeature function;
    private ModelDTO modelDTO;
    private List<Map<String, Object>> samples;

    public java.util.Map<String, String> getFunctionInputs() {
        return featureDTO.getFunctionInputs();
    }

    public java.util.Map<String, String> getFunctionOutputs() {
        return featureDTO.getFunctionOutputs();
    }

    public List<Map<String, Object>> calculateFunctionForAllSamples() {
        return samples.map(sample -> {
            val resolvedFunctionInputs = resolveFunctionInputs(sample);
            val functionResults = computeFunctionForInputs(resolvedFunctionInputs);
            val updatedFunctionResults = updateOutputKeys(functionResults, getFunctionOutputs());
            return updatedFunctionResults.merge(sample);
        }).collect(List.collector());
    }

    private Map<String, Object> resolveFunctionInputs(Map<String, Object> sample) {
        return getFunctionInputs().entrySet()
                .stream()
                .map(entry -> new Tuple2<>(entry.getKey(),
                        wildCardLookup(sample, entry.getKey(), entry.getValue())))
                .collect(HashMap.collector());
    }

    private Object wildCardLookup(Map<String, Object> sample, String key, String value) {
        if (value == null) {
            throw new RuntimeException(String.format("Feature=%s with function type=%s for model name=%s contains input key=%s with value of null.",
                    featureDTO.getFeatureName(), function.getFunctionType(), modelDTO.getModelName(), key));
        }

        if (value.startsWith("$")) {
            return sample.get(value.substring(1)).getOrElseThrow(() -> new RuntimeException(
                    String.format("Unable to find key=%s in sample=%s when calculating feature with name=%s and function type=%s for the model with name=%s.",
                            value.substring(1), sample, featureDTO.getFeatureName(), function.getFunctionType(), modelDTO.getModelName())));
        }
        return value;
    }

    private Map<String, Object> updateOutputKeys(Map<String, Object> functionResults, java.util.Map<String, String> functionOutputs) {
        return functionOutputs.keySet().stream()
                .map(key -> {
                    val resultOp = functionResults.get(key);
                    if (resultOp.isDefined()) {
                        var resultValue = resultOp.get();
                        return new Tuple2<>(functionOutputs.get(key), resultValue);
                    } else {
                        throw new RuntimeException(String.format(
                                "Unable to find key=%s in the output of the function with type=%s, for the feature=%s in model name=%s.",
                                key, function.getFunctionType(), featureDTO.getFeatureName(), modelDTO.getModelName()));
                    }
                }).collect(HashMap.collector());
    }

    private Map<String, Object> computeFunctionForInputs(Map<String, Object> functionInputs) {
        return function.compute(functionInputs);
    }
}

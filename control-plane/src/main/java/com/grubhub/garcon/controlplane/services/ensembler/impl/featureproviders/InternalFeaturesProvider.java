package com.grubhub.garcon.controlplane.services.ensembler.impl.featureproviders;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.ensembler.dto.EnrichedModelInferenceRequest;
import io.vavr.collection.Stream;
import lombok.Value;

@Value
public class InternalFeaturesProvider implements FeaturesProvider {
    EnrichedModelInferenceRequest modelInferenceRequest;

    @Override
    public Stream<FeatureDTO> provideFeatures() {
        return modelInferenceRequest.getSortedFeaturesForModel()
                .filter(FeatureDTO::isInternal)
                .filter(feature -> (feature.getSkipFetching() == null) || !feature.getSkipFetching());
    }
}

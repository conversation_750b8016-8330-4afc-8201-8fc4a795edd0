package com.grubhub.garcon.controlplane.services.ensembler.impl;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.custom.OptionalModel;
import com.grubhub.garcon.controlplane.custom.OptionalModelDTO;
import com.grubhub.garcon.controlplane.eventhub.ControlPlaneLogger;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.services.common.service.CachedConfigurationService;
import com.grubhub.garcon.controlplane.services.common.utils.LifeCycleUtils;
import com.grubhub.garcon.controlplane.services.controlplane.factories.ModelInferenceSequenceItemStreamFactory;
import com.grubhub.garcon.controlplane.services.ensembler.FeatureValueService;
import com.grubhub.garcon.controlplane.services.ensembler.impl.lifecycle.AliasSwapper;
import com.grubhub.garcon.controlplane.services.ensembler.impl.modelinvocation.RealModelInferenceInvocation;
import com.grubhub.garcon.controlplane.services.ensembler.impl.modelinvocation.TestModelInferenceInvocation;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplane.util.SessionAttributeProvider;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.EntityCollectionDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.RoutingGroupResponseDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EntitiesAliasDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.IndexesStatusDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceItem;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInvocationDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelTestInvocationStatusDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelWithInvocationStatus;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.TensorFlowModelMetadataDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.TensorFlowModelStatusDTO;
import com.grubhub.garcon.controlplaneapi.service.ensembler.AliasService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.EnsembleService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import com.grubhub.garcon.ensembler.cassandra.dao.EntityCollectionDao;
import com.grubhub.garcon.ensembler.cassandra.dao.ModelDao;
import com.grubhub.garcon.ensembler.cassandra.dao.ModelFeatureDao;
import com.grubhub.garcon.ensembler.cassandra.dao.ModelOutputDao;
import com.grubhub.garcon.ensembler.cassandra.dao.ModelTestInvocationStatusDao;
import com.grubhub.garcon.ensembler.cassandra.models.EntityCollection;
import com.grubhub.garcon.ensembler.cassandra.models.Model;
import com.grubhub.garcon.ensembler.cassandra.models.ModelFeature;
import com.grubhub.garcon.ensembler.cassandra.models.ModelOutput;
import com.grubhub.garcon.ensembler.cassandra.models.ModelTestInvocationStatus;
import com.grubhub.garcon.grpc.HeartbeatConfig;
import com.grubhub.garcon.modelinteraction.ModelInferenceClient;
import com.grubhub.garcon.modelinteraction.adapter.SearchEmbeddingsAdapter;
import com.grubhub.garcon.modelinteraction.tensorflow.TensorFlowModelConfiguration;
import com.grubhub.roux.config.ServiceConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.vavr.collection.HashMap;
import io.vavr.collection.HashSet;
import io.vavr.collection.LinkedHashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Set;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.NonNull;
import lombok.Value;
import lombok.With;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;

import java.time.Duration;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.grubhub.garcon.controlplane.metrics.Spans.span;
import static com.grubhub.garcon.controlplane.services.common.utils.ValidationUtils.validateInput;
import static com.grubhub.garcon.controlplane.util.ControlPlaneUtils.safeInvocationWithSideEffects;
import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus.AVAILABLE;
import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus.DISABLED;
import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus.ENABLED;
import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus.LOADED;
import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus.PENDING;
import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus.UNAVAILABLE;
import static com.grubhub.garcon.controlplanerpc.model.ModelType.SEARCH_EMBEDDINGS;
import static com.grubhub.garcon.controlplanerpc.model.ModelType.TENSOR_FLOW;
import static com.grubhub.garcon.ensembler.cassandra.models.EntityCollection.Constants.COLLECTION_MODEL;
import static io.vavr.Predicates.not;
import static java.util.Collections.emptyMap;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

@Slf4j
public class ModelServiceImpl implements ModelService {

    private static final String INVOKE_MODEL_INFERENCE = "invokeModelInference";
    private static final String SUCCESS = "SUCCESS";
    private static final String ERROR = "ERROR";
    private static final String PENDING_STATUS = "PENDING";
    private static final String CREATE_MODEL_MESSAGE = "Creating new model with";
    private static final String UPDATE_MODEL_MESSAGE = "Updating model with";
    private final ModelDao modelDao;
    private final AliasService aliasService;
    private final ModelFeatureDao modelFeatureDao;
    private final ControlPlaneLogger controlPlaneLogger;
    private final ControlPlaneMapper controlPlaneMapper;
    private final EnsembleService ensembleService;
    private final EntityCollectionDao entityCollectionDao;
    private final ModelInferenceClient client;
    private final FeatureValueService featureValueService;
    private final ModelOutputDao modelOutputDao;
    private final SessionAttributeProvider sessionAttributeProvider;
    private final MeterRegistry meterRegistry;
    private final TensorFlowModelConfiguration tensorFlowModelConfiguration;
    private final CachedConfigurationService cachedConfigurationService;
    private final FlowConfig flowConfig;
    private final SearchEmbeddingsAdapter searchEmbeddingsAdapter;
    private final ServiceConfig svcConfig;
    private final ModelTestInvocationStatusDao modelTestInvocationStatusDao;
    private final MetricService metricService;
    private final HeartbeatConfig heartbeatConfig;
    private final ModelInferenceSequenceItemStreamFactory modelInferenceSequenceItemStreamFactory;
    private final AliasSwapper aliasSwapper;

    @Inject
    public ModelServiceImpl(ModelDao modelDao,
                            AliasService aliasService,
                            ModelFeatureDao modelFeatureDao,
                            ControlPlaneLogger controlPlaneLogger,
                            ControlPlaneMapper controlPlaneMapper,
                            EnsembleService ensembleService,
                            EntityCollectionDao entityCollectionDao,
                            ModelInferenceClient client,
                            @Named("featureValueServiceWithCache") FeatureValueService featureValueService,
                            ModelOutputDao modelOutputDao,
                            SessionAttributeProvider sessionAttributeProvider,
                            MeterRegistry meterRegistry,
                            TensorFlowModelConfiguration tensorFlowModelConfiguration,
                            CachedConfigurationService cachedConfigurationService,
                            FlowConfig flowConfig,
                            SearchEmbeddingsAdapter searchEmbeddingsAdapter,
                            ServiceConfig serviceConfig,
                            ModelTestInvocationStatusDao modelTestInvocationStatusDao,
                            MetricService metricService,
                            HeartbeatConfig heartbeatConfig,
                            ModelInferenceSequenceItemStreamFactory modelInferenceSequenceItemStreamFactory,
                            AliasSwapper aliasSwapper) {
        this.modelDao = modelDao;
        this.aliasService = aliasService;
        this.modelFeatureDao = modelFeatureDao;
        this.controlPlaneLogger = controlPlaneLogger;
        this.controlPlaneMapper = controlPlaneMapper;
        this.ensembleService = ensembleService;
        this.entityCollectionDao = entityCollectionDao;
        this.client = client;
        this.featureValueService = featureValueService;
        this.modelOutputDao = modelOutputDao;
        this.sessionAttributeProvider = sessionAttributeProvider;
        this.meterRegistry = meterRegistry;
        this.tensorFlowModelConfiguration = tensorFlowModelConfiguration;
        this.cachedConfigurationService = cachedConfigurationService;
        this.flowConfig = flowConfig;
        this.searchEmbeddingsAdapter = searchEmbeddingsAdapter;
        this.svcConfig = serviceConfig;
        this.modelTestInvocationStatusDao = modelTestInvocationStatusDao;
        this.metricService = metricService;
        this.heartbeatConfig = heartbeatConfig;
        this.modelInferenceSequenceItemStreamFactory = modelInferenceSequenceItemStreamFactory;
        this.aliasSwapper = aliasSwapper;
    }

    @Override
    public Option<ModelDTO> getModel(@NonNull String modelName) {
        return Option.ofOptional(cachedConfigurationService.getModelAsync(modelName).join());
    }

    @Override
    public CompletableFuture<Option<ModelDTO>> getModelAsync(String modelName) {
        return cachedConfigurationService.getModelAsync(modelName).thenApply(Option::ofOptional);
    }

    @Override
    public Option<ModelDTO> getModelWithoutCache(String modelName) {
        return getModelAsyncWithoutCache(modelName).join();
    }

    // TODO(wcase): Make this async again after validating casserole 4 migration
    @Override
    public CompletableFuture<Option<ModelDTO>> getModelAsyncWithoutCache(String modelName) {
        Optional<Model> model = modelDao.select(modelName);
        if (model.isEmpty()) {
            return CompletableFuture.completedFuture(Option.none());
        }
        java.util.List<ModelFeature> modelFeatures = modelFeatureDao.selectAll(model.get().getModelName());
        List<FeatureDTO> modelFeatureDTOs = modelFeatures.stream()
                .map(controlPlaneMapper::toDTO)
                .collect(List.collector());

        java.util.List<ModelOutput> modelOutputs = modelOutputDao.selectAll(model.get().getModelName());
        List<ModelOutputDTO> modelOutputDTOs = modelOutputs.stream()
                .map(controlPlaneMapper::toDTO)
                .collect(List.collector());

        ModelDTO modelDTO = controlPlaneMapper.toDTO(model.get(), modelFeatureDTOs, modelOutputDTOs);
        return CompletableFuture.completedFuture(Option.of(modelDTO));
    }

    @Override
    public void configureModelsByRegion() {
        log.debug("Begin configuring models by region");
        Try.run(this::cleanAllCacheIfFigEnabled)
                .map(any -> tryToConfigureModelsInTF())
                .andThen(this::configureSearchEmbeddingsModelsAndAppend);


        if (tensorFlowModelConfiguration.modelTestInvocationEnabled()) {
            testModelInferenceInternal(ModelWithInvocationStatus::hasEmptyStatusPendingOrError)
                    .forEach(this::saveModelTestInvocation);
        }
        log.debug("Finished configuring models by region");
    }

    private void configureSearchEmbeddingsModelsAndAppend(List<ModelDTO> tensorFlowModels) {
        val searchEmbeddingsModels = tryToConfigureSearchEmbeddingsModels();
        log.debug("Successfully configured search embeddings models={}", searchEmbeddingsModels.map(ModelDTO::getModelName).toList());
        updateTensorFlowModelsIfNeeded(tensorFlowModels.appendAll(searchEmbeddingsModels));
    }

    public List<ModelInvocationDTO> testModelInference() {
        return testModelInferenceInternal(modelWithInvocationStatus -> true)
                .map(ModelWithInvocationStatus::toPartialModelInvocation)
                .map(partialModelInvocation ->
                        partialModelInvocation.totalNumberOfInvocations(tensorFlowModelConfiguration.getConfigureModelsInvocationsNumber()).build())
                .collect(List.collector());
    }

    public List<ModelWithInvocationStatus> testModelInferenceInternal(Predicate<ModelWithInvocationStatus> invocationFilter) {
        val modelsWithInferenceStatus = getModelsWithStatusIn(LOADED, AVAILABLE, ENABLED)
                .filter(not(ModelDTO::isModelTypeTensorFlowAndLoaded))
                .map(this::fetchTestInvocationStatus)
                .filter(invocationFilter)
                .map(this::enrichWithModelInferenceResult);

        logNotReadyModels(modelsWithInferenceStatus);
        logAvailableModels(modelsWithInferenceStatus);

        return List.ofAll(modelsWithInferenceStatus);
    }

    private void logAvailableModels(List<ModelWithInvocationStatus> modelsWithInferenceStatus) {
        log.debug("The following models are available, models={}",
                modelsWithInferenceStatus
                        .filter(ModelWithInvocationStatus::isInternalModelInferenceSuccessful)
                        .map(ModelWithInvocationStatus::getModel)
                        .map(ModelDTO::getModelName));
    }

    private void logNotReadyModels(List<ModelWithInvocationStatus> modelsWithInferenceStatus) {
        if (!modelsWithInferenceStatus.isEmpty()) {
            log.warn("The following models are NOT ready, models={}",
                    modelsWithInferenceStatus.filter(not(ModelWithInvocationStatus::isInternalModelInferenceSuccessful))
                            .map(ModelWithInvocationStatus::getModel)
                            .map(ModelDTO::getModelName));
        }
    }

    private void saveModelTestInvocation(ModelWithInvocationStatus modelWitStatus) {
        val modelTestInvocationStatusBuilder = ModelTestInvocationStatus.builder()
                .region(svcConfig.getDeployment().getDataCenter())
                .modelName(modelWitStatus.getModel().getModelName())
                .status(modelWitStatus.isInternalModelInferenceSuccessful() ? SUCCESS : ERROR)
                .build();

        modelTestInvocationStatusDao.insert(modelTestInvocationStatusBuilder);

    }

    private ModelWithInvocationStatus enrichWithModelInferenceResult(ModelWithInvocationStatus modelWithInvocationStatus) {
        boolean isInternalModelInferenceSuccessful = internalModelInferenceMultiOutputInvocation(modelWithInvocationStatus.getModel());
        return modelWithInvocationStatus
                .withInternalModelInferenceSuccessful(isInternalModelInferenceSuccessful);
    }

    private ModelWithInvocationStatus fetchTestInvocationStatus(ModelDTO model) {
        return successStatusOrNull(model)
                .map(controlPlaneMapper::toDTO)
                .map(modelTestInvocationStatus -> ModelWithInvocationStatus.builder()
                        .modelTestInvocationStatus(modelTestInvocationStatus)
                        .model(model)
                        .build())
                .getOrElse(fallbackModelWithInvocationStatus(model));
    }

    private ModelWithInvocationStatus fallbackModelWithInvocationStatus(ModelDTO model) {
        return ModelWithInvocationStatus.builder()
                .modelTestInvocationStatus(ModelTestInvocationStatusDTO.builder()
                        .status(PENDING_STATUS)
                        .modelName(model.getModelName())
                        .region(svcConfig.getDeployment().getDataCenter())
                        .build())
                .model(model)
                .build();
    }

    private Option<ModelTestInvocationStatus> successStatusOrNull(ModelDTO model) {
        String dataCenter = svcConfig.getDeployment().getDataCenter();
        return modelTestInvocationStatusDao.select(model.getModelName(), dataCenter);
    }

    public List<ModelDTO> tryToConfigureSearchEmbeddingsModels() {
        val searchEmbeddingsModels = filteredModelsByTypeAndStatus(SEARCH_EMBEDDINGS, ENABLED, LOADED, UNAVAILABLE, AVAILABLE);
        log.debug("Trying to configure search embeddings models={}", searchEmbeddingsModels.map(ModelDTO::getModelName).toList());
        return Try.run(() -> client.configureSearchEmbeddingModels(searchEmbeddingsModels))
                .onFailure(e -> log.error("Error configuring search embeddings models search_embeddings_models={}", searchEmbeddingsModels, e))
                .map(any -> searchEmbeddingsModels)
                .getOrElse(List.empty());

    }

    public List<ModelDTO> tryToConfigureModelsInTF() {
        List<ModelDTO> tensorFlowModels = tensorFlowModelsEligibleForConfiguration();
        log.debug("Trying to configure models in TF with models={}", tensorFlowModels.map(ModelDTO::getModelName).collect(Collectors.toList()));
        return Try.of(() -> client.configureTensorFlow(tensorFlowModels))
                .onSuccess(any -> log.debug("Finish configuring tensor flow models"))
                .onFailure(e -> log.error("Error configuring tensor flow models tensor_flow_models={}", tensorFlowModels, e))
                .map(ignore -> tensorFlowModels)
                .getOrElse(List.empty());
    }

    private void cleanAllCacheIfFigEnabled() {
        if (flowConfig.getDdmlConfigurationCleanDuringConfigureModels()) {
            log.debug("Cleaning cache during regional model configuration");
            cachedConfigurationService.cleanAllCache("configure_models_region");
        }
        log.debug("Skipping cache clean during regional model configuration");
    }


    @Override
    public void configureModelsGlobal() {
        configureModels();
        configureEnsembles();
    }

    private void configureModels() {
        log.info("Starting configuring models.");
        val loadedOrAvailableModels = getModelsWithStatusIn(LOADED, AVAILABLE);

        log.debug("Configuring models: {}", loadedOrAvailableModels.map(ModelDTO::getModelName)
                .collect(Collectors.joining(";")));

        Try.run(() -> updateDynamicModels(loadedOrAvailableModels))
                .onFailure(e -> log.error("Error updating dynamic models for models={}", loadedOrAvailableModels, e))
                .andThen(this::retainConfigurableNumberOfModels)
                .onFailure(e -> log.error("Error retaining models for models={}", loadedOrAvailableModels, e));

        val allDisabledModels = getAllModelsWithStatus(DISABLED.getName());
        deleteOldDisabledModels(allDisabledModels);
    }

    private void configureEnsembles() {
        log.info("Starting configuring ensembles.");
        val pendingEnsembles = ensembleService.getAllWithStatus(PENDING.getName());

        Try.run(() -> updateDynamicEnsembles(pendingEnsembles))
                .onFailure(e -> log.error("Error updating ensembles for ensembles={}", pendingEnsembles, e))
                .andThen(this::checkForStaleEnsemblesIfNeeded)
                .onFailure(e -> log.error("Error checking for stale ensembles for ensembles={}", pendingEnsembles, e))
                .andThen(this::retainConfigurableNumberOfEnsembles)
                .onFailure(e -> log.error("Error retaining ensembles.", e));
    }

    private List<ModelDTO> tensorFlowModelsEligibleForConfiguration() {
        val tensorFlowModels = filteredModelsByTypeAndStatus(TENSOR_FLOW, ENABLED, LOADED, UNAVAILABLE, AVAILABLE);
        log.info("Total models of type=TENSOR_FLOW to be configured={}", tensorFlowModels.size());
        return tensorFlowModels;
    }

    public void deleteOldDisabledModels(List<ModelDTO> disabledModels) {
        val ageLimitForModels = flowConfig.getDeleteModelsOlderThanDays();
        try {
            disabledModels
                    .filter(model -> Objects.nonNull(model.getUpdatedTimestamp()) && DISABLED.getName().equals(model.getStatus()))
                    .filter(model -> model.isModelOlderThan(ageLimitForModels))
                    .forEach(model -> {
                        log.info("Deleting disabled_model={} because it has not been updated within the last age_limit={} days. Updated timestamp={}",
                                model.getModelName(), ageLimitForModels, model.getUpdatedTimestamp());
                        deleteModel(model);
                    });
        } catch (Exception e) {
            log.error("Error deleting disabled_models={} that have not been updated within the last age_limit={} days",
                    disabledModels, ageLimitForModels);
        }
    }

    private void retainConfigurableNumberOfEnsembles() {
        log.info("Checking if some ensembles need to be deleted. Maintaining the top {} from each versioning name.",
                tensorFlowModelConfiguration.getMaxDynamicEnsemblesPerVersioning());

        sortEnabledEnsemblesAndGroupByVersion()
                .values()
                .flatMap(this::disableExcludedEnsembles)
                .forEach(ensembleService::updateEnsemble);
    }

    /**
     * Change this implementation, see comments below.
     */
    private Stream<EnsembleDTO> disableExcludedEnsembles(List<EnsembleDTO> ensembles) {
        // 1. Check it doesn't have an alias assigned to it.
        // 2. Update ensemble with the disabled status.
        // 3. Extract this logic to a different class (so then we can reuse this logic for models).
        log.info("Maintaining the top {} from ensembles={}",
                tensorFlowModelConfiguration.getMaxDynamicEnsemblesPerVersioning(), ensembles.map(EnsembleDTO::getEnsembleName));

        return ensembles
                .toStream()
                .takeRight(ensembles.size() - tensorFlowModelConfiguration.getMaxDynamicEnsemblesPerVersioning())
                .map(ensemble -> {
                    ensemble.setStatus(DISABLED.getName());
                    logOldDynamicEnsembles(ensembles, ensemble);
                    return ensemble;
                });
    }

    @VisibleForTesting
    Map<String, List<EnsembleDTO>> sortEnabledEnsemblesAndGroupByVersion() {
        val ensemblesByVersioningName = ensembleService
                .getAllWithStatus(ENABLED.getName())
                .filter(EnsembleDTO::isDynamicEnsemble)
                .filter(ensembleDTO -> ensembleDTO.getVersioningEnsembleName() != null)
                .collect(Collectors.groupingBy(
                                EnsembleDTO::getVersioningEnsembleName,
                                mapping(Function.identity(), collectingAndThen(List.collector(), LifeCycleUtils::sortEnsembleListDescendingByVersion))
                        )
                );

        log.info("Dynamic ensembles by versioning name={}", ensemblesByVersioningName.keySet());
        return LinkedHashMap.ofAll(ensemblesByVersioningName);
    }

    private void logOldDynamicEnsembles(List<EnsembleDTO> ensembles, EnsembleDTO ensemble) {
        log.info("Disabling old dynamic" +
                 " ensemble={}," +
                 " version={}," +
                 " versioning_ensemble_name={}," +
                 " maxDynamicModelsPerVersioning={}," +
                 " totalEnabledEnsembles={}",
                ensemble.getEnsembleName(),
                ensemble.getVersion(),
                ensemble.getVersioningEnsembleName(),
                tensorFlowModelConfiguration.getMaxDynamicEnsemblesPerVersioning(),
                ensembles.take(tensorFlowModelConfiguration.getMaxDynamicEnsemblesPerVersioning()).size()
        );
    }

    private void retainConfigurableNumberOfModels() {
        sortEnabledModelsAndGroupByVersion()
                .values()
                .flatMap(this::disableTheOldestModels)
                .forEach(this::updateModel);
    }

    private Stream<ModelDTO> disableTheOldestModels(List<ModelDTO> models) {
        Integer maxDynamicModels = computeMaxDynamicModels(models);

        return models
                .toStream()
                .takeRight(models.size() - maxDynamicModels)
                .filter(model -> shouldModelBeDisabled(models, model))
                .map(model -> {
                    model.setStatus(DISABLED.getName());
                    logOldDisabledModels(models, model);
                    return model;
                });
    }

    private boolean shouldModelBeDisabled(List<ModelDTO> models, ModelDTO model) {
        String latestAliasEntityId = getAliasEntityId(model.getLatestAlias());
        String previousAliasEntityId = getAliasEntityId(model.getPreviousAlias());

        String modelName = model.getModelName();
        if (modelName.equals(latestAliasEntityId) || modelName.equals(previousAliasEntityId)) {
            logSkipDisablingOldModels(models, model);
            return false;
        }
        return true;
    }

    private String getAliasEntityId(final String alias) {
        return getAlias(alias)
                .map(EntitiesAliasDTO::getEntityId)
                .getOrElse(StringUtils.EMPTY);
    }

    private Integer computeMaxDynamicModels(List<ModelDTO> models) {
        return models
                .headOption()
                .map(ModelDTO::getMaxDynamicModels)
                .filter(Objects::nonNull)
                .getOrElse(tensorFlowModelConfiguration::getMaxDynamicModelsPerVersioning);
    }

    @VisibleForTesting
    Map<String, List<ModelDTO>> sortEnabledModelsAndGroupByVersion() {

        val modelsByVersioningName = getModelsWithStatusIn(ENABLED)
                .filter(ModelDTO::isDynamicModel)
                .filter(model -> Objects.nonNull(model.getVersioningModelName()))
                .collect(Collectors.groupingBy(
                                ModelDTO::getVersioningModelName,
                        collectingAndThen(List.collector(), LifeCycleUtils::sortModelListDescendingByVersion))
                );
        return LinkedHashMap.ofAll(modelsByVersioningName);
    }

    private void logSkipDisablingOldModels(List<ModelDTO> models, ModelDTO model) {
        log.warn("Skipping disable of old dynamic" +
                        " model={}," +
                        " version={}," +
                        " versioning_model_name={}," +
                        " maxDynamicModelsPerVersioning={}," +
                        " totalEnabledModels={}," +
                        " model still has active alias.",
                model.getModelName(),
                model.getVersion(),
                model.getVersioningModelName(),
                tensorFlowModelConfiguration.getMaxDynamicModelsPerVersioning(),
                models.take(tensorFlowModelConfiguration.getMaxDynamicModelsPerVersioning())
                        .size()
        );
    }

    private void logOldDisabledModels(List<ModelDTO> models, ModelDTO model) {
        log.info("Disabling old dynamic" +
                 " model={}," +
                 " version={}," +
                 " versioning_model_name={}," +
                 " maxDynamicModelsPerVersioning={}," +
                 " totalEnabledModels={}",
                model.getModelName(),
                model.getVersion(),
                model.getVersioningModelName(),
                tensorFlowModelConfiguration.getMaxDynamicModelsPerVersioning(),
                models.take(tensorFlowModelConfiguration.getMaxDynamicModelsPerVersioning())
                        .size()
        );
    }

    private void checkForStaleEnsembles() {
        log.info("Starting checking for stale ensembles.");
        ensembleService.getAllWithStatus(ENABLED.getName())
                .toStream()
                .forEach(ensemble -> checkUnderlyingModels(ensemble)
                        .onEmpty(() -> checkUnderlyingEnsembles(ensemble))); // TODO(anyone): why onEmpty? Why not checking both?
    }

    private Option<String> checkUnderlyingEnsembles(EnsembleDTO ensemble) {
        return ensemble.getModels()
                .toStream()
                .filter(modelName -> this.isEnsembleAndIsNotEnabled(ensemble, modelName))
                .peek(ignore -> ensemble.setStatus(DISABLED.getName()))
                .peek(anEnsemble -> log.error("Ensemble={} has become DISABLED because the underlying ensemble={} is not enabled",
                        ensemble.getEnsembleName(),
                        anEnsemble)
                )
                .peek(ignore -> ensembleService.updateEnsemble(ensemble))
                .toOption();
    }

    private boolean isEnsembleAndIsNotEnabled(EnsembleDTO ensemble, String modelName) {
        if (ensemble.getModelList().exists(model -> model.getModelName().equals(modelName))) {
            // It's a model, we do not need to check
            return false;
        }
        return ensembleService.getEnsemble(modelName)
                .map(EnsembleDTO::notEnabled)
                .getOrElse(true);
    }

    private Option<String> checkUnderlyingModels(EnsembleDTO ensemble) {
        return ensemble.getModels().toStream()
                .filter(modelName -> isModelAndIsNotEnabled(ensemble, modelName))
                .peek(ignore -> ensemble.setStatus(DISABLED.getName()))
                .peek(modelName -> log.error("Ensemble={} has become DISABLED because the underlying model={} is not enabled",
                                ensemble.getEnsembleName(),
                                modelName
                        )
                )
                .peek(ignore -> ensembleService.updateEnsemble(ensemble))
                .toOption();
    }

    private boolean isModelAndIsNotEnabled(EnsembleDTO ensemble, String modelName) {
        if (!ensemble.getModelList().exists(model -> model.getModelName().equals(modelName))) {
            // It's an ensemble, we do not need to check
            return false;
        }
        return ensemble.getModelList()
                .toStream()
                .find(model -> model.getModelName().equals(modelName))
                .filter(ModelDTO::notEnabled)
                .isDefined();
    }

    private void updateDynamicEnsembles(List<EnsembleDTO> pendingEnsembles) {
        log.info("Updating dynamic ensembles in PENDING status. Ensembles={}", pendingEnsembles);
        if (pendingEnsembles == null || pendingEnsembles.isEmpty()) {
            log.info("No pending ensembles to update. Skipping promotion logic.");
            return;
        }

        updateStatusIfNeeded(pendingEnsembles);
    }

    private void updateStatusIfNeeded(List<EnsembleDTO> pendingEnsembles) {
        try {
            List<EnsembleDTO> enabledEnsembles = pendingEnsembles
                    .filter(this::allModelsForEnsembleAreEnabled)
                    .filter(EnsembleDTO::isDynamicEnsemble)
                    .map(EnsembleDTO::enableEnsemble)
                    .map(this::logEnsembleStatus)
                    .peek(ensembleService::createOrUpdateEnsemble)
                    .map(EnsembleDTO::getEnsembleName)
                    .flatMap(ensembleService::getEnsemble);

            if (heartbeatConfig.getWaitToSwapAliases() && !enabledEnsembles.isEmpty()) {
                try {
                    Thread.sleep(heartbeatConfig.getDcpCacheTTLInMinutes() * 60000L);
                } catch (InterruptedException e) {
                    log.error("Thread interrupted while updating dynamic ensembles. Continuing with the process.");
                }
            }

            log.info("Enabled ensembles to update aliases={}", enabledEnsembles);
            enabledEnsembles.forEach(this::updateEnsembleAliasesWithoutException);
        } catch (RuntimeException e) {
            log.error("Failed to update status for one of the ensembles {}", pendingEnsembles.map(EnsembleDTO::getEnsembleName).intersperse(","), e);
        }
    }

    public void updateEnsembleAliasesWithoutException(EnsembleDTO newEnsemble) {
        safeInvocationWithSideEffects(
                () -> aliasSwapper.updateEnsembleAliases(newEnsemble),
                String.format("Failed to update model alias for ensemble, ensemble_name=%s", newEnsemble.getEnsembleName())
        );
    }

    private EnsembleDTO logEnsembleStatus(EnsembleDTO ensemble) {
        log.info("Ensemble={} is now {}", ensemble.getEnsembleName(), ensemble.getStatus());
        return ensemble;
    }

    private boolean allModelsForEnsembleAreEnabled(EnsembleDTO ensemble) {
        return ensemble.getModels().flatMap(this::getModel).forAll(ModelDTO::isEnabled);
    }

    private void updateTensorFlowModelsIfNeeded(List<ModelDTO> tensorFlowModels) {
        tensorFlowModels.toStream()
                .map(this::toModelWithStatus)
                .forEach(modelAndStatus -> modelAndStatus.checkStatus(this::updateModel));
    }

    private void updateDynamicModels(List<ModelDTO> models) {

        // TFS models need to be in status AVAILABLE
        val tfsAvailableModels = models
                .filter(m -> (m.isModelTypeTensorFlow() || m.isModelTypeSearchEmbeddings()) && AVAILABLE.isEqualTo(m));

        // All other model types can be in status LOADED
        val othersLoadedModels = models
                .filter(m -> !m.isModelTypeTensorFlow() && LOADED.isEqualTo(m));

        log.info("TFS-models with status=AVAILABLE={}, all other models with status LOADED={}",
                tfsAvailableModels.size(), othersLoadedModels.size());

        updateDynamicModelsCommon(tfsAvailableModels, TensorFlowModelConfiguration::getDynamicModelsAliasSwapEnabled);
        updateDynamicModelsCommon(othersLoadedModels, TensorFlowModelConfiguration::getDynamicModelsAliasSwapEnabled);
    }

    private void updateDynamicModelsCommon(List<ModelDTO> models, Predicate<TensorFlowModelConfiguration> aliasSwapEnabled) {
        List<ModelDTO> enabledModels = models.filter(this::validateModel)
                .map(this::enableModel)
                .filter(Option::isDefined)
                .map(Option::get)
                .peek(enabledModel -> log.info("Dynamic model={} is marked as ENABLED", enabledModel.getModelName()));

        if (heartbeatConfig.getWaitToSwapAliases() && !enabledModels.isEmpty()) {
            try {
                Thread.sleep(heartbeatConfig.getDcpCacheTTLInMinutes() * 60000L);
            } catch (InterruptedException e) {
                log.error("Thread interrupted while updating dynamic models. Continuing with the process.");
            }
        }

        // Obtaining the models again from database to double-check their structure and avoid consistency issues.
        List<ModelDTO> modelsToSwapAliases = enabledModels
                .map(modelDTO -> this.getModelWithoutCache(modelDTO.getModelName()))
                .map(Option::get)
                .filter(model -> isModelDefinitionValid(model) && model.isEnabled()) // Re-checking after fetching to serve only valid and enabled models.
                .toList();

        modelsToSwapAliases.forEach(model -> updateModelAliasIfNeeded(aliasSwapEnabled, model));
    }

    private boolean validateModel(ModelDTO model) {
        boolean isModelDefinitionValid = isModelDefinitionValid(model);
        boolean featureStatusValid = checkFeaturesStatus(model);
        boolean testInvocationValid = checkModelInferenceInvocationAcrossRegions(model);
        boolean isDynamicModel = model.isDynamicModel();

        boolean validModel = isModelDefinitionValid && featureStatusValid && testInvocationValid && isDynamicModel;

        log.info("Model={} is valid={}. Details - Model definition={}, feature status={}, test invocations={}, dynamic model={}",
                model.getModelName(), validModel, isModelDefinitionValid, featureStatusValid, testInvocationValid, isDynamicModel);

        if (!validModel) {
            log.error("Model={} is not valid, not enabling it.", model.getModelName());
        }
        return validModel;
    }

    private boolean checkFeaturesStatus(ModelDTO model) {
        boolean allModelFeaturesEnabled = model.getModelFeatures().forAll(FeatureDTO::isEnabled);

        log.info("All model features enabled for model={}, status={}", model.getModelName(), allModelFeaturesEnabled);

        return allModelFeaturesEnabled;
    }

    private boolean checkModelInferenceInvocationAcrossRegions(ModelDTO model) {
        if (tensorFlowModelConfiguration.modelTestInvocationEnabled()) {
            List<ModelTestInvocationStatus> statuses = modelTestInvocationStatusDao.fetchTestInvocationStatus(model.getModelName());
            log.info("Model inference invocations status per region for model model_name={}, status = {}", model.getModelName(), statuses);
            val regions = tensorFlowModelConfiguration.getModelRegionsAsStream().collect(List.collector());

            if (regions.isEmpty()) {
                log.error("Regions from configurations are empty when checking model={}", model.getModelName());
                return true;
            }

            boolean allStatusOK = regions
                    .forAll(region -> statuses.find(
                                    status -> status.getRegion().equals(region) && status.getStatus().equals(SUCCESS)
                            ).isDefined()
                    );

            log.info("All regions model inference invocations for model model_name={}, status={}", model.getModelName(), allStatusOK);
            return allStatusOK;
        }

        log.info("Model test invocation in all regions skipped for model={}. Returning OK and moving on.", model.getModelName());
        return true;
    }

    private boolean internalModelInferenceMultiOutputInvocation(ModelDTO model) {
        if (!tensorFlowModelConfiguration.modelTestInvocationEnabled()) {
            log.info("Skipping test model invocation when configuring model, model_name={}", model.getModelName());
            return true;
        }
        val successRatio = Stream.range(0, tensorFlowModelConfiguration.getConfigureModelsInvocationsNumber())
                .map(index -> invokeModelInferenceMultiOutputWithoutValidation(model))
                .map(t -> t.isSuccess() ? 1 : 0)
                .sum();

        log.info("Successful test model, model_name={}, invocations={}, total required={}",
                model.getModelName(),
                successRatio,
                tensorFlowModelConfiguration.getConfigureModelsInvocationsNumber()
        );

        return successRatio.intValue() == tensorFlowModelConfiguration.getConfigureModelsInvocationsNumber();
    }

    private Try<ModelInferenceOutput> invokeModelInferenceMultiOutputWithoutValidation(ModelDTO model) {
        return Try.of(() ->
                        toInternalModelInvocationStrategy(model)
                                .invokeModel(
                                        defaultModelInferenceRequest(model), Instant.now()
                                )
                )
                .filter(Objects::nonNull)
                .map(this::validateResponseAndReturn)
                .filter(not(ModelInferenceOutput::isResponseNullOrEmpty))
                .filter(not(ModelInferenceOutput::isSearchEmbeddingsTestInvocationWithMockedIndex))
                .onFailure(ex -> log.error("Failed to perform test invocation for model, model_name={}", model.getModelName(), ex));
    }

    private ModelInferenceOutput validateResponseAndReturn(ModelInferenceOutput response) {
        if (response.isResponseNullOrEmpty()) {
            log.warn("TFS response for model model_name={} is empty! Check the model definition for more details", response.getModelName());
        }
        if (response.isSearchEmbeddingsTestInvocationWithMockedIndex()) {
            log.warn("Response for Search Embeddings model model_name={} contains mocked indices!", response.getModelName());
        }
        return response;
    }

    private ModelInferenceRequest defaultModelInferenceRequest(ModelDTO model) {
        return ModelInferenceRequest.builder()
                .modelName(model.getModelName())
                .features(singletonList(emptyMap()))
                .build();
    }

    private TestModelInferenceInvocation toInternalModelInvocationStrategy(ModelDTO model) {
        return TestModelInferenceInvocation.builder()
                .model(model)
                .client(client)
                .flowConfig(flowConfig)
                .meterRegistry(meterRegistry)
                .featureValueService(featureValueService)
                .build();
    }

    void updateModelAliasIfNeeded(Predicate<TensorFlowModelConfiguration> aliasSwapEnabled, ModelDTO model) {
        if (aliasSwapEnabled.test(this.tensorFlowModelConfiguration)) {
            safeInvocationWithSideEffects(
                    () -> aliasSwapper.updateModelAliases(model),
                    String.format("Failed to update model alias for model, model_name=%s", model.getModelName())
            );
        } else {
            log.debug("New dynamic model_name={}, version={} is ready to update alias={}" +
                      " but FIG dynamicModelsAliasSwapEnabled is disabled", model.getModelName(), model.getVersion(), model.getLatestAlias());
        }
    }

    Option<ModelDTO> enableModel(ModelDTO model) {
        try {
            log.info("About to enable model={}", model);
            final ModelDTO enabledModel = model.enableModel();

            // We update the model status only, so we need to change the model definition.
            // The features statuses are already set as ENABLED by the ingestion job, when the data is loaded in the feature store.
            this.updateModelDefOnly(enabledModel);
            return Option.of(enabledModel);
        } catch (Exception e) {
            log.error("There have been an error enabling the model={}", model.getModelName(), e);
            return Option.none();
        }
    }

    private ModelAndTfStatus toModelWithStatus(ModelDTO model) {
        return model.isModelTypeTensorFlow()
                ? new ModelAndTfStatus(model, getTensorFlowModelStatus(model.getModelName()).getTfStatus())
                : new ModelAndTfStatus(model, client.getSearchEmbeddingStatus(model).getName());
    }

    private List<ModelDTO> filteredModelsByTypeAndStatus(ModelType modelType, ModelStatus... statuses) {
        return Stream.of(statuses)
                .map(ModelStatus::getName)
                .map(this::getAllModelsWithStatusFromDatabase)
                .flatMap(List::toStream)
                .filter(model -> modelType.isEqualTo(model.getModelType()))
                .collect(List.collector());
    }

    private List<ModelDTO> getModelsWithStatusIn(ModelStatus... statuses) {
        return Stream.of(statuses)
                .map(ModelStatus::getName)
                .map(this::getAllModelsWithStatusFromDatabase)
                .flatMap(List::toStream)
                .collect(List.collector());
    }

    @Override
    public void createOrUpdateModel(@NonNull ModelDTO modelDTO) {
        OptionalModel.of(Option.ofOptional(modelDao.select(modelDTO.getModelName())))
                .ifPresent(any -> updateModel(modelDTO))
                .ifNotPresent(() -> createNewModel(modelDTO));
    }

    private boolean isModelDefinitionValid(@NotNull ModelDTO modelDTO) {
        try {
            validateInput(modelDTO);

            if (!EnumUtils.isValidEnum(ModelType.class, modelDTO.getModelType())) {
                log.error("Failed to create or validate model_name={} with model_type={}, model type must be a valid type.",
                        modelDTO.getModelName(), modelDTO.getModelType());
                throw new RuntimeException(String.format("Failed to create or validate model_name=%s with model_type=%s, model type must be a valid type.",
                        modelDTO.getModelName(), modelDTO.getModelType()));
            }
            if (modelDTO.getModelName() == null) {
                log.error("Failed to create or validate model, model name cannot be null.");
                throw new RuntimeException("Failed to create or validate model, model name cannot be null.");
            }
            modelDTO.getModelFeatures()
                    .filter(feature -> feature.getFunctionName() != null && !feature.getFunctionName().isEmpty())
                    .forEach(feature -> {
                        if (!FunctionFeatureType.isValidFunctionType(feature.getFunctionName())) {
                            throw new RuntimeException(
                                    String.format("Failed to create or validate model_name=%s, feature name=%s contains an invalid function_name=%s.",
                                            modelDTO.getModelName(), feature.getFeatureName(), feature.getFunctionName())
                            );
                        }
                    });
        } catch (Exception e) {
            log.error("Model definition invalid for model.", e);
            return false;
        }

        log.info("Model definition valid for model_name={}", modelDTO.getModelName());
        return true;
    }

    @VisibleForTesting
    void createNewModel(ModelDTO modelDTO) {
        if (!isModelDefinitionValid(modelDTO)) {
            log.error("Not able to create the new model because the model definition is invalid.");
            return;
        }

        logModelAction(modelDTO, CREATE_MODEL_MESSAGE);

        // CAUTION: This deletes the previous features and models, in case they change completely.
        deleteModelDependencies(modelDTO);
        createModelFeature(modelDTO);
        updateModelOutputs(modelDTO);
        OptionalModelDTO.of(modelDTO)
                .toModel(controlPlaneMapper::toModel)
                .trackUserAction(sessionAttributeProvider::resolveUserEmail)
                .saveToCassandra(modelDao::insert);
        cachedConfigurationService.cleanAllCache("create_new_model");
        updateEntityCollection(modelDTO);
        controlPlaneLogger.logCreateModel(modelDTO);
    }

    @Override
    public void updateModel(@NonNull ModelDTO modelDTO) {
        if (!isModelDefinitionValid(modelDTO)) {
            log.error("Not able to update the new model because the model definition is invalid.");
            return;
        }

        logModelAction(modelDTO, UPDATE_MODEL_MESSAGE);

        // CAUTION: This deletes the previous features and models, in case they change completely.
        deleteModelDependencies(modelDTO);
        createModelFeature(modelDTO);
        updateModelOutputs(modelDTO);
        OptionalModelDTO.of(modelDTO)
                .toModel(controlPlaneMapper::toModel)
                .trackUserAction(sessionAttributeProvider::resolveUserEmail)
                .saveToCassandra(modelDao::update);
        //TODO(abajzat) why do we clean all cache?
        cachedConfigurationService.cleanAllCache("update_model");
        updateEntityCollection(modelDTO);
        controlPlaneLogger.logUpdateModel(modelDTO);
        log.debug("Finish updating model for model_name={}", modelDTO.getModelName());
    }

    /**
     * This is a method used when we need to change the model definition only (which does not consider outputs or features).
     * It is used, for example, to change the model status, without having to change any of the underlying components.
     *
     * @param modelDTO model definition.
     */
    private void updateModelDefOnly(@NonNull ModelDTO modelDTO) {
        if (!isModelDefinitionValid(modelDTO)) {
            log.error("Not able to update the new model definition because it is invalid.");
            return;
        }

        logModelAction(modelDTO, UPDATE_MODEL_MESSAGE);
        OptionalModelDTO.of(modelDTO)
                .toModel(controlPlaneMapper::toModel)
                .trackUserAction(sessionAttributeProvider::resolveUserEmail)
                .saveToCassandra(modelDao::update);

        cachedConfigurationService.cleanAllCache("update_model");
        updateEntityCollection(modelDTO);
        controlPlaneLogger.logUpdateModel(modelDTO);
        log.debug("Finish updating model definition for model_name={}", modelDTO.getModelName());
    }

    private void logModelAction(ModelDTO modelDTO, String message) {
        log.info(message + " model_name={}, " +
                 " status = {}, " +
                 "modelType={}, " +
                 "versioningModelName={}, " +
                 "version={}, " +
                 "versioningStrategy={}, " +
                 "size(modelFeatures)={}, " +
                 "size(modelOutputs)={}, " +
                 "updatedUser={}, " +
                 "modelCategory={}",
                modelDTO.getModelName(),
                modelDTO.getStatus(),
                modelDTO.getModelType(),
                modelDTO.getVersioningModelName(),
                modelDTO.getVersion(),
                modelDTO.getVersioningStrategy(),
                modelDTO.getModelFeatures() == null ? 0 : modelDTO.getModelFeatures().size(),
                modelDTO.getModelOutputs() == null ? 0 : modelDTO.getModelOutputs().size(),
                modelDTO.getUpdatedUser(),
                modelDTO.getModelCategory());
    }

    private void deleteModelDependencies(@NonNull ModelDTO modelDTO) {
        log.debug("Being deleting model depencies for model_name={}", modelDTO.getModelName());
        modelFeatureDao.delete(modelDTO.getModelName());
        modelOutputDao.delete(modelDTO.getModelName());
        log.debug("Finish deleting model depencies for model_name={}", modelDTO.getModelName());
    }

    private void updateEntityCollection(ModelDTO modelDTO) {
        log.debug("Updating entity collection for model={}", modelDTO.getModelName());
        entityCollectionDao.update(new EntityCollection(EntityCollection.Constants.COLLECTION_MODEL,
                modelDTO.getModelName(), modelDTO.getStatus(), modelDTO.getModelCategory()));
    }

    private void updateModelOutputs(ModelDTO modelDTO) {
        log.debug("Updating model outputs for model={}", modelDTO.getModelName());
        val modelOutputs = Option
                .of(modelDTO)
                .map(ModelDTO::getModelOutputs)
                .filter(Objects::nonNull)
                .getOrElse(List::empty)
                .map(out -> controlPlaneMapper.toModel(modelDTO.getModelName(), out));

        modelOutputDao.insertAll(modelOutputs.asJava());
        log.debug("Finish updating model outputs for model={}", modelDTO.getModelName());
    }

    @Override
    public void deleteModel(ModelDTO model) {
        getModel(model.getModelName()).peek(fullModel -> {
            modelDao.delete(model.getModelName());
            modelFeatureDao.delete(model.getModelName());
            controlPlaneLogger.logDeleteModel(fullModel);
            entityCollectionDao.delete(EntityCollection.Constants.COLLECTION_MODEL, model.getModelName());
            cachedConfigurationService.cleanAllCache("delete_model");
            modelTestInvocationStatusDao.deleteMany(model.getModelName());
        });
    }

    private void createModelFeature(ModelDTO modelDTO) {
        log.debug("Begin creating model feature for model_name={}", modelDTO.getModelName());
        modelDTO.getModelFeatures()
                .orElse(List.empty())
                .toStream()
                .map(feature -> controlPlaneMapper.toModel(feature, modelDTO))
                .peek(modelFeatureDao::insert)
                .map(controlPlaneMapper::toDTO)
                .forEach(controlPlaneLogger::logCreateModelFeature);
        log.debug("Finish creating model feature for model_name={}", modelDTO.getModelName());
    }

    @Override
    public void publishModelOutputAverage(ModelInferenceOutput response) {
        String modelName = response.getModelName();
        String modelVersioningName = response.getVersioningModelName();
        List<Float> output = response.getFloatResponse();
        if (output == null || output.isEmpty()) {
            log.warn("Model Output is empty for model: {}", modelName);
            return;
        }
        final float sum = output.toStream().sum().floatValue();
        final float average = sum / output.size();

        metricService.gauge(Metrics.name(getClass(), "averageModelScore"), modelName,
                Tags.of("model_name", modelName), average);
        if (StringUtils.isNotBlank(modelVersioningName)) {
            metricService.gauge(Metrics.name(getClass(), "averageModelScore"), modelVersioningName,
                    Tags.of("model_versioning_name", modelVersioningName), average);
        }
    }

    @Override
    public List<Float> invokeModelInference(@NonNull ModelInferenceRequest modelInferenceRequest) {
        ModelInferenceOutput modelInferenceOutput = invokeModelInferenceMultiOutput(modelInferenceRequest);
        publishModelOutputAverage(modelInferenceOutput);
        return modelInferenceOutput.getFloatResponse();
    }

    @Override
    public ModelInferenceOutput invokeModelInferenceMultiOutput(@NonNull ModelInferenceRequest modelInferenceRequest) {
        val model = getModelByNameOrAlias(modelInferenceRequest).getOrElseThrow(
                () -> new RuntimeException(String.format("Neither model nor model alias with name=%s was found", modelInferenceRequest.getModelName()))
        );
        try {
            val requestTime = Instant.now();
            Timer timer = meterRegistry.timer(Metrics.name(getClass(), INVOKE_MODEL_INFERENCE),
                    Tags.of(
                            "model_name", modelInferenceRequest.getModelName(), "model_version", model.getVersion(),
                            "model_category", Option.of(model.getModelCategory()).getOrElse(""),
                            "model_group", Option.of(model.getModelGroup()).getOrElse("")
                    )
            );
            return span(INVOKE_MODEL_INFERENCE, getTagsForSpan(modelInferenceRequest, model), () -> timer.record(() -> {
                if (ENABLED.isNotEqualTo(model.getStatus())) {
                    if (model.getModelType().equals(SEARCH_EMBEDDINGS.name())) {
                        // SEAR-7490 - quickfix to bypass availability issues and paging - remove once there's a proper fix
                        log.error("[DISABLED_SEARCH_EMBEDDING] Search embeddings model is not ENABLED," +
                                  " going to return default value, model_name={}", model.getModelName());
                        val results = HashMap.of(
                                "entities", ModelInferenceOutputType.ofStringArrayList(List.empty()),
                                "scores", ModelInferenceOutputType.ofStringArrayList(List.empty()));
                        return ModelInferenceOutput.builder().response(results).build();
                    } else {
                        throw new RuntimeException(String.format("Cannot invoke model=%s with wrong status=%s",
                                model.getModelName(), model.getStatus()));
                    }
                }
                if (modelInferenceRequest.getFeatures().isEmpty()) {
                    log.debug("Invoking model={} with an empty list of features, returning empty output", modelInferenceRequest.getModelName());
                    return ModelInferenceOutput.EMPTY;
                }
                return createExternalModelInferenceInvocation(model).invokeModel(modelInferenceRequest, requestTime);
            }));
        } catch (Exception e) {
            meterRegistry.counter(Metrics.name(getClass(), "invokeModelFailures"),
                            Tags.of("model_name", modelInferenceRequest.getModelName(), "model_version", model.getVersion()))
                    .increment();
            throw createModelInvocationException(modelInferenceRequest, e);
        }
    }

    private RealModelInferenceInvocation createExternalModelInferenceInvocation(ModelDTO model) {
        return RealModelInferenceInvocation.builder()
                .featureValueService(featureValueService)
                .model(model)
                .controlPlaneLogger(controlPlaneLogger)
                .client(client)
                .flowConfig(flowConfig)
                .meterRegistry(meterRegistry)
                .build();
    }

    private java.util.Map<String, String> getTagsForSpan(ModelInferenceRequest modelInferenceRequest, ModelDTO model) {
        return HashMap.of(
                        "model_name", modelInferenceRequest.getModelName(),
                        "model_version", model.getVersion(),
                        "feature_size", String.valueOf(modelInferenceRequest.getFeatures().size()),
                        "caller_tracking_id", modelInferenceRequest.getCallerTrackingId(),
                        "model_category", Option.of(model.getModelCategory()).getOrElse(""),
                        "model_group", Option.of(model.getModelGroup()).getOrElse("")
                )
                .toJavaMap();
    }

    private RuntimeException createModelInvocationException(ModelInferenceRequest req, Exception e) {
        return new RuntimeException(String.format("Error invoking model=%s, callerTrackingId=%s, features_size=%d, " +
                                                  "globalFeatures=%s, features_first=%s",
                req.getModelName(),
                req.getCallerTrackingId(),
                CollectionUtils.isNotEmpty(req.getFeatures()) ? req.getFeatures().size() : 0,
                ControlPlaneUtils.toString(req.getGlobalFeatures()),
                CollectionUtils.isNotEmpty(req.getFeatures()) ? ControlPlaneUtils.toString(req.getFeatures().get(0)) : "{}"
        ), e);
    }

    @Override
    public List<FeatureDTO> getFilteredFeaturesForEnsembleOrModel(
            @NonNull RoutingGroupResponseDTO routingGroupResponseDTO, @NonNull Predicate<FeatureDTO> featuresSelector) {
        return getModelNameSet(routingGroupResponseDTO)
                .map(Set::toStream)
                .map(models -> mapToFilteredFeatures(models, featuresSelector))
                .filter(not(List::isEmpty))
                .getOrElseThrow(() -> new RuntimeException(String.format("Empty feature list for model or models of ensemble, name=%s",
                        routingGroupResponseDTO.getEnsembleName())));
    }

    private Option<Set<String>> getModelNameSet(RoutingGroupResponseDTO routingGroupResponseDTO) {
        if (routingGroupResponseDTO.isEnsembleIsModel()) {
            return Option.of(HashSet.of(routingGroupResponseDTO.getEnsembleName()));
        }
        return ensembleService.getEnsemble(routingGroupResponseDTO.getEnsembleName())
                .map(ensemble -> ensemble.getModels()
                        .flatMap(ensemble::getModel)
                        .map(ModelDTO::getModelName));
    }

    private List<FeatureDTO> mapToFilteredFeatures(Stream<String> models, @NonNull Predicate<FeatureDTO> featuresSelector) {
        return models.map(this::selectAllFeatures)
                .flatMap(Stream::ofAll)
                .filter(featuresSelector)
                .distinctBy(FeatureDTO::getDistinctByComparator)
                .collect(List.collector());
    }

    @Override
    public List<EntityCollectionDTO> getAllModels() {
        return entityCollectionDao.selectAll(EntityCollection.Constants.COLLECTION_MODEL)
                .map(controlPlaneMapper::toDTO);
    }

    @Override
    public List<ModelDTO> getAllModelsWithStatus(String status) {
        val queries = entityCollectionDao
                .selectAll(EntityCollection.Constants.COLLECTION_MODEL, status)
                .toStream()
                .map(EntityCollection::getEntityId)
                .map(this::getModelAsync);
        return ControlPlaneUtils.flatten(ControlPlaneUtils.waitForCompletion(queries));
    }

    @Override
    public List<ModelDTO> getAllModelsWithStatusFromDatabase(String status) {
        val queries = entityCollectionDao
                .selectAll(EntityCollection.Constants.COLLECTION_MODEL, status)
                .toStream()
                .map(EntityCollection::getEntityId)
                .map(this::getModelAsyncWithoutCache);
        return ControlPlaneUtils.flatten(ControlPlaneUtils.waitForCompletion(queries));
    }

    private List<FeatureDTO> selectAllFeatures(String modelName) {
        return getModel(modelName)
                .map(ModelDTO::getModelFeatures)
                .getOrElse(List::empty);
    }

    private Option<ModelDTO> getModelByNameOrAlias(ModelInferenceRequest modelInferenceRequest) {
        val modelNameOrAlias = modelInferenceRequest.getModelName();
        log.debug("Resolving model by name using database model_name={}", modelNameOrAlias);
        val modelOp = getModel(modelNameOrAlias);

        if (modelOp.isDefined()) {
            log.debug("Resolved model by name, model_name={}", modelNameOrAlias);
            return modelOp;
        }

        log.debug("Model by name not found. Resolving model by alias using cache model_alias={}", modelNameOrAlias);
        val modelAlias = cachedConfigurationService.getAliasAsync(modelNameOrAlias, COLLECTION_MODEL).join();

        if (modelAlias.isPresent()) {
            log.debug("Resolved model by alias using cache model_alias={}, model_name={}", modelNameOrAlias, modelAlias.get().getEntityId());
            return getModel(modelAlias.get().getEntityId());
        }

        return Option.none();
    }

    @Override
    public TensorFlowModelStatusDTO getTensorFlowModelStatus(String modelName) {
        log.debug("Getting TensorFlow model status for model_name={}", modelName);
        val modelOp = getModel(modelName)
                .getOrElseThrow(() -> new RuntimeException("Model not found: " + modelName));
        return getTensorFlowModelStatus(modelOp);
    }

    @Override
    public TensorFlowModelMetadataDTO getTensorFlowModelMetadata(String modelName) {
        return getModel(modelName)
                .filter(m -> TENSOR_FLOW.isEqualTo(m.getModelType()))
                .map(client::getTensorFlowMetadata)
                .getOrElseThrow(() -> new RuntimeException("Model not found: " + modelName));
    }

    private TensorFlowModelStatusDTO getTensorFlowModelStatus(ModelDTO modelDTO) {
        val status = Option.of(modelDTO)
                .map(client::getTensorFlowStatus)
                .getOrElse(UNAVAILABLE);
        log.debug("TensorFlow model status={}", status);
        return TensorFlowModelStatusDTO.from(modelDTO, status.getName());
    }

    @Override
    public List<TensorFlowModelStatusDTO> getAllTensorFlowModelStatus() {
        return getAllModelsWithStatus(null)
                .filter(m -> TENSOR_FLOW.isEqualTo(m.getModelType()))
                .map(this::getTensorFlowModelStatus);
    }

    @Override
    public void publishModelsAgeMetrics() {
        log.debug("Begin publishing models age metrics");
        publishForAllDynamicAndEnabledModels();
        publishForAllDynamicAndEnabledEnsembles();
        log.debug("Finish publishing models age metrics");
    }

    private void publishForAllDynamicAndEnabledEnsembles() {
        ensembleService.getAllWithStatus(ENABLED.getName())
                .filter(ensemble -> Objects.nonNull(ensemble.getUpdatedTimestamp()))
                .map(EnsembleDTO::getLatestAlias)
                .flatMap(this::getAlias)
                .map(EntitiesAliasDTO::getEntityId)
                .map(ensembleService::getEnsemble)
                .filter(Option::isDefined)
                .map(Option::get)
                .forEach(ensemble -> meterRegistry.gauge(Metrics.name(getClass(), "ensemblesAge"),
                        Tags.of("ensemble_name", ensemble.getEnsembleName(), "ensemble_version", ensemble.getVersion()),
                        Duration.between(ensemble.getUpdatedTimestamp(), Instant.now()).toHours()));
    }

    private void publishForAllDynamicAndEnabledModels() {
        getAllModelsWithStatus(ENABLED.getName())
                .filter(model -> Objects.nonNull(model.getUpdatedTimestamp()))
                .filter(ModelDTO::isDynamicModel)
                .filter(this::shouldPublishAgeReports)
                .map(ModelDTO::getLatestAlias)
                .flatMap(this::getAlias)
                .map(alias -> this.getModel(alias.getEntityId()).map(model -> Pair.of(alias, model)))
                .filter(Option::isDefined)
                .map(Option::get)
                .forEach(this::publishModelAgeRelatedMetrics);
    }

    private void publishModelAgeRelatedMetrics(Pair<EntitiesAliasDTO, ModelDTO> modelAndAlias) {
        publishModelAge(modelAndAlias.getRight());
        publishAliasAge(modelAndAlias);
    }

    private void publishAliasAge(Pair<EntitiesAliasDTO, ModelDTO> modelAndAlias) {
        Number number = modelAndAlias.getLeft().getUpdatedTimestamp() == null
                ? 0L
                : Duration.between(modelAndAlias.getLeft().getUpdatedTimestamp(), Instant.now()).toHours();

        metricService.gauge(Metrics.name(getClass(), "aliasAge"), modelAndAlias.getLeft().getAliasName(), Tags.of(
                "alias_name", modelAndAlias.getLeft().getAliasName(),
                "model_name", modelAndAlias.getRight().getModelName(),
                "model_version", modelAndAlias.getRight().getVersion()), number);
    }

    private void publishModelAge(ModelDTO model) {
        long modelAge = Duration.between(model.getUpdatedTimestamp(), Instant.now()).toHours();
        metricService.gauge(Metrics.name(getClass(), "modelsAge"), model.getModelName(),
                Tags.of("model_name", model.getModelName(), "model_version", model.getVersion()), modelAge);
    }

    private boolean shouldPublishAgeReports(ModelDTO model) {
        return !model.isSkipAgeReporting();
    }

    @Value
    @With
    @Slf4j
    static class ModelAndTfStatus {
        ModelDTO model;
        String status;

        private ModelAndTfStatus checkStatus(Consumer<ModelDTO> action) {
            if (ENABLED.isEqualTo(model.getStatus()) && !isStatusAvailable()) {
                logStatusUnavailable();
                val updatedModel = model.withStatus(UNAVAILABLE.getName());
                return withModel(updatedModel);
            } else if (modelIsLoadedOrUnavailable()) {
                if (isStatusAvailable()) {
                    logStatusAvailable();
                    val updatedModel = model.withStatus(AVAILABLE.getName());
                    action.accept(updatedModel);
                    return withModel(updatedModel);
                } else {
                    logDefault();
                }
                return this;
            }
            return this;
        }

        private void logDefault() {
            log.info("{}={} still not AVAILABLE in {}",
                    model.isModelTypeTensorFlow()
                            ? "TF model"
                            : "SearchEmbeddings model",
                    model.getModelName(),
                    model.isModelTypeTensorFlow()
                            ? "TensorFlow Serving"
                            : "Search-Embeddings service"
            );
        }

        private void logStatusUnavailable() {
            log.error("{}={} is not available={}",
                    model.isModelTypeTensorFlow()
                            ? "TF model"
                            : "SearchEmbeddings model",
                    model.getModelName(),
                    status);
        }

        private void logStatusAvailable() {
            log.info("{}={} has become AVAILABLE in {}",
                    model.isModelTypeTensorFlow()
                            ? "TF model"
                            : "SearchEmbeddings model",
                    model.getModelName(),
                    model.isModelTypeTensorFlow()
                            ? "TensorFlow Serving"
                            : "Search-Embeddings service"
            );
        }

        private boolean isStatusAvailable() {
            return AVAILABLE.isEqualTo(status);
        }

        private boolean modelIsLoadedOrUnavailable() {
            return LOADED.isEqualTo(model.getStatus()) || UNAVAILABLE.isEqualTo(model.getStatus());
        }
    }

    @Override
    public Option<EntitiesAliasDTO> getAlias(@NonNull String aliasName) {
        return aliasService.getAlias(aliasName, COLLECTION_MODEL);
    }

    @Override
    public void updateAlias(EntitiesAliasDTO entitiesAlias) {
        val aliasEntityId = entitiesAlias.getEntityId();
        val aliasName = entitiesAlias.getAliasName();
        getModel(aliasEntityId).getOrElseThrow(
                () -> new RuntimeException(String.format("Error updating alias with name=%s. " +
                                                         "Model with name=%s does not exist", aliasName, aliasEntityId))
        );
        aliasService.updateAlias(aliasName, aliasEntityId, COLLECTION_MODEL);
    }

    public void updateAlias(String aliasName, String modelName) {
        aliasService.updateAlias(aliasName, modelName, COLLECTION_MODEL);
    }

    @Override
    public void deleteAlias(@NonNull String aliasName) {
        aliasService.deleteAlias(aliasName, COLLECTION_MODEL);
    }

    @Override
    public List<ModelDTO> selectModels(Set<String> models) {
        if (models.isEmpty()) {
            log.debug("No models to select.");
            return List.empty();
        }
        return modelDao.selectModels(models.toJavaSet()).stream()
                .map(controlPlaneMapper::toDTO)
                .collect(List.collector());
    }

    @Override
    public ModelInferenceOutput invokeSequence(ModelInferenceSequenceRequest request) {
        return Try.of(() ->
                        modelInferenceSequenceItemStreamFactory.create(request)
                                .invokeModelInferenceMultiOutput(this::invokeModelInferenceMultiOutput)
                                .returnLastOutput()
                )
                .getOrElseThrow(e -> handleSequenceModelFailure(request, e));

    }

    private RuntimeException handleSequenceModelFailure(ModelInferenceSequenceRequest request, Throwable e) {
        return new RuntimeException(String.format("Error invoking sequence of models, callerTrackingId=%s, modelNames=%s",
                request.getCallerTrackingId(),
                request.getInvocations().stream().map(ModelInferenceSequenceItem::getModelName).collect(toList())), e);
    }

    @Override
    public List<IndexesStatusDTO> getSearchEmbeddingsIndexesStatusForModel(String modelName) {
        return getModel(modelName)
                .map(searchEmbeddingsAdapter::indexesStatusAsList)
                .getOrElse(List::empty);
    }


    private void checkForStaleEnsemblesIfNeeded() {
        if (tensorFlowModelConfiguration.getCheckForStaleEnsembles()) {
            checkForStaleEnsembles();
        }
    }
}

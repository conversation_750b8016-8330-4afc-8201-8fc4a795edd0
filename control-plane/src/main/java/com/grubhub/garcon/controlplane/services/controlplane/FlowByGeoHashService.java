package com.grubhub.garcon.controlplane.services.controlplane;

import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.NonNull;

import java.util.concurrent.CompletableFuture;

public interface FlowByGeoHashService {
    void insertGeoHashes(FlowDTO flow);
    void deleteGeoHashesForFlow(FlowDTO flowDTO);
    void insert(FlowByGeoHash flowByGeoHash);
    Option<FlowByGeoHash> select(@NonNull String flowSet, @NonNull String geohash);
    CompletableFuture<List<FlowByGeoHash>> selectAllAsync(@NonNull String flowSet, @NonNull String geohash);
    CompletableFuture<List<FlowByGeoHash>> selectAllAsyncWithoutCache(@NonNull String flowSet, @NonNull String geohash);
}

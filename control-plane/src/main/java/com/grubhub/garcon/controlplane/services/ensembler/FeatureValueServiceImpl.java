package com.grubhub.garcon.controlplane.services.ensembler;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureKeyDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureValueDTO;
import com.grubhub.garcon.ensembler.cassandra.dao.FeatureValueDao;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Slf4j
public class FeatureValueServiceImpl implements FeatureValueService {
    private final FeatureValueDao featureValueDao;
    private final ControlPlaneMapper controlPlaneMapper;

    @Override
    public CompletableFuture<Optional<FeatureValueDTO>> getFeatureValue(FeatureKeyDTO featureKey) {
        return featureValueDao
                .selectAsync(controlPlaneMapper.toModel(featureKey))
                .map(controlPlaneMapper::toDTO)
                .toCompletableFuture();
    }

    @Override
    public Map<FeatureKeyDTO, FeatureValueDTO> getMany(List<FeatureKeyDTO> featureKeys) {
        return featureValueDao.selectMany(featureKeys.map(controlPlaneMapper::toModel).asJava())
                .entrySet()
                .stream()
                .filter(e -> e.getValue().isPresent())
                .collect(HashMap.collector(
                        e -> controlPlaneMapper.toDTO(e.getKey()),
                        e -> controlPlaneMapper.toDTO(e.getValue().get())
                ));
    }

    @Override
    public CompletableFuture<Void> createOrUpdateFeatureValue(FeatureValueDTO featureValue) {
        return featureValueDao.createOrUpdateAsync(controlPlaneMapper.toModel(featureValue)).toCompletableFuture();
    }

    @Override
    public void createOrUpdateMany(List<FeatureValueDTO> featureValues) {
        featureValueDao.createOrUpdateMany(featureValues.map(controlPlaneMapper::toModel).asJava());
    }

    @Override
    public void deleteFeatureValue(FeatureKeyDTO featureKey) {
        featureValueDao.delete(controlPlaneMapper.toModel(featureKey));
    }

    @Override
    public void deleteMany(List<FeatureKeyDTO> featureKeys) {
        featureValueDao.deleteMany(featureKeys.map(controlPlaneMapper::toModel).asJava());
    }

    @Override
    public Option<FeatureValueDTO> updateCache(FeatureKeyDTO featureKey, Option<FeatureValueDTO> featureValue) {
        throw new UnsupportedOperationException("Operation not supported");
    }
}

package com.grubhub.garcon.controlplane.services.controlplane.gdpactions;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.config.GdpDataTransferConfig;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.services.common.utils.LifeCycleUtils;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionResultDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelStatus;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.vavr.collection.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GdpActionsSyncer {

    private final GdpDataTransferConfig gdpDataTransferConfig;
    private final ModelService modelService;
    private final MeterRegistry meterRegistry;

    @Inject
    public GdpActionsSyncer(GdpDataTransferConfig gdpDataTransferConfig,
                            ModelService modelService,
                            MeterRegistry meterRegistry) {
        this.gdpDataTransferConfig = gdpDataTransferConfig;
        this.modelService = modelService;
        this.meterRegistry = meterRegistry;
    }

    /**
     * Keeps the latest gdpDataTransferConfig.getAllowedNumberOfPendingVersionByModel() versions for each model versioning name.
     * For example:
     * gdpDataTransferConfig.getAllowedNumberOfPendingVersionByModel() = 1 and we have:
     * model_v1.233
     * model_v1.232
     * model_v1.231
     * <p>
     * We only keep model_v1.233 as PENDING, and the rest will be marked as DISABLED.
     */
    public void disableOlderModels(List<GdpActionResultDTO> actionResults) {
        if (gdpDataTransferConfig.getAllowedNumberOfPendingVersionByModel() == null || gdpDataTransferConfig.getAllowedNumberOfPendingVersionByModel() == 0) {
            // Here there's no check of models of the same version being already PENDING to be LOADED.
            return;
        }

        // All models coming from new actions. These models have already been inserted into Cassandra, but having them also here in case of consistency issues.
        List<ModelDTO> actionModels = actionResults.map(action -> GdpActionParser.loadActionContent(action.getAction(), ModelDTO.class));
        // All pending models that already are in the database.
        List<ModelDTO> pendingModels = modelService.getAllModelsWithStatus(ModelStatus.PENDING.getName());
        // We need to do distinct() here because some of the models are in DB and the action files.
        List<ModelDTO> allModels = actionModels.appendAll(pendingModels).distinct();

        allModels.groupBy(ModelDTO::getVersioningModelName) // Grouping by versioning name.
                .forEach(this::processModelGroup);
    }

    /**
     * These models within each group are being sorted by version in a descendent way and the latest
     * gdpDataTransferConfig.getAllowedNumberOfPendingVersionByModel() are the only ones being kept with the current status, the rest gets disabled.
     *
     * @param modelList list of models within a versioning name, such as: List(model_x_v1, model_x_v2, model_x_v3)
     */
    private void processModelGroup(String groupName, List<ModelDTO> modelList) {
        int numberOfModelsToKeep = Math.min(gdpDataTransferConfig.getAllowedNumberOfPendingVersionByModel(), modelList.size());
        log.warn("Keeping {} models for model versioning group={}", numberOfModelsToKeep, groupName);

        LifeCycleUtils.sortModelListDescendingByVersion(modelList)
                .slice(numberOfModelsToKeep, modelList.size())
                .forEach(this::disableOldModel);
    }

    private void disableOldModel(ModelDTO modelDTO) {
        log.warn("Disabling model={} because there's a newer version set as PENDING.", modelDTO.getModelName());
        modelDTO.setStatus(ModelStatus.DISABLED.getName());
        modelService.createOrUpdateModel(modelDTO);

        meterRegistry
                .counter(Metrics.name(getClass(), "DisabledModels"), Tags.of("model_name", modelDTO.getModelName()))
                .increment();
    }
}

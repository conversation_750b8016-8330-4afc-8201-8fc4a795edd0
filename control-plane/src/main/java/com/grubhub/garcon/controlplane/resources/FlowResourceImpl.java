package com.grubhub.garcon.controlplane.resources;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowResponseApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.*;
import com.grubhub.garcon.controlplaneapi.resource.controlplane.FlowResource;
import com.grubhub.garcon.controlplaneapi.service.controlplane.FlowService;
import com.grubhub.roux.api.responses.DeleteResponse;
import com.grubhub.roux.api.responses.GetResponse;
import com.grubhub.roux.api.responses.UpdateResponse;
import com.grubhub.roux.discovery.ServiceVisibility;
import com.grubhub.roux.uuid.UuidUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.grubhub.roux.discovery.ServiceVisibilitySetting.PUBLIC;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Slf4j
@ServiceVisibility(PUBLIC)
public class FlowResourceImpl implements FlowResource {

    private final FlowService flowService;

    @Override
    public GetResponse<FlowApi> findFlow(String flowId, boolean retrieveFromCache) {
        log.debug("Retrieving flow with flowId={}, retrieveFromCache={}", flowId, retrieveFromCache);
        final UUID flowUuid = UuidUtil.decode(flowId);
        Optional<FlowApi> flow = retrieveFromCache ?
                flowService.getFlow(flowUuid) :
                flowService.getFlowWithoutCache(flowUuid);

        return flow
                .map(GetResponse::success)
                .orElseGet(GetResponse::notFound);
    }

    @Override
    public UpdateResponse createOrUpdateFlow(@Valid FlowApi flowApi) {
        flowService.createOrUpdateFlow(flowApi);
        return UpdateResponse.success();
    }

    @Override
    public DeleteResponse deleteFlow(String flowId) {
        val flowIdAsUuid = UuidUtil.decode(flowId);
        return flowService.getFlowWithoutCache(flowIdAsUuid)
                .map(FlowApi::getFlowId)
                .map(UuidUtil::decode)
                .map(id -> {
                    flowService.deleteFlow(id);
                    return DeleteResponse.success();
                })
                .orElseGet(DeleteResponse::notFound);
    }

    @Override
    public GetResponse<FlowApi> getFlowByFlowSetAndLocation(String flowSet, String location) {
        return flowService.getFlowByFlowSetAndLocation(flowSet, location)
                .map(GetResponse::success)
                .orElseGet(GetResponse::notFound);
    }

    @Override
    public GetResponse<FlowResponseApi> resolveFlow(ResolveFlowRequest resolveFlowRequest) {
        log.debug("Calling resolve flow with input={}", resolveFlowRequest);
        return flowService.resolveFlow(resolveFlowRequest)
                .map(GetResponse::success)
                .orElseGet(GetResponse::notFound);
    }

    @Override
    public GetResponse<List<EntityCollectionDTO>> getAll() {
        return GetResponse.success(flowService.getAllFlows());
    }

    @Override
    public GetResponse<List<FlowSummaryDTO>> getAllDetailed(String flowSet) {
        return GetResponse.success(flowService.getAllFlowsDetailed(flowSet));
    }

    @Override
    public DeleteResponse cleanCache() {
        flowService.cleanCache();
        return DeleteResponse.success();
    }

    @Override
    public GetResponse<List<FlowByMarketDTO>> getFlowsByMarkets(String flowSet, List<String> marketNames) {
        return GetResponse.success(flowService.getFlowsByMarkets(flowSet, io.vavr.collection.List.ofAll(marketNames)).asJava());
    }

    @Override
    public GetResponse<List<FlowByFlowSetDTO>> getFlowsByFlowSet(@NotNull String flowSet, Boolean enabled) {
        return GetResponse.success(flowService.getFlowsByFlowSet(flowSet, enabled).asJava());
    }

    @Override
    public GetResponse<List<FlowSetApi>> getAllFlowSets() {
        return GetResponse.success(flowService.getAllFlowSets()
                .map(FlowResourceImpl::toFlowSetAPI)
                .asJava());
    }

    private static FlowSetApi toFlowSetAPI(EntityCollectionDTO entityCollection) {
        if (entityCollection == null) {
            return null;
        }
        FlowSetApi.FlowSetApiBuilder flowSetDTO = FlowSetApi.builder().status(entityCollection.getEntityStatus())
                .name(entityCollection.getEntityId());
        return flowSetDTO.build();
    }
}

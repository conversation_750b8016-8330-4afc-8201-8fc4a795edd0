package com.grubhub.garcon.controlplane.services.ensembler.rules;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import io.vavr.collection.Map;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ChainedFeatureStoreRule implements FeatureStoreRule {
    private final FeatureStoreRule head;
    private final FeatureStoreRule tail;

    @Override
    public Map<String, Object> applicableTo(Map<FeatureDTO, Object> sample) {
        return head.applicableTo(sample)
                 .merge(tail.applicableTo(sample));
    }
}

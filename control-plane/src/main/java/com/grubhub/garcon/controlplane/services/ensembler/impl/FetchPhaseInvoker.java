package com.grubhub.garcon.controlplane.services.ensembler.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.metrics.Spans;
import com.grubhub.garcon.controlplane.services.ensembler.FeatureValueService;
import com.grubhub.garcon.controlplane.services.ensembler.impl.featureproviders.InternalFeaturesProvider;
import com.grubhub.garcon.controlplane.services.ensembler.rules.FeatureDefaultValueType;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplanerpc.model.FunctionScope;
import com.grubhub.garcon.ensembler.dto.EnrichedModelInferenceRequest;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.vavr.Tuple;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import lombok.Value;
import lombok.val;

import static com.grubhub.garcon.controlplane.services.ensembler.rules.FeatureStoreFunctionRule.createFeatureStoreFunctionRule;
import static com.grubhub.garcon.controlplane.services.ensembler.rules.FeatureStoreInternalRule.createFeatureStoreInternalRule;
import static com.grubhub.garcon.controlplane.services.ensembler.rules.FeatureStoreRuntimeRule.matchFeatureStoreRuntime;
import static com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO.INDEX;
import static com.grubhub.garcon.controlplanerpc.model.FunctionScope.INTERNAL;
import static com.grubhub.garcon.controlplanerpc.model.FunctionScope.RUNTIME;

public class FetchPhaseInvoker {

    private static final String FEATURES_FETCH_RUNTIME_AND_EXTERNAL = "featuresFetchRuntimeAndExternal";
    private static final String FEATURES_FETCH_FUNCTIONS = "featuresFetchFunctions";
    private static final String FEATURES_FETCH_INTERNAL = "featuresFetchInternal";
    private static final String FEATURES_FETCH_POST_PROCESSING = "featuresFetchPostProcessing";
    private final EnrichedModelInferenceRequest modelInferenceRequest;
    private final FeatureValueService featureValueService;
    private final MeterRegistry meterRegistry;
    private final Timer featuresFetchRuntimeAndExternal;
    private final Timer featuresFetchFunctions;
    private final Timer featuresFetchInternal;
    private final Timer featuresFetchPostProcessing;

    @Inject
    public FetchPhaseInvoker(EnrichedModelInferenceRequest modelInferenceRequest,
                             @Named("featureValueServiceWithCache") FeatureValueService featureValueService,
                             MeterRegistry meterRegistry) {
        this.modelInferenceRequest = modelInferenceRequest;
        this.featureValueService = featureValueService;
        this.meterRegistry = meterRegistry;
        this.featuresFetchRuntimeAndExternal = meterRegistry.timer(Metrics.name(getClass(), FEATURES_FETCH_RUNTIME_AND_EXTERNAL),
                Tags.of("model_name", modelInferenceRequest.getModel().getModelName(), "model_version", modelInferenceRequest.getModel().getVersion()));
        this.featuresFetchFunctions = meterRegistry.timer(Metrics.name(getClass(), FEATURES_FETCH_FUNCTIONS),
                Tags.of("model_name", modelInferenceRequest.getModel().getModelName(), "model_version", modelInferenceRequest.getModel().getVersion()));
        this.featuresFetchInternal = meterRegistry.timer(Metrics.name(getClass(), FEATURES_FETCH_INTERNAL),
                Tags.of("model_name", modelInferenceRequest.getModel().getModelName(), "model_version", modelInferenceRequest.getModel().getVersion()));
        this.featuresFetchPostProcessing = meterRegistry.timer(Metrics.name(getClass(), FEATURES_FETCH_POST_PROCESSING),
                Tags.of("model_name", modelInferenceRequest.getModel().getModelName(), "model_version", modelInferenceRequest.getModel().getVersion()));
    }


    public ModelInferenceInput applyFetchPhase() {
        val processedFeaturesForRuntimeAndExternal =
                Spans.span(
                        FEATURES_FETCH_RUNTIME_AND_EXTERNAL,
                        getTagsForSpan(),
                        () -> featuresFetchRuntimeAndExternal.record(this::processFeaturesForRuntimeAndExternal)
                );

        val processedFeaturesForAllExceptInternal =
                Spans.span(
                        FEATURES_FETCH_FUNCTIONS,
                        getTagsForSpan(),
                        () -> featuresFetchFunctions.record(() -> processFeaturesForFunctions(processedFeaturesForRuntimeAndExternal, RUNTIME))
                );

        val processedFeaturesForAllInclusiveInternal =
                Spans.span(
                        FEATURES_FETCH_INTERNAL,
                        getTagsForSpan(),
                        () -> featuresFetchInternal.record(() -> processInternalFeatures(processedFeaturesForAllExceptInternal))
                );

        val allProcessedFeatures = Spans.span(
                FEATURES_FETCH_FUNCTIONS,
                getTagsForSpan(),
                () -> featuresFetchFunctions.record(() -> processFeaturesForFunctions(processedFeaturesForAllInclusiveInternal, INTERNAL))
        );

        return Spans.span(FEATURES_FETCH_POST_PROCESSING, getTagsForSpan(), () -> featuresFetchPostProcessing.record(() -> {
            val renamedProcessedFeatures = renameProcessedFeatures(allProcessedFeatures);
            val filteredProcessedFeatures = filterProcessedFeatures(renamedProcessedFeatures);

            return new ModelInferenceInput(modelInferenceRequest.getModel(), filteredProcessedFeatures, modelInferenceRequest.getInvocationType());
        }));
    }

    private java.util.Map<String, String> getTagsForSpan() {
        return HashMap.of(
                        "model_name", modelInferenceRequest.getModel().getModelName(),
                        "model_version", modelInferenceRequest.getModel().getVersion(),
                        "feature_size", String.valueOf(modelInferenceRequest.getModelInferenceRequest().getFeatures().size())
                )
                .toJavaMap();
    }

    private List<Map<String, Object>> processFeaturesForFunctions(List<Map<String, Object>> processedFeatures, FunctionScope functionScope) {
        return createFeatureStoreFunctionRule(modelInferenceRequest, processedFeatures, functionScope)
                .processFunctionFeatures();
    }

    private List<Map<String, Object>> processInternalFeatures(List<Map<String, Object>> processedFeatures) {
        return createFeatureStoreInternalRule(featureValueService, modelInferenceRequest, processedFeatures, meterRegistry)
                .processInternalFeatures(new InternalFeaturesProvider(modelInferenceRequest));
    }

    private List<Map<String, Object>> processFeaturesForRuntimeAndExternal() {
        return injectIndexInFeatures()
                .map(this::performFeaturesLookUpForRuntimeAndExternal)
                .map(this::matchFeatureStoreRule)
                .collect(List.collector());
    }

    private List<Map<String, Object>> filterProcessedFeatures(List<Map<String, Object>> processedFeatures) {
        val filter = modelInferenceRequest.getModel().getProcessedFeaturesFilter();
        if (filter == null || filter.isEmpty()) {
            return processedFeatures;
        }
        return processedFeatures.map(features -> features.filterKeys(filter::contains));
    }

    private List<Map<String, Object>> renameProcessedFeatures(List<Map<String, Object>> processedFeatures) {
        val mapping = modelInferenceRequest.getModel().getProcessedFeaturesMapping();
        if (mapping == null || mapping.isEmpty()) {
            return processedFeatures;
        }
        return processedFeatures.map(features -> features.map((key, value) -> Tuple.of(mapping.getOrElse(key, key), value)));
    }

    private Stream<Map<String, Object>> injectIndexInFeatures() {
        return modelInferenceRequest.getInputSamples().zipWithIndex((map, index) -> map.put(INDEX, index));
    }

    private Map<String, Object> matchFeatureStoreRule(Map<FeatureDTO, Object> sample) {
        return matchFeatureStoreRuntime().applicableTo(sample);
    }

    private Map<FeatureDTO, Object> performFeaturesLookUpForRuntimeAndExternal(Map<String, Object> sample) {
        return modelInferenceRequest
                .getFeaturesForModel()
                .filter(FeatureDTO::isRuntime)
                .map(feature -> new FeatureWithSample(feature, sample).featuresLookUp())
                .toMap(FeatureModelWithFeatureValue::getFeature, FeatureModelWithFeatureValue::getSample);
    }

    @Value
    private class FeatureWithSample {
        FeatureDTO feature;
        Map<String, Object> sample;

        private FeatureModelWithFeatureValue featuresLookUp() {
            return findInFeatures()
                    .orElse(this::findInGlobalFeatures)
                    .orElse(this::findInDefault)
                    .getOrElseThrow(() -> new RuntimeException(String.format("Missing required feature=%s when invoking model=%s, sample=%s",
                            feature.getFeatureName(), modelInferenceRequest.getModel().getModelName(), ControlPlaneUtils.toString(sample))));
        }

        private Option<FeatureModelWithFeatureValue> findInFeatures() {
            return sample
                    .get(feature.getFeatureName())
                    .map(featureValue -> new FeatureModelWithFeatureValue(feature, featureValue));
        }

        private Option<FeatureModelWithFeatureValue> findInGlobalFeatures() {
            return modelInferenceRequest.getGlobalFeatures()
                    .get(feature.getFeatureName())
                    .map(object -> new FeatureModelWithFeatureValue(feature, object));
        }

        private Option<FeatureModelWithFeatureValue> findInDefault() {
            boolean isOptional = feature.getFeatureOptional() != null && feature.getFeatureOptional();
            return Option.when(isOptional, new FeatureModelWithFeatureValue(feature,
                    FeatureDefaultValueType.parseTypeAndValue(feature.getFeatureDefaultType(), feature.getFeatureDefaultValue())
            ));
        }
    }

    @Value
    private static class FeatureModelWithFeatureValue {
        FeatureDTO feature;
        Object sample;
    }

}

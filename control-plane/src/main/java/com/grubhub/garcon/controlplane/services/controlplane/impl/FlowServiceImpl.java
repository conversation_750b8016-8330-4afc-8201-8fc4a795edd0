package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByFlowSetDao;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByMarketDao;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowDao;
import com.grubhub.garcon.controlplane.cassandra.models.DinerType;
import com.grubhub.garcon.controlplane.cassandra.models.Flow;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByFlowSet;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByMarket;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.config.MealTimeByTimeOfDayConfig;
import com.grubhub.garcon.controlplane.dto.Brand;
import com.grubhub.garcon.controlplane.dto.EnrichedResolveFlowRequest;
import com.grubhub.garcon.controlplane.dto.FlowResponseBuilder;
import com.grubhub.garcon.controlplane.dto.FlowResponseContext;
import com.grubhub.garcon.controlplane.dto.FlowWithGeoHashDTO;
import com.grubhub.garcon.controlplane.dto.VariationInput;
import com.grubhub.garcon.controlplane.eventhub.ControlPlaneLogger;
import com.grubhub.garcon.controlplane.eventhub.event.ResolveFlowEvent;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.mapper.FlowServiceApiMapper;
import com.grubhub.garcon.controlplane.mapper.MapperUtil;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.services.common.service.CachedConfigurationService;
import com.grubhub.garcon.controlplane.services.controlplane.FlowByGeoHashService;
import com.grubhub.garcon.controlplane.services.controlplane.FlowRoutingGroupsService;
import com.grubhub.garcon.controlplane.services.controlplane.MarketService;
import com.grubhub.garcon.controlplane.services.controlplane.domainstreams.FlowWithGeoHashStream;
import com.grubhub.garcon.controlplane.services.controlplane.rules.actions.MatchingAction;
import com.grubhub.garcon.controlplane.services.controlplane.rules.diner.ExistingDinerRule;
import com.grubhub.garcon.controlplane.services.controlplane.rules.diner.NewDinerRule;
import com.grubhub.garcon.controlplane.services.controlplane.rules.diner.UnauthenticatedDinerRule;
import com.grubhub.garcon.controlplane.services.controlplane.rules.matching.DefaultMatchingRule;
import com.grubhub.garcon.controlplane.services.controlplane.rules.matching.LocationMatchingRule;
import com.grubhub.garcon.controlplane.services.controlplane.seedrotation.PredefinedSeedSelector;
import com.grubhub.garcon.controlplane.services.controlplane.seedrotation.RandomizedSeedSelector;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.RoutingGroupStrategyFactory;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup.RoutingGroupStrategyInput;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplane.util.SessionAttributeProvider;
import com.grubhub.garcon.controlplane.validators.EnsembleWeightsAndNameValidator;
import com.grubhub.garcon.controlplane.validators.LocationMarketsValidator;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.api.FlowResponseApi;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.EntityCollectionDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowByFlowSetDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowByMarketDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowResponseDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowSummaryDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketByGeohashDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.ResolveFlowRequest;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.RoutingGroupResponseDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.service.controlplane.FlowService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.ensembler.cassandra.dao.EntityCollectionDao;
import com.grubhub.garcon.ensembler.cassandra.models.EntityCollection;
import com.grubhub.garcon.search.experiment.ExperimentBranchManager;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import com.grubhub.roux.uuid.UuidUtil;
import com.netflix.servo.util.VisibleForTesting;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import io.vavr.collection.Set;
import io.vavr.collection.Stream;
import io.vavr.collection.Traversable;
import io.vavr.control.Option;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintViolationException;
import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

import static com.grubhub.garcon.controlplane.custom.OptionalFlow.asOptionalFlow;
import static com.grubhub.garcon.controlplane.custom.OptionalFlowResolveRequest.asOptionalFlowResolveRequest;
import static com.grubhub.garcon.controlplane.custom.OptionalFlowResolveRequest.enrichResolveFlowRequestWithDinerType;
import static com.grubhub.garcon.controlplane.custom.OptionalFlowResolveRequest.toEnrichedResolveFlowRequest;
import static com.grubhub.garcon.controlplane.metrics.Spans.TRACING_HELPER;
import static com.grubhub.garcon.controlplane.services.common.utils.ValidationUtils.validateInput;
import static com.grubhub.garcon.controlplane.services.controlplane.domainstreams.FlowWithGeoHashStream.asFlowWithGeoHashStream;
import static com.grubhub.garcon.controlplane.services.controlplane.geo.GeoUtils.getGeoHash;
import static com.grubhub.garcon.controlplane.util.ControlPlaneTimeUtils.getDayOfYear;
import static com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketDTO.GLOBAL_MARKET;
import static com.grubhub.garcon.ensembler.cassandra.models.EntityCollection.Constants.NO_CATEGORY;
import static java.util.stream.Collectors.collectingAndThen;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Slf4j
public class FlowServiceImpl implements FlowService {
    private final FlowDao flowDao;
    private final ModelService modelService;
    private final ControlPlaneMapper controlPlaneMapper;
    private final FlowConfig flowConfig;
    private final FlowRoutingGroupsService flowRoutingGroupsService;
    private final MarketService marketService;
    private final FlowByGeoHashService flowByGeoHashService;
    private final JavaDateTimeHelper dateTimeHelper;
    private final SessionAttributeProvider sessionAttributeProvider;
    private final MealTimeByTimeOfDayConfig mealTimeByTimeOfDayConfig;
    private final ControlPlaneLogger controlPlaneLogger;
    private final EntityCollectionDao entityCollectionDao;
    private final FlowServiceApiMapper flowServiceApiMapper;
    private final MeterRegistry meterRegistry;
    private final EnsembleWeightsAndNameValidator ensembleWeightsAndNameValidator;
    private final ExperimentBranchManager experimentBranchManager;
    private final CachedConfigurationService cachedConfigurationService;
    private final FlowByMarketDao flowByMarketDao;
    private final FlowByFlowSetDao flowByFlowSetDao;

    @Override
    public void createOrUpdateFlow(@NonNull FlowApi flowApi) {
        val flow = flowServiceApiMapper.toDTO(flowApi);
        ensembleWeightsAndNameValidator.validateEnsembleWeightsAndName(flow);
        createOrUpdateFlowDTO(flow);
    }

    private void createOrUpdateFlowDTO(@NonNull FlowDTO flow) {
        LocationMatchingRule.match(() -> applyLocationMatchingStrategy(flow))
                .orElse(DefaultMatchingRule.match(() -> applyDefaultMatchingStrategy(flow)))
                .applicableTo(flow)
                .ifPresent(MatchingAction::apply);

        getFlow(flow.getFlowId()).peek(controlPlaneLogger::logCreateFlow);
    }

    private void applyLocationMatchingStrategy(FlowDTO flow) {
        asOptionalFlow(flow)
                .selectOrNone(this::getFlow)
                .map(flowServiceApiMapper::toDTO)
                .peek(aFlow -> updateFlowDTO(aFlow, validateFlow(flow)))
                .onEmpty(() -> createNewFlow(validateFlow(flow)));
    }

    private FlowDTO validateFlow(FlowDTO flow) {
        return new LocationMarketsValidator(marketService).validateLocationMarkets(flow);
    }

    private void createNewFlow(FlowDTO flow) {
        UUID.fromString(flow.getFlowId()); // If flow_id is not a valid UUID, this will throw IllegalArgumentException
        if (!flowConfig.isNewGeohashesResolutionEnabled()) {
            flowByGeoHashService.insertGeoHashes(flow);
        }
        flowRoutingGroupsService.insertRoutingGroups(flow);
        trackUserAction(flow);
        flowDao.insert(controlPlaneMapper.toModel(flow));
        flowByMarketDao.insertMany(getFlowByMarkets(flow).toJavaList());
        flowByFlowSetDao.insert(createFlowByFlowSet(flow));
        cachedConfigurationService.cleanAllCache("create_new_flow");
        updateEntityCollection(flow);

    }

    private FlowByFlowSet createFlowByFlowSet(FlowDTO flow) {
        return FlowByFlowSet.builder()
                .flowSet(flow.getFlowSet())
                .flowId(UUID.fromString(flow.getFlowId()))
                .enabled(flow.isEnabled())
                .build();
    }

    private List<FlowByMarket> getFlowByMarkets(FlowDTO flow) {
        return flow.getLocationMarkets()
                .map(marketName -> FlowByMarket.builder()
                        .flowId(UUID.fromString(flow.getFlowId()))
                        .marketName(marketName)
                        .flowSet(flow.getFlowSet())
                        .build()
                ).toList();
    }

    private void applyDefaultMatchingStrategy(FlowDTO flow) {
        asOptionalFlow(flow)
                .selectOrNone(this::getFlow)
                .map(flowServiceApiMapper::toDTO)
                .peek(aFlow -> updateFlowDTO(aFlow, flow))
                .onEmpty(() -> createNewDefaultFlow(flow));
    }

    private void createNewDefaultFlow(FlowDTO flow) {
        if (!flowConfig.isNewGeohashesResolutionEnabled()) {
            val market = marketService.selectGlobalMarket();
            val flowByGeoHashes = marketService.toFlowByGeoHash(flow, market);
            flowByGeoHashes.forEach(flowByGeoHashService::insert);
        }

        flowRoutingGroupsService.insertRoutingGroups(flow);
        trackUserAction(flow);
        flowDao.insert(controlPlaneMapper.toModel(flow));
        insertFlowByGlobalMarket(flow);
        flowByFlowSetDao.insert(createFlowByFlowSet(flow));
        cachedConfigurationService.cleanAllCache("create_new_default_flow");
        updateEntityCollection(flow);
    }

    private void insertFlowByGlobalMarket(FlowDTO flow) {
        val market = marketService.selectGlobalMarket();
        flowByMarketDao.insert(FlowByMarket.builder()
                .marketName(market.getMarketName())
                .flowSet(flow.getFlowSet())
                .flowId(UUID.fromString(flow.getFlowId()))
                .build());
    }

    public Option<FlowDTO> getFlowDTO(@NonNull UUID flowId) {
        return Option.ofOptional(cachedConfigurationService.getFlowAsync(flowId).join());
    }

    @Override
    public CompletableFuture<Option<FlowDTO>> getFlowAsync(@NonNull UUID flowId) {
        return cachedConfigurationService.getFlowAsync(flowId).thenApply(Option::ofOptional);
    }

    @Override
    public Optional<FlowApi> getFlowWithoutCache(@NonNull UUID flowId) {
        return getFlowAsyncWithoutCache(flowId)
                .join()
                .toJavaOptional()
                .map(flowServiceApiMapper::toApi);
    }

    // TODO(wcase): make async again after casserole 4 migration
    @Override
    public CompletableFuture<Option<FlowDTO>> getFlowAsyncWithoutCache(@NonNull UUID flowId) {
        Optional<Flow> flowOpt = flowDao.select(flowId);
        java.util.List<FlowRoutingGroupV2> flowRoutingGroups = flowRoutingGroupsService.selectAll(flowId);
        return hydratedFlow(Tuple.of(Option.ofOptional(flowOpt), List.ofAll(flowRoutingGroups)));
    }

    private CompletableFuture<Option<FlowDTO>> hydratedFlow(Tuple2<Option<Flow>, List<FlowRoutingGroupV2>> tuple) {
        Optional<Flow> flowOpt = tuple._1.toJavaOptional();
        List<FlowRoutingGroupV2> flowRoutingGroups = tuple._2;
        Optional<FlowDTO> flowDTOOpt = flowOpt.map(flow -> {
            io.vavr.collection.Map<String, List<FlowRoutingGroupDTO>> routingGroupsCriteria = getRoutingGroupsCriteria(flow);
            List<FlowRoutingGroupDTO> routingGroups = flowRoutingGroups.map(controlPlaneMapper::toDTO);
            FlowDTO flowDTO = controlPlaneMapper.toDTO(flow);
            flowDTO.setRoutingGroups(routingGroups);
            flowDTO.setRoutingGroupsCriteria(routingGroupsCriteria);
            return flowDTO;
        });
        return CompletableFuture.completedFuture(Option.ofOptional(flowDTOOpt));
    }

    private io.vavr.collection.Map<String, List<FlowRoutingGroupDTO>> getRoutingGroupsCriteria(Flow flow) {
        Map<String, List<FlowRoutingGroupDTO>> flowRoutingGroups = new HashMap<>();
        flow.getRoutingGroupsCriteria().forEach(routingGroupCriteria ->
                flowRoutingGroups.put(
                        routingGroupCriteria,
                        flowRoutingGroupsService.selectRoutingGroupsForCriteria(flow.getFlowId(), routingGroupCriteria).map(controlPlaneMapper::toDTO)
                )
        );
        return io.vavr.collection.HashMap.ofAll(flowRoutingGroups);
    }

    @Override
    public Optional<FlowApi> getFlow(@NonNull UUID flowId) {
        return getFlowDTO(flowId)
                .map(flowServiceApiMapper::toApi)
                .toJavaOptional();
    }

    public Option<FlowDTO> getFlow(String flowID) {
        return getFlowDTO(MapperUtil.uuidFromString(flowID));
    }

    @Override
    public void updateFlow(@NonNull FlowApi existingFlow, @NonNull FlowApi updatedFlow) {
        updateFlowDTO(
                flowServiceApiMapper.toDTO(existingFlow),
                flowServiceApiMapper.toDTO(updatedFlow)
        );
    }

    private void updateFlowDTO(@NonNull FlowDTO existingFlow, @NonNull FlowDTO updatedFlow) {
        log.debug("Going to update the following flow, flow_id={}", existingFlow.getFlowId());
        cleanBeforeUpdate(existingFlow, updatedFlow);
        insertFlowByGeohashesIfNeeded(existingFlow, updatedFlow);
        updateRoutingGroups(updatedFlow);
        trackUserAction(updatedFlow);
        updateFlow(updatedFlow);
        insertFlowsByMarkets(updatedFlow);
        updateFlowByFlowSet(existingFlow, updatedFlow);
        getFlow(updatedFlow.getFlowId()).peek(controlPlaneLogger::logUpdateFlow);
        cachedConfigurationService.cleanAllCache("update_flow_dto");
        updateEntityCollection(updatedFlow);
    }

    private void updateFlow(@NonNull FlowDTO updatedFlow) {
        flowDao.update(controlPlaneMapper.toModel(updatedFlow));
    }

    private void updateRoutingGroups(@NonNull FlowDTO updatedFlow) {

        io.vavr.collection.Map<String, List<FlowRoutingGroupV2>> flowRoutingGroupsV2 = flowRoutingGroupsService.populateGroupOrderV2(updatedFlow);
        flowRoutingGroupsService.updateFlowRoutingGroupsForFlowV2(flowRoutingGroupsV2.values().toStream().flatMap(List::toStream).toList());
        flowRoutingGroupsService.insertCriteriaRoutingGroups(updatedFlow, flowRoutingGroupsV2);
    }

    private void insertFlowByGeohashesIfNeeded(@NonNull FlowDTO existingFlow, @NonNull FlowDTO updatedFlow) {
        if (!flowConfig.isNewGeohashesResolutionEnabled() && !hasSameFlowByGeohashes(existingFlow, updatedFlow)) {
            log.debug("Inserting flow by geohashes for the following flow, flow_id={}", existingFlow.getFlowId());
            flowByGeoHashService.insertGeoHashes(updatedFlow);
        }
    }

    private void insertFlowsByMarkets(FlowDTO updatedFlow) {
        if (updatedFlow.getLocationMarkets() == null || updatedFlow.getLocationMarkets().isEmpty()) {
            log.debug("Inserting GLOBAL market for the following flow, flow_id={}", updatedFlow.getFlowId());
            insertFlowByGlobalMarket(updatedFlow);
        } else {
            List<FlowByMarket> flowByMarkets = updatedFlow.getLocationMarkets().map(marketName -> FlowByMarket.builder()
                    .flowId(UUID.fromString(updatedFlow.getFlowId()))
                    .marketName(marketName)
                    .flowSet(updatedFlow.getFlowSet())
                    .build()
            ).toList();
            log.debug("Inserting flow by markets flow_by_markets={} for the following flow, flow_id={}", flowByMarkets, updatedFlow.getFlowId());
            flowByMarketDao.insertMany(flowByMarkets.toJavaList());
        }
    }

    private boolean hasSameFlowByGeohashes(@NonNull FlowDTO existingFlow, @NonNull FlowDTO updatedFlow) {
        Set<FlowByGeoHash> existingGeohashes = marketService.getFlowsByGeoHash((existingFlow)).toSet();
        List<FlowByGeoHash> toBeUpdatedFlowsByGeohashes = marketService.getFlowsByGeoHash(updatedFlow);
        return toBeUpdatedFlowsByGeohashes.size() == existingGeohashes.size() &&
               toBeUpdatedFlowsByGeohashes.toJavaStream().allMatch(existingGeohashes::contains);
    }

    private void cleanBeforeUpdate(@NonNull FlowDTO existingFlow, @NonNull FlowDTO updatedFlow) {
        if (!flowConfig.isNewGeohashesResolutionEnabled() && !hasSameFlowByGeohashes(existingFlow, updatedFlow)) {
            log.debug("Going to delete the geohash for flow, flow_id={}", existingFlow.getFlowId());
            flowByGeoHashService.deleteGeoHashesForFlow(existingFlow);
        }

        flowRoutingGroupsService.deleteRoutingGroupsForFlow(controlPlaneMapper.toModel(existingFlow));
        List<String> locationMarkets = existingFlow.getLocationMarkets().collect(List.collector());
        log.debug("Going to delete the following location markets, location_markets={} for flow, flow_id={}",
                locationMarkets, existingFlow.getFlowId());
        locationMarkets
                .forEach(marketName -> flowByMarketDao.delete(existingFlow.getFlowSet(), marketName, UUID.fromString(existingFlow.getFlowId())));

    }

    private void trackUserAction(FlowDTO updatedFlow) {
        if (StringUtils.isEmpty(updatedFlow.getUpdatedUser())) {
            updatedFlow.setUpdatedUser(sessionAttributeProvider.resolveUserEmail());
        }
        updatedFlow.setUpdatedTimestamp(Date.from(Instant.now()));
    }

    private void updateFlowByFlowSet(FlowDTO existingFlow, FlowDTO updatedFlow) {
        var updateFlowByFlowSet = createFlowByFlowSet(updatedFlow);
        if (existingFlow.getFlowSet().equals(updatedFlow.getFlowSet()) && existingFlow.getFlowId().equals(updatedFlow.getFlowId())) {
            flowByFlowSetDao.update(updateFlowByFlowSet);
        } else {
            flowByFlowSetDao.delete(existingFlow.getFlowSet(), UuidUtil.decode(existingFlow.getFlowId()));
            flowByFlowSetDao.insert(updateFlowByFlowSet);
        }
    }

    private void updateEntityCollection(FlowDTO flow) {
        entityCollectionDao.update(new EntityCollection(EntityCollection.Constants.COLLECTION_FLOW, flow.getFlowId(),
                String.valueOf(flow.isEnabled()), NO_CATEGORY));
        entityCollectionDao.update(new EntityCollection(EntityCollection.Constants.COLLECTION_FLOW_SET,
                flow.getFlowSet(), "ENABLED", NO_CATEGORY));
    }

    @Override
    public void deleteFlow(@NonNull UUID flowId) {
        getFlowDTO(flowId)
                .peek(flow -> flowByFlowSetDao.delete(flow.getFlowSet(), flowId))
                .peek(flow -> getLocalMarketsOrGlobal(flow).forEach(marketName -> flowByMarketDao.delete(flow.getFlowSet(), marketName, flowId)))
                .peek(this::logAndDeleteFlowRelationship)
                .peek(flow -> flowDao.delete(UUID.fromString(flow.getFlowId())))
                .peek(any -> cachedConfigurationService.cleanAllCache("delete_flow"))
                .peek(this::deleteFlowSetEntityCollection)
                .peek(flow -> entityCollectionDao.delete(EntityCollection.Constants.COLLECTION_FLOW, flow.getFlowId()));
    }

    private io.vavr.collection.Set<String> getLocalMarketsOrGlobal(FlowDTO flow) {
        return Option.of(flow.getLocationMarkets())
                .filter(Traversable::nonEmpty)
                .getOrElse(this::getGlobalMarketName);
    }

    private io.vavr.collection.HashSet<String> getGlobalMarketName() {
        return io.vavr.collection.HashSet.of(GLOBAL_MARKET.getMarketName());
    }

    private void logAndDeleteFlowRelationship(FlowDTO flow) {
        flowRoutingGroupsService.deleteRoutingGroupsForFlow(controlPlaneMapper.toModel(flow));
        flowByGeoHashService.deleteGeoHashesForFlow(flow);
        controlPlaneLogger.logDeleteFlow(flow);
    }

    private void deleteFlowSetEntityCollection(FlowDTO flow) {
        if (getFlowsByFlowSet(flow.getFlowSet(), null).isEmpty()) {
            log.debug("Deleting flowSet={} from entity collection due to deleting last flow={} in the flowSet.",
                    flow.getFlowSet(), flow.getFlowName());
            entityCollectionDao.delete(EntityCollection.Constants.COLLECTION_FLOW_SET, flow.getFlowSet());
        }
    }

    @Override
    public Optional<FlowApi> getFlowByFlowSetAndLocation(String flowSet, String location) {
        return getFlowByFlowSetAndLocationDTO(flowSet, location).map(flowServiceApiMapper::toApi).toJavaOptional();
    }

    private Option<FlowDTO> getFlowByFlowSetAndLocationDTO(String flowSet, String location) {
        return Option.ofOptional(getGeoHash(location, flowConfig.getPrecision()))
                .flatMap(geoHash -> flowByGeoHashService.select(flowSet, geoHash))
                .map(FlowByGeoHash::getFlowId)
                .map(flowDao::select)
                .map(optionalFlow -> optionalFlow.orElse(null))
                .map(controlPlaneMapper::toDTO);
    }

    @Override
    public Optional<FlowResponseApi> resolveFlow(ResolveFlowRequest resolveFlowRequest) {
        try {
            log.debug("Resolving a flow using resolve_flow_request={}", resolveFlowRequest);
            validateInput(resolveFlowRequest);
            return Option.of(resolveFlowRequest)
                    .filter(this::isVariationIdNullOrEmpty)
                    .flatMap(this::resolveFlowInRealMode)
                    .map(flowServiceApiMapper::toApi)
                    .peek(flowResponseApi -> log.debug("Calling resolve flow with input={} return output={}", resolveFlowRequest, flowResponseApi))
                    .orElse(() -> resolveFlowInSimulationMode(resolveFlowRequest).map(flowServiceApiMapper::toApi))
                    .toJavaOptional();
        } catch (ConstraintViolationException cve) {
            meterRegistry.counter(Metrics.name(getClass(), "resolveFlowFailures"))
                    .increment();
            throw new RuntimeException(cve);
        } catch (Exception e) {
            meterRegistry.counter(Metrics.name(getClass(), "resolveFlowFailures"))
                    .increment();
            throw new RuntimeException(String.format("Could not resolve flow for request with callerTrackingId=%s, " +
                                                     "applicationId=%s, applicationVersion=%s, dinerId=%s, flowSet=%s, lat=%f, lng=%f, " +
                                                     "totalOrders=%s, variationId=%s, " +
                                                     "orderType=%s, queryText=\"%s\", whenFor=%s, requestTags=%s",
                    resolveFlowRequest.getCallerTrackingId(),
                    resolveFlowRequest.getApplicationId(),
                    resolveFlowRequest.getApplicationVersion(),
                    resolveFlowRequest.getDinerId(),
                    resolveFlowRequest.getFlowSet(),
                    resolveFlowRequest.getLat(),
                    resolveFlowRequest.getLng(),
                    resolveFlowRequest.getTotalOrders(),
                    resolveFlowRequest.getVariationId(),
                    resolveFlowRequest.getOrderType(),
                    resolveFlowRequest.getQueryText(),
                    resolveFlowRequest.getWhenFor(),
                    resolveFlowRequest.getRequestTags()
            ), e);
        }
    }

    private boolean isVariationIdNullOrEmpty(ResolveFlowRequest aResolveFlowRequest) {
        return !VariationInput.hasValidFormat(aResolveFlowRequest.getVariationId());
    }

    public Option<FlowResponseDTO> resolveFlowInSimulationMode(ResolveFlowRequest resolveFlowRequest) {
        return Option.of(resolveFlowRequest)
                .filter(aResolveFlowRequest -> aResolveFlowRequest.getVariationId() != null)
                .flatMap(this::resolveFlowInSimulationModeForNonNullVariationId);

    }

    private Option<FlowResponseDTO> resolveFlowInSimulationModeForNonNullVariationId(ResolveFlowRequest resolveFlowRequest) {
        log.info("ResolveFlow in simulation mode with variationId={}", resolveFlowRequest.getVariationId());
        val variationInput = new VariationInput(resolveFlowRequest.getVariationId());

        return toEnrichedResolveFlowRequest(resolveFlowRequest)
                .withUserEmail(sessionAttributeProvider.resolveUserEmail())
                .withUserDomain(sessionAttributeProvider.resolveUserEmail())
                .getFlowByGeoHash(this::getFlowWithGeoHashesForVariation)
                .selectFlow()
                .selectRoutingGroups(flow -> selectRoutingGroupsByIdAndName(flow, variationInput))
                .buildResponse(this::toFlowResponse);
    }

    private FlowWithGeoHashStream getFlowWithGeoHashesForVariation(EnrichedResolveFlowRequest enrichedResolveFlowRequest) {
        val variationInput = new VariationInput(enrichedResolveFlowRequest.getVariationId());

        return getFlowDTO(variationInput.getFlowId())
                .map(flow -> asFlowWithGeoHashes(marketService.getFlowsByGeoHash(flow), flow, enrichedResolveFlowRequest))
                .map(List::of)
                .map(FlowWithGeoHashStream::asFlowWithGeoHashStream)
                .getOrElse(FlowWithGeoHashStream::empty);
    }

    public Option<FlowResponseDTO> resolveFlowInRealMode(ResolveFlowRequest resolveFlowRequest) {
        val resolveFlowFindGeohashSpan = TRACING_HELPER.startSpanManual("resolveFlowFindGeohash",
                Collections.singletonMap("flow_set", resolveFlowRequest.getFlowSet()));
        val resolveFlowEventBuilder = ResolveFlowEvent.builder().requestTime(dateTimeHelper.instant());

        val flowWithGeoHashStream = enrichResolveFlowRequestWithDinerType(resolveFlowRequest)
                .withTrackingId(sessionAttributeProvider.resolveTrackingId())
                .withBrand(Brand.of(sessionAttributeProvider.resolveBrand()).getName())
                .withQueryTokens(resolveFlowRequest.getQueryText())
                .withQueryType(resolveFlowRequest.getQueryText())
                .withQueryKeywords(resolveFlowRequest.getQueryText())
                .withMealTimeByTimeOfDayConfig(mealTimeByTimeOfDayConfig)
                .withMealtime(resolveFlowRequest.getWhenFor())
                .withUserEmail(sessionAttributeProvider.resolveUserEmail())
                .withUserDomain(sessionAttributeProvider.resolveUserEmail())
                .withFlowConfig(flowConfig)
                .addToResolveFlowEvent(resolveFlowEventBuilder)
                .getFlowByGeoHash(this::getFlowByGeoHash)
                .addSpan(resolveFlowFindGeohashSpan);

        val resolveFlowApplyFiltersSpan = TRACING_HELPER.startSpanManual("resolveFlowApplyFilters",
                Collections.singletonMap("flow_set", resolveFlowRequest.getFlowSet()));

        val flowResponseContextAsOpt = flowWithGeoHashStream
                .applyFlowFilters(this::matchModelClassification)
                .clearCache()
                .selectFlow()
                .selectRoutingGroups(this::selectRoutingGroups)
                .addSpan(resolveFlowApplyFiltersSpan);

        val resolveFlowSelectRoutingGroupSpan = flowResponseContextAsOpt.getSelectRoutingGroupSpanOrNoop();

        return flowResponseContextAsOpt
                .buildResponse(this::toFlowResponse)
                .peek(flowResponse -> log.debug("Resolve flow response flow_response={}", flowResponse))
                .peek(this::flowMetric)
                .peek(flowResponse -> logEvent(resolveFlowEventBuilder, flowResponse))
                .onEmpty(() -> logEvent(resolveFlowEventBuilder, null))
                .peek(flowResponse -> resolveFlowSelectRoutingGroupSpan.finish());
    }

    private ModelInferenceOutput matchModelClassification(FlowWithGeoHashDTO flowWithGeoHash) {
        val modelInferenceRequest = ModelInferenceRequest.builder()
                .callerTrackingId(flowWithGeoHash.getTrackingId())
                .modelName(flowWithGeoHash.getClassificationModelName())
                .globalFeatures(Collections.emptyMap())
                .features(Collections.singletonList(buildFeaturesMap(flowWithGeoHash)))
                .build();
        return this.modelService.invokeModelInferenceMultiOutput(modelInferenceRequest);
    }

    private Map<String, Object> buildFeaturesMap(FlowWithGeoHashDTO flowWithGeoHash) {
        Map<String, Object> featuresMap = new HashMap<>(14);
        EnrichedResolveFlowRequest resolveFlowRequest = flowWithGeoHash.getResolveFlowRequest();

        featuresMap.put("application_id", resolveFlowRequest.getApplicationId());
        featuresMap.put("application_version", resolveFlowRequest.getApplicationVersion());
        featuresMap.put("DINER_ID", resolveFlowRequest.getDinerId());
        featuresMap.put("flow_set", flowWithGeoHash.getFlowSet());
        featuresMap.put("diner_latitude", resolveFlowRequest.getLat());
        featuresMap.put("diner_longitude", resolveFlowRequest.getLng());
        featuresMap.put("total_orders", resolveFlowRequest.getTotalOrders());
        featuresMap.put("order_type", flowWithGeoHash.getResolvedOrderType());
        featuresMap.put("query_text", resolveFlowRequest.asResolveFlowRequest().getQueryText());
        featuresMap.put("query_tokens", flowWithGeoHash.getResolvedQueryTokens());
        featuresMap.put("diner_type", flowWithGeoHash.getFlow().getResolvedDinerType());
        featuresMap.put("mealtime", flowWithGeoHash.getMealtime());
        featuresMap.put("query_type", flowWithGeoHash.getResolvedQueryType());
        featuresMap.put("request_tags", flowWithGeoHash.getFlow().getMatchingRequestTags());
        featuresMap.put("query_keyword", flowWithGeoHash.getResolvedQueryKeyword());
        return featuresMap;
    }

    private void flowMetric(FlowResponseDTO flowResponse) {
        String routingGroupCriteria = Option.of(flowResponse)
                .map(FlowResponseDTO::getRoutingGroup)
                .map(RoutingGroupResponseDTO::getRoutingGroupCriteria)
                .getOrElse(StringUtils.EMPTY);
        meterRegistry.counter(
                        Metrics.name(getClass(), "resolveFlow"),
                        Tags.of("flow_id", flowResponse.getFlowId(),
                                "flow_name", flowResponse.getFlowName(),
                                "group_name", flowResponse.getRoutingGroup().getGroupName(),
                                "flow_set", flowResponse.getFlowSet(),
                                "routing_group_criteria", routingGroupCriteria))
                .increment();
    }

    private void logEvent(ResolveFlowEvent.ResolveFlowEventBuilder resolveFlowEventBuilder, FlowResponseDTO flowResponse) {
        controlPlaneLogger.logResolveFlowRequest(resolveFlowEventBuilder.response(flowResponse).build());
    }

    @Override
    public java.util.List<EntityCollectionDTO> getAllFlows() {
        return getAllFlowDTO().toJavaList();
    }

    @Override
    public java.util.List<FlowSummaryDTO> getAllFlowsDetailed(String flowSet) {
        return getAllFlowDetailedDTO(flowSet).toJavaList();
    }

    @Override
    public List<FlowDTO> getFlowsWithIdIn(List<UUID> ids) {
        return flowDao.selectFlowsWithIds(ids.toSet())
                .stream()
                .map(controlPlaneMapper::toDTO)
                .collect(List.collector());
    }

    private List<EntityCollectionDTO> getAllFlowDTO() {
        return entityCollectionDao.selectAll(EntityCollection.Constants.COLLECTION_FLOW)
                .map(controlPlaneMapper::toDTO);
    }

    private Set<UUID> getFlowIds() {
        return entityCollectionDao.selectAll(EntityCollection.Constants.COLLECTION_FLOW)
                .map(x -> UUID.fromString(controlPlaneMapper.toDTO(x).getEntityId()))
                .collect(HashSet.collector());
    }

    private Set<UUID> getFlowIds(String flowSetList) {
        return Pattern.compile(",")
                .splitAsStream(flowSetList)
                .map(flowByFlowSetDao::select)
                .flatMap(java.util.List::stream)
                .map(dto -> UUID.fromString(controlPlaneMapper.toDTO(dto).getFlowId()))
                .collect(HashSet.collector());
    }

    private List<FlowSummaryDTO> getAllFlowDetailedDTO(String flowSetList) {
        Set<UUID> ids = StringUtils.isBlank(flowSetList)
                ? getFlowIds()
                : getFlowIds(flowSetList);

        return flowDao.selectFlowsWithIds(ids)
                .stream()
                .map(controlPlaneMapper::toSummaryDTO)
                .collect(List.collector());
    }

    @VisibleForTesting
    FlowResponseContext selectRoutingGroups(FlowWithGeoHashDTO flowWithGeoHash) {
        return new FlowResponseContext(flowWithGeoHash, flowWithGeoHash.getRoutingGroupsForFlowCriteria().toStream()
                .sorted(FlowRoutingGroupDTO::compareRoutingGroupsByOrder)
                .map(flowRoutingGroup -> controlPlaneMapper.toModelV2(flowRoutingGroup, flowWithGeoHash.getFlow()))
                .collect(List.collector()));
    }

    private FlowResponseContext selectRoutingGroupsByIdAndName(FlowWithGeoHashDTO flowWithGeoHash, VariationInput variationInput) {
        val flowId = flowWithGeoHash.getFlowIdAsUUID();
        val selectedRoutingGroup = selectRoutingGroupInSumulation(flowWithGeoHash, variationInput, flowId);

        return new FlowResponseContext(flowWithGeoHash, List.of(selectedRoutingGroup));
    }

    private FlowRoutingGroupV2 selectRoutingGroupInSumulation(FlowWithGeoHashDTO flowWithGeoHash, VariationInput variationInput, UUID flowId) {
        return flowWithGeoHash.getFlow()
                .getRoutingGroupsCriteria()
                .get("default")
                .get()
                .find(routingGroupCriteria -> isFlowIdAndGroupNameEqual(variationInput, flowId, routingGroupCriteria))
                .map(routingGroupCriteria -> controlPlaneMapper.toModelV2(routingGroupCriteria, flowWithGeoHash.getFlow()))
                .getOrElseThrow(() -> new RuntimeException("Routing group name was not found!"));
    }

    private boolean isFlowIdAndGroupNameEqual(VariationInput variationInput, UUID flowId, FlowRoutingGroupDTO routingGroup) {
        return UUID.fromString(routingGroup.getFlowId()).equals(flowId) &&
               routingGroup.getGroupName().equals(variationInput.getRoutingGroupName());
    }

    private Option<FlowResponseDTO> toFlowResponse(FlowResponseContext flowResponseContext) {
        val routingGroups = flowResponseContext.getFlowRoutingGroups();
        val flowWithGeoHash = flowResponseContext.getFlowWithGeoHash();

        var routingGroupStrategyInput = RoutingGroupStrategyInput.builder()
                .flow(flowWithGeoHash.getFlow())
                .flowConfig(flowConfig)
                .flowRoutingGroups(routingGroups)
                .branchManager(experimentBranchManager)
                .resolveFlowRequest(flowWithGeoHash.getResolveFlowRequest())
                .controlPlaneMapper(controlPlaneMapper).build();

        val routingGroupStrategy = RoutingGroupStrategyFactory.pickFlowRoutingGroupStrategy(routingGroupStrategyInput);
        val routingGroupV2 = routingGroupStrategy.selectRoutingGroup();

        val features = modelService.getFilteredFeaturesForEnsembleOrModel(routingGroupV2, feature -> !feature.isInternal());
        return getFlowResponseDTO(flowWithGeoHash, routingGroupV2, features);
    }

    private Option<FlowResponseDTO> getFlowResponseDTO(FlowWithGeoHashDTO flowWithGeoHash,
                                                       RoutingGroupResponseDTO routingGroupResponse,
                                                       List<FeatureDTO> features) {
        return Option.of(flowWithGeoHash)
                .map(FlowWithGeoHashDTO::getFlow)
                .map(flow -> {
                            val builder = createBuilder(flowWithGeoHash, routingGroupResponse, features);
                            return controlPlaneMapper.toFlowResponse(builder);
                        }
                );
    }

    private FlowResponseBuilder createBuilder(
            FlowWithGeoHashDTO flowWithGeoHash,
            RoutingGroupResponseDTO routingGroupResponse,
            List<FeatureDTO> features) {
        return FlowResponseBuilder
                .builder()
                .routingGroupResponse(routingGroupResponse)
                .flowWithGeoHashDTO(flowWithGeoHash)
                .markets(flowWithGeoHash.getLocationMarkets().toList())
                .features(features)
                .build();
    }

    private FlowWithGeoHashStream getFlowByGeoHash(EnrichedResolveFlowRequest resolveFlowRequest) {
        val flowsByLocation = getFlowsByLocation(resolveFlowRequest);
        return asFlowWithGeoHashStream(flowsByLocation);
    }

    private List<FlowWithGeoHashDTO> getFlowsByLocation(EnrichedResolveFlowRequest resolveFlowRequest) {
        return asOptionalFlowResolveRequest(resolveFlowRequest)
                .withResolvedGeoHash()
                .asFlowWithGeoHashes(this::pickGeohashFlowResolver);
    }

    private List<FlowWithGeoHashDTO> pickGeohashFlowResolver(EnrichedResolveFlowRequest resolveFlowRequest) {
        return flowConfig.isNewGeohashesResolutionEnabled()
                ? concatFlowsForCustomAndGlobalGeoHashesWithNewFlow(resolveFlowRequest)
                : concatFlowsForCustomAndGlobalGeoHashes(resolveFlowRequest);
    }

    private List<FlowWithGeoHashDTO> concatFlowsForCustomAndGlobalGeoHashes(EnrichedResolveFlowRequest resolveFlowRequest) {
        log.debug("Resolving flows using old geohash resolution for request={}", resolveFlowRequest);
        val flowSet = resolveFlowRequest.getFlowSet();
        List<FlowWithGeoHashDTO> flowWithGeoHashes = cachedConfigurationService
                .getFlowsByGeohash(flowSet, resolveFlowRequest.getResolvedGeoHash())
                .thenApply(flowByGeoHashes -> {
                    log.debug("Found flow by geohashes flow_by_geohashes={} for flow set flow_set={} and " +
                              "resolved geohash resolved_geohash={}",
                            flowByGeoHashes.stream().map(FlowByGeoHash::getGeohash),
                            flowSet, resolveFlowRequest.getResolvedGeoHash()
                    );
                    return flowByGeoHashes;
                })
                .thenCombine(
                        flowByGeoHashService
                                .selectAllAsync(flowSet, GLOBAL_MARKET.getMarketName())
                                .thenApply(flowByGeoHashes -> {
                                    log.debug("Found flow by geohashes flow_by_geohashes={} for flow set flow_set={} and " +
                                              "global market", flowByGeoHashes.map(FlowByGeoHash::getGeohash), flowSet);
                                    return flowByGeoHashes;
                                }),
                        (flowByGeohashes, globalFlowByGeohashes) ->
                                concatenateStreams(List.ofAll(flowByGeohashes), globalFlowByGeohashes)
                )
                .thenApply(flowByGeoHashes -> getEnabledFlowsWithGeoHashes(resolveFlowRequest, flowByGeoHashes))
                .join();
        log.debug("Found #{} flows flows_by_geohashes={}", flowWithGeoHashes.size(), flowWithGeoHashes.map(FlowWithGeoHashDTO::getFlowByGeoHashes));

        return flowWithGeoHashes;
    }

    private List<FlowWithGeoHashDTO> concatFlowsForCustomAndGlobalGeoHashesWithNewFlow(EnrichedResolveFlowRequest resolveFlowRequest) {
        log.debug("Resolving flows using new geohash resolution for request={}", resolveFlowRequest);
        List<FlowWithGeoHashDTO> flowWithGeoHashes = cachedConfigurationService
                .getMarketByGeohash(flowConfig.getPrecision(), resolveFlowRequest.getResolvedGeoHash())
                .thenApply(marketByGeohashDTOS -> {
                    log.debug("Found markets by geohashes market_by_geohashes={} for precision={} and " +
                              "resolved geohash resolved_geohash={}", marketByGeohashDTOS, flowConfig.getPrecision(), resolveFlowRequest.getResolvedGeoHash());
                    return marketByGeohashDTOS;
                })
                .thenCompose(markets -> getFlowsByMarkets(resolveFlowRequest, List.ofAll(markets)))
                .thenApply(flowByMarkets -> {
                    log.debug("Found flow by markets flow_by_markets={} for flow set flow_set={} and flowByMarkets={}" +
                              "the resolved markets", resolveFlowRequest.getFlowSet(), resolveFlowRequest.getFlowSet(), flowByMarkets);
                    return flowByMarkets;
                })
                .thenApply(flowByMarkets -> toFlowByGeohashes(flowByMarkets, resolveFlowRequest.getResolvedGeoHash()))
                .thenApply(flowByGeoHashes -> {
                    log.debug("Found flow by geohashes for new geohash resolution flow_by_geohashes={} for flow set flow_set={} and " +
                              "resolved geohash resolved_geohash={}", flowByGeoHashes.map(FlowByGeoHash::getGeohash),
                            resolveFlowRequest.getFlowSet(),
                            resolveFlowRequest.getResolvedGeoHash());
                    return flowByGeoHashes;
                })
                .thenApply(flowByGeoHashes -> getEnabledFlowsWithGeoHashes(resolveFlowRequest, flowByGeoHashes))
                .join();
        log.debug("Found #{} flows for the new resolution flows_by_geohashes={}, flow flow_ids={}",
                flowWithGeoHashes.size(), flowWithGeoHashes.map(FlowWithGeoHashDTO::getFlowByGeoHashes), flowWithGeoHashes.map(FlowWithGeoHashDTO::getFlowId));

        return flowWithGeoHashes;
    }

    private CompletableFuture<List<FlowByMarketDTO>> getFlowsByMarkets(EnrichedResolveFlowRequest resolveFlowRequest, List<MarketByGeohashDTO> markets) {
        return cachedConfigurationService.getFlowsByMarketsAsync(
                resolveFlowRequest.getFlowSet(),
                markets.map(MarketByGeohashDTO::getMarketName).append(GLOBAL_MARKET.getMarketName()).asJava()
        ).thenApply(List::ofAll);
    }

    private List<FlowByGeoHash> toFlowByGeohashes(List<FlowByMarketDTO> flowByMarkets, String geohash) {
        return flowByMarkets.map(controlPlaneMapper::toModel).map(flowByMarket -> flowByMarket.toFlowByGeoHash(geohash));
    }

    private List<FlowByGeoHash> concatenateStreams(List<FlowByGeoHash> geoHashesForCustomFlow, List<FlowByGeoHash> geoHashesForGlobalFlow) {
        return Stream
                .concat(getFlowByGeoHashesOrEmpty(geoHashesForCustomFlow, () -> log.warn("Custom geohashes are empty!")),
                        getFlowByGeoHashesOrEmpty(geoHashesForGlobalFlow, () -> log.warn("Global geohases are empty!")))
                .collect(List.collector());
    }

    private Stream<FlowByGeoHash> getFlowByGeoHashesOrEmpty(List<FlowByGeoHash> geoHashesForCustomFlow, Runnable warningMessage) {
        return Option.of(geoHashesForCustomFlow)
                .map(List::toStream)
                .onEmpty(warningMessage)
                .getOrElse(Stream::empty);
    }

    private List<FlowWithGeoHashDTO> getEnabledFlowsWithGeoHashes(EnrichedResolveFlowRequest resolveFlowRequest, List<FlowByGeoHash> flowByGeoHashes) {
        return flowByGeoHashes
                .toStream()
                .map(FlowByGeoHash::getFlowId)
                .map(this::getFlowAsync)
                .collect(collectingAndThen(
                        List.collector(),
                        flows -> waitForCompletion(resolveFlowRequest, flowByGeoHashes, flows)
                ));
    }

    private List<FlowWithGeoHashDTO> waitForCompletion(EnrichedResolveFlowRequest resolveFlowRequest, List<FlowByGeoHash> flowByGeoHashes,
                                                       List<CompletableFuture<Option<FlowDTO>>> flows) {
        return flows
                .toStream()
                .map(CompletableFuture::join)
                .filter(Option::isDefined)
                .map(Option::get)
                .map(flow -> flow.withResolvedDinerType(determineDinerType(flow, resolveFlowRequest).name()))
                .map(flow -> asFlowWithGeoHashes(flowByGeoHashes, flow, resolveFlowRequest))
                .filter(FlowWithGeoHashDTO::isEnabled)
                .collect(List.collector());
    }

    private DinerType determineDinerType(FlowDTO flow, EnrichedResolveFlowRequest resolveFlowRequest) {
        return UnauthenticatedDinerRule.match()
                .orElse(NewDinerRule.match(resolveFlowRequest.getFlowConfig()))
                .orElse(ExistingDinerRule.match(resolveFlowRequest.getFlowConfig()))
                .applicableTo(resolveFlowRequest.asResolveFlowRequest(), flow.getDinerTypeOrdersThreshold())
                .orElseThrow(() -> new RuntimeException("Unexpected exception occur when trying to determine diner type"));
    }

    private FlowWithGeoHashDTO asFlowWithGeoHashes(List<FlowByGeoHash> flowByGeoHashes, FlowDTO flow, EnrichedResolveFlowRequest request) {
        val geoHashes = flowByGeoHashes
                .toStream()
                .filter(flowByGeoHash -> isFlowIdEquals(flow, flowByGeoHash))
                .map(flowByGeoHash -> controlPlaneMapper.toDTO(flowByGeoHash, getSafeResolveDinerType(flow)))
                .collect(List.collector());
        return new FlowWithGeoHashDTO(flow, geoHashes, request);
    }

    private DinerType getSafeResolveDinerType(FlowDTO flow) {
        return Optional.ofNullable(flow)
                .map(FlowDTO::getResolvedDinerType)
                .map(DinerType::valueOf)
                .orElse(DinerType.EMPTY_ALL);
    }

    private boolean isFlowIdEquals(FlowDTO flow, FlowByGeoHash flowByGeoHash) {
        return Objects.equals(flowByGeoHash.getFlowId(), UuidUtil.decode(flow.getFlowId()));
    }

    @Override
    public List<FlowByMarketDTO> getFlowsByMarkets(String flowSet, List<String> marketNames) {
        return List.ofAll(cachedConfigurationService.getFlowsByMarketsAsync(flowSet, marketNames.asJava()).join());
    }

    @Override
    public CompletableFuture<java.util.List<FlowByMarketDTO>> getFlowsByMarketsWithoutCache(String flowSet, List<String> marketNames) {
        return flowByMarketDao.getFlowsByMarketsAsync(flowSet, marketNames.asJava())
                .map(controlPlaneMapper::toDTO)
                .toListCompletableFuture();
    }

    @Override
    public CompletableFuture<java.util.List<FlowByMarketDTO>> getFlowsByMarketWithoutCache(String flowSet, String marketName) {
        return flowByMarketDao.getFlowsByMarketAsync(flowSet, marketName)
                .map(controlPlaneMapper::toDTO)
                .toListCompletableFuture();
    }

    @Override
    public List<FlowByFlowSetDTO> getFlowsByFlowSet(String flowSet, Boolean enabled) {
        java.util.List<FlowByFlowSetDTO> flowByFlowSetList = flowByFlowSetDao.selectAsync(flowSet)
                .map(controlPlaneMapper::toDTO)
                .getList();
        if (enabled != null) { // If flag is not sent, return all flows
            return List.ofAll(flowByFlowSetList.stream().filter(flow -> flow.isEnabled() == enabled));
        }
        return List.ofAll(flowByFlowSetList);
    }

    @Override
    public List<EntityCollectionDTO> getAllFlowSets() {
        return entityCollectionDao.selectAll(EntityCollection.Constants.COLLECTION_FLOW_SET, "ENABLED")
                .map(controlPlaneMapper::toDTO);
    }

    @Override
    public void cleanCache() {
        cachedConfigurationService.cleanAllCache("clean_cache_api");
    }

    @Override
    public void configureFlowsGlobal(Integer randomSeedForFlow) {
        val selectedFlowsIds = selectFlowIdsForSeeding();
        val selectedFlows = selectFlowsForSeeding(selectedFlowsIds);
        updateSeeds(selectedFlows, randomSeedForFlow);
    }

    private List<UUID> selectFlowIdsForSeeding() {
        return entityCollectionDao.selectAll(EntityCollection.Constants.COLLECTION_FLOW, "true")
                .map(EntityCollection::getEntityId)
                .map(UUID::fromString)
                .toList();
    }

    private List<FlowDTO> selectFlowsForSeeding(List<UUID> flowIds) {
        return flowIds.map(this::getFlowAsync)
                .collect(collectingAndThen(
                        List.collector(),
                        ControlPlaneUtils::waitForCompletionWithOptions
                ));
    }

    private void updateSeeds(List<FlowDTO> flows, Integer randomSeedForFlow) {
        new PredefinedSeedSelector(flows).select().forEach(this::generatePredefinedSeed);
        new RandomizedSeedSelector(flows).select().forEach(flow -> generateRandomSeed(flow, randomSeedForFlow));
    }

    private void generateRandomSeed(FlowDTO flow, Integer randomSeedForFlow) {
        updateFlowSeed(flow, randomSeedForFlow, "RANDOM");
    }

    private void generatePredefinedSeed(FlowDTO flow) {
        updateFlowSeed(flow, getSeed(flow), "PREDEFINED");
    }

    private int getSeed(FlowDTO flow) {
        return Integer.parseInt(flow.getRoutingGroupsSeedRotation().get(getNextFlowRotatingSeedIndex(flow)));
    }

    private int getNextFlowRotatingSeedIndex(FlowDTO flow) {
        return getDayOfYear(dateTimeHelper) % getSizeOfRoutingGroupsSeed(flow);
    }

    private int getSizeOfRoutingGroupsSeed(FlowDTO flow) {
        return (flow.getRoutingGroupsSeedRotation() == null || flow.getRoutingGroupsSeedRotation().isEmpty())
                ? 1
                : flow.getRoutingGroupsSeedRotation().size();
    }

    private void updateFlowSeed(FlowDTO flow, int routingGroupsSeed, String method) {
        try {
            if (routingGroupsSeed != flow.getRoutingGroupsSeed()) {
                flow.setRoutingGroupsSeed(routingGroupsSeed);
                updateFlowSeedOnly(flow);
                log.info("Updating the flow with id flow_id={} and name flow_name={}. Seed={} = Method={}.",
                        flow.getFlowId(),
                        flow.getFlowName(),
                        routingGroupsSeed,
                        method);
            }
        } catch (Exception e) {
            log.error("Failed to get routing groups seed rotation routing_groups_seed_rotation={}, for flow with id flow_id={}",
                    flow.getRoutingGroupsSeedRotation(), flow.getFlowId(), e);
        }
    }

    private void updateFlowSeedOnly(FlowDTO flow) {
        updateFlow(flow);
        getFlow(flow.getFlowId()).peek(controlPlaneLogger::logUpdateFlow);
        cachedConfigurationService.cleanAllCache("update_flow_seed_only");
        updateEntityCollection(flow);
    }

}

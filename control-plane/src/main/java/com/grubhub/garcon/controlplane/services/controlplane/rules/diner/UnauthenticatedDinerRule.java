package com.grubhub.garcon.controlplane.services.controlplane.rules.diner;

import com.google.common.base.Strings;
import com.grubhub.garcon.controlplane.cassandra.models.DinerType;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.ResolveFlowRequest;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

import static com.grubhub.garcon.controlplane.cassandra.models.DinerType.UNAUTHENTICATED;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class UnauthenticatedDinerRule implements DinerRule {

    public static DinerRule match() {
        return new UnauthenticatedDinerRule();
    }

    @Override
    public Optional<DinerType> applicableTo(ResolveFlowRequest resolveFlowRequest, Integer dinerTypeOrdersThreshold) {
        return Strings.isNullOrEmpty(resolveFlowRequest.getDinerId())
                ? Optional.of(UNAUTHENTICATED)
                : Optional.empty();
    }
}

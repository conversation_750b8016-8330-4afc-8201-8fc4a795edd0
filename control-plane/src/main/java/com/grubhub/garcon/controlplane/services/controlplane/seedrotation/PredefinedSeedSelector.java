package com.grubhub.garcon.controlplane.services.controlplane.seedrotation;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import io.vavr.collection.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

@Slf4j
@RequiredArgsConstructor
public class PredefinedSeedSelector implements SeedSelector {

    private final List<FlowDTO> flows;

    private boolean nonEmptyRoutingGroupsSeedRotation(FlowDTO flow) {
        return flow.getRoutingGroupsSeedRotation() != null
                && !flow.getRoutingGroupsSeedRotation().isEmpty()
                && (flow.getRoutingGroupsSeedRotationEnabled() == null || !flow.getRoutingGroupsSeedRotationEnabled());
    }

    @Override
    public List<FlowDTO> select() {
       val selectedFlows = flows.filter(this::nonEmptyRoutingGroupsSeedRotation);
        if (!selectedFlows.isEmpty()) {
            log.debug("Flows considered for in place seed rotation: {}", flowsToString(selectedFlows));
        }
        return selectedFlows;
    }


}

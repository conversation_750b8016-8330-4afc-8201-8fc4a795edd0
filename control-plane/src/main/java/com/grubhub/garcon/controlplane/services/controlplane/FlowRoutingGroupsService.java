package com.grubhub.garcon.controlplane.services.controlplane;

import com.grubhub.garcon.controlplane.cassandra.models.Flow;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.NonNull;

import java.util.UUID;

public interface FlowRoutingGroupsService {
    void insertRoutingGroups(FlowDTO flow);

    Map<String, List<FlowRoutingGroupV2>> populateGroupOrderV2(FlowDTO flow);

    java.util.List<FlowRoutingGroupV2> selectAll(@NonNull UUID flowId);
    StreamOperation<FlowRoutingGroupV2> selectAllAsync(@NonNull UUID flowId);

    void deleteRoutingGroupsForFlow(@NonNull Flow flow);
    void updateFlowRoutingGroupsForFlowV2(@NonNull List<FlowRoutingGroupV2> flowRoutingGroups);
    void insertCriteriaRoutingGroups(FlowDTO flow, Map<String, List<FlowRoutingGroupV2>> flowRoutingGroups);
    List<FlowRoutingGroupV2> selectRoutingGroupsForCriteria(UUID flowId, @NonNull String routingGroupCriteria);
}

package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.cassandra.dao.MarketByGeohashDao;
import com.grubhub.garcon.controlplane.cassandra.dao.MarketDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import com.grubhub.garcon.controlplane.cassandra.models.Market;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.eventhub.ControlPlaneLogger;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.controlplane.MarketService;
import com.grubhub.garcon.controlplane.services.controlplane.geo.GeoPolygon;
import com.grubhub.garcon.controlplane.util.SessionAttributeProvider;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.EntityCollectionDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketByGeohashDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketDTO;
import com.grubhub.garcon.ensembler.cassandra.dao.EntityCollectionDao;
import com.grubhub.garcon.ensembler.cassandra.models.EntityCollection;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import io.vavr.collection.Set;
import io.vavr.collection.Stream;
import io.vavr.collection.Traversable;
import io.vavr.control.Option;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.val;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static com.grubhub.garcon.controlplane.custom.OptionalMarket.toModel;
import static com.grubhub.garcon.controlplane.services.common.utils.ValidationUtils.validateInput;
import static com.grubhub.garcon.controlplane.services.controlplane.geo.GeoUtils.createPolygon;
import static com.grubhub.garcon.controlplane.services.controlplane.geo.GeoUtils.geoPolygonToGeoHashes;
import static com.grubhub.garcon.ensembler.cassandra.models.EntityCollection.Constants.NO_CATEGORY;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class MarketServiceImpl implements MarketService {

    private final MarketDao marketDao;
    private final FlowConfig flowConfig;
    private final ControlPlaneLogger controlPlaneLogger;
    private final ControlPlaneMapper controlPlaneMapper;
    private final EntityCollectionDao entityCollectionDao;
    private final SessionAttributeProvider sessionAttributeProvider;
    private final MarketByGeohashDao marketByGeohashDao;

    @Override
    public Option<MarketDTO> getMarket(String marketName) {
        return Option.ofOptional(marketDao.select(marketName)
                .map(controlPlaneMapper::toDTO));
    }

    @Override
    public void deleteMarket(String marketName) {
        getMarket(marketName).peek(market -> {
            marketDao.delete(marketName);
            controlPlaneLogger.logDeleteMarket(market);
            entityCollectionDao.delete(EntityCollection.Constants.COLLECTION_MARKET, marketName);
        });
    }

    @Override
    public void createOrUpdateMarket(MarketDTO market) {
        validateInput(market);
        Option.of(market)
                .map(MarketDTO::getMarketName)
                .flatMap(this::getMarket)
                .peek(existingMarket -> updateMarket(market))
                .onEmpty(() -> createNewMarket(market));
    }

    private void createNewMarket(MarketDTO market) {
        toModel(controlPlaneMapper::toModel, market)
                .trackUserAction(sessionAttributeProvider::resolveUserEmail)
                .withGeoHashes(this::computeGeoHashesForMarket)
                .saveToCassandra(marketDao::insert);

        getMarket(market.getMarketName())
                .peek(controlPlaneLogger::logCreateMarket)
                .peek(this::updateEntityCollection);
    }

    @Override
    public void updateMarket(@NonNull MarketDTO marketDTO) {
        toModel(controlPlaneMapper::toModel, marketDTO)
                .trackUserAction(sessionAttributeProvider::resolveUserEmail)
                .withGeoHashes(market -> computeOrUseGeoHashesForMarket(market, marketDTO.getGeohashes()))
                .saveToCassandra(marketDao::update);

        getMarket(marketDTO.getMarketName())
                .peek(controlPlaneLogger::logUpdateMarket)
                .peek(this::updateEntityCollection);
    }

    private void updateEntityCollection(MarketDTO marketDTO) {
        entityCollectionDao.update(new EntityCollection(EntityCollection.Constants.COLLECTION_MARKET,
                marketDTO.getMarketName(), "ENABLED", NO_CATEGORY));
    }

    @Override
    public List<Market> getMarketsForFlow(FlowDTO flow) {
        return marketDao
                .selectMarketsByNames(flow.getLocationMarkets().toJavaSet())
                .collect(List.collector());
    }

    @Override
    public List<FlowByGeoHash> getFlowsByGeoHash(FlowDTO flow) {
        return getMarketsForFlow(flow)
                .toStream()
                .map(controlPlaneMapper::toDTO)
                .flatMap(market -> toFlowByGeoHash(flow, market))
                .collect(List.collector());
    }

    @Override
    public List<FlowByGeoHash> getFlowsByGeohashOrGlobal(FlowDTO flow) {
        return Option.of(flow)
                .map(this::getFlowsByGeoHash)
                .map(flowByGeoHashes -> flowByGeoHashes.appendAll(createGlobalFlowByGeohash(flow)))
                .filter(Traversable::nonEmpty)
                .getOrElse(() -> createGlobalFlowByGeohash(flow));
    }

    public Stream<FlowByGeoHash> toFlowByGeoHash(FlowDTO flow, MarketDTO market) {
        return market.getGeohashes()
                .toStream()
                .map(geoHash -> controlPlaneMapper
                        .toModel(
                                controlPlaneMapper.toModel(flow),
                                geoHash,
                                market.getMarketName()
                        )
                );
    }

    @Override
    public MarketDTO selectGlobalMarket() {
        return MarketDTO.GLOBAL_MARKET;
    }

    private Market computeGeoHashesForMarket(Market market) {
        if (!market.getGeohashes().isEmpty()) {
            return market;
        }
        val geoPolygon = createPolygon(market.getGeoPolygon());
        return market.withGeohashes(computeGeoHashesForPolygon(geoPolygon).toJavaSet());
    }

    private Market computeOrUseGeoHashesForMarket(Market market, Set<String> geohashes) {
        if (!market.getGeohashes().isEmpty()) {
            return market.withGeohashes(geohashes.toJavaSet());
        }
        val geoPolygon = createPolygon(market.getGeoPolygon());
        return market.withGeohashes(computeGeoHashesForPolygon(geoPolygon).toJavaSet());
    }

    private Set<String> computeGeoHashesForPolygon(GeoPolygon geoPolygon) {
        return HashSet.ofAll(geoPolygonToGeoHashes(geoPolygon, flowConfig.getPrecision(), flowConfig.getFlipCoordinates()));
    }

    @Override
    public List<EntityCollectionDTO> getAllMarkets() {
        return entityCollectionDao.selectAll(EntityCollection.Constants.COLLECTION_MARKET)
                .map(controlPlaneMapper::toDTO);
    }

    @Override
    public List<MarketByGeohashDTO> getMarketsByGeohashes(int precision, List<String> geohashes) {
        return List.ofAll(marketByGeohashDao.getMarketsByGeohashes(precision, geohashes.toJavaList())
                .stream().map(controlPlaneMapper::toDTO));
    }

    @Override
    public CompletableFuture<List<MarketByGeohashDTO>> getMarketsByGeohashesWithoutCache(int precision, List<String> geohashes) {
        return marketByGeohashDao.getMarketsByGeohashesAsync(precision, geohashes.toJavaList())
                .map(controlPlaneMapper::toDTO)
                .toListCompletableFuture()
                .thenApply(List::ofAll);
    }

    @Override
    public CompletableFuture<List<MarketByGeohashDTO>> getMarketsByGeohashWithoutCache(int precision, String geohash) {
        return marketByGeohashDao.getMarketsByGeohashAsync(precision, geohash)
                .map(controlPlaneMapper::toDTO)
                .toListCompletableFuture()
                .thenApply(List::ofAll);
    }
    
    @Override
    public void deleteMarketsByGeohashes(int precision, List<String> geohashes) {
        marketByGeohashDao.deleteMarketsByGeohashes(precision, geohashes.toJavaList());
    }

    @Override
    public void updateMarketsByGeohashes(List<MarketByGeohashDTO> marketByGeohashes) {
        marketByGeohashDao.upsert(marketByGeohashes.map(controlPlaneMapper::toModel).asJava());
    }

    private List<FlowByGeoHash> createGlobalFlowByGeohash(FlowDTO flow) {
        return List.of(FlowByGeoHash.builder()
                .flowId(UUID.fromString(flow.getFlowId()))
                .geohash(MarketDTO.GLOBAL_MARKET.getGeohashes().get())
                .flowSet(flow.getFlowSet())
                .marketName(MarketDTO.GLOBAL_MARKET.getMarketName())
                .build());
    }
}

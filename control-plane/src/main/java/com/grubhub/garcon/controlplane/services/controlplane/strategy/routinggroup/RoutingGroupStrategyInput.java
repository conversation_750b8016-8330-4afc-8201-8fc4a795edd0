package com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.dto.EnrichedResolveFlowRequest;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.search.experiment.ExperimentBranchManager;
import io.vavr.collection.List;
import lombok.Builder;

@Builder
public record RoutingGroupStrategyInput(FlowDTO flow, FlowConfig flowConfig, ExperimentBranchManager branchManager,
                                        List<FlowRoutingGroupV2> flowRoutingGroups, EnrichedResolveFlowRequest resolveFlowRequest,
                                        ControlPlaneMapper controlPlaneMapper) {
}

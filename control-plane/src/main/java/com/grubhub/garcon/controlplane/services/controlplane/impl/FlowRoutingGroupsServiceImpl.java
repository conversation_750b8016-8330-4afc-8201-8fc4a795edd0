package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowRoutingGroupCriteriaDao;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowRoutingGroupDao;
import com.grubhub.garcon.controlplane.cassandra.models.Flow;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupCriteria;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.controlplane.FlowRoutingGroupsService;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupDTO;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import com.grubhub.roux.uuid.UuidUtil;
import io.vavr.Tuple2;
import io.vavr.Value;
import io.vavr.collection.HashMap;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Set;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.util.Optional;
import java.util.UUID;

import static java.util.function.Predicate.not;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class FlowRoutingGroupsServiceImpl implements FlowRoutingGroupsService {

    private final FlowRoutingGroupDao flowRoutingGroupDao;
    private final FlowRoutingGroupCriteriaDao flowRoutingGroupCriteriaDao;
    private final ControlPlaneMapper controlPlaneMapper;
    @Override
    public List<FlowRoutingGroupV2> selectRoutingGroupsForCriteria(UUID flowId, @NonNull String routingGroupCriteria) {
        return flowRoutingGroupCriteriaDao.select(flowId, routingGroupCriteria)
                .map(flowRoutingGroupCriteria -> Stream.ofAll(flowRoutingGroupCriteria.getRoutingGroups())
                        .map(groupName -> flowRoutingGroupDao.select(flowId, routingGroupCriteria, groupName)).filter(Optional::isPresent)
                        .map(Optional::get))
                .map(Value::toList)
                .orElse(List.empty());
    }

    @Override
    public void insertRoutingGroups(FlowDTO flow) {
        Map<String, List<FlowRoutingGroupV2>> flowRoutingGroupsV2 = populateGroupOrderV2(flow);

        flowRoutingGroupDao.insertManyV2(flowRoutingGroupsV2.values().toStream()
                .flatMap(List::toStream).toList().toJavaList());

        insertCriteriaRoutingGroups(flow, flowRoutingGroupsV2);
    }

    @Override
    public Map<String, List<FlowRoutingGroupV2>> populateGroupOrderV2(FlowDTO flow) {
        return Option.of(flow.getRoutingGroupsCriteria())
                .getOrElse(HashMap::empty)
                .map(entry -> addGroupOrderToEachRoutingGroup(flow, entry))
                .collect(HashMap.collector());
    }

    private Tuple2<String, List<FlowRoutingGroupV2>> addGroupOrderToEachRoutingGroup(FlowDTO flow, Tuple2<String, List<FlowRoutingGroupDTO>> entry) {
        return new Tuple2<>(getLeft(entry), getRight(flow, entry));
    }

    private List<FlowRoutingGroupV2> getRight(FlowDTO flow, Tuple2<String, List<FlowRoutingGroupDTO>> entry) {
        return entry._2.map(routingGroups -> routingGroups.withRoutingGroupCriteria(entry._1))
                .map(routingGroup -> controlPlaneMapper.toModelV2(routingGroup, flow))
                .zipWithIndex()
                .map(FlowRoutingGroupsServiceImpl::setGroupOrderIfNull);
    }

    private static String getLeft(Tuple2<String, List<FlowRoutingGroupDTO>> entry) {
        return entry._1;
    }

    private static FlowRoutingGroupV2 setGroupOrderIfNull(Tuple2<FlowRoutingGroupV2, Integer> tuple) {
        if (tuple._1.getGroupOrder() == null) {
            tuple._1.setGroupOrder(tuple._2);
        }

        return tuple._1;
    }

    @Override
    public void insertCriteriaRoutingGroups(FlowDTO flow,
                                            Map<String, List<FlowRoutingGroupV2>> flowRoutingGroups) {
        Option.of(flowRoutingGroups.toStream().map(entry -> mapToRoutingGroupsCriteria(flow, entry)).toList())
                .filter(not(List::isEmpty))
                .toStream()
                .map(List::toJavaList)
                .forEach(flowRoutingGroupCriteriaDao::insert);

    }

    private FlowRoutingGroupCriteria mapToRoutingGroupsCriteria(FlowDTO flow, Tuple2<String, List<FlowRoutingGroupV2>> routingGroups) {
        return FlowRoutingGroupCriteria.builder()
                .routingGroups(routingGroups._2().map(FlowRoutingGroupV2::getGroupName).toJavaSet())
                .routingGroupCriteria(routingGroups._1())
                .flowId(UuidUtil.decode(flow.getFlowId()))
                .build();
    }

    @Override
    public java.util.List<FlowRoutingGroupV2> selectAll(@NonNull UUID flowId) {
        return flowRoutingGroupDao.selectAll(flowId);
    }

    @Override
    public StreamOperation<FlowRoutingGroupV2> selectAllAsync(@NonNull UUID flowId) {
        return flowRoutingGroupDao.selectAllAsync(flowId);
    }


    @Override
    public void deleteRoutingGroupsForFlow(@NonNull Flow flow) {
        flow.getRoutingGroupsCriteria()
                .forEach(routingGroupsCriteria ->
                        flowRoutingGroupCriteriaDao
                                .select(flow.getFlowId(), routingGroupsCriteria)
                                .stream()
                                .map(FlowRoutingGroupCriteria::getRoutingGroups)
                                .map(HashSet::ofAll)
                                .flatMap(Set::toJavaStream)
                                .forEach(groupName -> flowRoutingGroupDao.delete(flow.getFlowId(), routingGroupsCriteria, groupName))
                );

        flow.getRoutingGroupsCriteria()
                .stream()
                .forEach(routingGroupCriteria -> flowRoutingGroupCriteriaDao.delete(flow.getFlowId(), routingGroupCriteria));

    }

    @Override
    public void updateFlowRoutingGroupsForFlowV2(@NonNull List<FlowRoutingGroupV2> flowRoutingGroupsV2) {
        flowRoutingGroupsV2
                .toStream()
                .forEach(flowRoutingGroupDao::updateV2);
    }
}

package com.grubhub.garcon.controlplane.services.controlplane.strategy.branchselection;

import ch.hsr.geohash.GeoHash;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.dto.EnrichedResolveFlowRequest;
import lombok.Value;

@Value
public class VariantRequestContext {
    double lat;
    double lng;
    FlowConfig flowConfig;
    String dinerId;
    String trackingId;
    String callerTrackingId;
    String clientEntityId;

    public GeoHash getGeoHash() {
        return GeoHash.withCharacterPrecision(lat, lng, flowConfig.getRoutingGroupsGeohashPrecision());
    }

    public static VariantRequestContext from(EnrichedResolveFlowRequest enrichedResolveFlowRequest) {
        return new VariantRequestContext(
                enrichedResolveFlowRequest.getLat(),
                enrichedResolveFlowRequest.getLng(),
                enrichedResolveFlowRequest.getFlowConfig(),
                enrichedResolveFlowRequest.getDinerId(),
                enrichedResolveFlowRequest.getTrackingId(),
                enrichedResolveFlowRequest.getCallerTrackingId(),
                enrichedResolveFlowRequest.getClientEntityId()
        );
    }

}

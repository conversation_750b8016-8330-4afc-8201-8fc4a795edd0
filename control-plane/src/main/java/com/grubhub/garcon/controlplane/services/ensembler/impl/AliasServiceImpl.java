package com.grubhub.garcon.controlplane.services.ensembler.impl;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.common.service.CachedConfigurationService;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EntitiesAliasDTO;
import com.grubhub.garcon.controlplaneapi.service.ensembler.AliasService;
import com.grubhub.garcon.ensembler.cassandra.dao.AliasDao;
import com.grubhub.garcon.ensembler.cassandra.models.EntitiesAlias;
import com.grubhub.roux.casserole.api.operation.StreamOperation;
import com.grubhub.roux.casserole.api.operation.ValueOperation;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static com.grubhub.garcon.ensembler.cassandra.models.EntityCollection.Constants.COLLECTION_MODEL;

public class AliasServiceImpl implements AliasService {

    private final AliasDao aliasDao;
    private final ControlPlaneMapper controlPlaneMapper;
    private final CachedConfigurationService cachedConfigurationService;

    @Inject
    public AliasServiceImpl(AliasDao aliasDao,
                            ControlPlaneMapper controlPlaneMapper,
                            CachedConfigurationService cachedConfigurationService) {
        this.aliasDao = aliasDao;
        this.controlPlaneMapper = controlPlaneMapper;
        this.cachedConfigurationService = cachedConfigurationService;
    }

    @Override
    public Option<EntitiesAliasDTO> getAlias(@NonNull String aliasName, @NonNull String collectionName) {
        return aliasDao
                .getAlias(aliasName, collectionName)
                .map(controlPlaneMapper::toDTO)
                .map(Option::of)
                .orElse(Option.none());
    }

    @Override
    public CompletableFuture<Optional<EntitiesAliasDTO>> getAliasAsync(@NonNull String aliasName, String collectionName) {
        return aliasDao.getAliasAsync(aliasName, collectionName)
                .map(controlPlaneMapper::toDTO)
                .toCompletableFuture();
    }

    public void updateAlias(String aliasName, String entityId, String collectionName) {
        aliasDao.updateAlias(aliasName, entityId, collectionName);
        cachedConfigurationService.cleanAllCache("update_alias");
    }

    @Override
    public void deleteAlias(@NonNull String aliasName, String collectionName) {
        aliasDao.deleteAlias(aliasName, collectionName);
        cachedConfigurationService.cleanAllCache("delete_alias");
    }

    public Map<String, String> getAliasesAndModelsForEnsemble(EnsembleDTO ensemble) {
        return getModelsForAliases(List.ofAll(ensemble.getModels()));
    }

    public Map<String, String> getModelsForAliases(List<String> aliases) {
        if (aliases.isEmpty()) {
            return HashMap.empty();
        }

        ValueOperation<EntitiesAlias> firstOperation = aliasDao.getAliasAsync(aliases.head(), COLLECTION_MODEL);
        StreamOperation<EntitiesAlias> streamOperation = null;
        for (int i = 1; i < aliases.size(); i++) {
            if (streamOperation == null) {
                streamOperation = firstOperation.plus(aliasDao.getAliasAsync(aliases.get(i), COLLECTION_MODEL));
            } else {
                streamOperation = streamOperation.plus(aliasDao.getAliasAsync(aliases.get(i), COLLECTION_MODEL));
            }
        }
        // If there is only one alias, we can avoid the stream operation
        Map<String, String> aliasMap = HashMap.empty();
        if (streamOperation == null) {
            EntitiesAlias alias = firstOperation.get().orElse(EntitiesAlias.empty());
            aliasMap = HashMap.of(alias.getAliasName(), alias.getEntityId());
        } else {
            // Otherwise, process all the fetched aliases
            aliasMap = streamOperation.getList()
                    .stream()
                    .collect(HashMap.collector(
                            EntitiesAlias::getAliasName,
                            EntitiesAlias::getEntityId
                    ));
        }
        return aliasMap.filter(entry -> StringUtils.isNotBlank(entry._1))
                .filter(entry -> StringUtils.isNotBlank(entry._2));
    }

    private String getAliasName(EntitiesAlias alias) {
        return alias.getAliasName();
    }
}

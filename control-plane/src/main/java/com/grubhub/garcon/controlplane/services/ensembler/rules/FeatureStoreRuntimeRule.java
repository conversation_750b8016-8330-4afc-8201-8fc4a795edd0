package com.grubhub.garcon.controlplane.services.ensembler.rules;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import io.vavr.collection.Map;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class FeatureStoreRuntimeRule implements FeatureStoreRule {

    public static FeatureStoreRuntimeRule matchFeatureStoreRuntime() {
        return new FeatureStoreRuntimeRule();
    }
    @Override
    public Map<String, Object> applicableTo(Map<FeatureDTO, Object> sample) {
        return sample
                .filterKeys(FeatureDTO::isRuntime)
                .mapKeys(FeatureDTO::getFeatureName);
    }

}

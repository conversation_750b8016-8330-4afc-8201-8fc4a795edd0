package com.grubhub.garcon.controlplane.services.ensembler.rules;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import io.vavr.collection.Map;

public interface FeatureStoreRule {
    Map<String, Object> applicableTo(Map<FeatureDTO, Object> sample);

    default FeatureStoreRule orElse(FeatureStoreRule next) {
        return new ChainedFeatureStoreRule(this, next);
    }

}

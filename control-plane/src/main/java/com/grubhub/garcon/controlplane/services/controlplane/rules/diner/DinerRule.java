package com.grubhub.garcon.controlplane.services.controlplane.rules.diner;

import com.grubhub.garcon.controlplane.cassandra.models.DinerType;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.ResolveFlowRequest;

import java.util.Optional;

public interface DinerRule {

    Optional<DinerType> applicableTo(ResolveFlowRequest resolveFlowRequest, Integer dinerTypeOrdersThreshold);
    default DinerRule orElse(DinerRule next) {
        return new ChainedDinerRule(this, next);
    }

    default int getTotalOrdersMax(Integer dinerTypeOrdersThreshold, Integer configTotalOrdersMax) {
        return dinerTypeOrdersThreshold != null ? dinerTypeOrdersThreshold : configTotalOrdersMax;
    }
}

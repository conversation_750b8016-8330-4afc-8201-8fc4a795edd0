package com.grubhub.garcon.controlplane.services.controlplane.domainstreams;

import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.metrics.MetricsProvider;
import com.grubhub.garcon.controlplane.services.controlplane.FeatureSelector;
import com.grubhub.garcon.controlplane.services.controlplane.sequential.SequentialContextUtils;
import com.grubhub.garcon.controlplane.services.controlplane.sequential.SequentialExecutionProcessor;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceItem;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceRequest;
import io.vavr.collection.Stream;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Slf4j
@Setter
public class ConditionalModelInferenceSequenceItemStream extends ModelInferenceSequenceItemStream {
    private final SequentialExecutionProcessor sequentialExecutionProcessor;
    private final MetricsProvider metricsProvider;

    private static final String SEQUENCE_INVOCATION = "sequence_invocation";
    private static final String CONDITIONAL = "conditional";
    private static final String STATUS_TAG = "status";
    private static final String SUCCESS = "success";
    private static final String FAILURE = "failure";
    private static final String SKIPPED = "skipped";

    public ConditionalModelInferenceSequenceItemStream(Stream<ModelInferenceSequenceItem> stream,
                                                       SequentialExecutionProcessor sequentialExecutionProcessor,
                                                       ModelInferenceSequenceRequest request,
                                                       MetricsProvider metricsProvider) {
        super(stream, request);
        this.sequentialExecutionProcessor = sequentialExecutionProcessor;
        this.metricsProvider = metricsProvider;
    }

    @Override
    public FeatureSelector invokeModelInferenceMultiOutput(
            Function<ModelInferenceRequest, ModelInferenceOutput> invokeModel) {
        var globalContext = new HashMap<String, Object>();

        this.stream.forEach(inference -> {
            if (sequentialExecutionProcessor.shouldExecute(inference, globalContext)) {
                log.debug("Pre-condition to execute model {} was met.", inference.getModelName());
                featureSelector.updateFeaturesWithPreviousOutputs(inference);

                try {
                    ModelInferenceRequest modelInferenceRequest = super.buildModelInferenceRequest(request, inference);
                    ModelInferenceOutput output = invokeModel.apply(modelInferenceRequest);
                    featureSelector.updatePreviousOutput(output);
                    // update context, model execution status
                    syncContextOnSuccess(globalContext, output);
                } catch (Exception e) {
                    log.warn("Model {} failed within sequence with message={}", inference.getModelName(), e.getMessage());
                    // update context, model failure and not conditionally skipped
                    syncFailureExecutionContext(globalContext, inference);
                }
            } else {
                log.debug("Pre-condition to execute model {} has failed.", inference.getModelName());
                // update context, specify that model was skipped
                syncSkippedExecutionContext(globalContext, inference);
            }
        });

        return featureSelector;
    }

    /**
     * Adds model execution status to the context map for later use.
     */
    private void syncContextOnSuccess(Map<String, Object> currentContext, ModelInferenceOutput output) {
        val updates = SequentialContextUtils.mapOutputToMap(output);
        syncContext(currentContext, updates);
        this.metricsProvider.count(Metrics.name(ConditionalModelInferenceSequenceItemStream.class, SEQUENCE_INVOCATION, CONDITIONAL),
                Metrics.createTag(STATUS_TAG, SUCCESS));
    }

    /**
     * Adds skipped model execution status to the context map for later use.
     */
    private void syncSkippedExecutionContext(Map<String, Object> currentContext, ModelInferenceSequenceItem inference) {
        val updates = SequentialContextUtils.mapToSkippedExecutionMap(inference);
        syncContext(currentContext, updates);
        this.metricsProvider.count(Metrics.name(ConditionalModelInferenceSequenceItemStream.class, SEQUENCE_INVOCATION, CONDITIONAL),
                Metrics.createTag(STATUS_TAG, SKIPPED));
    }

    /**
     * Adds failed model execution status to the context map for later use.
     */
    private void syncFailureExecutionContext(Map<String, Object> currentContext, ModelInferenceSequenceItem inference) {
        val updates = SequentialContextUtils.mapToFailedExecutionMap(inference);
        syncContext(currentContext, updates);
        this.metricsProvider.count(Metrics.name(ConditionalModelInferenceSequenceItemStream.class, SEQUENCE_INVOCATION, CONDITIONAL),
                Metrics.createTag(STATUS_TAG, FAILURE));
    }

    /**
     * Updates the context with incoming updates. If two or more keys collide in the map, the updates will have
     * priority and will overwrite the existing context.
     *
     * @param currentContext context with current data
     * @param updates        any updates for execution/globals/outputs etc.
     */
    private void syncContext(Map<String, Object> currentContext, Map<String, Object> updates) {
        currentContext.putAll(updates);
    }

}

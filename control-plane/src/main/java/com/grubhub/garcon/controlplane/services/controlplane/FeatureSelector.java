package com.grubhub.garcon.controlplane.services.controlplane;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceItem;

public interface FeatureSelector {

    void updateFeaturesWithPreviousOutputs(ModelInferenceSequenceItem sequenceItem);
    void updatePreviousOutput(ModelInferenceOutput modelInferenceOutput);
    ModelInferenceOutput returnLastOutput();
}

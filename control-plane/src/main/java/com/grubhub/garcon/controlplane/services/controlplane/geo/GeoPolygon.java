package com.grubhub.garcon.controlplane.services.controlplane.geo;

import lombok.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * A GEO Polygon as defined in http://geojson.org/geojson-spec.html#polygon
 * The first polygon of the list is considered the outer boundary.
 * It can optionally define inner polygons to be excluded from the outer boundary.
 * Each element of a polygon should be a tuple (x, y) coordinate, x=longitude, y=latitude.
 * The last vertex should be the same as the first one.
 * Minimum 4 vertices to define a polygon (3 + first)
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Getter
@Setter
public class GeoPolygon {
    private String type;
    private List<List<List<Double>>> coordinates;

    @Override
    public String toString() {
        if (coordinates.isEmpty()) {
            return "Empty";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("Outer: ").append(coordsToStr(coordinates.get(0)));
        if (coordinates.size() > 1) {
            sb.append("\nInner: [");
            coordinates.subList(1, coordinates.size()).forEach(coords -> sb.append("\n\t").append(coordsToStr(coords)).append(","));
            sb.append("\n]");
        }
        return sb.toString();
    }

    private String coordsToStr(List<List<Double>> coords) {
        return coords.stream().map(i -> String.format("[%f, %f]", i.get(0), i.get(1))).collect(Collectors.joining(", "));
    }
}

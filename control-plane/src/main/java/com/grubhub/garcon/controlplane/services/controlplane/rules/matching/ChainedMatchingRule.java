package com.grubhub.garcon.controlplane.services.controlplane.rules.matching;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplane.services.controlplane.rules.actions.MatchingAction;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

@RequiredArgsConstructor
public class ChainedMatchingRule implements MatchingRule {

    private final MatchingRule head;
    private final MatchingRule tail;

    @Override
    public Optional<MatchingAction> applicableTo(FlowDTO flow) {
        return head.applicableTo(flow)
                .map(Optional::of)
                .orElseGet(() -> tail.applicableTo(flow));
    }
}

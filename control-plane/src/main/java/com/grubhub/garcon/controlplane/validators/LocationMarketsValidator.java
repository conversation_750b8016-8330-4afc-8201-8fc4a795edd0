package com.grubhub.garcon.controlplane.validators;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.services.controlplane.MarketService;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;

import java.util.function.Function;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class LocationMarketsValidator {

    private final MarketService marketService;

    public FlowDTO validateLocationMarkets(FlowDTO flow) {
        return Try.of(() -> isLocationMarketMissingFromDatabase(flow))
                .getOrElseThrow((Function<Throwable, RuntimeException>) RuntimeException::new);
    }

    private FlowDTO isLocationMarketMissingFromDatabase(FlowDTO flow) {
        for (String locationMarket : flow.getLocationMarkets()) {
            if (marketService.getMarket(locationMarket).isEmpty()) {
                throw new RuntimeException(
                        String.format(
                                "For flow name %s location market %s is missing from database",
                                flow.getFlowName(),
                                locationMarket
                        )
                );
            }
        }
        return flow;
    }
}

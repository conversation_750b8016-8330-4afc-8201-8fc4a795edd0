package com.grubhub.garcon.controlplane.services.controlplane;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceItem;
import io.vavr.collection.Stream;
import lombok.RequiredArgsConstructor;
import lombok.val;

@RequiredArgsConstructor
public class SingleFeatureSelector implements FeatureSelector {

    private ModelInferenceOutput previousOutput;

    @Override
    public void updateFeaturesWithPreviousOutputs(ModelInferenceSequenceItem sequenceItem) {
        if (shouldUpdateFeature(sequenceItem)) {
            updateFeatures(previousOutput, sequenceItem);
        }
    }

    private void updateFeatures(ModelInferenceOutput previousOutput, ModelInferenceSequenceItem sequenceItem) {
        val features = sequenceItem.getFeatures();
        val nestedOutputs = sequenceItem.getNestedOutputs();
        for (String nestedOutputKey : sequenceItem.getNestedOutputs().keySet()) {
            val featureAttributesToAdd = previousOutput.getResponseValueMap()
                    .get(nestedOutputKey).getOrElseThrow(() -> new RuntimeException(
                            String.format("Unable to find key=%s in the feature attributes of the previous output when executing model_name=%s",
                                    nestedOutputKey, sequenceItem.getModelName())));
            if (features.isEmpty()) {
                Stream.ofAll(featureAttributesToAdd)
                        .forEach(attribute -> features.add(new java.util.HashMap<>()));
            } else if (features.size() != featureAttributesToAdd.size()) {
                throw new RuntimeException(
                        String.format("Total features=%s of model_name=%s is different than size=%s of nested output.",
                                features.size(), sequenceItem.getModelName(), featureAttributesToAdd.size()));
            }
            Stream.ofAll(features)
                    .forEachWithIndex((feature, index) -> feature.put(nestedOutputs.get(nestedOutputKey), featureAttributesToAdd.get(index)));
        }
    }

    private boolean shouldUpdateFeature(ModelInferenceSequenceItem sequenceItem) {
        return previousOutput != null && sequenceItem.getNestedOutputs() != null;
    }

    @Override
    public void updatePreviousOutput(ModelInferenceOutput modelInferenceOutput) {
        this.previousOutput = modelInferenceOutput;
    }

    @Override
    public ModelInferenceOutput returnLastOutput() {
        return this.previousOutput;
    }
}

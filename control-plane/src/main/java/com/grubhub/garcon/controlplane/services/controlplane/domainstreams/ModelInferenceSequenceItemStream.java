package com.grubhub.garcon.controlplane.services.controlplane.domainstreams;

import com.grubhub.garcon.controlplane.custom.ForwardingStream;
import com.grubhub.garcon.controlplane.services.controlplane.CompositeFeatureSelector;
import com.grubhub.garcon.controlplane.services.controlplane.FeatureSelector;
import com.grubhub.garcon.controlplane.services.controlplane.SingleFeatureSelector;
import com.grubhub.garcon.controlplaneapi.models.ensembler.ModelInferenceRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceItem;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceRequest;
import io.vavr.collection.Stream;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Function;

@Slf4j
@Setter
public class ModelInferenceSequenceItemStream implements ForwardingStream<ModelInferenceSequenceItem> {
    protected Stream<ModelInferenceSequenceItem> stream;
    protected ModelInferenceSequenceRequest request;
    protected final FeatureSelector featureSelector;

    public ModelInferenceSequenceItemStream(Stream<ModelInferenceSequenceItem> stream, ModelInferenceSequenceRequest request) {
        this.stream = stream;
        this.request = request;
        this.featureSelector = stream.toJavaStream()
                .anyMatch(sequenceItem -> sequenceItem.getNestedOutputInfo() != null && !sequenceItem.getNestedOutputInfo().isEmpty())
                ? new CompositeFeatureSelector()
                : new SingleFeatureSelector();
    }


    @Override
    public java.util.stream.Stream<ModelInferenceSequenceItem> getStream() {
        return this.stream.toJavaStream();
    }

    protected void syncFeaturesWithPreviousOutputs() {
        log.debug("running syncFeaturesWithPreviousOutputs");
        this.setStream(Stream.ofAll(
                this.stream.toJavaStream()
                        .peek(this::updateIfPreviousOutputIsNotEmpty)
        ));
    }

    private void updateIfPreviousOutputIsNotEmpty(ModelInferenceSequenceItem sequenceItem) {
        featureSelector.updateFeaturesWithPreviousOutputs(sequenceItem);
    }

    protected ModelInferenceRequest buildModelInferenceRequest(ModelInferenceSequenceRequest request, ModelInferenceSequenceItem sequenceItem) {
        return ModelInferenceRequest.builder()
                .callerTrackingId(request.getCallerTrackingId())
                .modelName(sequenceItem.getModelName())
                .globalFeatures(sequenceItem.getGlobalFeatures())
                .features(sequenceItem.getFeatures())
                .build();
    }

    public FeatureSelector invokeModelInferenceMultiOutput(Function<ModelInferenceRequest, ModelInferenceOutput> invokeModel) {
        this.syncFeaturesWithPreviousOutputs();

        this.stream.toJavaStream()
                .map(sequenceItem -> buildModelInferenceRequest(request, sequenceItem))
                .map(invokeModel)
                .forEach(featureSelector::updatePreviousOutput);
        return featureSelector;
    }
}

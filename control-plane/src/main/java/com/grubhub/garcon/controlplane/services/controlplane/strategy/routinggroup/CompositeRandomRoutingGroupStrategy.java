package com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.hash.Hasher;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.branchselection.CompositeStringsBranchSelection;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.RoutingGroupResponseDTO;
import com.grubhub.garcon.search.experiment.api.ExperimentConfiguration;
import com.grubhub.garcon.search.experiment.api.ValueBranchSelector;
import com.grubhub.garcon.search.experiment.utility.MappingProviders;
import io.vavr.collection.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.UUID;
import java.util.function.Consumer;
import java.util.regex.Pattern;

@Slf4j
public class CompositeRandomRoutingGroupStrategy extends SelectRandomRoutingGroupStrategy<List<String>> {

    private static final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");

    private final List<String> compositeKeys;

    private CompositeRandomRoutingGroupStrategy(final FlowDTO flow, final FlowConfig flowConfig,
                                                final List<FlowRoutingGroupV2> flowRoutingGroups,
                                                final ValueBranchSelector<List<String>, String> branchSelector,
                                                final ControlPlaneMapper controlPlaneMapper,
                                                final List<String> compositeKeys) {
        super(flow, flowConfig, branchSelector, flowRoutingGroups, controlPlaneMapper);
        this.compositeKeys = compositeKeys;
    }

    public static CompositeRandomRoutingGroupStrategy from(final RoutingGroupStrategyInput routingGroupStrategyInput,
                                                           final List<String> compositeKeys) {
        final List<FlowRoutingGroupV2> flowRoutingGroups = routingGroupStrategyInput.flowRoutingGroups();
        final FlowDTO flow = routingGroupStrategyInput.flow();
        final FlowConfig flowConfig = routingGroupStrategyInput.flowConfig();

        final ExperimentConfiguration.Simple variantConfig = createVariantConfig(flow, flowConfig, flowRoutingGroups);
        final ValueBranchSelector<List<String>, String> branchSelector =
                routingGroupStrategyInput.branchManager().createCutoffValueSelector(MappingProviders.createStringProvider(variantConfig),
                        CompositeRandomRoutingGroupStrategy::compositeStringHasher);

        return new CompositeRandomRoutingGroupStrategy(flow, flowConfig, flowRoutingGroups, branchSelector,
                routingGroupStrategyInput.controlPlaneMapper(), compositeKeys);
    }

    private static Consumer<Hasher> compositeStringHasher(final List<String> values) {
        return (digest) -> values.forEach(value -> appendToHasher(digest, value));
    }

    @VisibleForTesting
    static Hasher appendToHasher(Hasher digest, String value) {
        if (StringUtils.isEmpty(value)) {
            return digest;
        }

        if (UUID_PATTERN.matcher(value).matches()) {
            try {
                UUID uuid = UUID.fromString(value);
                return digest.putLong(uuid.getMostSignificantBits())
                        .putLong(uuid.getLeastSignificantBits());
            } catch (IllegalArgumentException exception) {
                log.warn("Failed to parse value={} to UUID. Falling back to hashing as string. Exception:", value, exception);
            }
        }

        return digest.putUnencodedChars(value);
    }

    @Override
    public RoutingGroupResponseDTO selectRoutingGroup() {
        return toRoutingGroupResponse(selectUsingCompositeStrings());
    }

    private FlowRoutingGroupV2 selectUsingCompositeStrings() {
        return new CompositeStringsBranchSelection(branchSelector, compositeKeys)
                .select()
                .flatMap(groupName -> flowRoutingGroups.find(group -> group.getGroupName().equals(groupName)).toOption())
                .getOrElse(() -> flowRoutingGroups.get(flowRoutingGroups.size() - 1));
    }

}

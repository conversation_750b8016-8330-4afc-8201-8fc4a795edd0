package com.grubhub.garcon.controlplane.services.ensembler.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.eventhub.ControlPlaneLogger;
import com.grubhub.garcon.controlplane.eventhub.event.LoggedInvokeModel;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.mapper.VavrMapper;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.services.common.service.CachedConfigurationService;
import com.grubhub.garcon.controlplane.services.controlplane.sequential.ExpressionExecutor;
import com.grubhub.garcon.controlplane.services.ensembler.rules.EnsembleFunctionStrategy;
import com.grubhub.garcon.controlplane.services.ensembler.rules.EnsembleStrategy;
import com.grubhub.garcon.controlplane.services.ensembler.rules.EnsembleWeightsStrategy;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplane.util.SessionAttributeProvider;
import com.grubhub.garcon.controlplane.validators.ModelsValidator;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.EntityCollectionDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EnsembleInvocationRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EntitiesAliasDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.service.ensembler.AliasService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.EnsembleService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.garcon.ensembler.cassandra.dao.EnsembleDao;
import com.grubhub.garcon.ensembler.cassandra.dao.EnsembleFunctionDao;
import com.grubhub.garcon.ensembler.cassandra.dao.EnsembleWeightDao;
import com.grubhub.garcon.ensembler.cassandra.dao.EntityCollectionDao;
import com.grubhub.garcon.ensembler.cassandra.models.Ensemble;
import com.grubhub.garcon.ensembler.cassandra.models.EnsembleFunction;
import com.grubhub.garcon.ensembler.cassandra.models.EnsembleWeight;
import com.grubhub.garcon.ensembler.cassandra.models.EntityCollection;
import com.grubhub.garcon.ensembler.custom.OptionalEnsembleDTO;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.vavr.Tuple2;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.collection.Set;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.NonNull;
import lombok.With;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.time.Duration;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;

import static com.grubhub.garcon.controlplane.metrics.Spans.TRACING_HELPER;
import static com.grubhub.garcon.controlplane.services.common.utils.ValidationUtils.validateInput;
import static com.grubhub.garcon.ensembler.cassandra.models.EntityCollection.Constants.COLLECTION_ENSEMBLE;
import static com.grubhub.garcon.ensembler.cassandra.models.EntityCollection.Constants.NO_CATEGORY;

@Slf4j
@With
public class EnsembleServiceImpl implements EnsembleService {

    private static final String INVOKE_ENSEMBLE_UNDERLYING_MODELS_TOTAL = "invokeEnsembleUnderlyingModelsTotal";
    private static final String INVOKE_ENSEMBLE_INIT = "invokeEnsembleInit";
    private static final String METRIC_NOT_DEFINED = "not_defined";

    private final EnsembleDao ensembleDao;
    private final EnsembleWeightDao ensembleWeightDao;
    private final EnsembleFunctionDao ensembleFunctionDao;
    private final ControlPlaneMapper controlPlaneMapper;
    private final ControlPlaneLogger controlPlaneLogger;
    private final ModelService modelService;
    private final AliasService aliasService;
    private final EntityCollectionDao entityCollectionDao;
    private final SessionAttributeProvider sessionAttributeProvider;
    private final MeterRegistry meterRegistry;
    private final ExecutorService executorService;
    private final CachedConfigurationService cachedConfigurationService;
    private final ModelInferenceInvoker modelInferenceInvoker;

    private final ExpressionExecutor expressionExecutor;

    @Inject
    public EnsembleServiceImpl(EnsembleDao ensembleDao,
                               EnsembleWeightDao ensembleWeightDao,
                               EnsembleFunctionDao ensembleFunctionDao,
                               ControlPlaneMapper controlPlaneMapper,
                               ControlPlaneLogger controlPlaneLogger,
                               ModelService modelService,
                               AliasService aliasService,
                               EntityCollectionDao entityCollectionDao,
                               SessionAttributeProvider sessionAttributeProvider,
                               MeterRegistry meterRegistry,
                               @Named("invokerExecutor") ExecutorService executorService,
                               CachedConfigurationService cachedConfigurationService,
                               ModelInferenceInvoker modelInferenceInvoker,
                               ExpressionExecutor expressionExecutor) {
        this.ensembleDao = ensembleDao;
        this.ensembleWeightDao = ensembleWeightDao;
        this.ensembleFunctionDao = ensembleFunctionDao;
        this.controlPlaneMapper = controlPlaneMapper;
        this.controlPlaneLogger = controlPlaneLogger;
        this.modelService = modelService;
        this.aliasService = aliasService;
        this.entityCollectionDao = entityCollectionDao;
        this.sessionAttributeProvider = sessionAttributeProvider;
        this.meterRegistry = meterRegistry;
        this.executorService = executorService;
        this.cachedConfigurationService = cachedConfigurationService;
        this.modelInferenceInvoker = modelInferenceInvoker;
        this.expressionExecutor = expressionExecutor;
    }

    @Override
    public Option<EnsembleDTO> getEnsemble(String ensembleName) {
        return Option.ofOptional(cachedConfigurationService.getEnsembleAsync(ensembleName).join());
    }

    @Override
    public CompletableFuture<Option<EnsembleDTO>> getEnsembleAsync(String ensembleName) {
        return cachedConfigurationService.getEnsembleAsync(ensembleName).thenApply(Option::ofOptional);
    }

    @Override
    public Option<EnsembleDTO> getEnsembleWithoutCache(String ensembleName) {
        return getEnsembleAsyncWithoutCache(ensembleName).join();
    }

    // TODO(wcase): Use async methods after verifying casserole 4
    @Override
    public CompletableFuture<Option<EnsembleDTO>> getEnsembleAsyncWithoutCache(String ensembleName) {
        Optional<Ensemble> ensembleOpt = ensembleDao.select(ensembleName);
        if (ensembleOpt.isEmpty()) {
            return CompletableFuture.completedFuture(Option.none());
        }

        Ensemble ensemble = ensembleOpt.get();
        java.util.List<ModelDTO> modelsOfEnsemble = ensembleDao.selectModelsForEnsemble(ensemble).stream()
                .map(controlPlaneMapper::toDTO).toList();
        Map<String, String> modelsAndAliases = aliasService.getModelsForAliases(List.ofAll(ensemble.getModels()));
        log.debug("Models and aliases for ensemble={}, modelsAndAliases={}", ensembleName, modelsAndAliases);
        List<ModelDTO> modelsByAliases = modelService.selectModels(modelsAndAliases.values().toSet());

        return CompletableFuture.completedFuture(
                Option.of(controlPlaneMapper.toDTO(
                        ensemble,
                        mergeModelsAndAliases(ensemble, List.ofAll(modelsOfEnsemble), modelsAndAliases, modelsByAliases),
                        modelsAndAliases,
                        toEnsembleWeightsMap(List.ofAll(ensembleDao.selectAllEnsembleWeightsForEnsemble(ensemble))),
                        toEnsembleFunctionMap(List.ofAll(ensembleDao.selectAllEnsembleFunctionsForEnsemble(ensemble))))
                )
        );
    }

    private List<ModelDTO> mergeModelsAndAliases(Ensemble ensemble,
                                                 List<ModelDTO> modelsOfEnsemble,
                                                 Map<String, String> aliasesOfEnsemble,
                                                 List<ModelDTO> modelsByAliases) {
        return List.ofAll(ensemble.getModels())
                .map(modelName -> aliasesOfEnsemble.containsKey(modelName)
                        ? modelsByAliases.find(model -> aliasesOfEnsemble.get(modelName)
                                .map(entityId -> model.getModelName().equals(entityId))
                                .getOrElse(false))
                        .getOrElseThrow(() -> new RuntimeException(String.format("Could not find model for alias=%s", modelName)))
                        : modelsOfEnsemble.find(model -> model.getModelName().equals(modelName)).getOrElse((ModelDTO) null)
                )
                .filter(Objects::nonNull);
    }

    @Override
    public void createOrUpdateEnsemble(EnsembleDTO ensembleDTO) {
        validateInput(ensembleDTO);
        validateStrategy(ensembleDTO);

        OptionalEnsembleDTO.of(ensembleDTO)
                .selectFromDatabase(this::getEnsemble)
                .map(any -> validateModels(ensembleDTO))
                .peek(existingEnsemble -> updateEnsemble(ensembleDTO))
                .onEmpty(() -> createNewEnsemble(validateModels(ensembleDTO)));
    }

    private EnsembleDTO validateStrategy(EnsembleDTO ensembleDTO) {
        String strategy = ensembleDTO.getEnsembleStrategy();

        if (strategy == null) {
            return ensembleDTO;
        }

        if (strategy.equalsIgnoreCase(EnsembleFunctionStrategy.FUNCTIONS) || strategy.equalsIgnoreCase(EnsembleFunctionStrategy.FUNCTION)) {
            if (ensembleDTO.getEnsembleFunctions() == null || ensembleDTO.getEnsembleFunctions().isEmpty()) {
                throwEnsembleStrategyException(ensembleDTO);
            }
        } else if (EnsembleWeightsStrategy.WEIGHT.equalsIgnoreCase(strategy) || EnsembleWeightsStrategy.WEIGHTS.equalsIgnoreCase(strategy)) {
            if (ensembleDTO.getEnsembleWeights() == null || ensembleDTO.getEnsembleWeights().isEmpty()) {
                throwEnsembleStrategyException(ensembleDTO);
            }
        } else {
            throw new RuntimeException(String.format("Error invoking ensemble=%s, the specified ensemble ensembleStrategy=%s " +
                                                     "is not valid, must be one of [function, weight]",
                    ensembleDTO.getEnsembleName(),
                    ensembleDTO.getEnsembleStrategy()));
        }

        return ensembleDTO;
    }

    private EnsembleDTO validateModels(EnsembleDTO ensembleDTO) {
        return Try.of(() -> new ModelsValidator(this, aliasService).validateModels(ensembleDTO))
                .getOrElseThrow((Function<Throwable, RuntimeException>) RuntimeException::new);
    }

    private Option<EnsembleDTO> createNewEnsemble(EnsembleDTO ensembleDTO) {

        return OptionalEnsembleDTO.of(ensembleDTO)
                .toModel(controlPlaneMapper::toModel)
                .trackUserAction(sessionAttributeProvider::resolveUserEmail)
                .peek(ensembleDao::insert)
                .peek(any -> insertEnsembleWeights(ensembleDTO))
                .peek(any -> insertEnsembleFunctions(ensembleDTO))
                .map(Ensemble::getEnsembleName)
                .flatMap(this::getEnsemble)
                .peek(controlPlaneLogger::logCreateEnsemble)
                .peek(any -> cachedConfigurationService.cleanAllCache("create_new_ensemble"))
                .peek(any -> updateEntityCollection(ensembleDTO));

    }

    @Override
    public List<ModelDTO> selectModelsWithNamesIn(Set<String> models) {
        return modelService.selectModels(models);
    }

    private void insertEnsembleWeights(EnsembleDTO ensembleDTO) {
        ensembleDTO.getEnsembleWeights()
                .orElse(HashMap.empty())
                .map(tuple -> buildEnsembleWeight(ensembleDTO, tuple))
                .forEach(ensembleWeightDao::insert);
    }

    private void insertEnsembleFunctions(EnsembleDTO ensembleDTO) {
        if (ensembleDTO.getEnsembleFunctions() == null) { //todo delete this when ensembleFunctions get @NotEmpty tag
            return;
        }
        ensembleDTO.getEnsembleFunctions()
                .orElse(HashMap.empty())
                .map(tuple -> buildEnsembleFunction(ensembleDTO, tuple))
                .forEach(ensembleFunctionDao::insert);
    }

    private EnsembleWeight buildEnsembleWeight(EnsembleDTO ensembleDTO, Tuple2<String, Map<String, Float>> ensembleWeightEntry) {
        return new EnsembleWeight(ensembleDTO.getEnsembleName(), ensembleWeightEntry._1, ensembleWeightEntry._2.toJavaMap());
    }

    private EnsembleFunction buildEnsembleFunction(EnsembleDTO ensembleDTO, Tuple2<String, Map<String, String>> ensembleFunctionEntry) {
        return new EnsembleFunction(ensembleDTO.getEnsembleName(),
                ensembleFunctionEntry._1, ensembleFunctionEntry._2.getOrElse("formula", null), ensembleFunctionEntry._2.getOrElse("type", null));
    }

    @Override
    public Option<EnsembleDTO> updateEnsemble(EnsembleDTO updatedEnsemble) {
        return getEnsemble(updatedEnsemble.getEnsembleName())
                .peek(ensemble -> updateModels(updatedEnsemble, ensemble))
                .peek(ensemble -> deleteAllEnsembleWeights(updatedEnsemble))
                .peek(ensemble -> deleteAllEnsembleFunctions(updatedEnsemble))
                .peek(any -> insertEnsembleWeightsForEnsemble(updatedEnsemble))
                .peek(any -> insertEnsembleFunctionForEnsemble(updatedEnsemble))
                .map(any -> updatedEnsemble)
                .peek(ensemble -> ensemble.setUpdatedUser(sessionAttributeProvider.resolveUserEmail()))
                .peek(ensemble -> ensemble.setUpdatedTimestamp(Instant.now()))
                .peek(ensemble -> ensembleDao.update(controlPlaneMapper.toModel(updatedEnsemble)))
                .map(EnsembleDTO::getEnsembleName)
                .flatMap(this::getEnsemble)
                .peek(controlPlaneLogger::logUpdateEnsemble)
                .peek(any -> cachedConfigurationService.cleanAllCache("update_ensemble"))
                .peek(any -> updateEntityCollection(updatedEnsemble));

    }

    private void deleteAllEnsembleWeights(EnsembleDTO updatedEnsemble) {
        ensembleWeightDao.deleteAll(updatedEnsemble.getEnsembleName());
    }

    private void deleteAllEnsembleFunctions(EnsembleDTO updatedEnsemble) {
        ensembleFunctionDao.deleteAll((updatedEnsemble.getEnsembleName()));
    }

    private void updateModels(EnsembleDTO updatedEnsemble, EnsembleDTO ensemble) {
        ensemble.setModels(updatedEnsemble.getModels());
    }

    private void insertEnsembleWeightsForEnsemble(EnsembleDTO updatedEnsemble) {
        updatedEnsemble
                .getEnsembleWeights()
                .toStream()
                .map(ensembleWeightAsTuple -> createNewEnsembleWeight(updatedEnsemble, ensembleWeightAsTuple))
                .forEach(ensembleWeightDao::insert);
    }

    private void insertEnsembleFunctionForEnsemble(EnsembleDTO updatedEnsemble) {
        updatedEnsemble
                .getEnsembleFunctions()
                .toStream()
                .map(ensembleFunctionAsTuple -> createNewEnsembleFunction(updatedEnsemble, ensembleFunctionAsTuple))
                .forEach(ensembleFunctionDao::insert);
    }

    private EnsembleFunction createNewEnsembleFunction(EnsembleDTO ensemble, Tuple2<String, Map<String, String>> ensembleFunctionAsTuple) {
        EnsembleFunction ensembleFunction = new EnsembleFunction();
        ensembleFunction.setEnsembleName(ensemble.getEnsembleName());
        ensembleFunction.setEnsembleFunction(ensembleFunctionAsTuple._1);
        ensembleFunction.setFormula(ensembleFunctionAsTuple._2.getOrElse("formula", null));
        ensembleFunction.setFunctionType(ensembleFunctionAsTuple._2.getOrElse("function_type", null));
        return ensembleFunction;

    }

    private EnsembleWeight createNewEnsembleWeight(EnsembleDTO ensemble, Tuple2<String, Map<String, Float>> ensembleWeightAsTuple) {
        EnsembleWeight ensembleWeight = new EnsembleWeight();
        ensembleWeight.setEnsembleName(ensemble.getEnsembleName());
        ensembleWeight.setEnsembleWeight(ensembleWeightAsTuple._1);
        ensembleWeight.setModelWeights(ensembleWeightAsTuple._2.toJavaMap());
        return ensembleWeight;
    }

    private void updateEntityCollection(EnsembleDTO ensembleDTO) {
        log.info("Updating entity collection for ensemble={}, status={}", ensembleDTO.getEnsembleName(), ensembleDTO.getStatus());
        entityCollectionDao.update(new EntityCollection(EntityCollection.Constants.COLLECTION_ENSEMBLE,
                ensembleDTO.getEnsembleName(), ensembleDTO.getStatus(), NO_CATEGORY));
    }

    @Override
    public void deleteEnsemble(String ensembleName) {
        getEnsemble(ensembleName).peek(ensembleDTO -> {
            ensembleDao.delete(ensembleName);
            // Delete all associated weights & functions
            ensembleWeightDao.deleteAll(ensembleName);
            ensembleFunctionDao.deleteAll(ensembleName);
            controlPlaneLogger.logDeleteEnsemble(ensembleDTO);
            entityCollectionDao.delete(EntityCollection.Constants.COLLECTION_ENSEMBLE, ensembleName);
            cachedConfigurationService.cleanAllCache("delete_ensemble");
        });

    }

    private Map<String, Map<String, Float>> toEnsembleWeightsMap(List<EnsembleWeight> ensembleWeights) {
        return ensembleWeights.toStream().collect(
                HashMap.collector(
                        EnsembleWeight::getEnsembleWeight, ensembleWeight -> HashMap.ofAll(ensembleWeight.getModelWeights())
                )
        );
    }

    private Map<String, Map<String, String>> toEnsembleFunctionMap(List<EnsembleFunction> ensembleFunctions) {

        return ensembleFunctions.toStream().collect(
                HashMap.collector(
                        EnsembleFunction::getEnsembleFunction, ensembleFunction -> HashMap.of(
                                "formula", ensembleFunction.getFormula(),
                                "type", ensembleFunction.getFunctionType())
                )
        );
    }

    @Override
    public List<EntityCollectionDTO> getAllEnsembles() {
        return entityCollectionDao.selectAll(EntityCollection.Constants.COLLECTION_ENSEMBLE)
                .map(controlPlaneMapper::toDTO);
    }

    @Override
    public java.util.List<Float> invokeEnsemble(EnsembleInvocationRequest ensembleInvocationRequest) {
        try {
            final EnsembleDTO ensemble = getEnsembleByNameOrAlias(ensembleInvocationRequest.getEnsembleName())
                    .getOrElseThrow(() -> new RuntimeException(String.format("Ensemble (or alias)=%s not found", ensembleInvocationRequest.getEnsembleName())));

            Timer timer = createTagsForInternalInvocationTimer(ensembleInvocationRequest, ensemble);
            val response = timer.record(() -> invokeEnsembleInternal(ensembleInvocationRequest, ensemble));

            return VavrMapper.toJavaList(response);
        } catch (Exception e) {
            meterRegistry.counter(Metrics.name(getClass(), "invokeEnsembleFailures"),
                            Metrics.ensembleTags(ensembleInvocationRequest.getEnsembleName()))
                    .increment();
            throw new RuntimeException(
                    String.format(
                            "Error invoking ensemble=%s, weight=%s, function=%s, callerTrackingId=%s, " +
                            "features_size=%d, globalFeatures=%s, features_first=%s",
                            ensembleInvocationRequest.getEnsembleName(),
                            ensembleInvocationRequest.getEnsembleWeight(),
                            ensembleInvocationRequest.getEnsembleFunction(),
                            ensembleInvocationRequest.getCallerTrackingId(),
                            ensembleInvocationRequest.getFeatures().size(),
                            ControlPlaneUtils.toString(ensembleInvocationRequest.getGlobalFeatures()),
                            ensembleInvocationRequest.getFeatures().isEmpty() ? "{}" :
                                    ControlPlaneUtils.toString(ensembleInvocationRequest.getFeatures().get(0))
                    ), e);
        }
    }

    @Override
    public Option<EnsembleDTO> getEnsembleByNameOrAlias(String ensembleNameOrAliasName) {
        log.debug("Resolving ensemble by name using ensemble_name={}", ensembleNameOrAliasName);

        // First, try finding the correspondent ensemble.
        final Option<EnsembleDTO> ensembleOp = getEnsemble(ensembleNameOrAliasName);
        if (ensembleOp.isDefined()) {
            log.debug("Resolved ensemble by name, ensemble_name={}", ensembleNameOrAliasName);
            return ensembleOp;
        }

        // If the ensemble is not defined, look-up the alias.
        log.debug("Ensemble by name not found. Resolving ensemble by alias using cache ensemble_alias={}", ensembleNameOrAliasName);
        val ensembleAlias = cachedConfigurationService.getAliasAsync(ensembleNameOrAliasName, COLLECTION_ENSEMBLE).join();

        if (ensembleAlias.isPresent()) {
            log.debug("Resolved using ensemble_alias={}, ensemble_name={}", ensembleNameOrAliasName, ensembleAlias.get().getEntityId());
            return getEnsemble(ensembleAlias.get().getEntityId());
        }

        return Option.none();
    }

    @Override
    public List<EnsembleDTO> getAllWithStatus(String status) {
        val queries = entityCollectionDao
                .selectAll(EntityCollection.Constants.COLLECTION_ENSEMBLE, status)
                .toStream().map(e -> getEnsembleAsync(e.getEntityId()));
        return ControlPlaneUtils.flatten(ControlPlaneUtils.waitForCompletion(queries));
    }

    private List<Float> invokeEnsembleInternal(EnsembleInvocationRequest ensembleInvocationRequest, EnsembleDTO ensemble) {
        val timerInvokeEnsembleInitSpan = TRACING_HELPER.startSpanManual(INVOKE_ENSEMBLE_INIT, getTagsForSpan(ensembleInvocationRequest, ensemble));
        val requestTime = Instant.now();

        Timer timerInvokeEnsembleInit = meterRegistry.timer(Metrics.name(getClass(), INVOKE_ENSEMBLE_INIT),
                Tags.of("ensemble_name", ensembleInvocationRequest.getEnsembleName(), "ensemble_version", ensemble.getVersion()));

        if (!Ensemble.Constants.STATUS_ENABLED.equals(ensemble.getStatus())) {
            throw new RuntimeException(String.format("Cannot invoke ensemble=%s with wrong status=%s", ensemble.getEnsembleName(), ensemble.getStatus()));
        }

        if (ensembleInvocationRequest.getFeatures().isEmpty()) {
            log.debug("Invoking ensemble={} with an empty list of features, returning empty output", ensembleInvocationRequest.getEnsembleName());
            return List.empty();
        }

        timerInvokeEnsembleInit.record(Duration.between(requestTime, Instant.now()));
        timerInvokeEnsembleInitSpan.finish();

        val timerInvokeEnsembleUnderlyingModelsSpan = TRACING_HELPER.startSpanManual(
                "invokeEnsembleUnderlyingModelsTotal",
                getTagsForSpan(ensembleInvocationRequest, ensemble)
        );
        Timer timerInvokeEnsembleUnderlyingModels = meterRegistry.timer(Metrics.name(getClass(), INVOKE_ENSEMBLE_UNDERLYING_MODELS_TOTAL),
                Tags.of("ensemble_name", ensembleInvocationRequest.getEnsembleName(), "ensemble_version", ensemble.getVersion()));

        boolean useParallelism = ensemble.getModels().size() > 1;
        CountDownLatch latch = useParallelism ? new CountDownLatch(ensemble.getModels().size()) : null;
        ConcurrentMap<String, ModelInferenceOutput> modelResults = new ConcurrentHashMap<>();

        timerInvokeEnsembleUnderlyingModels.record(
                () -> invokeRecursively(ensembleInvocationRequest, ensemble, modelResults, latch, useParallelism));

        try {
            if (useParallelism) {
                latch.await();
            }
            timerInvokeEnsembleUnderlyingModelsSpan.finish();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Unexpected error occurred", e);
        }

        final EnsembleStrategy ensembleStrategy = this.selectEnsembleStrategyTypeOrThrow(ensemble, ensembleInvocationRequest);
        final List<Float> result = ensembleStrategy.applyStrategy(modelResults);
        controlPlaneLogger.logInvokeEnsembleEvent(
                ensembleInvocationRequest,
                result,
                buildLoggingModels(HashMap.ofAll(modelResults)),
                Duration.between(requestTime, Instant.now())
        );
        return result;
    }

    private EnsembleStrategy selectEnsembleStrategyTypeOrThrow(EnsembleDTO ensemble, EnsembleInvocationRequest ensembleInvocationRequest) {
        final String strategy = ensembleInvocationRequest.getEnsembleStrategy();
        if (EnsembleFunctionStrategy.FUNCTION.equalsIgnoreCase(strategy) || EnsembleFunctionStrategy.FUNCTIONS.equalsIgnoreCase(strategy)) {
            return new EnsembleFunctionStrategy(ensemble, ensembleInvocationRequest, expressionExecutor, meterRegistry);
        } else if (EnsembleWeightsStrategy.WEIGHT.equalsIgnoreCase(strategy) || EnsembleWeightsStrategy.WEIGHTS.equalsIgnoreCase(strategy)) {
            return new EnsembleWeightsStrategy(ensemble, ensembleInvocationRequest, meterRegistry);
        }
        return new EnsembleWeightsStrategy(ensemble, ensembleInvocationRequest, meterRegistry);
    }

    private void throwEnsembleStrategyException(EnsembleDTO ensemble) {
        throw new RuntimeException(String.format("Error invoking ensemble=%s, the specified ensemble with ensembleStrategy=%s " +
                                                 "did not have the correct fields for the specified strategy",
                ensemble.getEnsembleName(),
                ensemble.getEnsembleStrategy()));
    }

    private java.util.Map<String, String> getTagsForSpan(EnsembleInvocationRequest ensembleInvocationRequest, EnsembleDTO ensemble) {
        return HashMap.of("ensemble_name", ensembleInvocationRequest.getEnsembleName(), "ensemble_version", ensemble.getVersion()).toJavaMap();
    }

    private List<LoggedInvokeModel> buildLoggingModels(Map<String, ModelInferenceOutput> modelResults) {
        return modelResults
                .toStream()
                .map(tuple -> new LoggedInvokeModel(tuple._1, tuple._2.getFloatResponse(), tuple._2.getProcessedFeatures()))
                .collect(List.collector());
    }

    public void invokeRecursively(
            EnsembleInvocationRequest ensembleInvocationRequest,
            EnsembleDTO ensemble,
            ConcurrentMap<String, ModelInferenceOutput> accumulator,
            CountDownLatch countDownLatch,
            boolean useParallelism) {

        for (String modelName : ensemble.getModels()) {
            // verify if it is a model or an ensemble
            val modelOp = ensemble.getModel(modelName);
            if (modelOp.isDefined()) {
                val specificModelName = modelOp.get().getModelName();
                if (useParallelism) {
                    Runnable runnable = () -> recordAndExecuteUnderlyingModel(ensembleInvocationRequest, ensemble,
                            accumulator, specificModelName, countDownLatch);
                    executorService.submit(runnable);
                } else {
                    recordAndExecuteUnderlyingModel(ensembleInvocationRequest, ensemble, accumulator, specificModelName, null);
                }
            } else {
                if (useParallelism) {
                    executorService.submit(() -> recordAndExecuteUnderlyingEnsemble(ensembleInvocationRequest, accumulator, countDownLatch, modelName));
                } else {
                    recordAndExecuteUnderlyingEnsemble(ensembleInvocationRequest, accumulator, null, modelName);
                }
            }
        }
    }

    private void recordAndExecuteUnderlyingEnsemble(EnsembleInvocationRequest ensembleInvocationRequest,
                                                    ConcurrentMap<String, ModelInferenceOutput> accumulator,
                                                    CountDownLatch countDownLatch,
                                                    String modelName) {
        try {
            val startingExecution = Instant.now();
            val retrievedEnsemble = getEnsembleOrThrow(ensembleInvocationRequest, modelName);
            val timer = meterRegistry.timer(Metrics.name(getClass(), "invokeEnsembleUnderlyingEnsemble" + modelName),
                    Tags.of("ensemble_name", modelName, "ensemble_version", retrievedEnsemble.getVersion()));
            invokeEnsemblesRecursively(ensembleInvocationRequest, accumulator, retrievedEnsemble, countDownLatch);
            timer.record(Duration.between(startingExecution, Instant.now()));
        } finally {
            if (countDownLatch != null) {
                countDownLatch.countDown();
            }
        }
    }

    private void recordAndExecuteUnderlyingModel(
            EnsembleInvocationRequest ensembleInvocationRequest,
            EnsembleDTO ensemble,
            ConcurrentMap<String, ModelInferenceOutput> accumulator,
            String modelName,
            CountDownLatch latch) {
        try {
            val timer = meterRegistry.timer(Metrics.name(getClass(), "invokeEnsembleUnderlyingModel" + modelName),
                    Tags.of("model_name", modelName, "model_version", getModelVersion(ensemble, modelName)));
            val invokeEnsembleUnderlyingModelSpan = TRACING_HELPER.startSpanManual("invokeEnsembleUnderlyingModel" + modelName,
                    HashMap.of("model_name", modelName, "model_version", getModelVersion(ensemble, modelName)).toJavaMap());
            val startExecution = Instant.now();

            ModelInferenceOutput modelInferenceOutput = modelInferenceInvoker.getModelInferenceOutput(ensembleInvocationRequest, ensemble, modelName);
            accumulator.put(modelName, modelInferenceOutput);

            timer.record(Duration.between(startExecution, Instant.now()));
            invokeEnsembleUnderlyingModelSpan.finish(Duration.between(startExecution, Instant.now()).toNanos() / 1_000);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Error invoking underlying model=%s of ensemble=%s", modelName, ensemble.getEnsembleName()), e);
        } finally {
            if (latch != null) {
                latch.countDown();
            }
        }
    }

    private EnsembleDTO getEnsembleOrThrow(EnsembleInvocationRequest ensembleInvocationRequest, String ensembleName) {
        return getEnsembleByNameOrAlias(ensembleName)
                .getOrElseThrow(() -> new RuntimeException(
                        String.format("Could not find internal model or ensemble with name/alias=%s when invoking parent ensemble=%s",
                                ensembleName,
                                ensembleInvocationRequest.getEnsembleName()
                        ))
                );
    }

    private void invokeEnsemblesRecursively(
            EnsembleInvocationRequest ensembleInvocationRequest,
            ConcurrentMap<String, ModelInferenceOutput> accumulator,
            EnsembleDTO retrievedEnsemble,
            CountDownLatch latch) {

        ConcurrentMap<String, ModelInferenceOutput> localAccumulator = new ConcurrentHashMap<>();
        invokeRecursively(ensembleInvocationRequest, retrievedEnsemble, localAccumulator, latch, false);
        final EnsembleStrategy ensembleStrategy = this.selectEnsembleStrategyTypeOrThrow(retrievedEnsemble, ensembleInvocationRequest);
        List<Float> linearCombination = ensembleStrategy.applyStrategy(localAccumulator);
        accumulator.put(retrievedEnsemble.getEnsembleName(), ModelInferenceOutput.from(linearCombination));
    }

    private String getModelVersion(EnsembleDTO ensemble, String modelName) {
        return ensemble.getModelList().toStream().find(model -> model.getModelName().equals(modelName)).map(ModelDTO::getVersion).getOrElse("unknown");
    }

    @Override
    public Option<EntitiesAliasDTO> getAlias(@NonNull String aliasName) {
        return aliasService.getAlias(aliasName, COLLECTION_ENSEMBLE);
    }

    @Override
    public void updateAlias(@NonNull String aliasName, @NonNull String ensembleName) {
        getEnsemble(ensembleName).getOrElseThrow(
                () -> new RuntimeException(String.format("Error updating alias with name=%s. Ensemble with name=%s does not exist", aliasName, ensembleName))
        );
        aliasService.updateAlias(aliasName, ensembleName, COLLECTION_ENSEMBLE);
    }

    @Override
    public void deleteAlias(@NonNull String aliasName) {
        aliasService.deleteAlias(aliasName, COLLECTION_ENSEMBLE);
    }

    private Timer createTagsForInternalInvocationTimer(EnsembleInvocationRequest ensembleInvocationRequest, EnsembleDTO ensemble) {
        Tags tags = Tags.of(
                "ensemble_name", Optional.ofNullable(ensembleInvocationRequest.getEnsembleName()).orElse(METRIC_NOT_DEFINED),
                "ensemble_version", Optional.ofNullable(ensemble.getVersion()).orElse(METRIC_NOT_DEFINED),
                "ensemble_strategy", Optional.ofNullable(ensembleInvocationRequest.getEnsembleStrategy()).orElse(METRIC_NOT_DEFINED),
                "ensemble_weight", Optional.ofNullable(ensembleInvocationRequest.getEnsembleWeight()).orElse(METRIC_NOT_DEFINED),
                "ensemble_function", Optional.ofNullable(ensembleInvocationRequest.getEnsembleFunction()).orElse(METRIC_NOT_DEFINED)
        );
        return meterRegistry.timer(Metrics.name(getClass(), "invokeEnsemble"), tags);
    }
}

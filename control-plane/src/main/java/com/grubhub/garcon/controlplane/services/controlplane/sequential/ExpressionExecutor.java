package com.grubhub.garcon.controlplane.services.controlplane.sequential;

import org.apache.commons.jexl3.JexlExpression;

import java.util.Map;

public interface ExpressionExecutor {
    JexlExpression createExpression(String clause);

    boolean evaluateToBoolean(JexlExpression expression, Map<String, Object> contextMap);

    Float evaluateToFloat(JexlExpression expression, Map<String, Object> contextMap);
}

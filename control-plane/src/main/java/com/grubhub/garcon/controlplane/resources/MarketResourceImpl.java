package com.grubhub.garcon.controlplane.resources;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.services.controlplane.MarketService;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketByGeohashDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.EntityCollectionDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketDTO;
import com.grubhub.garcon.controlplaneapi.resource.controlplane.MarketResource;
import com.grubhub.roux.api.responses.DeleteResponse;
import com.grubhub.roux.api.responses.GetResponse;
import com.grubhub.roux.api.responses.UpdateResponse;
import com.grubhub.roux.discovery.ServiceVisibility;
import io.vavr.collection.List;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import static com.grubhub.roux.discovery.ServiceVisibilitySetting.PUBLIC;


import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.core.MediaType;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
@ServiceVisibility(PUBLIC)
public class MarketResourceImpl implements MarketResource {

    private final MarketService marketService;

    @Override
    public GetResponse<MarketDTO> findMarket(@NonNull String marketName) {
        return marketService.getMarket(marketName)
                .map(GetResponse::success)
                .getOrElse(GetResponse::notFound);
    }

    @Override
    @Consumes(MediaType.APPLICATION_JSON)
    public UpdateResponse createOrUpdateMarket(@Valid MarketDTO market) {
        marketService.createOrUpdateMarket(market);
        return UpdateResponse.success();
    }

    @Override
    @Consumes(MediaType.APPLICATION_JSON)
    public DeleteResponse deleteMarket(String marketName) {
        marketService.deleteMarket(marketName);
        return DeleteResponse.success();
    }

    @Override
    public GetResponse<List<EntityCollectionDTO>> getAll() {
        return GetResponse.success(marketService.getAllMarkets());
    }

    @Override
    public GetResponse<List<MarketByGeohashDTO>> getMarketByGeohashes(int precision, java.util.List<String> geohashes) {
        return GetResponse.success(marketService.getMarketsByGeohashes(precision, List.ofAll(geohashes)));
    }

    @Override
    public DeleteResponse deleteMarketByGeohashes(int precision, java.util.List<String> geohashes) {
        marketService.deleteMarketsByGeohashes(precision, List.ofAll(geohashes));
        return DeleteResponse.success();
    }

    @Override
    public UpdateResponse updateMarketsByGeohashes(java.util.List<MarketByGeohashDTO> marketByGeohashes) {
        marketService.updateMarketsByGeohashes(List.ofAll(marketByGeohashes));
        return UpdateResponse.success();
    }
}

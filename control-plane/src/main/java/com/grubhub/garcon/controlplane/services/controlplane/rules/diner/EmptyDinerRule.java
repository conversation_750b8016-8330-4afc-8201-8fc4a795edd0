package com.grubhub.garcon.controlplane.services.controlplane.rules.diner;

import com.grubhub.garcon.controlplane.cassandra.models.DinerType;
import com.grubhub.garcon.controlplane.cassandra.models.Flow;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.ResolveFlowRequest;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

import static com.grubhub.garcon.controlplane.cassandra.models.DinerType.EMPTY_ALL;

@RequiredArgsConstructor
public class EmptyDinerRule implements DinerRule {

    private final Flow flow;

    public static DinerRule match(@NonNull Flow flow) {
        return new EmptyDinerRule(flow);
    }

    @Override
    public Optional<DinerType> applicableTo(ResolveFlowRequest resolveFlowRequest, Integer dinerTypeOrdersThreshold) {
        return Optional.ofNullable(flow)
                .map(Flow::getMatchingDinerTypes)
                .filter(dinerTypes -> dinerTypes.size() == 0)
                .map(any -> EMPTY_ALL);
    }
}

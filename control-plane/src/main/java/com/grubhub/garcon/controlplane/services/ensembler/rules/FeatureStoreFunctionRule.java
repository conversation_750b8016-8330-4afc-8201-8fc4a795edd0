package com.grubhub.garcon.controlplane.services.ensembler.rules;

import com.google.common.collect.ImmutableList;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplanerpc.model.FunctionFeatureType;
import com.grubhub.garcon.controlplanerpc.model.FunctionScope;
import com.grubhub.garcon.ensembler.dto.EnrichedModelInferenceRequest;
import com.grubhub.garcon.modelinteraction.computablefeatures.BaseFunctionFeature;
import com.grubhub.garcon.modelinteraction.computablefeatures.CoordinatesToGeohashFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.GeohashToCoordinatesFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.StringCoalesceFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.StringReplaceFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.StringToLowerCaseFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.stringconcat.StringConcatenateFourFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.stringconcat.StringConcatenateFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.stringconcat.StringConcatenateThreeFunction;
import com.grubhub.garcon.modelinteraction.computablefeatures.textnormalizer.TextNormalizerFunction;
import io.vavr.Tuple2;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.function.Predicate;

@RequiredArgsConstructor
@Slf4j
public class FeatureStoreFunctionRule {

    private final EnrichedModelInferenceRequest modelInferenceRequest;
    private final List<Map<String, Object>> processedFeatures;
    private final Predicate<FeatureDTO> hasFunctionScope;

    public FeatureStoreFunctionRule(EnrichedModelInferenceRequest modelInferenceRequest,
                                    List<Map<String, Object>> processedFeatures,
                                    FunctionScope functionScope) {
        this.modelInferenceRequest = modelInferenceRequest;
        this.processedFeatures = processedFeatures;
        this.hasFunctionScope = feature -> feature.getFunctionScope() != null && feature.getFunctionScope().equals(functionScope.getValue());
    }

    /**
     * Register all function implementations here so they can be invoked when needed
     */
    private static final ImmutableList<BaseFunctionFeature> featureFunctionList = ImmutableList.of(
            new CoordinatesToGeohashFunction(),
            new GeohashToCoordinatesFunction(),
            new StringToLowerCaseFunction(),
            new TextNormalizerFunction(),
            new StringCoalesceFunction(),
            new StringConcatenateFunction(),
            new StringConcatenateThreeFunction(),
            new StringConcatenateFourFunction(),
            new StringReplaceFunction()
    );

    private static final Map<FunctionFeatureType, BaseFunctionFeature> featureFunctionMap = featureFunctionList.stream()
            .map(featureFunction -> new Tuple2<>(featureFunction.getFunctionType(), featureFunction))
            .collect(HashMap.collector());

    public static FeatureStoreFunctionRule createFeatureStoreFunctionRule(
            @NonNull EnrichedModelInferenceRequest modelInferenceRequest,
            @NonNull List<Map<String, Object>> processedFeatures,
            FunctionScope functionScope) {
        return new FeatureStoreFunctionRule(modelInferenceRequest, processedFeatures, functionScope);
    }

    public List<Map<String, Object>> processFunctionFeatures() {
        val functionFeatures = modelInferenceRequest.getSortedFeaturesForModel()
                .filter(FeatureDTO::isFunction)
                .filter(hasFunctionScope);

        if (functionFeatures.isEmpty()) {
            return processedFeatures;
        }

        List<Map<String, Object>> result = processedFeatures;
        for (val functionFeature: functionFeatures) {
            val functionWithSamples = new FunctionFeatureWithSamples(functionFeature, getFunction(functionFeature), modelInferenceRequest.getModel(), result);
            val functionResultsForSamples = functionWithSamples.calculateFunctionForAllSamples();
            result = mergeFeaturesPerSample(functionResultsForSamples, result);
        }

        return result;
    }

    private List<Map<String, Object>> mergeFeaturesPerSample(List<Map<String, Object>> featuresA, List<Map<String, Object>> featuresB) {
        return featuresA.zip(featuresB).map(i -> i._1.merge(i._2));
    }

    private BaseFunctionFeature getFunction(FeatureDTO functionFeature) {
        val type = FunctionFeatureType.valueOfIgnoreCase(functionFeature.getFunctionName());
        return featureFunctionMap.get(type).getOrElseThrow(() -> new RuntimeException(String.format(
                "Function name=%s for the feature name=%s, is not found in the function list.",
                functionFeature.getFunctionName(), functionFeature.getFeatureName())));
    }
}

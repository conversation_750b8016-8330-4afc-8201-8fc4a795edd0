package com.grubhub.garcon.controlplane.services.controlplane.sequential;

import com.grubhub.garcon.controlplane.services.controlplane.sequential.exceptions.ExpressionExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jexl3.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Singleton;
import java.util.Map;

@Slf4j
@Singleton
public class JexlExpressionExecutorImpl implements ExpressionExecutor {
    private static final JexlEngine jexl = new JexlBuilder()
            .cache(512)
            .strict(true)
            .silent(true)
            .create();

    /**
     * @param expression JexlExpression to execute
     * @param contextMap context holding variables, functions, classes etc.
     * @return true/false based on the expression execution
     * @throws ExpressionExecutionException if the expression is incorrect
     */
    private Object evaluateExpression(JexlExpression expression, Map<String, Object> contextMap, Class<?> expectedType) {
        JexlContext context = new MapContext(contextMap);
        Object result = expression.evaluate(context);

        if (ObjectUtils.isEmpty(result)) {
            log.error("Invalid expression provided - missing expression result.");
            throw new ExpressionExecutionException("Null response received from the evaluation. This can be caused by null variables " +
                    "or out of scope classes/functions.");
        }

        if (!expectedType.isAssignableFrom(result.getClass())) {
            log.error("Invalid expression provided - all expressions should have a " + expectedType.getSimpleName() + " return type.");
            throw new ExpressionExecutionException("Invalid return type provided in the expression." +
                    " Expected: " + expectedType.getSimpleName() + ", received: " + result.getClass());
        }

        return result;
    }

    public boolean evaluateToBoolean(JexlExpression expression, Map<String, Object> contextMap) {
        Object result = evaluateExpression(expression, contextMap, Boolean.class);
        return (Boolean) result;
    }

    public Float evaluateToFloat(JexlExpression expression, Map<String, Object> contextMap) {
        Object result = evaluateExpression(expression, contextMap, Number.class);
        return ((Number) result).floatValue();
    }

    public JexlExpression createExpression(String clause) {
        if (StringUtils.isEmpty(clause)) {
            throw new ExpressionExecutionException("Invalid empty clause provided.");
        }
        return jexl.createExpression(clause);
    }
}

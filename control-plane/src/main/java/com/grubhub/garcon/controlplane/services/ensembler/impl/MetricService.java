package com.grubhub.garcon.controlplane.services.ensembler.impl;

import com.google.inject.Inject;
import com.netflix.spectator.impl.AtomicDouble;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class MetricService {

    Map<String, AtomicDouble> gaugeMap = new ConcurrentHashMap<>();

    private final MeterRegistry meterRegistry;

    public void gauge(@NonNull String gaugeName, @NonNull String gaugeKey, @NonNull Tags tags, Number value) {
        if (value != null) {
            log.debug("Gauge name={}, tags={}, value={}", gaugeName, tags, value);

            double gaugeValue = value.doubleValue();

            AtomicDouble gauge = gaugeMap.computeIfAbsent(getGaugeMapKey(gaugeName, gaugeKey), x -> createGauge(gaugeName, tags, gaugeValue));

            gauge.set(gaugeValue);

        } else {
            log.info("Gauge for metric={} was called with null value. Skipping.", gaugeName);
        }
    }

    private String getGaugeMapKey(String gaugeName, String gaugeKey) {
        return gaugeName + gaugeKey;
    }

    private AtomicDouble createGauge(@NonNull String gaugeName, @NonNull Tags tags, double initValue) {
        return meterRegistry.gauge(gaugeName, tags, new AtomicDouble(initValue));
    }
}

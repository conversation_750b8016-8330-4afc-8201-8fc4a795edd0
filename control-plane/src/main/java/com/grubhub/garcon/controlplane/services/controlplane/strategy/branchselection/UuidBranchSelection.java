package com.grubhub.garcon.controlplane.services.controlplane.strategy.branchselection;

import com.grubhub.garcon.search.experiment.api.ValueBranchSelector;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;

@RequiredArgsConstructor
@Slf4j
public class UuidBranchSelection implements BranchSelection {
    private static final UUID DEFAULT_UUID = UUID.fromString("00000000-0000-0000-0000-000000000000");
    private final ValueBranchSelector<UUID, String> delegate;
    private final VariantRequestContext variantRequestContext;
    private final Function<VariantRequestContext, String> uuidProvider;

    @Override
    public Option<String> select() {
        return Option.ofOptional(selectUsingProvidedUUID().or(this::selectUsingTrackingId));
    }

    private Optional<String> selectUsingTrackingId() {
        return Optional.ofNullable(variantRequestContext)
                .map(VariantRequestContext::getTrackingId)
                .map(UuidBranchSelection::fromUUIDOrEmpty)
                .flatMap(delegate::select);
    }

    private static UUID fromUUIDOrEmpty(String trackingId) {
        return Try.of(() -> UUID.fromString(trackingId))
                .onFailure(ex -> log.info("Failed to parse the tracking_id={}. Falling back to tracking_id={}.", trackingId, DEFAULT_UUID))
                .getOrElse(DEFAULT_UUID);
    }

    private Optional<String> selectUsingProvidedUUID() {
        return Optional.ofNullable(variantRequestContext)
                .map(uuidProvider)
                .filter(StringUtils::isNotBlank)
                .map(UUID::fromString)
                .flatMap(delegate::select);
    }
}

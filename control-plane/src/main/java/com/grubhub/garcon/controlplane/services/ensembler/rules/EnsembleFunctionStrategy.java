package com.grubhub.garcon.controlplane.services.ensembler.rules;

import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.services.controlplane.sequential.ExpressionExecutor;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EnsembleInvocationRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.vavr.Tuple2;
import io.vavr.collection.List;
import org.apache.commons.jexl3.JexlExpression;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static com.grubhub.garcon.controlplane.metrics.Spans.convertTagsMapToList;
import static com.grubhub.garcon.controlplane.metrics.Spans.span;

public class EnsembleFunctionStrategy implements EnsembleStrategy {

    private final EnsembleDTO ensemble;
    private final EnsembleInvocationRequest ensembleInvocationRequest;
    private final ExpressionExecutor expressionExecutor;
    private final Timer combineResultFunctionTimer;
    private static final String COMBINE_FUNCTIONS = "applyStrategyFunctionCombineResults";
    public static final String FUNCTION = "function";
    public static final String FUNCTIONS = "functions";

    public EnsembleFunctionStrategy(final EnsembleDTO ensemble,
                                    final EnsembleInvocationRequest ensembleInvocationRequest,
                                    final ExpressionExecutor expressionExecutor,
                                    final MeterRegistry meterRegistry) {
        this.ensemble = ensemble;
        this.ensembleInvocationRequest = ensembleInvocationRequest;
        this.expressionExecutor = expressionExecutor;
        this.combineResultFunctionTimer = meterRegistry.timer(Metrics.name(getClass(), COMBINE_FUNCTIONS), convertTagsMapToList(getTagsForSpan()));
    }

    @Override
    public List<Float> applyStrategy(ConcurrentMap<String, ModelInferenceOutput> modelResults) {
        return span(COMBINE_FUNCTIONS, getTagsForSpan(), () -> combineResultFunctionTimer.record(() -> this.applyStrategyInternal(modelResults)));
    }

    private List<Float> applyStrategyInternal(ConcurrentMap<String, ModelInferenceOutput> modelResults) {
        Map<String, String> function = getEnsembleFunctionForInput(ensembleInvocationRequest, ensemble);

        if (modelResults.isEmpty()) {
            throw new RuntimeException(String.format("Error invoking function function=%s, model results are empty",
                    ensembleInvocationRequest.getEnsembleFunction()));
        }

        Map.Entry<String, ModelInferenceOutput> sizeOfScoreLists = modelResults.entrySet().stream().findFirst().get();
        int sizeofScoreLists = sizeOfScoreLists.getValue().getFloatResponse().size();

        ConcurrentMap<String, List<Float>> easierModelResults = new ConcurrentHashMap<>();
        modelResults.forEach((key, value) -> {
            easierModelResults.put(this.getAliasForModel(key), value.getFloatResponse());
        });

        // Initializing the Jexl expression outside the loop and evaluating inside.
        JexlExpression expression = expressionExecutor.createExpression(function.get("formula"));
        List<Float> results = List.range(0, sizeofScoreLists)
                .map(i -> {
                    Map<String, Object> keyValuePair = new HashMap<>();
                    easierModelResults.forEach((key, value) -> keyValuePair.put(key, value.get(i)));
                    return expressionExecutor.evaluateToFloat(expression, keyValuePair);
                });
        return List.ofAll(results);
    }

    private static Map<String, String> getEnsembleFunctionForInput(EnsembleInvocationRequest ensembleInvocationRequest, EnsembleDTO ensemble) {
        try {
            return ensemble.getEnsembleFunction(ensembleInvocationRequest.getEnsembleFunction()).toJavaMap();
        } catch (Exception e) {
            throw new RuntimeException(String.format("Internal ensemble=%s doesn't have function=%s, request_ensemble=%s",
                    ensemble.getEnsembleName(),
                    ensembleInvocationRequest.getEnsembleFunction(),
                    ensembleInvocationRequest.getEnsembleName())
            );

        }
    }

    //Search map by value to get alias for a specific model name
    private String getAliasForModel(String model) {
        for (Tuple2<String, String> entry : ensemble.getAliasesForModels()) {
            if (model.equals(entry._2())) {
                return entry._1();
            }
        }
        return model;
    }

    private java.util.Map<String, String> getTagsForSpan() {
        return new HashMap<>() {{
            put("ensemble_name", ensembleInvocationRequest.getEnsembleName());
            put("ensemble_version", ensemble.getVersion());
            put("ensemble_function", ensembleInvocationRequest.getEnsembleFunction());
        }};
    }

}



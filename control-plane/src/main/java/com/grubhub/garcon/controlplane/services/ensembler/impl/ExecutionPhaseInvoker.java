package com.grubhub.garcon.controlplane.services.ensembler.impl;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceInput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceResult;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelOutputDTO;
import com.grubhub.garcon.controlplanerpc.model.ModelType;
import com.grubhub.garcon.modelinteraction.ModelInferenceClient;
import io.vavr.Tuple;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import static com.grubhub.garcon.controlplane.metrics.Spans.span;

@RequiredArgsConstructor
@Slf4j
public class ExecutionPhaseInvoker {

    private final ModelInferenceInput modelInferenceInput;

    public Output invokeInfer(ModelInferenceClient client) {
        log.debug("invokeInfer modelInferenceInput={}", modelInferenceInput);
        val model = modelInferenceInput.getModel();
        if (modelInferenceInput.getProcessedFeatures().isEmpty()) {
            throw new RuntimeException(String.format("Invocation of model=%s has empty list of processed features", model.getModelName()));
        }
        ModelInferenceResult result = client.infer(ModelType.valueOf(model.getModelType()), modelInferenceInput);
        log.debug("invokeInfer result={}", result);
        return span("normalize", getTagsForSpan(), () -> {
            Map<String, List<ModelInferenceOutputType>> normalizedOutputs = normalizeOutputs(result.getOutput());
            return Output.builder()
                    .modelInferenceInput(modelInferenceInput)
                    .rawOutput(result.getOutput())
                    .normalizedOutput(normalizedOutputs)
                    .isDefaultOutput(result.isDefaultOutput())
                    .build();
        });
    }

    private java.util.Map<String, String> getTagsForSpan() {
        return HashMap.of(
                "model_name", modelInferenceInput.getModel().getModelName(),
                "model_version", modelInferenceInput.getModel().getVersion()
        )
                .toJavaMap();
    }

    private Map<String, List<ModelInferenceOutputType>> normalizeOutputs(Map<String, List<ModelInferenceOutputType>> rawOutputs) {
        return HashMap.ofEntries(modelInferenceInput.getModel().getModelOutputs()
                .map(out -> Tuple.of(out.getOutputName(), normalizeOutput(rawOutputs.get(out.getOutputName()).get(), out)))
        );
    }

    private List<ModelInferenceOutputType> normalizeOutput(List<ModelInferenceOutputType> rawOutput, ModelOutputDTO modelOutput) {
        if (modelOutput.getNormalized() == null || !modelOutput.getNormalized()) {
            return rawOutput;
        }
        val outType = modelOutput.getOutputTypeEnum();
        if (!outType.canNormalize()) {
            return rawOutput;
        }
        logNormalizedInference(modelOutput);
        return outType.normalize(rawOutput, modelOutput);
    }

    private void logNormalizedInference(ModelOutputDTO m) {
        log.debug("Performing output normalization for model={}, output_name={}, type={}, avg={}, std={}, min={}, " +
                        "max={}, range_min={}, range_max={}",
                modelInferenceInput.getModel(),
                m.getOutputName(),
                m.getNormalizedType(),
                m.getNormalizedAvg(),
                m.getNormalizedStd(),
                m.getNormalizedMin(),
                m.getNormalizedMax(),
                m.getNormalizedRangeMin(),
                m.getNormalizedRangeMax()
        );
    }

    @Value
    @Builder
    public static class Output {
        ModelInferenceInput modelInferenceInput;
        Map<String, List<ModelInferenceOutputType>> rawOutput;
        Map<String, List<ModelInferenceOutputType>> normalizedOutput;
        boolean isDefaultOutput;
    }

}

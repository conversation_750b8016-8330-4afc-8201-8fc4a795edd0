package com.grubhub.garcon.controlplane.services.ensembler.impl;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EnsembleInvocationRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;

import static com.grubhub.garcon.controlplane.services.ensembler.rules.EnsembleWeightsStrategy.getEnsembleWeightsForInput;

@Slf4j
public class DefaultModelInferenceInvoker implements ModelInferenceInvoker {

    public static final Float MODEL_WEIGHT_CALCULATION_EPSILON = 0.000001f;

    private final ModelService modelService;

    @Inject
    public DefaultModelInferenceInvoker(final ModelService modelService) {
        this.modelService = modelService;
    }

    public ModelInferenceOutput getModelInferenceOutput(EnsembleInvocationRequest ensembleInvocationRequest, EnsembleDTO ensemble, String modelName) {
        final Map<String, Float> ensembleWeightsForInput = getEnsembleWeightsForInput(ensembleInvocationRequest, ensemble);
        final Option<Float> modelWeightsOp = ensembleWeightsForInput.get(modelName);
        if (modelWeightsOp.isEmpty()) {
            throw new RuntimeException(String.format("Unable to find weight for model=%s of ensemble=%s", modelName, ensemble.getEnsembleName()));
        }

        if (Math.abs(modelWeightsOp.get()) < MODEL_WEIGHT_CALCULATION_EPSILON) {
            log.debug(String.format("Weights for model=%s of ensemble=%s are lower than %s. Defaulting weights to zeroes.",
                    modelName,
                    ensemble.getEnsembleName(),
                    MODEL_WEIGHT_CALCULATION_EPSILON));
            return ModelInferenceOutput.fromListOfNZeroes(ensembleInvocationRequest.getFeatures().size());
        }
        ModelInferenceOutput output = modelService.invokeModelInferenceMultiOutput(ensembleInvocationRequest.toModelInvocationRequest(modelName));
        modelService.publishModelOutputAverage(output);
        return output;
    }

}

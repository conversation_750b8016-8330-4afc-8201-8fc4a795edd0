package com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.branchselection.VariantRequestContext;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import io.vavr.collection.List;

public class RoutingGroupStrategyFactory {


    public static RoutingGroupStrategy pickFlowRoutingGroupStrategy(final RoutingGroupStrategyInput routingGroupStrategyInput) {
        List<FlowRoutingGroupV2> flowRoutingGroups = routingGroupStrategyInput.flowRoutingGroups();
        ControlPlaneMapper controlPlaneMapper = routingGroupStrategyInput.controlPlaneMapper();
        FlowDTO flow = routingGroupStrategyInput.flow();

        if (flowRoutingGroups != null && flowRoutingGroups.size() == 1) {
            return new SelectFirstRoutingGroupStrategy(controlPlaneMapper, flowRoutingGroups);
        }

        final VariantRequestContext variantRequestContext = VariantRequestContext.from(routingGroupStrategyInput.resolveFlowRequest());

        if (flow.getRoutingGroupsBucketingMode() == null) {
            return GeohashRandomRoutingGroupStrategy.from(routingGroupStrategyInput, variantRequestContext);
        }

        return switch (flow.getRoutingGroupsBucketingMode()) {
            case DINER ->
                    UuidRandomRoutingGroupStrategy.from(routingGroupStrategyInput, variantRequestContext, VariantRequestContext::getDinerId);
            case CALLER_TRACKING_ID ->
                    UuidRandomRoutingGroupStrategy.from(routingGroupStrategyInput, variantRequestContext, VariantRequestContext::getCallerTrackingId);
            case TRACKING_ID ->
                    UuidRandomRoutingGroupStrategy.from(routingGroupStrategyInput, variantRequestContext, VariantRequestContext::getTrackingId);
            case DINER_AND_CLIENT_ENTITY_ID -> {
                final List<String> compositeKeys = List.of(variantRequestContext.getDinerId(), variantRequestContext.getClientEntityId());
                yield CompositeRandomRoutingGroupStrategy.from(routingGroupStrategyInput, compositeKeys);
            }
            default -> GeohashRandomRoutingGroupStrategy.from(routingGroupStrategyInput, variantRequestContext);
        };
    }

}

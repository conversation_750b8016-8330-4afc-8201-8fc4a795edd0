package com.grubhub.garcon.controlplane.services.controlplane.seedrotation;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import io.vavr.collection.List;

public interface SeedSelector {
    List<FlowDTO> select();

    private static String flowToString(FlowDTO flow) {
        return "{ flow_id=" + flow.getFlowId() + ", flow_name=" + flow.getFlowName() + " }";
    }

    default String flowsToString(List<FlowDTO> selectedFlows) {
        return selectedFlows
                .map(SeedSelector::flowToString)
                .intersperse(",")
                .reduce(String::concat);
    }

}

package com.grubhub.garcon.controlplane.validators;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowRoutingGroupDTO;
import com.grubhub.garcon.controlplaneapi.service.ensembler.EnsembleService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import lombok.RequiredArgsConstructor;
import lombok.val;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class EnsembleWeightsAndNameValidator {

    private final EnsembleService ensembleService;
    private final ModelService modelService;

    public FlowDTO validateEnsembleWeightsAndName(FlowDTO flow) {
        flow.getRoutingGroupsCriteria().forEach(criteria -> {
            for (FlowRoutingGroupDTO routingGroup : criteria._2) {
                val ensembleOrModelName = routingGroup.getEnsembleName();

                // If the ensemble is actually a model.
                if (routingGroup.isEnsembleIsModel()) {
                    val modelAsOption = modelService.getModel(ensembleOrModelName);
                    if (modelAsOption.isEmpty()) {
                        modelService.getModel(modelService.getAlias(ensembleOrModelName).getOrElseThrow(() ->
                                new RuntimeException(String.format("For flow_name=%s, model_name (or alias_name)=%s does not exist in database.",
                                        flow.getFlowName(), ensembleOrModelName))).getEntityId());
                    }
                } else {
                    val ensembleAsOption = ensembleService.getEnsembleByNameOrAlias(ensembleOrModelName);
                    if (ensembleAsOption.isEmpty()) {
                        throw new RuntimeException(
                                String.format("For flow_name=%s, ensemble_name=%s does not exist in database.", flow.getFlowName(), ensembleOrModelName)
                        );
                    } else if (!ensembleAsOption.get().getEnsembleWeights().containsKey(routingGroup.getEnsembleWeight())) {
                        throw new RuntimeException(
                                String.format("For flow_name=%s, ensemble_name=%s does not contain ensemble_weight=%s.",
                                        flow.getFlowName(), ensembleOrModelName, routingGroup.getEnsembleWeight())
                        );
                    }
                }
            }
        });

        return flow;
    }
}

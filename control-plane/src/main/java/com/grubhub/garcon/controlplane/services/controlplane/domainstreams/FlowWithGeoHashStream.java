package com.grubhub.garcon.controlplane.services.controlplane.domainstreams;

import com.grubhub.garcon.controlplane.custom.ForwardingStream;
import com.grubhub.garcon.controlplane.custom.OptionalResolveFlowContext;
import com.grubhub.garcon.controlplane.dto.FlowWithGeoHashDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import io.opentracing.Span;
import io.vavr.collection.List;
import io.vavr.collection.Stream;
import lombok.val;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;

import static com.grubhub.garcon.controlplane.custom.OptionalResolveFlowContext.asOptionalResolveFlowContext;

public class FlowWithGeoHashStream implements ForwardingStream<FlowWithGeoHashDTO> {

    private final Stream<FlowWithGeoHashDTO> stream;
    private final Map<String, ModelInferenceOutput> cache;

    private FlowWithGeoHashStream(Stream<FlowWithGeoHashDTO> stream) {
        this.stream = stream;
        this.cache = new HashMap<>();
    }

    private FlowWithGeoHashStream(Stream<FlowWithGeoHashDTO> stream, Map<String, ModelInferenceOutput> cache) {
        this.stream = stream;
        this.cache = cache == null ? new HashMap<>() : cache;
    }


    public static FlowWithGeoHashStream asFlowWithGeoHashStream(Stream<FlowWithGeoHashDTO> stream) {
        return new FlowWithGeoHashStream(stream);
    }

    public static FlowWithGeoHashStream empty() {
        return new FlowWithGeoHashStream(Stream.empty());
    }

    public static FlowWithGeoHashStream asFlowWithGeoHashStream(List<FlowWithGeoHashDTO> flowsWithGeoHashes) {
        return new FlowWithGeoHashStream(flowsWithGeoHashes.toStream());
    }

    private FlowWithGeoHashStream of(Stream<FlowWithGeoHashDTO> stream) {
        return new FlowWithGeoHashStream(stream, this.cache);
    }

    private FlowWithGeoHashStream filterByDinerType(Predicate<FlowWithGeoHashDTO> predicate) {
        return of(this.stream.filter(predicate));
    }

    private FlowWithGeoHashStream filterByRoutingGroupSelectionCriteria(Predicate<FlowWithGeoHashDTO> predicate) {
        return of(this.stream.filter(predicate));
    }

    private FlowWithGeoHashStream thenFilterBy(Predicate<FlowWithGeoHashDTO> predicate) {
        return of(this.stream.filter(predicate));
    }

    public FlowWithGeoHashStream addSpan(Span span) {
        span.finish();
        return this;
    }

    public FlowWithGeoHashStream applyFlowFilters(Function<FlowWithGeoHashDTO, ModelInferenceOutput> mapping) {
        return of(this.stream)
                .filterByRoutingGroupSelectionCriteria(FlowWithGeoHashDTO::containsRoutingGroupSelectionCriteria)
                .filterByDinerType(FlowWithGeoHashDTO::containsDinerType)
                .thenFilterBy(FlowWithGeoHashDTO::containsOrderTypes)
                .thenFilterBy(FlowWithGeoHashDTO::containsMealTime)
                .thenFilterBy(FlowWithGeoHashDTO::containsMatchingApplication)
                .thenFilterBy(FlowWithGeoHashDTO::containsQueryType)
                .thenFilterBy(FlowWithGeoHashDTO::containsQueryTokens)
                .thenFilterBy(FlowWithGeoHashDTO::containsQueryKeywords)
                .thenFilterBy(FlowWithGeoHashDTO::containsMatchingBrands)
                .thenFilterBy(FlowWithGeoHashDTO::containsRequestedTags)
                .thenFilterByMatchingModelClassification(FlowWithGeoHashDTO::containsModelClassification, mapping)
                .thenFilterBy(FlowWithGeoHashDTO::containsUserDomains)
                .thenFilterBy(FlowWithGeoHashDTO::containsUserEmail)
                .thenFilterBy(FlowWithGeoHashDTO::containsApplicationVersions)
                .thenFilterBy(FlowWithGeoHashDTO::containsClientEntityIds);
    }

    private FlowWithGeoHashStream thenFilterByMatchingModelClassification(
            Predicate<FlowWithGeoHashDTO> predicate,
            Function<FlowWithGeoHashDTO, ModelInferenceOutput> modelOutputFetcher) {

        return of(this.stream
                .filter(flowWithGeoHash -> {
                    if (flowWithGeoHash.matchingModelClassificationsNonNullOrEmpty()) {
                        val newFlowWithGeoHash = toModelClassifications(modelOutputFetcher, flowWithGeoHash);
                        return predicate.test(newFlowWithGeoHash);
                    }
                    return true;
                }));
    }

    private FlowWithGeoHashDTO toModelClassifications(Function<FlowWithGeoHashDTO, ModelInferenceOutput> modelOutputFetcher,
                                                      FlowWithGeoHashDTO flowWithGeoHashDTO) {

        val modelOutputs = cache.computeIfAbsent(flowWithGeoHashDTO.getModelInferenceCacheKey(),
                key -> modelOutputFetcher.apply(flowWithGeoHashDTO));
        val responses = getModelClassificationsFromOutput(flowWithGeoHashDTO, modelOutputs);
        val enrichedRequest = flowWithGeoHashDTO.getResolveFlowRequest()
                .withResolvedModelClassifications(responses);
        return flowWithGeoHashDTO.withResolveFlowRequest(enrichedRequest);
    }

    private Set<String> getModelClassificationsFromOutput(FlowWithGeoHashDTO flowWithGeoHashDTO, ModelInferenceOutput modelOutputs) {
        return modelOutputs
                .getStringArrayResponse(flowWithGeoHashDTO.getClassificationModelOutputName())
                .toStream()
                .take(1)
                .map(List::toJavaSet)
                .getOrElseThrow(
                        () -> new RuntimeException(String.format("Classification model output name classification_model_output_name=%s " +
                                        " for flow flow_name=%s, model_name=%s, not found in map with keys [%s]",
                                flowWithGeoHashDTO.getClassificationModelOutputName(),
                                flowWithGeoHashDTO.getFlowName(),
                                modelOutputs.getModelName(),
                                modelOutputs.getResponse().keySet()))
                );
    }

    public OptionalResolveFlowContext selectFlow() {
        return asOptionalResolveFlowContext(this.stream.minBy(i -> i.getFlow().getPriority()).getOrNull());
    }

    public FlowWithGeoHashStream clearCache() {
        this.cache.clear();
        return this;
    }

    @Override
    public java.util.stream.Stream<FlowWithGeoHashDTO> getStream() {
        return stream.toJavaStream();
    }

}

package com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.RoutingGroupResponseDTO;
import io.vavr.collection.List;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SelectFirstRoutingGroupStrategy implements RoutingGroupStrategy {
    private final ControlPlaneMapper controlPlaneMapper;
    private final List<FlowRoutingGroupV2> routingGroups;

    @Override
    public RoutingGroupResponseDTO selectRoutingGroup() {
        if (routingGroups == null || routingGroups.size() == 0) {
            return null;
        }

        return controlPlaneMapper.toRoutingGroupResponse(routingGroups.get(0), 1);
    }
}

package com.grubhub.garcon.controlplane.services.ensembler.rules;

import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public enum FeatureDefaultValueType {
    STRING {
        @Override
        public Object doParseValue(String rawValue) {
            return rawValue;
        }
    },
    FLOAT {
        @Override
        public Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseDouble(rawValue, 0.0).floatValue();
        }
    },
    DOUBLE {
        @Override
        public Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseDouble(rawValue, 0.0);
        }
    },
    LONG {
        @Override
        public Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseDouble(rawValue, 0.0).longValue();
        }
    },
    INTEGER {
        @Override
        public Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseInteger(rawValue, 0);
        }
    },
    BOOLEAN {
        @Override
        public Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseBoolean(rawValue, false);
        }
    },
    NULL {
        @Override
        public Object doParseValue(String rawValue) {
            return null;
        }
    },
    FLOAT_ARRAY {
        @Override
        protected Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseFloatList(rawValue);
        }
    },
    DOUBLE_ARRAY {
        @Override
        protected Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseDoubleList(rawValue);
        }
    },
    LONG_ARRAY {
        @Override
        protected Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseLongList(rawValue);
        }
    },
    INTEGER_ARRAY {
        @Override
        protected Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseIntegerList(rawValue);
        }
    },
    STRING_ARRAY {
        @Override
        protected Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseStringList(rawValue);
        }
    },
    BOOLEAN_ARRAY {
        @Override
        protected Object doParseValue(String rawValue) {
            return ControlPlaneUtils.parseBooleanArray(rawValue);
        }
    }
    ;

    public Object parseValue(String rawValue) {
        try {
            return this.doParseValue(rawValue);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Could not parse feature default value of type=%s, value=%s", this.name(), rawValue), e);
        }
    }

    public static FeatureDefaultValueType parseType(String typeStr) {
        String typeStrToUse = StringUtils.defaultIfEmpty(typeStr, FeatureDefaultValueType.STRING.name());
        try {
            return FeatureDefaultValueType.valueOf(typeStrToUse);
        } catch (Exception e) {
            log.error("Invalid default feature type: {}", typeStr, e);
            return FeatureDefaultValueType.STRING;
        }
    }

    public static Object parseTypeAndValue(String typeStr, String rawValue) {
        return FeatureDefaultValueType.parseType(typeStr).parseValue(rawValue);
    }

    protected abstract Object doParseValue(String rawValue);


}

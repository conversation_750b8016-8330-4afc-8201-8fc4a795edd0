package com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.branchselection.UuidBranchSelection;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.branchselection.VariantRequestContext;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.RoutingGroupResponseDTO;
import com.grubhub.garcon.search.experiment.api.ExperimentConfiguration;
import com.grubhub.garcon.search.experiment.api.ValueBranchSelector;
import com.grubhub.garcon.search.experiment.utility.MappingProviders;
import io.vavr.collection.List;

import java.util.UUID;
import java.util.function.Function;

public class UuidRandomRoutingGroupStrategy extends SelectRandomRoutingGroupStrategy<UUID> {

    private final VariantRequestContext variantRequestContext;
    private final Function<VariantRequestContext, String> uuidProvider;

    private UuidRandomRoutingGroupStrategy(final FlowDTO flow, final FlowConfig flowConfig,
                                           final List<FlowRoutingGroupV2> flowRoutingGroups,
                                           final ValueBranchSelector<UUID, String> branchSelector,
                                           final ControlPlaneMapper controlPlaneMapper,
                                           final VariantRequestContext variantRequestContext,
                                           final Function<VariantRequestContext, String> uuidProvider) {
        super(flow, flowConfig, branchSelector, flowRoutingGroups, controlPlaneMapper);
        this.uuidProvider = uuidProvider;
        this.variantRequestContext = variantRequestContext;
    }

    public static UuidRandomRoutingGroupStrategy from(final RoutingGroupStrategyInput routingGroupStrategyInput,
                                                      final VariantRequestContext variantRequestContext,
                                                      final Function<VariantRequestContext, String> uuidProvider) {

        List<FlowRoutingGroupV2> flowRoutingGroups = routingGroupStrategyInput.flowRoutingGroups();
        FlowDTO flow = routingGroupStrategyInput.flow();
        FlowConfig flowConfig = routingGroupStrategyInput.flowConfig();

        final ExperimentConfiguration.Simple variantConfig = createVariantConfig(flow, flowConfig, flowRoutingGroups);
        final ValueBranchSelector<UUID, String> branchSelector =
                routingGroupStrategyInput.branchManager().createCutoffUuidSelector(MappingProviders.createStringProvider(variantConfig));

        return new UuidRandomRoutingGroupStrategy(flow, flowConfig, flowRoutingGroups, branchSelector,
                routingGroupStrategyInput.controlPlaneMapper(), variantRequestContext, uuidProvider);
    }

    @Override
    public RoutingGroupResponseDTO selectRoutingGroup() {
        return toRoutingGroupResponse(selectUsingUuid());
    }

    private FlowRoutingGroupV2 selectUsingUuid() {
        return new UuidBranchSelection(branchSelector, variantRequestContext, uuidProvider)
                .select()
                .flatMap(groupName -> flowRoutingGroups.find(group -> group.getGroupName().equals(groupName)).toOption())
                .getOrElse(() -> flowRoutingGroups.get(flowRoutingGroups.size() - 1));
    }
}

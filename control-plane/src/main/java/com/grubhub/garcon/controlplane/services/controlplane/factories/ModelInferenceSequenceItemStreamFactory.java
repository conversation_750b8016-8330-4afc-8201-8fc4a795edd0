package com.grubhub.garcon.controlplane.services.controlplane.factories;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.metrics.MetricsProvider;
import com.grubhub.garcon.controlplane.services.controlplane.domainstreams.ConditionalModelInferenceSequenceItemStream;
import com.grubhub.garcon.controlplane.services.controlplane.domainstreams.ModelInferenceSequenceItemStream;
import com.grubhub.garcon.controlplane.services.controlplane.sequential.SequentialExecutionProcessor;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceItem;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceRequest;
import io.vavr.collection.Stream;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

public class ModelInferenceSequenceItemStreamFactory {
    private final SequentialExecutionProcessor sequentialExecutionProcessor;
    private final MetricsProvider metricsProvider;

    @Inject
    public ModelInferenceSequenceItemStreamFactory(SequentialExecutionProcessor sequentialExecutionProcessor,
                                                   MetricsProvider metricsProvider) {
        this.sequentialExecutionProcessor = sequentialExecutionProcessor;
        this.metricsProvider = metricsProvider;
    }

    /**
     * This method checks whether request contains 'pre-condition' field. If it does, the return type will be
     * a conditional model inference stream, otherwise it's a normal inference stream.
     *
     * @param request sequential inference request
     * @return ModelInferenceSequenceItemStream or ConditionalModelInferenceSequenceItemStream
     * <p>
     */
    public ModelInferenceSequenceItemStream create(ModelInferenceSequenceRequest request) {
        boolean isConditionalRequest = false;
        if (CollectionUtils.isNotEmpty(request.getInvocations())) {
            isConditionalRequest = request.getInvocations().stream()
                    .anyMatch(ModelInferenceSequenceItemStreamFactory::isConditionalInvocationRequest);
        }
        val requestsStream = Stream.ofAll(request.getInvocations());
        if (isConditionalRequest) {
            return new ConditionalModelInferenceSequenceItemStream(requestsStream, sequentialExecutionProcessor,
                    request, metricsProvider);
        }
        return new ModelInferenceSequenceItemStream(requestsStream, request);
    }

    private static boolean isConditionalInvocationRequest(ModelInferenceSequenceItem r) {
        return ObjectUtils.isNotEmpty(r.getPreCondition());
    }
}

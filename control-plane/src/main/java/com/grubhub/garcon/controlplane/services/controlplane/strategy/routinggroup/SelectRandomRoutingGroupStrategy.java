package com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup;

import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.RoutingGroupResponseDTO;
import com.grubhub.garcon.search.experiment.api.ExperimentConfiguration;
import com.grubhub.garcon.search.experiment.api.ValueBranchSelector;
import io.vavr.collection.List;
import lombok.val;

import java.util.ArrayList;
import java.util.Collections;
import java.util.stream.Collector;
import java.util.stream.Collectors;

public abstract class SelectRandomRoutingGroupStrategy<T> implements RoutingGroupStrategy {

    private static final Float ZERO = 0f;
    private static final Float ONE = 1f;

    final FlowDTO flow;
    final FlowConfig flowConfig;
    final ValueBranchSelector<T, String> branchSelector;
    final List<FlowRoutingGroupV2> flowRoutingGroups;
    private final ControlPlaneMapper controlPlaneMapper;


    public SelectRandomRoutingGroupStrategy(final FlowDTO flow, final FlowConfig flowConfig,
                                            final ValueBranchSelector<T, String> branchSelector,
                                            final List<FlowRoutingGroupV2> flowRoutingGroups,
                                            final ControlPlaneMapper controlPlaneMapper) {
        this.flow = flow;
        this.flowConfig = flowConfig;
        this.branchSelector = branchSelector;
        this.flowRoutingGroups = flowRoutingGroups;
        this.controlPlaneMapper = controlPlaneMapper;
    }

    RoutingGroupResponseDTO toRoutingGroupResponse(final FlowRoutingGroupV2 flowRoutingGroup) {
        return controlPlaneMapper.toRoutingGroupResponse(flowRoutingGroup, 1F);
    }

    protected static ExperimentConfiguration.Simple createVariantConfig(final FlowDTO flow, final FlowConfig flowConfig,
                                                                        final List<FlowRoutingGroupV2> flowRoutingGroups) {
        val experimentConfig = new ExperimentConfiguration.Simple();
        val routingGroupsSeed = flow.getRoutingGroupsSeed() != 0 ? flow.getRoutingGroupsSeed() : flowConfig.getRoutingGroupsSeed();
        experimentConfig.setSeed(routingGroupsSeed);
        experimentConfig.setMode(ExperimentConfiguration.Mode.ON);
        experimentConfig.setOptions(Collections.singletonMap("bucketingMode", flow.getRoutingGroupsBucketingMode()));
        experimentConfig.setMapping(computeMapping(flowRoutingGroups));
        return experimentConfig;
    }

    private static String computeMapping(final List<FlowRoutingGroupV2> flowRoutingGroups) {

        return getPercentagesWithEdges(flowRoutingGroups)
                .zipWith(
                        flowRoutingGroups
                                .map(FlowRoutingGroupV2::getGroupName),
                        (percentage, groupName) -> percentage + "=" + groupName
                )
                .intersperse(";")
                .append(";")
                .append(String.valueOf(ONE))
                .collect(Collectors.joining());
    }

    private static List<Float> getPercentagesWithEdges(final List<FlowRoutingGroupV2> flowRoutingGroups) {
        return List.ofAll(
                flowRoutingGroups.toStream()
                        .map(FlowRoutingGroupV2::getRoutingPercentage)
                        .prepend(ZERO)
                        .collect(createCumulativeCollector())
        );
    }

    private static Collector<Float, ArrayList<Float>, ArrayList<Float>> createCumulativeCollector() {
        return Collector.of(
                ArrayList::new,
                (a, b) -> a.add(a.isEmpty() ? b : b + a.get(a.size() - 1)),
                (a, b) -> {
                    throw new UnsupportedOperationException();
                }
        );
    }

}

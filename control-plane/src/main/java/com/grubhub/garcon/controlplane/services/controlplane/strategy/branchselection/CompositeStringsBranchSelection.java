package com.grubhub.garcon.controlplane.services.controlplane.strategy.branchselection;

import com.grubhub.garcon.search.experiment.api.ValueBranchSelector;
import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;


import java.util.Optional;

@RequiredArgsConstructor
public class CompositeStringsBranchSelection implements BranchSelection {
    private final ValueBranchSelector<List<String>, String> delegate;
    private final List<String> keys;

    @Override
    public Option<String> select() {
        return Option.ofOptional(selectUsingCompositeKeys(keys));
    }


    private Optional<String> selectUsingCompositeKeys(final List<String> keys) {
        return Optional.ofNullable(keys)
                .flatMap(delegate::select);
    }

}

package com.grubhub.garcon.controlplane.services.controlplane.strategy.branchselection;

import ch.hsr.geohash.GeoHash;
import com.grubhub.garcon.search.experiment.api.ValueBranchSelector;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class GeohashBranchSelection implements BranchSelection {
    private final ValueBranchSelector<GeoHash, String> delegate;
    private final VariantRequestContext variantRequestContext;

    @Override
    public Option<String> select() {
        return Option.ofOptional(delegate.select(variantRequestContext.getGeoHash()));
    }
}

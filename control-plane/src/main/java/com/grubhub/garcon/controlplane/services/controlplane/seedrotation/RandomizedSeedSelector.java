package com.grubhub.garcon.controlplane.services.controlplane.seedrotation;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import io.vavr.collection.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

@RequiredArgsConstructor
@Slf4j
public class RandomizedSeedSelector implements SeedSelector {

    private final List<FlowDTO> flows;

    private boolean isRoutingGroupsSeedRotationEnabled(FlowDTO flow) {
        return (flow.getRoutingGroupsSeedRotationEnabled() != null && flow.getRoutingGroupsSeedRotationEnabled())
                && (flow.getRoutingGroupsSeedRotation() == null || flow.getRoutingGroupsSeedRotation().isEmpty());
    }

    @Override
    public List<FlowDTO> select() {
        val selectedFlows = flows.filter(this::isRoutingGroupsSeedRotationEnabled);

        if (!selectedFlows.isEmpty()) {
            log.debug("Flows considered for in place seed rotation: {}", flowsToString(selectedFlows));
        }
        return selectedFlows;
    }
}

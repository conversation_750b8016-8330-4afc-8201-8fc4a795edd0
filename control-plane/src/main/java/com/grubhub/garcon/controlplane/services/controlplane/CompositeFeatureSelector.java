package com.grubhub.garcon.controlplane.services.controlplane;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceItem;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelNestedOutputInfo;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelNestedOutputPlacement.FEATURE;
import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelNestedOutputPlacement.GLOBAL;
import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelNestedOutputTransformation.FLATTEN_AS_FEATURE_ARRAY;
import static com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelNestedOutputTransformation.FLATTEN_AS_SINGLE_ARRAY;
import static java.util.stream.Collectors.toList;

@Slf4j
@Getter
public class CompositeFeatureSelector implements FeatureSelector {

    private final List<ModelInferenceOutput> previousOutput;

    public CompositeFeatureSelector() {
        this.previousOutput = new ArrayList<>();
    }


    public void updatePreviousOutput(ModelInferenceOutput modelInferenceOutput) {
        previousOutput.add(modelInferenceOutput);
    }

    @Override
    public ModelInferenceOutput returnLastOutput() {
        if (previousOutput.isEmpty()) {
            return null;
        }
        return previousOutput.get(previousOutput.size() - 1);
    }

    private boolean shouldUpdateFeature(ModelInferenceSequenceItem sequenceItem) {
        return !previousOutput.isEmpty() && ObjectUtils.isNotEmpty(sequenceItem.getNestedOutputInfo());
    }

    private void updateFeatures(java.util.List<ModelInferenceOutput> previousOutput,
                                ModelInferenceSequenceItem sequenceItem) {
        for (val nestedOutputInfo : sequenceItem.getNestedOutputInfo()) {
            io.vavr.collection.List<?> featureAttributesToAdd = getTransformedFeaturesFromPreviousOutput(previousOutput, sequenceItem, nestedOutputInfo);

            updateFeature(sequenceItem, nestedOutputInfo, featureAttributesToAdd);
        }
    }

    public void updateFeaturesWithPreviousOutputs(ModelInferenceSequenceItem sequenceItem) {
        if (shouldUpdateFeature(sequenceItem)) {
            updateFeatures(previousOutput, sequenceItem);
        }
    }


    private io.vavr.collection.List<?> getTransformedFeaturesFromPreviousOutput(
            java.util.List<ModelInferenceOutput> previousOutput,
            ModelInferenceSequenceItem sequenceItem,
            ModelNestedOutputInfo nestedOutputKey) {

        log.debug("previousOutput: {}", previousOutput);
        io.vavr.collection.List<?> featureAttributesToAdd = previousOutput
                .get(nestedOutputKey.getSourceItemIndex())
                .getResponseValueMap()
                .get(nestedOutputKey.getSourceOutputName())
                .getOrElse(() -> {
                    log.warn("Unable to find key={} in the feature attributes of the previous output when executing model_name={}",
                            nestedOutputKey.getFeatureName(), sequenceItem.getModelName());
                    return io.vavr.collection.List.empty();
                });
        return transform(
                nestedOutputKey,
                featureAttributesToAdd);
    }

    private void updateFeature(ModelInferenceSequenceItem sequenceItem,
                               ModelNestedOutputInfo nestedOutputInfo,
                               io.vavr.collection.List<?> finalFeatureAttributesToAdd) {

        if (nestedOutputInfo.getPlacement() == GLOBAL) {
            if (nestedOutputInfo.getTransformation() == FLATTEN_AS_FEATURE_ARRAY) {
                throw new RuntimeException(
                        String.format("Unsupported transformation=%s in combination with placement=%s for model_name=%s output",
                                FLATTEN_AS_FEATURE_ARRAY, GLOBAL, sequenceItem.getModelName()));
            } else {
                sequenceItem.getGlobalFeatures().put(nestedOutputInfo.getFeatureName(), finalFeatureAttributesToAdd);
            }
        } else {

            if (nestedOutputInfo.getTransformation() == FLATTEN_AS_SINGLE_ARRAY) {
                throw new RuntimeException(
                        String.format("Unsupported transformation=%s in combination with placement=%s for model_name=%s output",
                                FLATTEN_AS_SINGLE_ARRAY, FEATURE, sequenceItem.getModelName()));
            } else {
                if (sequenceItem.getFeatures().isEmpty()) {
                    sequenceItem.setFeatures(IntStream.range(0, finalFeatureAttributesToAdd.size())
                            .boxed()
                            .map(java.util.HashMap<String, Object>::new)
                            .collect(toList()));

                } else if (!sequenceItem.getFeatures().isEmpty() && finalFeatureAttributesToAdd.isEmpty()) {
                    log.warn("Previous models outputs from model hierarchy are empty or couldn't be found, model_name={}", sequenceItem.getModelName());
                    return;
                } else if (sequenceItem.getFeatures().size() != finalFeatureAttributesToAdd.size()) {
                    throw new RuntimeException(
                            String.format("Total features=%s of model_name=%s is different than size=%s of nested output.",
                                    sequenceItem.getFeatures().size(), sequenceItem.getModelName(), finalFeatureAttributesToAdd.size()));
                }

                io.vavr.collection.Stream.ofAll(sequenceItem.getFeatures())
                        .forEachWithIndex((feature, index) ->
                                feature.put(nestedOutputInfo.getFeatureName(), getValue(finalFeatureAttributesToAdd, nestedOutputInfo, index)));
            }
        }
    }

    private Object getValue(io.vavr.collection.List<?> finalFeatureAttributesToAdd,
                            ModelNestedOutputInfo nestedOutputInfo, int index) {
        if (finalFeatureAttributesToAdd.get(index) instanceof Map) {
            return ((Map<String, ?>) finalFeatureAttributesToAdd.get(index)).get(nestedOutputInfo.getFeatureName()).get();
        }
        return finalFeatureAttributesToAdd.get(index);
    }


    @JsonIgnore
    public io.vavr.collection.List<?> transform(ModelNestedOutputInfo modelNestedOutputInfo,
                                                io.vavr.collection.List<?> featureAttributesToAdd) {
        switch (modelNestedOutputInfo.getTransformation()) {
            case NONE:
                return featureAttributesToAdd;
            case FLATTEN_AS_SINGLE_ARRAY:
                return getFlattenStream(featureAttributesToAdd)
                        .collect(io.vavr.collection.List.collector());
            case FLATTEN_AS_FEATURE_ARRAY:
                return getFlattenStream(featureAttributesToAdd)
                        .map(elem -> HashMap.of(modelNestedOutputInfo.getFeatureName(), elem))
                        .collect(io.vavr.collection.List.collector());
            default:
                log.error("Not being able to identify the transformation type for feature name {}, falling back to NONE",
                        modelNestedOutputInfo.getFeatureName());
                return featureAttributesToAdd;
        }
    }

    @JsonIgnore
    private io.vavr.collection.Stream<Object> getFlattenStream(io.vavr.collection.List<?> featureAttributesToAdd) {
        return featureAttributesToAdd.toStream().flatMap(this::toResponseStream);
    }

    @JsonIgnore
    private io.vavr.collection.Stream<?> toResponseStream(Object elem) {
        if (elem instanceof io.vavr.collection.List) {
            return ((io.vavr.collection.List<?>) elem).toStream();
        }
        return io.vavr.collection.Stream.of(elem);
    }
}

package com.grubhub.garcon.controlplane.services.controlplane.sequential;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutputType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceItem;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class SequentialContextUtils {
    private static final String KEY_TEMPLATE = "{model_name}.{field_type}.{name}";

    private static final String OUTPUTS_FIELD_TYPE = "OUTPUTS";
    private static final String EXECUTION_FIELD_TYPE = "EXECUTION";
    private static final String VERSION_SUFFIX_REGEX = "_v\\d*.\\d*";

    public static Map<String, Object> mapOutputToMap(ModelInferenceOutput output) {
        if (Objects.isNull(output) || Objects.isNull(output.getModelName())) {
            log.debug("Failed to create context for sequential execution. Missing fields for inference output: {}", output);
            throw new IllegalArgumentException("Missing required fields for sequential context processing");
        }
        Map<String, Object> result;

        if (!CollectionUtils.isEmpty(output.getResponse().toJavaList())) {
            result = process("success", true, getModelBaseName(output.getModelName()), EXECUTION_FIELD_TYPE);
            output.getResponse().forEach((keyValue) -> {
                io.vavr.collection.List<ModelInferenceOutputType> value = keyValue._2;
                result.put(formatKey(keyValue._1, getModelBaseName(output.getModelName()), OUTPUTS_FIELD_TYPE),
                        value.toJavaList()
                                .stream().map(ModelInferenceOutputType::getValue).collect(Collectors.toList()));
            });
        } else {
            result = process("success", false, getModelBaseName(output.getModelName()), EXECUTION_FIELD_TYPE);
        }

        result.put(formatKey("skipped", getModelBaseName(output.getModelName()), EXECUTION_FIELD_TYPE), false);

        return result;
    }

    public static Map<String, Object> mapToSkippedExecutionMap(ModelInferenceSequenceItem inferenceSequenceItem) {
        Map<String, Object> result;
        result = process("skipped", true, getModelBaseName(inferenceSequenceItem.getModelName()), EXECUTION_FIELD_TYPE);
        result.put(formatKey("success", getModelBaseName(inferenceSequenceItem.getModelName()), EXECUTION_FIELD_TYPE), false);
        return result;
    }

    public static Map<String, Object> mapToFailedExecutionMap(ModelInferenceSequenceItem inferenceSequenceItem) {
        Map<String, Object> result;
        result = process("skipped", false, getModelBaseName(inferenceSequenceItem.getModelName()), EXECUTION_FIELD_TYPE);
        result.put(formatKey("success", getModelBaseName(inferenceSequenceItem.getModelName()), EXECUTION_FIELD_TYPE), false);
        return result;
    }

    /**
     * @param modelName full model identifier which may containt "LATEST" or version suffix. We want to make it easier
     *                  to identify the model, so we're stripping any suffix which doesn't belong to a model's base name
     * @return modelName without any suffix
     */
    private static String getModelBaseName(String modelName) {
        var result = modelName.replace("_LATEST", "");
        // replace any versioning
        return result.replaceAll(VERSION_SUFFIX_REGEX, "");
    }

    private static Map<String, Object> process(String key, Object value, String modelName, String type) {
        val map = new HashMap<String, Object>();

        map.put(formatKey(key, modelName, type), value);
        return map;
    }

    private static String formatKey(String key, String modelName, String type) {
        return KEY_TEMPLATE.replace("{model_name}", modelName)
                .replace("{field_type}", type)
                .replace("{name}", key);
    }
}

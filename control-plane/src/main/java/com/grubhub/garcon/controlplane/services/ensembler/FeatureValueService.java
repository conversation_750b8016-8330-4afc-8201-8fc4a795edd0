package com.grubhub.garcon.controlplane.services.ensembler;

import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureKeyDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureValueDTO;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.control.Option;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

public interface FeatureValueService {
    CompletableFuture<Optional<FeatureValueDTO>> getFeatureValue(FeatureKeyDTO featureKey);

    Map<FeatureKeyDTO, FeatureValueDTO> getMany(List<FeatureKeyDTO> featureKeys);

    CompletableFuture<Void> createOrUpdateFeatureValue(FeatureValueDTO featureValue);

    void createOrUpdateMany(List<FeatureValueDTO> featureValues);

    void deleteFeatureValue(FeatureKeyDTO featureKey);

    void deleteMany(List<FeatureKeyDTO> featureKeys);

    Option<FeatureValueDTO> updateCache(FeatureKeyDTO featureKey, Option<FeatureValueDTO> featureValue);
}

package com.grubhub.garcon.controlplane.services.controlplane;

import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import com.grubhub.garcon.controlplane.cassandra.models.Market;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketByGeohashDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.EntityCollectionDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MarketDTO;
import io.vavr.collection.List;
import io.vavr.collection.Stream;
import io.vavr.control.Option;
import lombok.NonNull;

import java.util.concurrent.CompletableFuture;

public interface MarketService {

    /**
     * Get market data by marketName.
     * @param marketName the name/id corresponding to the market.
     * @return Market associated with the given name
     */
    Option<MarketDTO> getMarket(@NonNull String marketName);

    void createOrUpdateMarket(@NonNull MarketDTO market);

    void deleteMarket(@NonNull String marketName);

    void updateMarket(@NonNull MarketDTO market);

    List<Market> getMarketsForFlow(FlowDTO flow);

    List<FlowByGeoHash> getFlowsByGeoHash(FlowDTO flow);

    List<FlowByGeoHash> getFlowsByGeohashOrGlobal(FlowDTO flow);

    Stream<FlowByGeoHash> toFlowByGeoHash(FlowDTO flow, MarketDTO market);

    MarketDTO selectGlobalMarket();

    List<EntityCollectionDTO> getAllMarkets();

    List<MarketByGeohashDTO> getMarketsByGeohashes(int precision, List<String> geohashes);

    CompletableFuture<List<MarketByGeohashDTO>> getMarketsByGeohashesWithoutCache(int precision, List<String> geohashes);

    CompletableFuture<List<MarketByGeohashDTO>>getMarketsByGeohashWithoutCache(int precision, String geohash);

    void deleteMarketsByGeohashes(int precision, List<String> geohashes);

    void updateMarketsByGeohashes(List<MarketByGeohashDTO> marketByGeohashes);
}

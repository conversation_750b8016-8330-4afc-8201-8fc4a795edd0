package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Throwables;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.cassandra.dao.GdpActionResultDao;
import com.grubhub.garcon.controlplane.config.GdpDataTransferConfig;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.s3.S3ClientProvider;
import com.grubhub.garcon.controlplane.s3.S3Utils;
import com.grubhub.garcon.controlplane.services.controlplane.GdpActionsService;
import com.grubhub.garcon.controlplane.services.controlplane.gdpactions.GdpActionParser;
import com.grubhub.garcon.controlplane.services.controlplane.gdpactions.GdpActionsSyncer;
import com.grubhub.garcon.controlplane.services.ensembler.FeatureValueService;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionResultDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionType;
import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureValueDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelDTO;
import com.grubhub.garcon.controlplaneapi.service.ensembler.EnsembleService;
import com.grubhub.garcon.controlplaneapi.service.ensembler.ModelService;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.vavr.Tuple3;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import software.amazon.awssdk.services.s3.S3Client;

import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
public class GdpActionsServiceImpl implements GdpActionsService {

    private final S3ClientProvider s3ClientProvider;
    private final GdpDataTransferConfig gdpDataTransferConfig;
    private final ModelService modelService;
    private final EnsembleService ensembleService;
    private final JavaDateTimeHelper dateTimeHelper;
    private final GdpActionResultDao gdpActionResultDao;
    private final ControlPlaneMapper controlPlaneMapper;
    private final FeatureValueService featureValueService;
    private final MeterRegistry meterRegistry;
    private final GdpActionsSyncer gdpActionsSyncer;

    @Inject
    public GdpActionsServiceImpl(S3ClientProvider s3ClientProvider,
                                 GdpDataTransferConfig gdpDataTransferConfig,
                                 ModelService modelService,
                                 EnsembleService ensembleService,
                                 JavaDateTimeHelper dateTimeHelper,
                                 GdpActionResultDao gdpActionResultDao,
                                 ControlPlaneMapper controlPlaneMapper,
                                 @Named("featureValueService") FeatureValueService featureValueService,
                                 MeterRegistry meterRegistry,
                                 GdpActionsSyncer gdpActionsSyncer) {
        this.s3ClientProvider = s3ClientProvider;
        this.gdpDataTransferConfig = gdpDataTransferConfig;
        this.modelService = modelService;
        this.ensembleService = ensembleService;
        this.dateTimeHelper = dateTimeHelper;
        this.gdpActionResultDao = gdpActionResultDao;
        this.controlPlaneMapper = controlPlaneMapper;
        this.featureValueService = featureValueService;
        this.meterRegistry = meterRegistry;
        this.gdpActionsSyncer = gdpActionsSyncer;
    }

    @Override
    public List<GdpActionResultDTO> processPendingActions() {
        // Find latest date dir
        val s3 = s3ClientProvider.get();
        val s3ActionsPath = S3Utils.path(gdpDataTransferConfig.getBucket(), gdpDataTransferConfig.getActionsPrefix());
        log.info("Looking GDP pending actions dates in S3path={}", s3ActionsPath.getUri());
        val lastDate = S3Utils.getLastDirNameInPrefix(s3, s3ActionsPath.getUri());
        val s3ActionsDatePath = S3Utils.path(s3ActionsPath.getUri(), lastDate + "/").getUri();
        if (lastDate.isEmpty()) {
            log.info("There is not any GDP actions in s3Path={}, lastDate={}", s3ActionsPath, lastDate);
            return List.empty();
        }

        // Parse all actions in date dir
        log.info("Discovering GDP actions for date={} in S3path={}", lastDate, s3ActionsDatePath);
        val files = S3Utils.listFilesInPrefix(s3, s3ActionsDatePath, HashSet.of("json"));
        val processedDbActions = this.getAllProcessedForDate(lastDate);
        log.info("Total GDP actions files found={} in S3path={}, currently processed actions for same date={}",
                files.size(), s3ActionsDatePath, processedDbActions.size());
        val allActions = files.map(path -> this.loadActionFile(s3, path, lastDate));
        val loadedOkActions = allActions.filter(GdpActionResultDTO::isOk);
        val loadErrors = allActions.size() - loadedOkActions.size();
        log.info("GDP actions parsed for date={}, parse_ok={}, parse_issues={}, total_files_size={}", lastDate,
                loadedOkActions.size(), loadErrors, allActions.size());

        // Detect new actions to process
        val processedActionNames = processedDbActions.map(a -> a.getAction().getActionName()).toSet();
        val pendingActions = loadedOkActions.filter(a -> !processedActionNames.contains(a.getAction().getActionName()));
        log.info("GDP new pending actions to process={}, total_files={}, total_in_db={}, skipping={}", pendingActions.size(),
                loadedOkActions.size(), processedActionNames.size(), loadedOkActions.size() - pendingActions.size());

        Map<GdpActionType, List<GdpActionResultDTO>> groupedActions = groupPendingActions(pendingActions);

        List<GdpActionResultDTO> results = List.empty();
        for (Map.Entry<GdpActionType, List<GdpActionResultDTO>> entry : groupedActions.entrySet()) {
            var actionType = entry.getKey();
            var actionResultList = entry.getValue();
            switch (actionType) {
                case CREATE_MODEL:
                    results = results.appendAll(actionResultList.map(actionResult -> this.processAction(actionResult, this::doCreateModel)));
                    gdpActionsSyncer.disableOlderModels(actionResultList);
                    break;
                case CREATE_ENSEMBLE:
                    results = results.appendAll(actionResultList.map(actionResult -> this.processAction(actionResult, this::doCreateEnsemble)));
                    break;
                case INGEST_FEATURES:
                    results = results.appendAll(actionResultList.map(actionResult -> this.processAction(actionResult, this::doIngestFeatures)));
                    break;
                default:
                    throw new RuntimeException("Unsupported GDP action type=" + actionType);
            }
        };

        val resultsSplit = results.partition(GdpActionResultDTO::isOk);
        log.info("Finished processing GDP pending actions for date={}, total_scan={}, parsed_ok={}, processed_in_db={}, skipped={}, " +
                        "pending={}, processed_ok={}, processed_issues={}", lastDate, allActions.size(), loadedOkActions.size(),
                processedDbActions.size(), loadedOkActions.size() - pendingActions.size(), pendingActions.size(),
                resultsSplit._1.size(), resultsSplit._2.size());

        return results;
    }

    /**
     * Sorts pending actions by group and sequence.
     * All the actions will be taken into account at this point. It doesn't ensure that the underlying entity is being created successfully yet. ]
     * Groups by GdpActionType to be able to execute different processing methods.
     */
    @VisibleForTesting
    Map<GdpActionType, List<GdpActionResultDTO>> groupPendingActions(List<GdpActionResultDTO> pendingActions) {
        val groupsMap = pendingActions.map(i -> i.getAction().getActionGroup()).distinct().zipWithIndex().toMap(i -> i);
        log.info("Distinct action groups to process={}", groupsMap.keySet().mkString(", "));

        // Sorts by group first and then by sequence.
        List<GdpActionResultDTO> sortedActions = pendingActions
                .sortBy(i -> groupsMap.getOrElse(i.getAction().getActionGroup(), 0) * 10000 + i.getAction().getSequenceNumber());

        return sortedActions
                .groupBy(actionResult -> actionResult.getAction().getActionType()) // Group by action type.
                .toSortedMap(Comparator.comparing(GdpActionType::getActionOrderToBeIngested), t -> t) // Order each entry by getActionOrderToBeIngested.
                .toJavaMap();
    }

    @Override
    public List<GdpActionResultDTO> getAllProcessedForDate(String actionDate) {
        return gdpActionResultDao.selectAll(actionDate).map(controlPlaneMapper::toDTO);
    }


    @VisibleForTesting
    GdpActionResultDTO processAction(GdpActionResultDTO parseResult, Consumer<GdpActionDTO> createFunction) {
        val action = parseResult.getAction();
        try {
            log.info("Processing GDP pending action with name={}, actionType={}, group={}, seqNumber={}, s3Path={}, createdOn={}",
                    action.getActionName(), action.getActionType(), action.getActionGroup(), action.getSequenceNumber(),
                    action.getFilePath(), action.getCreatedDateTime());

            // Actual invocation to create model/ensemble or features.
            createFunction.accept(action);

            // Create result
            val result = GdpActionResultDTO.builder()
                    .action(action)
                    .ok(true)
                    .resultMsg("")
                    .loadedDateTime(parseResult.getLoadedDateTime())
                    .processedDateTime(currentTimeStr())
                    .build();

            // Insert processed action in DB
            gdpActionResultDao.insert(controlPlaneMapper.toModel(result));
            log.info("Successfully processed GDP pending action with date={}, name={}, actionType={}, group={}, seqNumber={}, " +
                            "s3Path={}, createdOn={}",
                    action.getActionDate(), action.getActionName(), action.getActionType(), action.getActionGroup(),
                    action.getSequenceNumber(), action.getFilePath(), action.getCreatedDateTime());
            return result;
        } catch (Exception e) {
            log.info("Error processing GDP pending action with date={}, name={}, actionType={}, group={}, seqNumber={}, " +
                            "s3Path={}, createdOn={}",
                    action.getActionDate(), action.getActionName(), action.getActionType(), action.getActionGroup(),
                    action.getSequenceNumber(), action.getFilePath(), action.getCreatedDateTime(), e);
            return GdpActionResultDTO.builder()
                    .action(action)
                    .ok(false)
                    .resultMsg(String.format("Error processing GDP pending action from s3Path=%s, exception=%s",
                            action.getFilePath(), Throwables.getStackTraceAsString(e)))
                    .loadedDateTime(parseResult.getLoadedDateTime())
                    .processedDateTime(currentTimeStr())
                    .build();
        } finally {
            meterRegistry
                    .counter(Metrics.name(getClass(), "GDPActions"), Tags.of("action_type", parseResult.getAction().getActionType().name()))
                    .increment();
        }
    }

    @SuppressWarnings("unchecked")
    private void doIngestFeatures(GdpActionDTO action) {
        List<FeatureValueDTO> features = GdpActionParser.loadActionContent(action, List.class);
        featureValueService.createOrUpdateMany(features);
        log.info("Ingested features from GDP action={}, date={}, s3Path={}, features={}, features_count={}",
                action.getActionName(),
                action.getActionDate(),
                action.getFilePath(),
                features.map(featureValueDTO -> new Tuple3<>(
                                featureValueDTO.getFeatureName(),
                                featureValueDTO.getMajorVersion(),
                                featureValueDTO.getMinorVersion())
                        )
                        .distinct()
                        .map(Tuple3::toString)
                        .intersperse(","),
                features.size()
        );
    }

    @VisibleForTesting
    void doCreateModel(GdpActionDTO action) {
        val model = GdpActionParser.loadActionContent(action, ModelDTO.class);
        this.modelService.createOrUpdateModel(model);
        log.info("Created model from GDP action={}, date={}, s3Path={}, model_name={}, model_version={}," +
                        "model_type={}, model_versioning_name={}, model_features_size={}",
                action.getActionName(),
                action.getActionDate(),
                action.getFilePath(),
                model.getModelName(),
                model.getVersion(),
                model.getModelType(),
                model.getVersioningModelName(),
                model.getModelFeatures().size()
        );
    }

    private void doCreateEnsemble(GdpActionDTO action) {
        val ensemble = GdpActionParser.loadActionContent(action, EnsembleDTO.class);
        this.ensembleService.createOrUpdateEnsemble(ensemble);
        log.info("Created ensemble from GDP action={}, date={}, s3Path={}, ensemble_name={}, ensemble_version={}," +
                        "ensemble_versioning_name={}",
                action.getActionName(),
                action.getActionDate(),
                action.getFilePath(),
                ensemble.getEnsembleName(),
                ensemble.getVersion(),
                ensemble.getVersioningEnsembleName()
        );
    }
    @VisibleForTesting
     GdpActionResultDTO loadActionFile(S3Client s3, String path, String actionDate) {
        try {
            log.info("Loading GDP pending action from s3Path={}", path);
            val action = S3Utils.loadJsonObject(s3, path, GdpActionDTO.class)
                    .withFilePath(path)
                    .withActionDate(actionDate);
            log.info("Loaded GDP pending action with date={}, name={}, actionType={}, group={}, seqNumber={}, s3Path={}, createdOn={}",
                    action.getActionDate(), action.getActionName(), action.getActionType(), action.getActionGroup(),
                    action.getSequenceNumber(), action.getFilePath(), action.getCreatedDateTime());
            return GdpActionResultDTO.builder()
                    .action(action)
                    .ok(true)
                    .resultMsg("")
                    .loadedDateTime(currentTimeStr())
                    .build();
        } catch (Exception e) {
            log.error("Error parsing GDP pending action from s3Path={}", path, e);
            return GdpActionResultDTO.builder()
                    .action(null)
                    .ok(false)
                    .resultMsg(String.format("Error parsing GDP pending action from s3Path=%s, exception=%s", path,
                            Throwables.getStackTraceAsString(e)))
                    .loadedDateTime(currentTimeStr())
                    .processedDateTime(currentTimeStr())
                    .build();
        }
    }

    private String currentTimeStr() {
        return dateTimeHelper.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_DATE_TIME);
    }

}

package com.grubhub.garcon.controlplane.services.controlplane.sequential;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceSequenceItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jexl3.JexlExpression;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Map;

@Slf4j
public class SequentialExecutionProcessor {
    private final ExpressionExecutor expressionExecutor;

    @Inject
    public SequentialExecutionProcessor(ExpressionExecutor expressionExecutor) {
        this.expressionExecutor = expressionExecutor;
    }

    public boolean shouldExecute(ModelInferenceSequenceItem request, Map<String, Object> contextMap) {
        if (ObjectUtils.isEmpty(request.getPreCondition())) {
            // if there's no precondition, it's free to run
            return true;
        }

        JexlExpression expression = expressionExecutor.createExpression(request.getPreCondition().getClause());
        return expressionExecutor.evaluateToBoolean(expression, contextMap);
    }
}

package com.grubhub.garcon.controlplane.resources;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.services.controlplane.GdpActionsService;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.GdpActionResultDTO;
import com.grubhub.garcon.controlplaneapi.resource.controlplane.GdpActionsResource;
import com.grubhub.roux.api.responses.GetResponse;
import com.grubhub.roux.discovery.ServiceVisibility;
import io.vavr.collection.List;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import static com.grubhub.roux.discovery.ServiceVisibilitySetting.PUBLIC;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
@ServiceVisibility(PUBLIC)
public class GdpActionsResourceImpl implements GdpActionsResource {

    private final GdpActionsService gdpActionsService;

    @Override
    public GetResponse<List<GdpActionResultDTO>> processPendingActions() {
        return GetResponse.success(this.gdpActionsService.processPendingActions());
    }

    @Override
    public GetResponse<List<GdpActionResultDTO>> getAllProcessedForDate(@NonNull String actionDate) {
        return GetResponse.success(this.gdpActionsService.getAllProcessedForDate(actionDate));
    }

}

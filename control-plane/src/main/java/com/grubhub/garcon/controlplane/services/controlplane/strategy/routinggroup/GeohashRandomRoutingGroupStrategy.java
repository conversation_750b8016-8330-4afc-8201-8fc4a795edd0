package com.grubhub.garcon.controlplane.services.controlplane.strategy.routinggroup;

import ch.hsr.geohash.GeoHash;
import com.grubhub.garcon.controlplane.cassandra.models.FlowRoutingGroupV2;
import com.grubhub.garcon.controlplane.config.FlowConfig;
import com.grubhub.garcon.controlplane.mapper.ControlPlaneMapper;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.branchselection.GeohashBranchSelection;
import com.grubhub.garcon.controlplane.services.controlplane.strategy.branchselection.VariantRequestContext;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.RoutingGroupResponseDTO;
import com.grubhub.garcon.search.experiment.api.ExperimentConfiguration;
import com.grubhub.garcon.search.experiment.api.ValueBranchSelector;
import com.grubhub.garcon.search.experiment.utility.MappingProviders;
import io.vavr.collection.List;

public class GeohashRandomRoutingGroupStrategy extends SelectRandomRoutingGroupStrategy<GeoHash> {

    private final VariantRequestContext variantRequestContext;

    private GeohashRandomRoutingGroupStrategy(final FlowDTO flow, final FlowConfig flowConfig,
                                              final List<FlowRoutingGroupV2> flowRoutingGroups,
                                              final ValueBranchSelector<GeoHash, String> branchSelector,
                                              final ControlPlaneMapper controlPlaneMapper,
                                              final VariantRequestContext variantRequestContext) {
        super(flow, flowConfig, branchSelector, flowRoutingGroups, controlPlaneMapper);
        this.variantRequestContext = variantRequestContext;
    }

    public static GeohashRandomRoutingGroupStrategy from(final RoutingGroupStrategyInput routingGroupStrategyInput,
                                                         final VariantRequestContext variantRequestContext) {
        final List<FlowRoutingGroupV2> flowRoutingGroups = routingGroupStrategyInput.flowRoutingGroups();
        final FlowDTO flow = routingGroupStrategyInput.flow();
        final FlowConfig flowConfig = routingGroupStrategyInput.flowConfig();

        final ExperimentConfiguration.Simple variantConfig = createVariantConfig(flow, flowConfig, flowRoutingGroups);
        final ValueBranchSelector<GeoHash, String> branchSelector =
                routingGroupStrategyInput.branchManager().createCutoffGeoSelector(MappingProviders.createStringProvider(variantConfig));

        return new GeohashRandomRoutingGroupStrategy(flow, flowConfig, flowRoutingGroups, branchSelector,
                routingGroupStrategyInput.controlPlaneMapper(), variantRequestContext);
    }

    @Override
    public RoutingGroupResponseDTO selectRoutingGroup() {
        return toRoutingGroupResponse(selectUsingGeohash());
    }

    private FlowRoutingGroupV2 selectUsingGeohash() {
        return new GeohashBranchSelection(branchSelector, variantRequestContext)
                .select()
                .flatMap(groupName -> flowRoutingGroups.find(group -> group.getGroupName().equals(groupName)).toOption())
                .getOrElse(() -> flowRoutingGroups.get(flowRoutingGroups.size() - 1));
    }
}

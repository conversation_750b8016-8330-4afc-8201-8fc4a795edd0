package com.grubhub.garcon.controlplane.exceptions;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.grubhub.roux.api.ValidationError;
import com.grubhub.roux.api.ValidationErrorList;
import io.vavr.collection.HashSet;
import io.vavr.collection.List;
import io.vavr.collection.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;

@Provider
@Slf4j
@RequiredArgsConstructor
public class ControlPlaneConstraintViolationExceptionMapper implements ExceptionMapper<ConstraintViolationException> {

    private final PropertyNamingStrategy propertyProvider;
    @Override
    public Response toResponse(ConstraintViolationException exception) {
        log.debug(exception.getMessage());
        return Response.status(Response.Status.BAD_REQUEST)
                .entity(buildValidationResponse(HashSet.ofAll(exception.getConstraintViolations())))
                .build();
    }

    public List<ValidationError> buildValidationResponse(Set<ConstraintViolation<?>> errors) {
        ValidationErrorList.Builder errorBuilder = ValidationErrorList.builder();

        for (val error : errors) {
            errorBuilder.addError(propertyProvider.nameForField(null, null, error.getPropertyPath().toString()),
                    error.getMessage());
        }
        val validationErrors = List.ofAll(errorBuilder.build());

        log.debug("Validation errors were found={}", validationErrors);

        return validationErrors;
    }
}

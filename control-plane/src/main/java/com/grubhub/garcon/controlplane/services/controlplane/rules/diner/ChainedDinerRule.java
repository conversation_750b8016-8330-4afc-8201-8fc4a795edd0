package com.grubhub.garcon.controlplane.services.controlplane.rules.diner;

import com.grubhub.garcon.controlplane.cassandra.models.DinerType;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.ResolveFlowRequest;
import lombok.RequiredArgsConstructor;

import java.util.Optional;
@RequiredArgsConstructor
public class ChainedDinerRule implements DinerRule {

    private final DinerRule head;
    private final DinerRule tail;

    @Override
    public Optional<DinerType> applicableTo(ResolveFlowRequest variantRequest, Integer dinerTypeOrdersThreshold) {
        return head.applicableTo(variantRequest, dinerTypeOrdersThreshold)
                .map(Optional::of)
                .orElseGet(() -> tail.applicableTo(variantRequest, dinerTypeOrdersThreshold));
    }
}

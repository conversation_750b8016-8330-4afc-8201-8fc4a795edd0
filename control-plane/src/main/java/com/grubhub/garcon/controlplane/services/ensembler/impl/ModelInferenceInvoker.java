package com.grubhub.garcon.controlplane.services.ensembler.impl;

import com.grubhub.garcon.controlplaneapi.models.ensembler.EnsembleDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.EnsembleInvocationRequest;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.ModelInferenceOutput;

public interface ModelInferenceInvoker {
    ModelInferenceOutput getModelInferenceOutput(EnsembleInvocationRequest ensembleInvocationRequest, EnsembleDTO ensemble, String modelName);
}

package com.grubhub.garcon.controlplane.services.ensembler.rules;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplane.services.common.utils.InternalFieldsUtils;
import com.grubhub.garcon.controlplane.services.ensembler.FeatureValueService;
import com.grubhub.garcon.controlplane.services.ensembler.impl.featureproviders.FeaturesProvider;
import com.grubhub.garcon.controlplane.util.ControlPlaneUtils;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FeatureDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureKeyDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureValueDTO;
import com.grubhub.garcon.ensembler.dto.EnrichedModelInferenceRequest;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.vavr.Tuple;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.Value;
import lombok.With;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.CompletableFuture;

import static com.grubhub.garcon.controlplane.metrics.Spans.TRACING_HELPER;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;

@RequiredArgsConstructor
@Slf4j
public class FeatureStoreInternalRule {

    private static final String MULTIKEY_LOOKUP_SET = ";";
    private static final ObjectMapper mapper = ObjectMapperHelper.INSTANCE;
    private final EnrichedModelInferenceRequest modelInferenceRequest;
    private final FeatureValueService featureValueService;
    private final List<Map<String, Object>> processedFeatures;
    private final MeterRegistry meterRegistry;


    public static FeatureStoreInternalRule createFeatureStoreInternalRule(
            @NonNull FeatureValueService featureValueService,
            @NonNull EnrichedModelInferenceRequest modelInferenceRequest,
            @NonNull List<Map<String, Object>> processedFeatures,
            @NonNull MeterRegistry meterRegistry) {
        return new FeatureStoreInternalRule(modelInferenceRequest, featureValueService, processedFeatures, meterRegistry);
    }

    @SneakyThrows
    public List<Map<String, Object>> processInternalFeatures(FeaturesProvider featuresProvider) {
        val internalFeatures = featuresProvider.provideFeatures();

        if (internalFeatures.isEmpty()) {
            return processedFeatures;
        }

        List<Map<String, Object>> result = processedFeatures;
        for (val internalFeature : internalFeatures) {
            val featureFetchInternalSpan = TRACING_HELPER.startSpanManual(
                    "featureFetchInternal-" + internalFeature.getFeatureName(),
                    getTagsForSpan(internalFeature)
            );
            FeatureWithSamples featureWithSamples = new FeatureWithSamples(internalFeature, result);
            List<Map<String, Object>> featureValues = getFeatureValuesInParallel(featureWithSamples);
            if (log.isDebugEnabled()) {
                log.debug("Internal feature values={}", featureValues);
            }

            result = mergeFeaturesPerSample(featureValues, result);
            featureFetchInternalSpan.finish();
        }

        return result.isEmpty() ? processedFeatures : result;
    }

    private java.util.Map<String, String> getTagsForSpan(FeatureDTO internalFeature) {
        return HashMap.of("model_name", modelInferenceRequest.getModel().getModelName(),
                        "model_version", modelInferenceRequest.getModel().getVersion(),
                        "feature_name", internalFeature.getFeatureName(),
                        "feature_version", internalFeature.getMajorVersion() + "." + internalFeature.getMinorVersion(),
                        "feature_size", String.valueOf(modelInferenceRequest.getModelInferenceRequest().getFeatures().size()))
                .toJavaMap();
    }

    private List<Map<String, Object>> mergeFeaturesPerSample(List<Map<String, Object>> featuresA, List<Map<String, Object>> featuresB) {
        return featuresA.zip(featuresB).map(i -> i._1.merge(i._2));
    }

    private List<Map<String, Object>> getFeatureValuesInParallel(FeatureWithSamples featureWithSamples) {
        // Gets the values for the correspondent feature (cache/db).
        List<FeaturePair> collectedFeatureValues = featureWithSamples.collectFeatureValues();
        List<FeaturePair> samplePairs = featureWithSamples.mapInputSamples();

        // Converts them into a map.
        Map<FeatureKeyDTO, Map<String, Object>> collectedFeatureMap =
                HashMap.ofEntries(collectedFeatureValues.map(i -> Tuple.of(i.featureKey, i.featureValue)));

        logCounter(featureWithSamples, collectedFeatureValues);

        return samplePairs.map(samplePair -> generateFeatureMap(featureWithSamples, collectedFeatureMap, samplePair));
    }

    private Map<String, Object> generateFeatureMap(FeatureWithSamples featureWithSamples,
                                                   Map<FeatureKeyDTO, Map<String, Object>> collectedFeatureMap,
                                                   FeaturePair samplePair) {
        Map<String, Object> features = collectedFeatureMap
                .get(samplePair.getFeatureKey())
                .filter(Map::nonEmpty)
                .getOrElse(() -> getDefaultIfExists(samplePair, featureWithSamples));

        if (featureWithSamples.getFeature().shouldExposeStoreFields() && featureWithSamples.getFeature().getFeatureOrder() != null) {
            // Add a new feature for the lookup key value used for this row.
            // Using "merge" to avoid intermediate map copies when using "put" on an immutable map.
            Map<String, Object> newFeature = HashMap.of(
                    InternalFieldsUtils.getInternalLookupKeysField(featureWithSamples.getFeature().getFeatureOrder()),
                    samplePair.getFeatureKey().getFeatureKey()
            );
            return features.merge(newFeature);
        }

        return features;
    }

    private void logCounter(FeatureWithSamples featureWithSamples, List<FeatureStoreInternalRule.FeaturePair> features) {
        meterRegistry.counter(Metrics.name(getClass(), "fetchInternalFeatures"),
                        "feature_name", featureWithSamples.getFeature().getFeatureName(),
                        "major_version", featureWithSamples.getFeature().getMajorVersion(),
                        "minor_version", featureWithSamples.getFeature().getMinorVersion()
                )
                .increment(features.size());
    }

    private Map<String, Object> getDefaultIfExists(FeaturePair featurePair, FeatureWithSamples featureWithSamples) {
        boolean isOptional = featurePair.getFeature().getFeatureOptional() != null && featurePair.getFeature().getFeatureOptional();
        val result = Option.when(isOptional,
                        () -> {
                            // avoid computing and caching the defaultValue for this field if it's already done
                            // we'll store this in the instance for easy access
                            Map<String, Object> defaultValue = featureWithSamples.getLocallyCachedDefaultFeatureValue() != null
                                    ? featureWithSamples.getLocallyCachedDefaultFeatureValue()
                                    : getFeatureDefaultValue(featurePair);

                            featureWithSamples.setLocallyCachedDefaultFeatureValue(defaultValue);
                            return defaultValue;
                        }
                )
                .peek(defaultFeatureValue -> logDefaultFeatureValue(featurePair, defaultFeatureValue))
                .getOrElse(HashMap::empty);
        if (isOptional) {
            meterRegistry.counter(Metrics.name(getClass(), "getDefaultValueForInternalFeature"),
                            "feature_name", featurePair.getFeature().getFeatureName(),
                            "major_version", featurePair.getFeature().getMajorVersion(),
                            "minor_version", featurePair.getFeature().getMinorVersion()
                    )
                    .increment();
        }
        return result;
    }

    private void logDefaultFeatureValue(FeaturePair featurePair, Map<String, Object> defaultFeatureValue) {
        log.debug(
                "Could not find " +
                "internal_feature={}," +
                "model={}," +
                "feature_major={}," +
                "feature_minor={}," +
                "feature_key={}," +
                "using default_value={}",
                featurePair.featureKey.getFeatureName(),
                modelInferenceRequest.getModel().getModelName(),
                featurePair.featureKey.getMajorVersion(),
                featurePair.featureKey.getMinorVersion(),
                featurePair.featureKey.getFeatureKey(),
                defaultFeatureValue
        );
    }

    private Map<String, Object> getFeatureDefaultValue(FeaturePair featurePair) {
        return Option.of(featurePair)
                .map(FeaturePair::getFeatureDefaultValue)
                .filter(StringUtils::isNotEmpty)
                .map(featureValue -> tryToReadMap(featurePair.getFeature(), featureValue))
                .filter(Try::isSuccess)
                .map(Try::get)
                .peek(defaultFeatureValue -> updateCacheIfNeeded(featurePair, defaultFeatureValue))
                .getOrElse(HashMap::empty);
    }

    private void updateCacheIfNeeded(FeaturePair featurePair, Map<String, Object> defaultFeatureValue) {
        Option.of(defaultFeatureValue)
                .filter(any -> modelInferenceRequest.isModelFeaturesStoreDefaultInCache())
                .peek(any -> addMetricForFeatureValueInCache(featurePair))
                .peek(any -> featureValueService.updateCache(featurePair.getFeatureKey(), Option.of(
                        featurePair.getFeatureKey()
                                .toPartialFeatureValue()
                                .featureValue(serializeMap(defaultFeatureValue))
                                .build()))
                );
    }

    private void addMetricForFeatureValueInCache(FeaturePair featurePair) {
        meterRegistry.counter(Metrics.name(getClass(), "storeDefaultValueForInternalFeatureInCache"),
                Tags.of("feature_name", featurePair.getFeatureKey().getFeatureName(),
                        "major_version", String.valueOf(featurePair.getFeatureKey().getMajorVersion()),
                        "minor_version", String.valueOf(featurePair.getFeatureKey().getMinorVersion())));
    }

    private String serializeMap(Map<String, ?> map) {
        try {
            return ObjectMapperHelper.INSTANCE.writeValueAsString(map);
        } catch (Exception e) {
            throw new RuntimeException("Error converting to map json string: " + map);
        }
    }

    private Map<String, Object> readValueAsMap(FeatureDTO featureModel, FeatureValueDTO featureValue) {
        return readValueAsMap(featureModel, Option.of(featureValue));
    }

    private Map<String, Object> readValueAsMap(FeatureDTO featureModel, Option<FeatureValueDTO> featureValue) {
        return featureValue
                .map(FeatureValueDTO::getFeatureValue)
                .map(afeatureValue -> tryToReadMap(featureModel, afeatureValue))
                .filter(Try::isSuccess)
                .map(Try::get)
                .getOrElse(HashMap::empty);
    }

    private Try<Map<String, Object>> tryToReadMap(FeatureDTO feature, String aFeatureStore) {
        return Try.of(() -> readMap(aFeatureStore))
                .onFailure(e -> log.error("Failed to convert feature={} value to map={} when invoking model={}",
                        feature.getFeatureName(), aFeatureStore, modelInferenceRequest.getModel().getModelName(), e));
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> readMap(String aFeatureStore) throws JsonProcessingException {
        return mapper.readValue(aFeatureStore, Map.class);
    }

    private FeatureKeyDTO buildFeatureKey(Object featureKey, FeatureDTO aFeature) {
        return FeatureKeyDTO
                .builder()
                .featureName(aFeature.getFeatureName())
                .minorVersion(Integer.valueOf(aFeature.getMinorVersion()))
                .majorVersion(Integer.valueOf(aFeature.getMajorVersion()))
                .featureKey(String.valueOf(featureKey))
                .build();
    }

    @Value
    private static class FeaturePair {
        private static final FeaturePair EMPTY_FEATURE_PAIR = new FeaturePair(null, HashMap.empty(), null);
        FeatureKeyDTO featureKey;
        Map<String, Object> featureValue;
        FeatureDTO feature;

        String getFeatureDefaultValue() {
            return feature.getFeatureDefaultValue();
        }
    }

    @With
    @Getter
    @AllArgsConstructor
    private class FeatureWithSamples {
        FeatureDTO feature;
        List<Map<String, Object>> samples;
        @Setter
        Map<String, Object> locallyCachedDefaultFeatureValue;

        private FeatureWithSamples(FeatureDTO theFeature, List<Map<String, Object>> theSamples) {
            this.feature = theFeature;
            this.samples = theSamples;
        }

        private List<FeaturePair> collectFeatureValues() {
            return modelInferenceRequest.isCacheroleFeatureFlagEnabled()
                    ? getFeaturesUsingMultiGet()
                    : getFeaturesInParallel();
        }

        private List<FeaturePair> getFeaturesUsingMultiGet() {
            List<FeatureKeyDTO> featureKeys = samples
                    .toStream()
                    .map(this::featuresLookUp)
                    .distinct()
                    .toList();

            val allFeatureValues = featureValueService.getMany(featureKeys);
            return featureKeys
                    .map(featureKey -> new FeaturePair(
                            featureKey,
                            allFeatureValues.get(featureKey).map(value -> readValueAsMap(feature, value)).getOrElse(HashMap::empty),
                            feature)
                    );
        }

        private List<FeaturePair> getFeaturesInParallel() {
            return samples
                    .toStream()
                    .map(this::featuresLookUp)
                    .distinct()
                    .map(this::fetchFeatureValue)
                    .collect(collectingAndThen(
                            toList(),
                            ControlPlaneUtils::waitForCompletion
                    ))
                    .collect(List.collector());
        }

        private FeatureKeyDTO featuresLookUp(Map<String, Object> sample) {
            val globalFeatures = modelInferenceRequest.getGlobalFeatures();
            val keyFieldValues = feature.getFeatureStoreFields().map(keyName -> findValueOfKey(keyName, sample, globalFeatures));
            val composedKey = String.join(MULTIKEY_LOOKUP_SET, keyFieldValues);
            return buildFeatureKey(composedKey, feature);
        }

        private String findValueOfKey(String key, Map<String, Object> sample, Map<String, Object> globalFeatures) {
            return getKeyFromFeaturesMap(key, sample) //Search key in sample inputs
                    .orElse(getKeyFromFeaturesMap(key, globalFeatures)) //Search key in global features
                    .map(String::valueOf)
                    .getOrElseThrow(() -> new RuntimeException(String.format("Missing required key=%s when invoking model=%s, " +
                                                                             "feature=%s, sample=%s, globalFeatures=%s",
                            key, modelInferenceRequest.getModel().getModelName(), feature.getFeatureName(),
                            ControlPlaneUtils.toString(sample), ControlPlaneUtils.toString(globalFeatures))));
        }

        private Option<Object> getKeyFromFeaturesMap(String key, Map<String, Object> map) {
            if (map == null) {
                return Option.none();
            }
            return map.get(key);
        }

        private CompletableFuture<FeaturePair> fetchFeatureValue(FeatureKeyDTO featureKey) {
            return featureValueService.getFeatureValue(featureKey)
                    .thenApply(featureValueAsOption -> new FeaturePair(
                            featureKey,
                            readValueAsMap(feature, Option.ofOptional(featureValueAsOption)),
                            feature
                    ));
        }

        private List<FeaturePair> mapInputSamples() {
            return samples.map(sample -> new FeaturePair(featuresLookUp(sample), sample, feature));
        }
    }
}

package com.grubhub.garcon.controlplane.services.controlplane.geo;

import ch.hsr.geohash.GeoHash;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.grubhub.garcon.ensembler.mapper.ObjectMapperHelper;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static ch.hsr.geohash.GeoHash.withCharacterPrecision;

/**
 * Tools to interact with GEO coordinates
 */
@UtilityClass
@Slf4j
public class GeoUtils {

    private static final ObjectMapper objectMapper = ObjectMapperHelper.INSTANCE;
    private static final Pattern POINT_PATTERN = Pattern.compile("^POINT\\s?\\(\\s*(-?\\d+(\\.\\d+)?)\\s+(-?\\d+(\\.\\d+)?)\\)$");

    /**
     * Computes all GEO hashes contained by the given GEO polygon, using the given precision.
     * It returns cells which center are inside the polygon.
     * If the GEO polygon has inner polygons then the geo-hashes of those are removed from the output.
     * @param polygon GEO Polygon to split into geo-hashes
     * @param precision geo-hash precision (ex: 5). A large number will produce more geo-hashes.
     * @param flipCoordinates true to flip x-y coordinates (expected format: x=lat, y=long)
     * @return All geo-hashes inside the given polygon
     */
    public static Set<String> geoPolygonToGeoHashes(GeoPolygon polygon, int precision, boolean flipCoordinates) {
        List<ConvexPolygon> convexPolygons = ConvexPolygon.from(polygon, flipCoordinates);
        ConvexPolygon outerPolygon = convexPolygons.get(0);
        List<ConvexPolygon> innerPolygons = convexPolygons.subList(1, convexPolygons.size());

        //Convert outer and inner polygons to geo hashes
        Set<String> outerGeoHashes = polygonToGeoHashes(outerPolygon, precision);
        Set<String> innerGeoHashes = innerPolygons.stream().map(p -> polygonToGeoHashes(p, precision)).flatMap(Collection::stream).collect(Collectors.toSet());

        //Remove inner geo-hashes
        outerGeoHashes.removeAll(innerGeoHashes);
        return outerGeoHashes;
    }

    /**
     * Create GEO polygon from list of values
     * @param values list of tuples(x, y) as a continuous array
     * @return GEO polygon
     */
    public static GeoPolygon createPolygon(double[] values) {
        int size = values.length / 2;
        if (values.length < 8 && values.length % 2 != 0) {
            throw new RuntimeException("Invalid size: " + values.length);
        }
        List<List<Double>> coordinates = new ArrayList<>(values.length / 2);
        for (int i = 0; i < size; i++) {
            coordinates.add(Arrays.asList(values[i * 2], values[i * 2 + 1]));
        }
        return new GeoPolygon("Polygon", Collections.singletonList(coordinates));
    }

    /**
     * Create GeoPolygon from Json string: http://geojson.org/geojson-spec.html#polygon
     */
    public static GeoPolygon createPolygon(String polygonAsString) {
        try {
            return objectMapper.readValue(polygonAsString, GeoPolygon.class);
        } catch (Exception e) {
            throw new RuntimeException("Error parsing GeoPolygon JSON string: " + polygonAsString, e);
        }
    }

    public static Optional<Point2d> getCoordinate(String location) {
        Matcher matcher = POINT_PATTERN.matcher(Strings.nullToEmpty(location).toUpperCase());
        if (matcher.find()) {
            try {
                double longitude = Double.parseDouble(matcher.group(1));
                double latitude = Double.parseDouble(matcher.group(3));
                return Optional.of(new Point2d(longitude, latitude));
            } catch (Exception e) {
                log.error("Could not determine coordinate for string={}", location, e);
                return Optional.empty();
            }
        }
        return Optional.empty();
    }

    public static Optional<String> getGeoHash(String location, int precision) {
        return getCoordinate(location)
                .map(value -> withCharacterPrecision(value.y, value.x, precision))
                .map(GeoHash::toBase32);
    }


    /**
     * Convert convex polygon into a set of geo-hashes of the given precision
     */
    private static Set<String> polygonToGeoHashes(ConvexPolygon polygon, int precision) {
        //Get polygon bounding box
        BoundingBox boundingBox = polygon.computeBoundingBox();
        Point2d northEast = boundingBox.max;
        Point2d southWest = boundingBox.min;

        //Convert extreme vertices to geo-hash
        GeoHash hashNorthEast = GeoHash.withCharacterPrecision(northEast.x, northEast.y, precision);
        GeoHash hashSouthWest = GeoHash.withCharacterPrecision(southWest.x, southWest.y, precision);


        //Compute steps to iterate over latitude and longitude grid
        BoundingBox cellBbox = new BoundingBox(
                new Point2d(hashNorthEast.getBoundingBox().getMinLat(), hashNorthEast.getBoundingBox().getMinLon()),
                new Point2d(hashNorthEast.getBoundingBox().getMaxLat(), hashNorthEast.getBoundingBox().getMaxLon()));
        Point2d neCenterPoint = cellBbox.computeCenter();
        Point2d swCenterPoint = Point2d.from(hashSouthWest.getBoundingBoxCenterPoint());
        int latitudeStep = ((int) Math.floor((neCenterPoint.x - swCenterPoint.x) / cellBbox.getWidth())) + 1;
        int longitudeStep = ((int) Math.floor((neCenterPoint.y - swCenterPoint.y) / cellBbox.getHeight())) + 1;

        //Loop through latitude/longitude grid, one step at a time, from south-west to north-east corner
        Set<String> output = new HashSet<>();
        GeoHash currentLatCell = hashSouthWest.getSouthernNeighbour().getSouthernNeighbour().getWesternNeighbour().getWesternNeighbour();
        for (int i = 0; i < latitudeStep + 2; i++) {
            //Move one cell up
            currentLatCell = currentLatCell.getNorthernNeighbour();
            GeoHash prevCell = currentLatCell;
            for (int j = 0; j < longitudeStep + 2; j++) {
                //Move one cell right
                GeoHash currentCell = prevCell.getEasternNeighbour();
                //Check if cell is inside the polygon
                Point2d bboxCenter = Point2d.from(currentCell.getBoundingBoxCenterPoint());
                if (polygon.contains(bboxCenter)) {
                    output.add(currentCell.toBase32());
                }
                prevCell = currentCell;
            }
        }
        return output;
    }


    /**
     * A Convex polygon represented by its list of vertices (without repeating the last one)
     */
    public static class ConvexPolygon {
        public Point2d[] vertices;
        public ConvexPolygon(Point2d[] vertices) {
            this.vertices = Arrays.copyOf(vertices, vertices.length);
        }
        public static List<ConvexPolygon> from(GeoPolygon p, boolean flipCoordinates) {
            return p.getCoordinates().stream()
                    .map(points -> new ConvexPolygon(points.subList(0, points.size() - 1)
                            .stream().map(list -> Point2d.from(list, flipCoordinates))
                            .toArray(Point2d[]::new)))
                    .collect(Collectors.toList());
        }
        public BoundingBox computeBoundingBox() {
            return BoundingBox.from(Stream.of(vertices));
        }
        public boolean contains(Point2d p) {
            return Point2d.inConvexPolygon(p, this);
        }
    }

    /**
     * A 2D bounding box defined by its min and max point
     */
    public static class BoundingBox {
        public Point2d min;
        public Point2d max;
        public BoundingBox(Point2d min, Point2d max) {
            this.min = min;
            this.max = max;
        }
        public static BoundingBox from(Stream<Point2d> points) {
            BoundingBox bbox = new BoundingBox(new Point2d(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY),
                    new Point2d(Double.NEGATIVE_INFINITY, Double.NEGATIVE_INFINITY));
            points.forEach(v -> {
                if (v.x < bbox.min.x) {
                    bbox.min.x = v.x;
                }
                if (v.x > bbox.max.x) {
                    bbox.max.x = v.x;
                }
                if (v.y < bbox.min.y) {
                    bbox.min.y = v.y;
                }
                if (v.y > bbox.max.y) {
                    bbox.max.y = v.y;
                }
            });
            return bbox;
        }
        @Override
        public String toString() {
            return String.format("min: %s, max: %s", min, max);
        }
        public double getWidth(){
            return max.x - min.x;
        }
        public double getHeight(){
            return max.y - min.y;
        }
        public Point2d computeCenter() {
            return new Point2d((min.x + max.x) / 2, (min.y + max.y) / 2);
        }
    }

    /**
     * Point in 2D
     */
    public static class Point2d {
        public double x;
        public double y;
        public Point2d(double x, double y) {
            this.x = x;
            this.y = y;
        }
        @Override
        public String toString() {
            return String.format("(%f, %f)", x, y);
        }
        public static Point2d from(List<Double> list, boolean flipCoordinates) {
            return flipCoordinates ? new Point2d(list.get(1), list.get(0)) : new Point2d(list.get(0), list.get(1));
        }
        public static Point2d from(ch.hsr.geohash.WGS84Point p) {
            return new Point2d(p.getLatitude(), p.getLongitude());
        }
        public void set(double x, double y) {
            this.x = x;
            this.y = y;
        }

        public static Point2d add(Point2d u, Point2d v) {
            return new Point2d(u.x + v.x, u.y + v.y);
        }

        public static Point2d sub(Point2d u, Point2d v) {
            return new Point2d(u.x - v.x, u.y - v.y);
        }

        public static Point2d add(Point2d u, double s) {
            return new Point2d(u.x + s, u.y + s);
        }

        public static double cross(Point2d u, Point2d v) {
            return u.x * v.y - u.y * v.x;
        }

        public static boolean same(Point2d u, Point2d v, double epsilon) {
            return Math.abs(u.x - v.x) < epsilon && Math.abs(u.y - v.y) < epsilon;
        }

        /**
         * Test if the given point is inside the give polygon
         */
        public static boolean inConvexPolygon(Point2d testPoint, ConvexPolygon polygon) {
            //Keep track of cross product sign changes
            int pos = 0;
            int neg = 0;
            for (int i = 0; i < polygon.vertices.length; i++) {
                //If point is in the polygon
                if (same(polygon.vertices[i], testPoint, 0.0001)) {
                    return true;
                }

                //Segment between test point and current vertex
                Point2d segment1 = sub(testPoint, polygon.vertices[i]);

                //Segment between next and current vertex
                int nextIdx = i + 1 == polygon.vertices.length ? 0 : i + 1;
                Point2d segment2 = sub(polygon.vertices[nextIdx], polygon.vertices[i]);

                //Compute the cross product
                double d = cross(segment1, segment2);

                //If the sign changes, then point is outside
                if (d > 0) {
                    pos++;
                }
                if (d < 0) {
                    neg++;
                }
                if (pos > 0 && neg > 0) {
                    return false;
                }
            }
            return true;
        }

    }
}

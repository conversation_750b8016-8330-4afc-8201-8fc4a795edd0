package com.grubhub.garcon.controlplane.services.controlplane.impl;

import com.google.inject.Inject;
import com.grubhub.garcon.controlplane.cassandra.dao.FlowByGeoHashDao;
import com.grubhub.garcon.controlplane.cassandra.models.FlowByGeoHash;
import com.grubhub.garcon.controlplane.services.common.service.CachedConfigurationService;
import com.grubhub.garcon.controlplane.services.controlplane.FlowByGeoHashService;
import com.grubhub.garcon.controlplane.services.controlplane.MarketService;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.val;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class FlowByGeoHashServiceImpl implements FlowByGeoHashService {
    private final FlowByGeoHashDao flowByGeoHashDao;
    private final MarketService marketService;
    private final CachedConfigurationService cachedConfigurationService;

    public void insertGeoHashes(FlowDTO flow) {
        flowByGeoHashDao.insertMany(marketService.getFlowsByGeoHash(flow).asJava());
    }

    public void insert(FlowByGeoHash flowByGeoHash) {
        flowByGeoHashDao.insert(flowByGeoHash);
    }

    @Override
    public Option<FlowByGeoHash> select(@NonNull String flowSet, @NonNull String geoHash) {
        return Option.ofOptional(flowByGeoHashDao.select(flowSet, geoHash));
    }

    @Override
    public CompletableFuture<List<FlowByGeoHash>> selectAllAsync(@NonNull String flowSet, @NonNull String geohash) {
        return cachedConfigurationService.getFlowsByGeohash(flowSet, geohash).thenApply(List::ofAll);
    }

    @Override
    public CompletableFuture<List<FlowByGeoHash>> selectAllAsyncWithoutCache(@NonNull String flowSet, @NonNull String geohash) {
        return flowByGeoHashDao.selectAllAsync(flowSet, geohash).toListCompletableFuture().thenApply(List::ofAll);
    }

    @Override
    public void deleteGeoHashesForFlow(FlowDTO flowDTO) {
        val geohashes = marketService.getFlowsByGeohashOrGlobal(flowDTO).map(FlowByGeoHash::getGeohash);
        flowByGeoHashDao.delete(flowDTO.getFlowSet(), UUID.fromString(flowDTO.getFlowId()), geohashes.asJava());
    }
}

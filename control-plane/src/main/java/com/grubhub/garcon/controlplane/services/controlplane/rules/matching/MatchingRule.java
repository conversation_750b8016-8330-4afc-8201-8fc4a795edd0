package com.grubhub.garcon.controlplane.services.controlplane.rules.matching;

import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.FlowDTO;
import com.grubhub.garcon.controlplaneapi.models.controlplane.dto.MatchingStrategy;
import com.grubhub.garcon.controlplane.services.controlplane.rules.actions.MatchingAction;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

public interface MatchingRule {
    Optional<MatchingAction> applicableTo(FlowDTO flow);

    default MatchingRule orElse(MatchingRule next) {
        return new ChainedMatchingRule(this, next);
    }

    static boolean isMatchingStrategyEqualToLocation(FlowDTO flow) {
        return Optional.ofNullable(flow.getMatchingStrategy())
                .filter(StringUtils::isNotBlank)
                .map(MatchingStrategy::valueOf)
                .map(matchingStrategy -> matchingStrategy == MatchingStrategy.LOCATION)
                .orElse(false);
    }

}

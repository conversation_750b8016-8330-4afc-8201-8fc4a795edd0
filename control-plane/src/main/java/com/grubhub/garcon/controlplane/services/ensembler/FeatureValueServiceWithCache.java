package com.grubhub.garcon.controlplane.services.ensembler;

import com.google.common.base.Throwables;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.cacherole.Cacherole;
import com.grubhub.garcon.controlplane.metrics.Metrics;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureKeyDTO;
import com.grubhub.garcon.controlplaneapi.models.ensembler.dto.FeatureValueDTO;
import com.grubhub.roux.api.datetime.JavaDateTimeHelper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.vavr.Tuple2;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import net.javacrumbs.futureconverter.java8guava.FutureConverter;

import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;


@Slf4j
public class FeatureValueServiceWithCache implements FeatureValueService {
    private static final String CACHE_HIT_COUNTER = Metrics.name(FeatureValueServiceWithCache.class, "internalFeaturesCacheHits");
    private static final String CACHE_MISSED_COUNTER = Metrics.name(FeatureValueServiceWithCache.class, "internalFeaturesCacheMisses");
    private static final String CACHE_GET_TIMER = Metrics.name(FeatureValueServiceWithCache.class, "internalFeaturesGet");
    private final FeatureValueService featureValueService;
    private final Cacherole cacherole;
    private final MeterRegistry meterRegistry;
    private final JavaDateTimeHelper dateTimeHelper;

    @Inject
    public FeatureValueServiceWithCache(@Named("featureValueService") FeatureValueService featureValueService,
                                        @Named("cacherole-internalFeatures") Cacherole cacherole,
                                        MeterRegistry meterRegistry,
                                        JavaDateTimeHelper dateTimeHelper) {
        this.featureValueService = featureValueService;
        this.cacherole = cacherole;
        this.meterRegistry = meterRegistry;
        this.dateTimeHelper = dateTimeHelper;
    }

    @Override
    public CompletableFuture<Optional<FeatureValueDTO>> getFeatureValue(FeatureKeyDTO featureKey) {
        if (!cacherole.isEnabled()) {
            return featureValueService.getFeatureValue(featureKey);
        }
        val timer = meterRegistry.timer(CACHE_GET_TIMER,
                Tags.of("feature_name", featureKey.getFeatureName(),
                        "major_version", String.valueOf(featureKey.getMajorVersion()),
                        "minor_version", String.valueOf(featureKey.getMinorVersion())
                ));

        val requestTime = dateTimeHelper.instant();
        return FutureConverter.toCompletableFuture(cacherole.getAsync(featureKey.getCacheKey(), FeatureValueDTO.class))
                .thenApply(featureValueAsOptional -> featureValueAsOptional.toJavaUtil())
                .handle((result, error) -> {
                    timer.record(Duration.between(requestTime, Instant.now()));
                    CompletableFuture<Optional<FeatureValueDTO>> res;
                    if (error != null) {
                        log.warn(Throwables.getRootCause(error).getMessage());
                        res = readFromSource(featureKey).thenApply(Option::toJavaOptional);
                    } else if (result.isPresent()) {
                        logCacheHit(Option.ofOptional(result));
                        res = CompletableFuture.completedFuture(result);
                    } else {
                        res = readFromSource(featureKey).thenApply(Option::toJavaOptional);
                    }
                    return res;
                })
                .thenCompose(Function.identity());
    }

    private void logCacheHit(Option<FeatureValueDTO> result) {
        result.peek(featureValue -> meterRegistry.counter(CACHE_HIT_COUNTER,
                        Tags.of("feature_name", featureValue.getFeatureName(),
                                "major_version", String.valueOf(featureValue.getMinorVersion()),
                                "minor_version", String.valueOf(featureValue.getMinorVersion())))
                .increment());
    }

    private CompletableFuture<Option<FeatureValueDTO>> readFromSource(FeatureKeyDTO featureKey) {
        return featureValueService.getFeatureValue(featureKey)
                .thenApply(featureValueAsOption -> updateCache(featureKey, Option.ofOptional(featureValueAsOption)))
                .thenApply(featureValueAsOption -> featureValueAsOption
                        .peek(featureValue ->
                                meterRegistry.counter(
                                                CACHE_MISSED_COUNTER,
                                                Tags.of("feature_name", featureValue.getFeatureName(),
                                                        "major_version", String.valueOf(featureValue.getMinorVersion()),
                                                        "minor_version", String.valueOf(featureValue.getMinorVersion()))

                                        )
                                        .increment()
                        )
                );
    }

    @Override
    public Option<FeatureValueDTO> updateCache(FeatureKeyDTO featureKey, Option<FeatureValueDTO> featureValueAsOption) {
        return featureValueAsOption
                .peek(featureValue -> cacherole.put(featureKey.getCacheKey(), featureValue));
    }

    @Override
    public Map<FeatureKeyDTO, FeatureValueDTO> getMany(List<FeatureKeyDTO> featureKeys) {
        if (!cacherole.isEnabled()) {
            return featureValueService.getMany(featureKeys);
        }

        val cachedFeatureKeys = getCachedFeatureKeys(featureKeys);
        val nonCachedFeatureKeys = featureKeys.removeAll(cachedFeatureKeys.keySet());

        addCacheMissedCounter(nonCachedFeatureKeys);

        val featureValuesFromDb = featureValueService.getMany(nonCachedFeatureKeys);

        featureValuesFromDb.forEach((k, v) -> cacherole.putAsync(v.getCacheKey(), v));

        return cachedFeatureKeys.merge(featureValuesFromDb);
    }

    private void addCacheMissedCounter(List<FeatureKeyDTO> nonCachedFeatureKeys) {
        meterRegistry.counter(CACHE_MISSED_COUNTER,
                        Tags.of("feature_name", nonCachedFeatureKeys.isEmpty()
                                        ? "null"
                                        : nonCachedFeatureKeys.get(0).getFeatureName(),
                                "major_version", nonCachedFeatureKeys.isEmpty()
                                        ? "null"
                                        : String.valueOf(nonCachedFeatureKeys.get(0).getMinorVersion()),
                                "minor_version", nonCachedFeatureKeys.isEmpty()
                                        ? "null"
                                        : String.valueOf(nonCachedFeatureKeys.get(0).getMinorVersion())
                        )
                )
                .increment(nonCachedFeatureKeys.size());
    }

    Map<FeatureKeyDTO, FeatureValueDTO> getCachedFeatureKeys(List<FeatureKeyDTO> featureKeys) {
        val timer = getMultiGetTimer(featureKeys);
        val requestTime = Instant.now();

        val cacheKeys = cacheKeysAsMap(featureKeys);

        Map<FeatureKeyDTO, FeatureValueDTO> cachedFeatureValuesByKey = HashMap.ofAll(
                        cacherole
                                .getBulkOrEmpty(cacheKeys.keySet().toJavaList(), FeatureValueDTO.class))
                .mapKeys(cacheKeys::get)
                .reject(this::emptyKeyOrValue)
                .map((k, v) -> new Tuple2<>(k.get(), v.get()));
        timer.record(Duration.between(requestTime, Instant.now()));

        addCacheHitMetric(cachedFeatureValuesByKey.values().toList());

        return cachedFeatureValuesByKey;
    }

    private boolean emptyKeyOrValue(Tuple2<Option<FeatureKeyDTO>, java.util.Optional<FeatureValueDTO>> tuple) {
        return tuple._1().isEmpty() || tuple._2().isEmpty();
    }

    private Map<String, FeatureKeyDTO> cacheKeysAsMap(List<FeatureKeyDTO> featureKeys) {
        return featureKeys.map(key -> new Tuple2<>(key.getCacheKey(), key)).collect(HashMap.collector());
    }

    private void addCacheHitMetric(List<FeatureValueDTO> cachedFeatureValues) {
        meterRegistry.counter(CACHE_HIT_COUNTER,
                Tags.of("feature_name", cachedFeatureValues.isEmpty()
                                ? "null"
                                : Option.of(cachedFeatureValues.get(0)).map(FeatureValueDTO::getFeatureName).getOrElse("null"),
                        "major_version", cachedFeatureValues.isEmpty()
                                ? "null"
                                : Option.of(cachedFeatureValues.get(0))
                                .map(FeatureValueDTO::getMajorVersion)
                                .map(String::valueOf)
                                .getOrElse("null"),
                        "minor_version", cachedFeatureValues.isEmpty()
                                ? "null"
                                : Option.of(cachedFeatureValues.get(0))
                                .map(FeatureValueDTO::getMinorVersion)
                                .map(String::valueOf)
                                .getOrElse("null"))

        ).increment(cachedFeatureValues.size());
    }

    private Timer getMultiGetTimer(List<FeatureKeyDTO> featureKeys) {
        return meterRegistry.timer(CACHE_GET_TIMER,
                Tags.of("feature_name", featureKeys.isEmpty() ? "null" : featureKeys.get(0).getFeatureName(),
                        "major_version", featureKeys.isEmpty() ? "null" : String.valueOf(featureKeys.get(0).getMajorVersion()),
                        "minor_version", featureKeys.isEmpty() ? "null" : String.valueOf(featureKeys.get(0).getMinorVersion())
                ));
    }

    @Override
    public CompletableFuture<Void> createOrUpdateFeatureValue(FeatureValueDTO featureValue) {
        cacherole.put(featureValue.getCacheKey(), featureValue);
        return featureValueService.createOrUpdateFeatureValue(featureValue);
    }

    @Override
    public void createOrUpdateMany(List<FeatureValueDTO> featureValues) {
        featureValues
                .map(featureValue -> cacherole.put(featureValue.getCacheKey(), featureValue));
        featureValueService.createOrUpdateMany(featureValues);
    }

    @Override
    public void deleteFeatureValue(FeatureKeyDTO featureKey) {
        cacherole.delete(featureKey.getCacheKey());
        featureValueService.deleteFeatureValue(featureKey);
    }

    @Override
    public void deleteMany(List<FeatureKeyDTO> featureKeys) {
        featureKeys.map(featureKey -> cacherole.delete(featureKey.getCacheKey()));
        featureValueService.deleteMany(featureKeys);
    }
}

package com.grubhub.garcon.ddmlcacherole.dao;

import com.google.common.base.Optional;
import com.grubhub.garcon.cacherole.Cacherole;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import static net.javacrumbs.futureconverter.java8guava.FutureConverter.toCompletableFuture;

@Slf4j
public abstract class SimpleCacheDao implements CacheDao {

    protected Cacherole cacherole;

    public SimpleCacheDao(Cacherole cacherole) {
        this.cacherole = cacherole;
    }

    public boolean isEnabled() {
        return this.cacherole.isEnabled();
    }

    public <T> CompletableFuture<Optional<T>> getAsync(String key, Class<T> type, String collectionType) {
        log.debug("Getting async cache key={}. Collection type={}", key, collectionType);
        return toCompletableFuture(cacherole.getAsync(key, type));
    }

    public void putAsync(String key, Object value, String collectionType) {
        log.debug("Storing async cache key={}. Collection type={}", key, collectionType);
        cacherole.putAsync(key, value);
    }

    public void deleteBatch(List<String> keys, String collectionType) {
        // cacherole.deleteBatch method does not throw exception.
        List<Boolean> results = cacherole.deleteBatch(keys.toArray(String[]::new));
        logDeletion(results, keys, collectionType);
    }

    private void logDeletion(List<Boolean> deletedResults, List<String> totalKeys, String collectionType) {
        long successRatio = deletedResults.stream()
                .filter(result -> result)
                .count();

        log.info("Successfully deleted from the cache={}/{} for collection={} from total keys={}.",
                successRatio,
                deletedResults.size(),
                collectionType,
                totalKeys.size());
    }
}

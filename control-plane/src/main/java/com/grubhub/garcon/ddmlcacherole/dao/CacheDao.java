package com.grubhub.garcon.ddmlcacherole.dao;

import com.google.common.base.Optional;

import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface CacheDao {
    boolean isEnabled();
    <T> CompletableFuture<Optional<T>> getAsync(String key, Class<T> type, String collectionType);
    void putAsync(String key, Object value, String collectionType);
    void deleteBatch(List<String> keys, String collectionName);
}

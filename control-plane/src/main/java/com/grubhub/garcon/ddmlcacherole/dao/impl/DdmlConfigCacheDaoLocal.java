package com.grubhub.garcon.ddmlcacherole.dao.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.cacherole.Cacherole;
import com.grubhub.garcon.ddmlcacherole.dao.CacheDao;
import com.grubhub.garcon.ddmlcacherole.dao.SimpleCacheDao;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class DdmlConfigCacheDaoLocal extends SimpleCacheDao implements CacheDao {

    @Inject
    public DdmlConfigCacheDaoLocal(@Named("cacherole-ddmlConfigurationLocal") final Cacherole cacherole) {
        super(cacherole);
    }
}

package com.grubhub.garcon.ddmlcacherole.dao.impl;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.cacherole.Cacherole;
import com.grubhub.garcon.ddmlcacherole.dao.CacheDao;
import com.grubhub.garcon.ddmlcacherole.dao.SimpleCacheDao;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class DdmlConfigCacheDaoExternal extends SimpleCacheDao implements CacheDao {

    @Inject
    public DdmlConfigCacheDaoExternal(@Named("cacherole-ddmlConfiguration") final Cacherole cacherole) {
        super(cacherole);
    }
}

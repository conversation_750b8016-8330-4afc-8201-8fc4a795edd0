package com.grubhub.garcon.ddmlcacherole.dao.impl;

import com.google.common.base.Optional;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.grubhub.garcon.controlplane.config.TwoTierCacheConfig;
import com.grubhub.garcon.ddmlcacherole.dao.CacheDao;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * The two-tier cache DAO implementation aims to provide an extra in-memory level caching that can be configurable individually
 * for different entity (or collection) types (FLOW, MODEL, etc.), with the goal of relieve load from the centralized memcached cluster
 * and reduce the network throughput.
 * <p>
 * If the first tier is not enabled, this should work exactly the same as a simple cache configuration.
 * Otherwise, an implementation of GuavaCache will be used first, having Memcached as a second layer.
 */
@Slf4j
public class TwoTierCacheDaoImpl implements CacheDao {

    private final CacheDao secondTierDao;
    private final CacheDao firstTierDao;
    private final TwoTierCacheConfig twoTierConfig;

    @Inject
    public TwoTierCacheDaoImpl(@Named("cacheDao-ddmlConfigCacheDaoExternal") final CacheDao secondTierDao,
                               @Named("cacheDao-ddmlConfigCacheDaoLocal") final CacheDao firstTierDao,
                               TwoTierCacheConfig twoTierConfig) {
        this.secondTierDao = secondTierDao;
        this.firstTierDao = firstTierDao;
        this.twoTierConfig = twoTierConfig;
    }

    @Override
    public boolean isEnabled() {
        // Not checking firstTierDao.isEnabled() because we could work with the second tier only.
        return secondTierDao.isEnabled();
    }

    @Override
    public <T> CompletableFuture<Optional<T>> getAsync(String key, Class<T> type, String collectionType) {
        log.debug("Get async for two-tier cache. Key={}. Collection type={}", key, collectionType);
        if (!twoTierEnabledForEntity(collectionType)) {
            log.debug("Two-tier cache disabled for entity={}. Defaulting to Memcached only.", collectionType);
            return secondTierDao.getAsync(key, type, collectionType);
        }

        return firstTierDao.getAsync(key, type, collectionType).thenComposeAsync(firstTierResult -> {
            if (firstTierResult.isPresent()) {
                log.debug("First tier value present. Returning. Key={}. Collection type={}", key, collectionType);
                return CompletableFuture.completedFuture(firstTierResult);
            }

            log.debug("First tier value not found. Key={}. Collection type={}", key, collectionType);
            return secondTierDao.getAsync(key, type, collectionType).thenComposeAsync(
                    resultSecondTier -> {
                        if (resultSecondTier.isPresent()) {
                            log.debug("Second tier value present. Setting value in first tier. Key={}. Collection type={}", key, collectionType);
                            firstTierDao.putAsync(key, resultSecondTier, collectionType);
                        }

                        return CompletableFuture.completedFuture(resultSecondTier);
                    }
            );
        });
    }

    @Override
    public void putAsync(String key, Object value, String collectionType) {
        if (twoTierEnabledForEntity(collectionType)) {
            firstTierDao.putAsync(key, value, collectionType);
        }
        secondTierDao.putAsync(key, value, collectionType);
    }

    @Override
    public void deleteBatch(List<String> keys, String collectionType) {
        if (twoTierEnabledForEntity(collectionType)) {
            try {
                firstTierDao.deleteBatch(keys, collectionType);
            } catch (RuntimeException e) {
                log.error("First layer deleteBatch method failed during execution for keys={}. Continuing to second layer.", keys, e);
            }
        }

        secondTierDao.deleteBatch(keys, collectionType);
    }

    private boolean twoTierEnabledForEntity(String collectionType) {
        return twoTierEnabled() && twoTierConfig.getEntityLevel().getOrDefault(collectionType, false);
    }

    private boolean twoTierEnabled() {
        return twoTierConfig.getEnabled() && firstTierDao.isEnabled();
    }

}
